<template>
  <v-row style="min-height: calc(100vh - 48px)" :style="{'background-color': pageBackgroundColor}" wrap>
    <!-- Record Form -->
    <v-col v-if="$hasPerms('add_videorecordlog') && is_secure_page" cols="12" :md="layout === 'full' ? 12 : 6">
      <v-row class="px-3 pt-3">
        <h2>{{ $t('record.record-video') }}</h2>
        <v-spacer />
        <v-btn id="v-btn-setting" data-test="v-btn-setting" icon @click="dialog = true">
          <v-icon>mdi-cog</v-icon>
        </v-btn>
      </v-row>
      <v-divider class="my-3" />
      <record-form
        :is-return-mode="is_return_mode"
        :is-transfer-mode="is_transfer_mode"
        :is-piece-scan="!!settings.company.RECORD_PIECE_SCAN_MODE_ENABLE"
        :layout="layout"
        @open-setting="dialog = true"
        @click-view-history="history_dialog = true"
        @update:is-recording="is_recording=$event"
      />
    </v-col>

    <!-- Record History -->
    <v-col v-if="$hasPerms('add_videorecordlog') && is_secure_page && $vuetify.breakpoint.mdAndUp" cols="12" :md="layout === 'full' ? 12 : 6">
      <v-row class="pa-3">
        <h2>{{ $t('record.history') }}</h2>
        <v-spacer />
        <span class="caption">
          {{ $version() }}
        </span>
      </v-row>
      <v-divider class="mb-3" />
      <record-history-table />
    </v-col>

    <!--  -->
    <div v-if="!is_secure_page" style="display: flex; width: 100%; height: 80vh; justify-content: center; align-items: center;">
      <div class="text-center">
        <v-img src="/google-drive.png" width="200" class="mx-auto" />

        <v-btn
          color="primary"
          class="my-4"
          style="font-size: 24px; padding: 20px 40px; height: 80px;"
          @click="gapiSignIn"
        >
          {{ $t('connect-google-drive') }}
        </v-btn>
        <br>
        <a class="mt-4" href="/$/record">
          {{ $t('click-here-to-start') }}
        </a>
      </div>
    </div>
    <setting-dialog v-model="dialog" />
    <get-start-dialog :value="getStartStatus" @close="startIntro()" @skip="skipGetStart()" />
    <v-dialog
      v-if="$vuetify.breakpoint.smAndDown"
      v-model="history_dialog"
      eager
      fullscreen
      hide-overlay
      transition="dialog-bottom-transition"
    >
      <v-card>
        <v-card-title>
          <h2>{{ $t('record.history') }}</h2>
          <v-spacer />
          <v-btn icon @click="history_dialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-divider />
        <RecordHistoryTable />
        <div style="height: 40px" />
      </v-card>
    </v-dialog>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapActions } from 'vuex'
import introJs from 'intro.js'
import RecordForm from '~/components/home/<USER>'
import RecordHistoryTable from '~/components/home/<USER>'
import SettingDialog from '~/components/SettingDialog.vue'
import GetStartDialog from '~/components/getting-start/GetStartDialog.vue'
import { FEATURE_FLAGS, LoggedInUser } from '~/models'
import { items } from '~/components/layout/AppDrawer.vue'
import { SettingState } from '~/store/settings'
import { wait } from '~/utils'
export default Vue.extend({
  components: {
    RecordForm,
    RecordHistoryTable,
    SettingDialog,
    GetStartDialog
  },

  middleware: 'auth',

  data () {
    return {
      dialog: false,
      history_dialog: false,
      is_recording: false,
      is_return_mode: false,
      is_transfer_mode: false,
      is_secure_page: true,
      support_dialog: false
    }
  },

  head () {
    return {
      title: 'Dobybot.com - Record'
    }
  },

  computed: {
    layout (): string {
      if (this.settings.company.RECORD_PIECE_SCAN_MODE_ENABLE && this.is_recording) {
        const v = (this as any).$vuetify
        if (v.breakpoint.smAndDown) {
          return 'mobile-full'
        } else {
          return 'full'
        }
      }

      return 'simple'
    },

    settings (): SettingState {
      return this.$store.state.settings
    },

    getStartStatus (): boolean {
      return this.$store.state.get_start_status
    },

    introStatus (): boolean {
      return this.$getIntroStatus()
    },

    pageBackgroundColor (): string {
      if (this.is_return_mode) {
        return '#FFCDD2'
      }
      if (this.is_transfer_mode) {
        return 'rgb(*********** / 43%)'
      }
      return 'white'
    }
  },

  watch: {
    'settings.company.HOMEPAGE_URL': {
      handler (val) {
        if (val && !this.$hasPerms('add_videorecordlog')) {
          this.redirectHomePage()
        }
      }
    }
  },

  mounted () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.RECORD)) {
      setTimeout(() => {
        if (this.settings.company.HOMEPAGE_URL) {
          this.redirectHomePage()
        } else {
          this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
        }
      }, 1000)
    }

    const already_run_get_stared = !!localStorage.getItem('get-start-setting')
    const is_old_user = (!!localStorage.getItem('deviceId') && !!localStorage.getItem('googleDriveRootFolder'))

    if (is_old_user || already_run_get_stared) {
      if (localStorage.getItem('open-setting')) {
        this.dialog = true
        localStorage.removeItem('open-setting')
      }
      localStorage.setItem('get-start-setting', 'true')
    } else {
      // new user
      this.$store.dispatch('openGetStart')
    }

    this.$store.dispatch('gapi/autoSignIn')

    this.is_secure_page = this.$route.path.startsWith('/$/record')

    const mode = this.$route.query.mode
    if (mode === 'transfer') {
      this.is_transfer_mode = true
    }
    if (mode === 'return') {
      this.is_return_mode = true
    }
  },

  methods: {
    /**
     * สำหรับ กรณีที่ user ไม่มี permission หน้า record ให้ redirect ไปที่หน้าแรกสุดที่สามารถเข้าได้
     */
    redirectHomePage () {
      const user = this.$auth.user as never as LoggedInUser
      const perm = user.user_permissions[0]

      let target = '' as string | undefined

      if (this.settings.company.HOMEPAGE_URL !== 'auto') {
        target = this.settings.company.HOMEPAGE_URL
      } else {
        for (const item of items) {
          if ('permission' in item && item.permission === perm) {
            target = item.to || item.href
            break
          }

          if ('children' in item) {
            for (const child of item.children) {
              if (child.permission === perm) {
                target = child.to || child.href
                break
              }
            }
          }
        }
      }

      if (!target) {
        this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
        return
      }

      this.$router.push({ path: target })
    },

    openHelp (url: string) {
      window.open(url)
    },

    async startIntro () {
      this.$store.dispatch('closeGetStart')
      localStorage.removeItem('open-setting')

      this.$store.commit('set', { is_intro_running: true })

      // Scan barcode to start recording
      await this.$runIntro(introJs()
        .setOptions({
          steps: [{
            element: '#v-text-field-control-code',
            intro: this.$t('intro-start-by-scan-order-number'),
            title: this.$t('start-recording')
          }],
          showButtons: false,
          showBullets: false,
          exitOnOverlayClick: false
        })
        .onstart(() => {
          setTimeout(() => {
            this.$nuxt.$emit('focus-control-text-field')
          }, 100)
        }))

      // wait for video to start playing
      await wait(1000)

      // Focus video player
      let next: boolean = false
      next = await this.$runIntro(introJs()
        .setOptions({
          steps: [{
            element: '#main-video-player',
            intro: this.$t('intro-the-system-will-start-record-automatically'),
            title: ''
          }],
          doneLabel: 'Next',
          showBullets: false,
          exitOnOverlayClick: false
        })
      )

      if (!next) { return }

      // Scan barcode to stop recording
      await this.$runIntro(introJs()
        .setOptions({
          steps: [{
            element: '#v-text-field-control-code',
            intro: this.$t('intro-scan-barcode-stop-recording'),
            title: this.$t('stop-recording')
          }],
          showButtons: false,
          showBullets: false,
          exitOnOverlayClick: false
        })
        .onstart(() => {
          setTimeout(() => {
            this.$nuxt.$emit('focus-control-text-field')
          }, 100)
        })
      )

      // wait for #table-record-log to be moved
      await wait(1000)

      // Focus upload history table
      await this.$runIntro(introJs()
        .setOptions({
          steps: [
            {
              element: '#table-record-log',
              intro: this.$t('view-record-history-here'),
              title: this.$t('record-history')
            },
            {
              element: '#v-btn-setting',
              intro: this.$t('change-setting-here'),
              title: this.$t('setting')
            }
          ]
        })
      )

      this.$store.commit('set', { is_intro_running: false })
      this.showSupportDialog()
    },

    showSupportDialog () {
      this.$alert({
        title: this.$t('intro-youre-ready'),
        text: `
          <div class="text-center d-flex flex-column align-center">
            <p class="text-h6">
              ${this.$t('if-you-have-any-question')}
              <br />
              ${this.$t('contact-dobybot-support')}
            </p>
            <img width="200" src="/dbb-line.png" />
            <a target="#" class="mt-2" href="https://page.line.me/856wntly?openQrModal=true">
              ${this.$t('chat-with-us')}
            </a>
          </div>
        `,
        theme: 'success'
      })
    },

    openHelpDialog () {
      this.$alert({
        title: '"โหมดคืนของ" ถูกย้ายไปอยู่ในแถบด้านซ้าย',
        text: `
          <div class="text-center d-flex flex-column align-center">
            <img style="width: 100%; border: 1px solid black;" src="/help/move-return-mode-menu.png"> 
            <br>
            "โหมดคืนของ" ถูกย้ายไปอยู่ในแถบด้านซ้าย
          </div>
        `,
        theme: 'success'
      })
    },

    skipGetStart () {
      this.$store.dispatch('closeGetStart')
      localStorage.removeItem('open-setting')
    },

    ...mapActions('gapi', {
      gapiSignIn: 'signIn',
      gapiSignOut: 'signOut'
    })
  }
})
</script>
