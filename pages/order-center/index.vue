<template>
  <div>
    <!-- Page Title -->
    <v-row class="print-container">
      <v-col cols="12">
        <!-- TODO: Refactor to separated component -->
        <v-row class="pa-3">
          <h2>
            Order Center
          </h2>
          <v-spacer />

          <v-btn
            v-if="$hasPerms('add_pickorder')"
            small
            outlined
            color="success"
            class="no-print mx-1"
            @click="createEasyOrder()"
          >
            <v-icon small>
              mdi-plus
            </v-icon>
            <span v-if="$vuetify.breakpoint.mdAndUp" class="pl-3">
              {{ $t('order-center.create-order') }}
            </span>
          </v-btn>

          <v-btn
            v-if="$isPim()"
            small
            outlined
            color="success"
            class="no-print mx-1"
            @click="toggleShowJDReadyToShipForm()"
          >
            <v-icon small>
              mdi-package-up
            </v-icon>
            <span v-if="$vuetify.breakpoint.mdAndUp" class="pl-3">
              {{ $t('order-center.scan-ready-to-ship-kerry') }}
            </span>
          </v-btn>
          <v-btn
            small
            outlined
            color="success"
            class="no-print mx-1"
            data-test="v-btn-scanned-ready-to-ship"
            @click="toggleShowReadyToShipForm()"
          >
            <v-icon small>
              mdi-package-up
            </v-icon>
            <span v-if="$vuetify.breakpoint.mdAndUp" class="pl-3">
              {{ $t('order-center.scan-ready-to-ship') }}
            </span>
          </v-btn>
          <sync-orders-menu class="no-print" :start-date="filter.start" :end-date="filter.end" @sync-complete="onSearchSubmit()" />
          <!-- <v-btn v-if="settings.company.ZORT_STORENAME" color="info" small outlined @click="onSyncZortButtonClick()">
            <v-icon small>
              mdi-refresh
            </v-icon>
            <span v-if="$vuetify.breakpoint.mdAndUp" class="pl-3">{{ $t('order-center.update') }}</span>
          </v-btn> -->
          <order-center-menu
            class="no-print"
            @click-print-pick-orders="print_pick_orders.dialog = true"
            @click-print-etax-qrcode="openETaxPage()"
            @click-export-pick-order-xlsx="exportPickOrders()"
            @click-export-pick-order-detail-xlsx="exportPickOrderDetail()"
          />
        </v-row>
        <v-divider class="mb-3" />
      </v-col>
    </v-row>

    <!-- Scan Ready To Ship -->
    <v-row dense class="no-print" justify="center">
      <v-col cols="12" md="6">
        <!--  -->
        <order-center-ready-to-ship-form
          v-if="show_ready_to_ship_form"
          @ready-to-ship-success="onPickOrderReadyToShip(true, $event)"
          @ready-to-ship-failed="onPickOrderReadyToShip(false, $event)"
        />

        <pimrypie-kerry-ready-to-ship-form
          v-if="show_jd_ready_to_ship_form"
          @ready-to-ship-success="onPickOrderReadyToShip(true, $event)"
          @ready-to-ship-failed="onPickOrderReadyToShip(false, $event)"
        />
      </v-col>
    </v-row>

    <!-- Search -->
    <v-row dense>
      <v-col cols="12">
        <order-center-search-form :search.sync="search" @submit="onSearchSubmit()" />
      </v-col>
    </v-row>

    <!-- Filter -->
    <v-row class="print-container">
      <v-col cols="12">
        <order-center-filter-form v-if="show_filter_form" :form-data.sync="filter" />

        <div v-if="$vuetify.breakpoint.smAndDown" class="text-center">
          <v-btn v-if="!show_filter_form" x-small outlined @click="show_filter_form = true">
            Show Filter
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <!-- Main Table -->
    <order-center-data-table
      :table-data.sync="table"
      :loading="loading"
      :is-get-pick-order.sync="is_get_pick_order"
      @click-view-ready-to-ship-log="getReadyToShipLog($event)"
      @clicked:open-shipping-form="openShippingFormDialog($event)"
      @click:edit-order="editEasyOrder($event)"
      @click:show-order-detail="showEasyOrderDetail($event)"
      @click:open-doc-dialog="openTaxDocumentDialog($event)"
    />

    <ready-to-ship-log-dialog v-model="rts_log.dialog" :logs="rts_log.items" />
    <print-pick-orders-dialog v-model="print_pick_orders.dialog" />

    <shipping-form-dialog
      v-if="selected_pick_order"
      :dialog="is_open_shipping_form"
      :pick-order="selected_pick_order"
      :po-tracking="selected_po_tracking"
      @update:pick_order="patchUpdatePickOrder($event)"
      @cancel-tracking-number="cancelTrackingNumber($event)"
      @created:tracking-number="updateTrackingNumber($event)"
      @updated:po_tracking="updateShippingStatus($event)"
      @click-close="closeShippingFormDialog()"
    />

    <easy-order-create-dialog
      v-model="dialog_create_easy_order.show"
      @update:pick_order="easyAddOrder($event)"
      @created:tracking-number="updateTrackingNumber($event)"
    />
    <easy-order-update-dialog
      v-model="dialog_edit_easy_order.show"
      :order-json="dialog_edit_easy_order.order_json"
      :tax-document="dialog_edit_easy_order.tax_document"
      :po-tracking="dialog_edit_easy_order.po_tracking"
      @update:pick_order="easyUpdateOrder($event)"
      @created:tracking-number="updateTrackingNumber($event)"
    />
    <easy-order-detail-dialog
      v-model="dialog_detail_easy_order.show"
      :order-json="dialog_detail_easy_order.order_json"
      @update:pick_order="easyUpdateOrder($event)"
      @created:tracking-number="updateTrackingNumber($event)"
    />

    <tax-document-dialog
      v-model="dialog_detail_documents.show"
      :documents="dialog_detail_documents.documents"
      :selected-doc-id.sync="dialog_detail_documents.selected_doc_id"
      :pick-order="dialog_detail_documents.pick_order"
      @cancel:tax-doc="onCancelTaxDoc()"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import moment from 'moment'
import _ from 'lodash'

import { saveAs } from 'file-saver'
import OrderCenterDataTable from '~/components/order-center/OrderCenterDataTable.vue'
import OrderCenterFilterForm from '~/components/order-center/OrderCenterFilterForm.vue'
import OrderCenterSearchForm from '~/components/order-center/OrderCenterSearchForm.vue'
import OrderCenterReadyToShipForm from '~/components/order-center/OrderCenterReadyToShipForm.vue'
import ReadyToShipLogDialog from '~/components/order-center/ReadyToShipLogDialog.vue'
import PimrypieKerryReadyToShipForm from '~/components/order-center/PimrypieKerryReadyToShipForm.vue'
import { SettingState } from '~/store/settings'
import {
  PickOrder, VideoRecordLog, ImageCaptureLog, VDataTableOption,
  SimpleUser, FEATURE_FLAGS, PickOrderTrackingNo, OrderJson, TaxDocument
} from '~/models'
import { getDynamicRESTPagination, getDynamicRESTSorting, getReportServiceHost } from '~/utils/api'
import OrderCenterMenu from '~/components/order-center/OrderCenterMenu.vue'
import ShippingFormDialog from '~/components/settings/shipping/ShippingFormDialog.vue'
// import ChangePrintStatusDialog from '~/components/order-center/ChangePrintStatusDialog.vue'
import PrintPickOrdersDialog from '~/components/order-center/PrintPickOrdersDialog.vue'
import SyncOrdersMenu from '~/components/order-center/SyncOrdersMenu.vue'
import EasyOrderCreateDialog from '~/components/order-center/EasyOrderCreateDialog.vue'
import EasyOrderUpdateDialog from '~/components/order-center/EasyOrderUpdateDialog.vue'
import EasyOrderDetailDialog from '~/components/order-center/EasyOrderDetailDialog.vue'
import TaxDocumentDialog from '~/components/order-center/TaxDocumentDialog.vue'

export default Vue.extend({
  components: {
    OrderCenterSearchForm,
    OrderCenterFilterForm,
    OrderCenterDataTable,
    OrderCenterReadyToShipForm,
    PimrypieKerryReadyToShipForm,
    ReadyToShipLogDialog,
    OrderCenterMenu,
    PrintPickOrdersDialog,
    SyncOrdersMenu,
    ShippingFormDialog,
    EasyOrderCreateDialog,
    EasyOrderUpdateDialog,
    EasyOrderDetailDialog,
    TaxDocumentDialog
  },
  middleware: 'auth',
  data () {
    return {
      search: '',
      is_get_pick_order: false,
      filter: {
        start: '',
        end: '',
        shop: '',
        order_warehousecode: '',
        order_saleschannel: '',
        order_shippingchannel: '',
        order_trackingno: null,
        order_status: '',
        ready_to_ship: null,
        has_videos: null,
        has_fixcases: false,
        has_remark: false,
        has_return_videos: false,
        is_request_tax_invoice: false,
        printing_pick_slip_status: null,
        printing_airway_bill_status_choices: null,
        record_start: '',
        record_end: '',
        rts_start: '',
        rts_end: '',
        etax_status: ''
      },

      table: {
        meta: { total_results: 0, total_pages: 1, page: 1, per_page: 25 },
        options: {
          page: 1,
          itemsPerPage: 25,
          sortBy: [],
          sortDesc: [],
          groupBy: [],
          groupDesc: [],
          multiSort: false,
          mustSort: false
        } as VDataTableOption,
        pick_orders: [] as PickOrder[],
        // fix_cases: {},
        tax_documents: new Map() as Map<number, TaxDocument>,
        video_record_logs: new Map() as Map<number, VideoRecordLog>,
        image_capture_logs: new Map() as Map<number, ImageCaptureLog>,
        pick_order_tracking_nos: new Map() as Map<number, PickOrderTrackingNo>,
        users: new Map() as Map<number, SimpleUser>
      },

      loading: false,
      show_ready_to_ship_form: false,
      show_jd_ready_to_ship_form: false,
      show_filter_form: true,

      rts_log: {
        dialog: false,
        items: []
      },

      // change_print_status: {
      //   dialog: false
      // },

      print_pick_orders: {
        dialog: false
      },

      is_open_shipping_form: false as boolean,
      selected_pick_order: null as PickOrder | null,
      selected_po_tracking: null as PickOrderTrackingNo | null,
      error_message: '' as string,

      dialog_create_easy_order: {
        show: false,
        order_json: null as OrderJson | null
      },

      dialog_edit_easy_order: {
        show: false,
        order_json: null as OrderJson | null,
        tax_document: null as TaxDocument | null,
        po_tracking: null as PickOrderTrackingNo | null
      },

      dialog_detail_easy_order: {
        show: false,
        order_json: null as OrderJson | null
      },

      dialog_detail_documents: {
        show: false,
        documents: [] as TaxDocument[],
        selected_doc_id: null as number | string | null,
        pick_order: null as PickOrder | null
      }

    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },

  watch: {
    'table.options': {
      handler () {
        if (!this.loading) {
          this.getPickOrders()
        }
      },
      deep: true
    }
  },

  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.ORDER_CENTER)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }

    if (!this.$hasPerms('view_pickorder')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }

    if (this.$route.query.search && this.$route.query.date) {
      this.search = this.$route.query.search as string
      this.filter.start = this.$route.query.date as string
      this.filter.end = this.$route.query.date as string
    } else {
      this.filter.start = moment().subtract(3, 'days').format('YYYY-MM-DD')
      this.filter.end = moment().format('YYYY-MM-DD')
    }

    if ((this as any).$vuetify.breakpoint.smAndDown) {
      this.show_filter_form = false
    }

    this.getPickOrders()
    this.registerBoardcastChannel()
  },

  methods: {
    registerBoardcastChannel () {
      const channel = new BroadcastChannel('etax-success')
      channel.onmessage = async (event: any) => {
        await this.getPickOrders()
        const pick_order = this.table.pick_orders.find(x => x.uuid === event.data.pick_order_uuid) as PickOrder
        const tax_doc = this.table.tax_documents.get(pick_order.taxdocument_set[0]) as TaxDocument
        const doc_type = tax_doc.doc_type.toLowerCase().replace(' ', '_')
        console.log({ doc_type })
        this.openTaxDocumentDialog({ doc_id: doc_type, pick_order })
      }
    },
    openETaxPage () {
      window.open('/etax/printing', '_blank')
    },
    // Event Handler
    onSearchSubmit () {
      this.table.options.page = 1
      this.getPickOrders()
    },

    onPickOrderReadyToShip (is_success: boolean, pick_order: PickOrder) {
      const idx = this.table.pick_orders.findIndex(x => x.id === pick_order.id)

      if (idx >= 0) {
        this.table.pick_orders.splice(idx, 1)
      }

      const video_record_logs = pick_order.videorecordlog_set as any
      const log = _.last(video_record_logs) as any
      if (log) {
        const user = log.upload_by
        if (user) {
          this.table.users.set(user.id, user)
          log.upload_by = log.upload_by.id
        }

        this.table.video_record_logs.set(log.id, log)
      }

      pick_order.videorecordlog_set = video_record_logs.map((x: any) => x.id)
      this.table.pick_orders.unshift(pick_order)

      this.$nextTick(() => {
        const color = is_success ? 'green' : 'orange'
        document.querySelector(`.pickorder-${pick_order.id}`)?.classList.add(color, 'lighten-4')
        setTimeout(() => {
          document.querySelector(`.pickorder-${pick_order.id}`)?.classList.remove(color, 'lighten-4')
        }, 5000)
      })
    },

    toggleShowJDReadyToShipForm () {
      this.show_jd_ready_to_ship_form = !this.show_jd_ready_to_ship_form
      this.table.meta.total_results = 100

      if (this.show_jd_ready_to_ship_form) {
        this.table.pick_orders = []
      } else {
        this.getPickOrders()
      }
    },

    toggleShowReadyToShipForm () {
      this.show_ready_to_ship_form = !this.show_ready_to_ship_form
      this.table.meta.total_results = 100

      if (this.show_ready_to_ship_form) {
        this.table.pick_orders = []
      } else {
        this.getPickOrders()
      }
    },

    onCancelTaxDoc () {
      this.getPickOrders()
    },

    openShippingFormDialog (data:{pick_order:PickOrder, po_tracking:PickOrderTrackingNo}) {
      this.is_open_shipping_form = true
      this.selected_pick_order = data.pick_order
      if (data.po_tracking && data.po_tracking.status !== 'CANCELLED') {
        this.selected_po_tracking = data.po_tracking
      } else {
        this.selected_po_tracking = null
      }
    },

    closeShippingFormDialog () {
      this.is_open_shipping_form = false
    },

    updateTrackingNumber (data:{new_po_tracking:PickOrderTrackingNo, form_data:any}) {
      const new_po_tracking = data.new_po_tracking
      const form_data = data.form_data
      this.table.pick_orders = this.table.pick_orders.map((po: PickOrder) => {
        if (po.id === new_po_tracking.pick_order) {
          po.order_trackingno = new_po_tracking.tracking_no
          po.order_shippingchannel = new_po_tracking.shipping_provider
          po.pickordertrackingno_set?.push(new_po_tracking.id)

          po.order_json.shippingname = form_data.shippingname
          po.order_json.shippingphone = form_data.shippingphone
          po.order_json.shippingaddress = form_data.shippingaddress
          po.order_json.shippingprovince = form_data.shippingprovince
          po.order_json.shippingdistrict = form_data.shippingdistrict
          po.order_json.shippingsubdistrict = form_data.shippingsubdistrict
          po.order_json.shippingpostcode = form_data.shippingpostcode
          return po
        }

        return po
      })
      this.table.pick_order_tracking_nos.set(new_po_tracking.id, new_po_tracking)
    },

    updateShippingStatus (po_tracking: PickOrderTrackingNo) {
      this.table.pick_order_tracking_nos.set(po_tracking.id, po_tracking)

      // Important this code is trigger the pick_orders to update front-end
      this.table.pick_orders = [...this.table.pick_orders]
    },

    async cancelTrackingNumber (tracking_no:string) {
      const action = '/api/shipping/flash/cancel-tracking-number/'
      try {
        const response = await this.$axios.post(action, { tracking_no })
        const new_po_tracking:PickOrderTrackingNo = response.data

        this.table.pick_orders = this.table.pick_orders.map((po: PickOrder) => {
          if (po.id === new_po_tracking.pick_order) {
            po.order_shippingchannel = new_po_tracking.shipping_provider

            return po
          }

          return po
        })
        this.table.pick_order_tracking_nos.set(new_po_tracking.id, new_po_tracking)

        this.closeShippingFormDialog()
      } catch (error) {
        console.log(error)
      }
    },

    async onSyncZortButtonClick () {
      if (!this.filter.start || !this.filter.end) {
        alert(this.$t('order-center.sync'))
        return
      }

      this.$loader(true)
      this.updatePickOrderWithZort({ start_date: this.filter.start, end_date: this.filter.end })
      const { count_new_orders } = await this.syncZortOrders({ start_date: this.filter.start, end_date: this.filter.end })
      this.getPickOrders()
      this.$loader(false)

      setTimeout(() => {
        alert(this.$t('order-center.count_new_orders', [count_new_orders]))
      }, 500)
    },

    openTaxDocumentDialog (data: {doc_id: string | number | null, pick_order: PickOrder}) {
      this.dialog_detail_documents.selected_doc_id = data.doc_id
      this.dialog_detail_documents.documents = data.pick_order.taxdocument_set.map((id: number) => this.table.tax_documents.get(id) as TaxDocument)
      this.dialog_detail_documents.pick_order = data.pick_order
      this.dialog_detail_documents.show = true
    },

    // APIs
    async getPickOrders () {
      const action = '/api/picking/resource/pick-orders/'
      const params = this.getPickOrderQueryParams()

      this.loading = true
      try {
        const response = await this.$axios.get(action, { params })

        this.table.meta = response.data.meta
        this.table.pick_orders = response.data.pick_orders

        const image_capture_logs: ImageCaptureLog[] = response.data.image_capture_logs || []
        this.table.image_capture_logs = new Map(image_capture_logs.map(x => [x.id, x]))

        const pick_order_tracking_nos: PickOrderTrackingNo[] = response.data.pick_order_tracking_nos || []
        this.table.pick_order_tracking_nos = new Map(pick_order_tracking_nos.map(x => [x.id, x]))

        const video_record_logs: VideoRecordLog[] = response.data.video_record_logs || []
        this.table.video_record_logs = new Map(video_record_logs.map(x => [x.id, x]))

        const tax_documents: TaxDocument[] = response.data.tax_documents || []
        this.table.tax_documents = new Map(tax_documents.map(x => [x.id, x]))

        const users: SimpleUser[] = response.data.users || []
        this.table.users = new Map(users.map(x => [x.id, x]))
        this.is_get_pick_order = true
        console.log('Table', this.table)
      } catch (error: any) {
        console.error(error)
      }
      this.loading = false
    },

    async exportPickOrders () {
      const start = this.filter.start
      const end = this.filter.end

      if (!this.filter.start || !this.filter.end) {
        this.$alert({ title: ' ', text: 'กรุณาเลือกวันที่เริ่มต้น (จาก) และวันที่สิ้นสุด (ถึง) สำหรับการส่งข้อมูลออกเป็น Microsoft Excel', theme: 'error' })
        return
      }

      if (moment(end).diff(start, 'days') > 31) {
        this.$alert({ title: ' ', text: 'ไม่สามารถส่งข้อมูลออกเป็น Microsoft Excel เกินระยะเวลา 30 วัน', theme: 'error' })
        return
      }

      const host = getReportServiceHost()
      const path = '/api/reports/v2/pick-order/'
      const params = {
        start_date: this.filter.start,
        end_date: this.filter.end,
        fmt: 'xlsx'
      }

      this.$loader(true)
      try {
        const response = await this.$axios.get(host + path, { params, responseType: 'blob' })
        saveAs(response.data, 'order-center.xlsx')
      } catch (error: any) {
        console.error(error)
      }
      this.$loader(false)
    },

    async exportPickOrderDetail () {
      const start = this.filter.start
      const end = this.filter.end

      if (!this.filter.start || !this.filter.end) {
        this.$alert({ title: ' ', text: 'กรุณาเลือกวันที่เริ่มต้น (จาก) และวันที่สิ้นสุด (ถึง) สำหรับการส่งข้อมูลออกเป็น Microsoft Excel', theme: 'error' })
        return
      }

      if (moment(end).diff(start, 'days') > 31) {
        this.$alert({ title: ' ', text: 'ไม่สามารถส่งข้อมูลออกเป็น Microsoft Excel เกินระยะเวลา 30 วัน', theme: 'error' })
        return
      }

      const host = getReportServiceHost()
      const path = '/api/reports/v2/pick-order-detail/'
      const params = {
        start_date: this.filter.start,
        end_date: this.filter.end,
        fmt: 'xlsx'
      }

      this.$loader(true)
      try {
        const response = await this.$axios.get(host + path, { params, responseType: 'blob' })
        saveAs(response.data, 'order-center-detail.xlsx')
      } catch (error: any) {
        console.error(error)
        this.$snackbar('error', this.$t('error.unknown'))
      }
      this.$loader(false)
    },
    createEasyOrder () {
      this.dialog_create_easy_order.show = true
    },

    editEasyOrder (pick_order: PickOrder) {
      this.dialog_edit_easy_order.show = true
      this.dialog_edit_easy_order.order_json = JSON.parse(JSON.stringify(pick_order.order_json))

      const tax_doc_id = _.last(pick_order.taxdocument_set)
      if (tax_doc_id) {
        const tax_doc = this.table.tax_documents.get(tax_doc_id) as TaxDocument
        this.dialog_edit_easy_order.tax_document = tax_doc
      } else {
        this.dialog_edit_easy_order.tax_document = null
      }

      const po_tracking_id = _.last(pick_order.pickordertrackingno_set)
      if (po_tracking_id) {
        const po_tracking = this.table.pick_order_tracking_nos.get(po_tracking_id) as PickOrderTrackingNo
        this.dialog_edit_easy_order.po_tracking = po_tracking
      } else {
        this.dialog_edit_easy_order.po_tracking = null
      }
    },

    showEasyOrderDetail (pick_order: PickOrder) {
      this.dialog_detail_easy_order.show = true
      this.dialog_detail_easy_order.order_json = JSON.parse(JSON.stringify(pick_order.order_json))
    },

    easyAddOrder (pick_order: PickOrder) {
      this.table.pick_orders.unshift(pick_order)
    },

    easyUpdateOrder (pick_order: PickOrder) {
      this.table.pick_orders = this.table.pick_orders.map(
        (po: PickOrder) => po.id === pick_order.id ? pick_order : po)
    },

    getPickOrderQueryParams () {
      // Pagination & Sorting
      const params: any = {
        ...getDynamicRESTPagination(this.table.options),
        ...getDynamicRESTSorting(this.table.options),
        include: [
          'videorecordlog_set.*',
          'videorecordlog_set.upload_by.*',
          'ready_to_ship_by.*',
          'imagecapturelog_set.*',
          'imagecapturelog_set.create_by.*',
          'pickordertrackingno_set.*',
          'taxdocument_set.*',
          'create_by.*'
        ]
      }
      // Exclusion
      // params.exclude = ['order_json']

      // Filtering
      params.search = this.search
      if (this.filter.order_status) {
        params.order_status = this.filter.order_status
      }
      params['filter{fixcase_set|is_complete}'] = false
      if (this.filter.start) {
        params['filter{order_date.gte}'] = this.filter.start
      }
      if (this.filter.end) {
        params['filter{order_date.lte}'] = this.filter.end
      }
      if (this.filter.order_warehousecode) {
        params['filter{order_warehousecode}'] = this.filter.order_warehousecode
      }
      if (this.filter.order_saleschannel) {
        params['filter{order_saleschannel}'] = this.filter.order_saleschannel
      }
      if (this.filter.order_shippingchannel) {
        params['filter{order_shippingchannel}'] = this.filter.order_shippingchannel
      }
      if (this.filter.has_videos !== null) {
        params['filter{has_videos}'] = this.filter.has_videos
      }
      if (this.filter.ready_to_ship !== null) {
        params['filter{ready_to_ship}'] = this.filter.ready_to_ship
      }
      if (this.filter.has_fixcases) {
        params['filter{has_fixcases}'] = this.filter.has_fixcases
      }
      if (this.filter.has_remark) {
        params['filter{-remark}'] = ''
      }
      if (this.filter.has_return_videos) {
        params['filter{has_return_videos}'] = this.filter.has_return_videos
      }
      if (this.filter.is_request_tax_invoice) {
        params.is_request_tax_inv = true
      }

      if (this.filter.printing_pick_slip_status !== null) {
        if (this.filter.printing_pick_slip_status) {
          params['filter{print_count.gte}'] = 1
        } else {
          params['filter{print_count}'] = 0
        }
      }

      if (this.filter.printing_airway_bill_status_choices !== null) {
        if (this.filter.printing_airway_bill_status_choices) {
          params['filter{airway_bill_print_count.gte}'] = 1
        } else {
          params['filter{airway_bill_print_count}'] = 0
        }
      }

      if (this.filter.record_start) {
        params.record_start = this.filter.record_start
      }
      if (this.filter.record_end) {
        params.record_end = this.filter.record_end
      }

      if (this.filter.rts_start) {
        params['filter{ready_to_ship_timestamp.gte}'] = this.filter.rts_start
      }
      if (this.filter.rts_end) {
        params['filter{ready_to_ship_timestamp.lte}'] = this.filter.rts_end
      }

      if (this.filter.order_trackingno !== null) {
        if (this.filter.order_trackingno) {
          params['filter{-order_trackingno}'] = ''
        } else {
          params['filter{order_trackingno}'] = ''
        }
      }
      if (this.filter.etax_status !== null) {
        params.etax_status = this.filter.etax_status
      }

      return params
    },

    async syncZortOrders (params: { start_date: string, end_date: string }): Promise<{count_new_orders: number}> {
      const action = '/api/picking/zort/sync-orders/'
      const data = params
      const res = await this.$axios.post(action, data)
      return res.data
    },

    async updatePickOrderWithZort (params: { start_date: string; end_date: string }) {
      const action = '/api/picking/pick-orders/update-with-zort/'
      const data = params
      try {
        await this.$axios.post(action, data)
      } catch (error: any) {
        // if (error.response.data.code === 'missing_api_credentials') {
        //   alert(error.response.data.message)

        //   if (this.$hasPerms('change_settingvalue')) {
        //     this.$router.push({ path: '/settings' })
        //   }
        // }
      }
    },

    async getReadyToShipLog (pick_order: PickOrder) {
      const params = {
        'filter{pick_order}': pick_order.id
      }

      try {
        const response = await this.$axios.get('/api/logging/resource/ready-to-ship-log/', { params })
        this.rts_log.items = response.data.ready_to_ship_logs
        this.rts_log.dialog = true
      } catch (error) {
        console.error(error)
      }
    },

    async patchUpdatePickOrder (pick_order: PickOrder) {
      const action = `/api/picking/orders/${pick_order.order_number}/`
      try {
        await this.$axios.patch(action, {
          shippingname: pick_order.order_json.shippingname,
          shippingphone: pick_order.order_json.shippingphone,
          shippingaddress: pick_order.order_json.shippingaddress,
          shippingprovince: pick_order.order_json.shippingprovince,
          shippingdistrict: pick_order.order_json.shippingdistrict,
          shippingsubdistrict: pick_order.order_json.shippingsubdistrict,
          shippingpostcode: pick_order.order_json.shippingpostcode,
          isCOD: pick_order.order_json.isCOD
        })
        // TODO: Snackbar Success
      } catch (error) {
        console.log(error)
        // TODO: display error
      }
    }
  }
})
</script>
