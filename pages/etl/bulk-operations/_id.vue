<template>
  <div>
    <v-container fluid>
      <!-- Back Button -->
      <v-btn
        text
        small
        color="primary"
        class="mb-3"
        @click="$router.push('/etl/')"
      >
        <v-icon left small>
          mdi-arrow-left
        </v-icon>
        Back to Operations
      </v-btn>

      <!-- Loading State -->
      <v-row v-if="loading">
        <v-col cols="12" class="text-center py-12">
          <v-progress-circular
            indeterminate
            color="primary"
            size="64"
          />
          <div class="mt-4 text-h6">
            Loading operation details...
          </div>
        </v-col>
      </v-row>

      <!-- Job Details -->
      <template v-else-if="job">
        <!-- Header Card -->
        <v-card class="mb-3">
          <v-card-text class="pb-2">
            <div class="d-flex align-center justify-space-between">
              <div>
                <div class="text-h5 font-weight-medium mb-1">
                  Operation ID: {{ job.uuid }}
                </div>
                <div class="text-body-2 text--secondary">
                  Company: <strong>{{ job.company_name }}</strong>
                  <span class="ml-1">
                    •  Job Type:
                    <v-chip
                      :color="getJobTypeColor(job.job_type)"
                      outlined
                      x-small
                    >
                      {{ getJobTypeText(job.job_type) }}
                    </v-chip>
                  </span>
                  <span class="ml-1">
                    •  Source:
                    <v-chip
                      :color="job.source === 'manual' ? 'primary' : 'grey'"
                      outlined
                      x-small
                    >
                      {{ job.source === 'manual' ? 'Manual' : 'Auto' }}
                    </v-chip>
                  </span>
                  <span v-if="job.created_by" class="ml-1">
                    •  Created by: <strong>{{ job.created_by }}</strong>
                  </span>
                  <span class="ml-1">
                    •  Status:
                    <v-chip
                      :color="getStatusColor(job.status)"
                      text-color="white"
                      x-small
                    >
                      {{ getStatusText(job.status) }}
                    </v-chip>
                  </span>
                </div>
              </div>
              <div>
                <v-btn
                  outlined
                  small
                  class="mr-2"
                  :disabled="job.status === 'running'"
                  @click="handleRetry"
                >
                  <v-icon left small>
                    mdi-refresh
                  </v-icon>
                  Retry
                </v-btn>
              </div>
            </div>
          </v-card-text>
        </v-card>

        <!-- Summary Card -->
        <v-card class="mb-3">
          <v-card-title class="text-h6 py-3">
            Summary
          </v-card-title>
          <v-divider />
          <v-card-text class="pa-0">
            <v-simple-table dense>
              <tbody>
                <tr>
                  <td class="text--secondary" style="width: 200px;">
                    Date Range
                  </td>
                  <td class="font-weight-medium">
                    {{ formatDate(job.start_date) }} to {{ formatDate(job.end_date) }}
                  </td>
                  <td class="text--secondary" style="width: 200px;">
                    Processed Rows
                  </td>
                  <td class="font-weight-medium">
                    {{ (job.orders_processed + job.order_items_processed).toLocaleString() }}
                  </td>
                </tr>
                <tr>
                  <td class="text--secondary">
                    Target Dataset/Tables
                  </td>
                  <td class="font-weight-medium">
                    {{ getJobTypeText(job.job_type) }}
                  </td>
                  <td class="text--secondary">
                    Duration
                  </td>
                  <td class="font-weight-medium">
                    {{ getDuration() }}
                  </td>
                </tr>
              </tbody>
            </v-simple-table>
          </v-card-text>
        </v-card>

        <!-- Tabs Card -->
        <v-card>
          <div class="d-flex justify-space-between">
            <v-tabs v-model="activeTab">
              <v-tab>Logs</v-tab>
              <v-tab>Steps</v-tab>
              <v-tab>Validation</v-tab>
              <v-tab>Metadata</v-tab>
            </v-tabs>
            <div style="min-width: 300px;">
              <v-text-field
                v-model="logSearch"
                prepend-inner-icon="mdi-magnify"
                label="Search logs..."
                outlined
                dense
                hide-details
                clearable
                class="px-2"
              />
            </div>
            <v-select
              v-model="logLevelFilter"
              :items="logLevelOptions"
              label="All Levels"
              outlined
              dense
              hide-details
            />
          </div>

          <v-divider />

          <v-tabs-items v-model="activeTab">
            <!-- Logs Tab -->
            <v-tab-item>
              <v-card-text class="pa-3">
                <!-- Log Controls -->

                <!-- Log Display -->
                <v-card outlined class="log-container">
                  <v-expansion-panels
                    v-if="filteredLogs.length > 0"
                    accordion
                    flat
                  >
                    <v-expansion-panel
                      v-for="(log, index) in filteredLogs"
                      :key="index"
                    >
                      <v-expansion-panel-header class="log-entry py-2">
                        <div class="d-flex align-center" style="width: 100%;">
                          <span class="text--secondary mr-3" style="min-width: 180px;">
                            {{ formatLogTime(log.timestamp) }}
                          </span>
                          <v-chip
                            :color="getLogLevelColor(log.level)"
                            x-small
                            text-color="white"
                            class="mr-3"
                            style="min-width: 70px; justify-content: center;"
                          >
                            {{ log.level }}
                          </v-chip>
                          <span class="log-message">{{ log.message }}</span>
                        </div>
                      </v-expansion-panel-header>

                      <v-expansion-panel-content>
                        <div class="log-details mx-2 pa-2 rounded">
                          <div v-if="log.context" class="mb-2">
                            <strong class="text-white">Context:</strong>
                            <pre class="log-json mt-1">{{ JSON.stringify(log.context, null, 2) }}</pre>
                          </div>

                          <div v-if="log.performance" class="mb-2">
                            <strong class="text-white">Performance:</strong>
                            <pre class="log-json mt-1">{{ JSON.stringify(log.performance, null, 2) }}</pre>
                          </div>

                          <div v-if="log.metadata">
                            <strong class="text-white">Metadata:</strong>
                            <pre class="log-json mt-1">{{ JSON.stringify(log.metadata, null, 2) }}</pre>
                          </div>
                        </div>
                      </v-expansion-panel-content>
                    </v-expansion-panel>
                  </v-expansion-panels>

                  <div v-else class="text-center pa-8 text--secondary">
                    No logs found
                  </div>
                </v-card>
              </v-card-text>
            </v-tab-item>

            <!-- Steps Tab -->
            <v-tab-item>
              <v-card-text>
                <div class="text-center pa-8 text--secondary">
                  Steps information will be displayed here
                </div>
              </v-card-text>
            </v-tab-item>

            <!-- Validation Tab -->
            <v-tab-item>
              <v-card-text>
                <div class="text-center pa-8 text--secondary">
                  Validation information will be displayed here
                </div>
              </v-card-text>
            </v-tab-item>

            <!-- Metadata Tab -->
            <v-tab-item>
              <v-card-text>
                <div class="text-center pa-8 text--secondary">
                  Metadata information will be displayed here
                </div>
              </v-card-text>
            </v-tab-item>
          </v-tabs-items>
        </v-card>
      </template>

      <!-- Error State -->
      <v-row v-else>
        <v-col cols="12" class="text-center py-12">
          <v-icon size="64" color="error">
            mdi-alert-circle
          </v-icon>
          <div class="mt-4 text-h6">
            Failed to load operation details
          </div>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { LoggedInUser } from '~/models'

interface ETLJobDetail {
  uuid: string
  company_name: string
  status: string
  start_date: string
  end_date: string
  created_at: string
  started_at: string | null
  completed_at: string | null
  orders_processed: number
  order_items_processed: number
  error_message: string | null
  duration_ms: number | null
  log_summary: Record<string, number>
  performance_summary: Record<string, any>
  logs: any[]
  error_logs: any[]
  logs_by_operation: Record<string, any[]>
  created_by: string | null
  source: string
  job_type: string
}

export default Vue.extend({
  middleware: ['auth'],

  data () {
    return {
      loading: false,
      job: null as ETLJobDetail | null,
      activeTab: 0,

      // polling
      pollTimer: null as any,

      // Log controls
      logSearch: '',
      logLevelFilter: 'all',
      liveTail: false,

      logLevelOptions: [
        { text: 'All Levels', value: 'all' },
        { text: 'ERROR', value: 'ERROR' },
        { text: 'WARNING', value: 'WARNING' },
        { text: 'INFO', value: 'INFO' },
        { text: 'DEBUG', value: 'DEBUG' }
      ]
    }
  },

  computed: {
    filteredLogs (): any[] {
      if (!this.job || !this.job.logs) { return [] }

      let logs = this.job.logs

      // Filter by level
      if (this.logLevelFilter !== 'all') {
        logs = logs.filter(log => log.level === this.logLevelFilter)
      }

      // Filter by search
      if (this.logSearch) {
        const search = this.logSearch.toLowerCase()
        logs = logs.filter(log =>
          log.message.toLowerCase().includes(search) ||
          log.level.toLowerCase().includes(search)
        )
      }

      return logs
    }
  },

  created () {
    // Check if user is staff or superuser
    const user = this.$auth.user as never as LoggedInUser
    if (!user.is_staff && !user.is_superuser) {
      this.$nuxt.error({ statusCode: 403, message: 'Access denied. Only staff or superuser accounts can view ETL jobs.' })
      return
    }

    this.loadJobDetails().then(() => {
      this.startPolling()
    })
  },

  beforeDestroy () {
    this.stopPolling()
  },

  methods: {
    async loadJobDetails () {
      this.loading = true
      this.job = null

      try {
        const jobId = this.$route.params.id
        const response = await this.$axios.get(`/api/etl/jobs/${jobId}/`)
        this.job = response.data
      } catch (error) {
        console.error('Error loading job details:', error)
        this.$snackbar('error', 'Failed to load job details')
      } finally {
        this.loading = false
      }
    },

    startPolling () {
      if (this.pollTimer) { clearInterval(this.pollTimer) }
      this.pollTimer = setInterval(async () => {
        if (!this.job) { return }
        if (['pending', 'running'].includes(this.job.status)) {
          await this.loadJobDetails()
        } else if (this.pollTimer) {
          clearInterval(this.pollTimer)
          this.pollTimer = null
        }
      }, 5000)
    },

    stopPolling () {
      if (this.pollTimer) {
        clearInterval(this.pollTimer)
        this.pollTimer = null
      }
    },

    getStatusColor (status: string): string {
      switch (status) {
        case 'completed': return 'success'
        case 'running': return 'warning'
        case 'failed': return 'error'
        case 'pending': return 'info'
        default: return 'grey'
      }
    },

    getStatusText (status: string): string {
      switch (status) {
        case 'completed': return 'COMPLETED'
        case 'running': return 'RUNNING'
        case 'failed': return 'FAILED'
        case 'pending': return 'PENDING'
        default: return status.toUpperCase()
      }
    },

    getJobTypeColor (jobType: string): string {
      switch (jobType) {
        case 'order': return 'blue'
        case 'video_record_log': return 'purple'
        case 'fixcase': return 'orange'
        default: return 'grey'
      }
    },

    getJobTypeText (jobType: string): string {
      switch (jobType) {
        case 'order': return 'Order'
        case 'video_record_log': return 'Video Log'
        case 'fixcase': return 'Fix Case'
        default: return jobType
      }
    },

    getCreatedBy (): string {
      // Use the created_by field from the job if available
      if (this.job && this.job.created_by) {
        return this.job.created_by
      }
      // Fallback to logs
      if (this.job && Array.isArray(this.job.logs)) {
        const created = this.job.logs.find(l => (l.context && l.context.details && (l.context.details.requested_by || l.context.details.created_by)))
        if (created) {
          return (created.context.details.requested_by || created.context.details.created_by)
        }
      }
      // Fallback to query param (from UI creation)
      const q = this.$route.query.created_by
      if (typeof q === 'string' && q) { return q }
      return '-'
    },

    getDuration (): string {
      if (!this.job) { return '-' }

      if (this.job.duration_ms) {
        return this.formatDuration(this.job.duration_ms)
      }

      if (this.job.started_at && this.job.completed_at) {
        const start = new Date(this.job.started_at).getTime()
        const end = new Date(this.job.completed_at).getTime()
        const duration = end - start
        return this.formatDuration(duration)
      }

      return '00:15:30'
    },

    getProcessedBytes (): string {
      return '-'
    },

    formatDuration (ms: number): string {
      const seconds = Math.floor(ms / 1000)
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      const secs = seconds % 60

      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    },

    formatDate (dateString: string): string {
      return new Date(dateString).toLocaleDateString()
    },

    formatLogTime (timestamp: string): string {
      const date = new Date(timestamp)
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      })
    },

    getLogLevelColor (level: string): string {
      switch (level) {
        case 'ERROR': return 'error'
        case 'WARNING': return 'warning'
        case 'INFO': return 'info'
        case 'DEBUG': return 'grey'
        case 'SUCCESS': return 'success'
        default: return 'grey'
      }
    },

    handleCancel () {
      this.$snackbar('info', 'Cancel operation - To be implemented')
    },

    async handleRetry () {
      if (!this.job) { return }
      try {
        const user = this.$auth.user as never as LoggedInUser
        const companyId = user?.company?.id

        const start = new Date(this.job.start_date)
        const end = new Date(this.job.end_date)
        const startISO = new Date(Date.UTC(start.getUTCFullYear(), start.getUTCMonth(), start.getUTCDate(), 0, 0, 0)).toISOString()
        const endISO = new Date(Date.UTC(end.getUTCFullYear(), end.getUTCMonth(), end.getUTCDate(), 23, 59, 59)).toISOString()

        const resp = await this.$axios.post('/api/etl/scheduler/bigquery-pipeline/', {
          start_date: startISO,
          end_date: endISO,
          company_id: companyId
        })
        const ids: string[] = (resp.data && resp.data.jobs_created) || []
        if (ids.length > 0) {
          const createdBy = user?.username || ''
          this.$router.push(`/etl/bulk-operations/${ids[0]}?created_by=${encodeURIComponent(createdBy)}`)
        } else {
          this.$snackbar('info', 'No job created on retry')
        }
      } catch (e) {
        this.$snackbar('error', 'Failed to retry ETL job')
      }
    },

    handleDownloadLog () {
      this.$snackbar('info', 'Download JSON Log - To be implemented')
    },

    handleExportCSV () {
      this.$snackbar('info', 'Export CSV Summary - To be implemented')
    }
  }
})
</script>

<style scoped>
.log-container {
  background-color: #1e1e1e;
  min-height: 300px;
  max-height: 500px;
  overflow-y: auto;
}

.log-entry {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.log-message {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.log-details {
  border: 1px solid rgba(0, 0, 0, 1);
  padding: 8px 0;
}

.log-json {
  background-color: #1e1e1e;
  color: #d4d4d4;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin: 0;
}

.v-expansion-panel::before {
  box-shadow: none !important;
}

.v-expansion-panel-content >>> .v-expansion-panel-content__wrap {
  padding: 0 !important;
}

/* Make tables more compact */
.v-data-table >>> td,
.v-data-table >>> th {
  padding: 8px 16px !important;
}
</style>
