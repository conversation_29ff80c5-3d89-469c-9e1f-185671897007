<template>
  <div>
    <v-container fluid>
      <!-- Header -->
      <div class="d-flex align-center justify-space-between mb-4">
        <div>
          <h1 class="text-h5 font-weight-medium mb-1">
            Bulk Upload Operations
          </h1>
          <p class="text-body-2 text--secondary mb-0">
            Manage and monitor bulk data uploads to BigQuery.
          </p>
        </div>
        <div>
          <v-btn color="primary" dark @click="createDialog = true">
            <v-icon left small>
              mdi-plus
            </v-icon>
            Create ETL
          </v-btn>
        </div>
      </div>

      <!-- Create ETL Dialog -->
      <v-dialog v-model="createDialog" max-width="520px">
        <v-card>
          <v-card-title class="text-h6">
            Create ETL Job
          </v-card-title>
          <v-divider />
          <v-card-text>
            <v-row dense>
              <v-col cols="12">
                Select a date range to import orders into BigQuery for your company.
              </v-col>
              <v-col cols="12" md="6">
                <v-menu
                  v-model="createDateFromMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  max-width="290px"
                  min-width="290px"
                >
                  <template #activator="{ on, attrs }">
                    <v-text-field
                      v-model="createForm.dateFrom"
                      label="Start date"
                      prepend-inner-icon="mdi-calendar"
                      outlined
                      dense
                      hide-details
                      readonly
                      clearable
                      v-bind="attrs"
                      v-on="on"
                    />
                  </template>
                  <v-date-picker v-model="createForm.dateFrom" @input="createDateFromMenu = false" />
                </v-menu>
              </v-col>
              <v-col cols="12" md="6">
                <v-menu
                  v-model="createDateToMenu"
                  :close-on-content-click="false"
                  transition="scale-transition"
                  offset-y
                  max-width="290px"
                  min-width="290px"
                >
                  <template #activator="{ on, attrs }">
                    <v-text-field
                      v-model="createForm.dateTo"
                      label="End date"
                      prepend-inner-icon="mdi-calendar"
                      outlined
                      dense
                      hide-details
                      readonly
                      clearable
                      v-bind="attrs"
                      v-on="on"
                    />
                  </template>
                  <v-date-picker v-model="createForm.dateTo" @input="createDateToMenu = false" />
                </v-menu>
              </v-col>
            </v-row>
          </v-card-text>
          <v-divider />
          <v-card-actions>
            <v-spacer />
            <v-btn text @click="createDialog = false">
              Cancel
            </v-btn>
            <v-btn color="primary" :loading="createLoading" :disabled="!createForm.dateFrom || !createForm.dateTo" @click="submitCreate">
              Create
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- Statistics Cards -->
      <v-row dense class="mb-3">
        <v-col cols="12" sm="6" md="3">
          <v-card class="stat-card">
            <v-card-text class="pa-3">
              <div class="d-flex align-center">
                <v-icon color="primary" size="32" class="mr-3">
                  mdi-calendar-today
                </v-icon>
                <div>
                  <div class="text-h5 font-weight-bold">
                    {{ statistics.jobs_24h }}
                  </div>
                  <div class="text-caption text--secondary">
                    Today's Ops
                  </div>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" sm="6" md="3">
          <v-card class="stat-card">
            <v-card-text class="pa-3">
              <div class="d-flex align-center">
                <v-icon color="warning" size="32" class="mr-3">
                  mdi-play-circle
                </v-icon>
                <div>
                  <div class="text-h5 font-weight-bold warning--text">
                    {{ statistics.running_jobs }}
                  </div>
                  <div class="text-caption text--secondary">
                    Running
                  </div>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" sm="6" md="3">
          <v-card class="stat-card">
            <v-card-text class="pa-3">
              <div class="d-flex align-center">
                <v-icon color="error" size="32" class="mr-3">
                  mdi-alert-circle
                </v-icon>
                <div>
                  <div class="text-h5 font-weight-bold error--text">
                    {{ statistics.failed_24h }}
                  </div>
                  <div class="text-caption text--secondary">
                    Failed (24h)
                  </div>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>

        <v-col cols="12" sm="6" md="3">
          <v-card class="stat-card">
            <v-card-text class="pa-3">
              <div class="d-flex align-center">
                <v-icon color="success" size="32" class="mr-3">
                  mdi-check-circle
                </v-icon>
                <div>
                  <div class="text-h5 font-weight-bold success--text">
                    {{ statistics.success_rate_7d }}%
                  </div>
                  <div class="text-caption text--secondary">
                    Success Rate (7d)
                  </div>
                </div>
              </div>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>

      <!-- Filters Card -->
      <v-card class="mb-3">
        <v-card-text class="pa-3">
          <v-row dense align="center">
            <v-col cols="12" md="3">
              <v-select
                v-model="filters.status"
                :items="statusOptions"
                label="Status"
                outlined
                dense
                hide-details
                clearable
                @change="resetToFirstPageAndLoad"
              />
            </v-col>

            <v-col cols="12" md="3">
              <v-menu
                v-model="dateFromMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                max-width="290px"
                min-width="290px"
              >
                <template #activator="{ on, attrs }">
                  <v-text-field
                    v-model="filters.dateFrom"
                    label="Date From"
                    prepend-inner-icon="mdi-calendar"
                    outlined
                    dense
                    hide-details
                    readonly
                    clearable
                    v-bind="attrs"
                    v-on="on"
                    @click:clear="filters.dateFrom = null; resetToFirstPageAndLoad()"
                  />
                </template>
                <v-date-picker
                  v-model="filters.dateFrom"
                  @input="dateFromMenu = false; resetToFirstPageAndLoad()"
                />
              </v-menu>
            </v-col>

            <v-col cols="12" md="3">
              <v-menu
                v-model="dateToMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                max-width="290px"
                min-width="290px"
              >
                <template #activator="{ on, attrs }">
                  <v-text-field
                    v-model="filters.dateTo"
                    label="Date To"
                    prepend-inner-icon="mdi-calendar"
                    outlined
                    dense
                    hide-details
                    readonly
                    clearable
                    v-bind="attrs"
                    v-on="on"
                    @click:clear="filters.dateTo = null; resetToFirstPageAndLoad()"
                  />
                </template>
                <v-date-picker
                  v-model="filters.dateTo"
                  @input="dateToMenu = false; resetToFirstPageAndLoad()"
                />
              </v-menu>
            </v-col>

            <v-col cols="12" md="3">
              <v-btn
                color="primary"
                outlined
                block
                @click="loadJobs"
              >
                <v-icon left small>
                  mdi-refresh
                </v-icon>
                Refresh
              </v-btn>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>

      <!-- Operations Table -->
      <v-card>
        <v-card-title class="py-3">
          <span class="text-h6">Operations</span>
          <v-spacer />
          <v-text-field
            v-model="search"
            prepend-inner-icon="mdi-magnify"
            label="Search"
            outlined
            dense
            hide-details
            style="max-width: 300px;"
            @input="resetToFirstPageAndLoad"
          />
        </v-card-title>

        <v-divider />

        <v-data-table
          :headers="headers"
          :items="jobs"
          :loading="loading"
          :server-items-length="pagination.total_count"
          :options.sync="tableOptions"
          :footer-props="{ itemsPerPageOptions: [10, 20, 50, 100] }"
          dense
          @update:options="onTableOptionsChanged"
          @click:row="viewJobDetails"
        >
          <!-- Operation ID -->
          <template #item.uuid="{ item }">
            <span class="font-weight-medium primary--text">
              #{{ item.uuid.substring(0, 8) }}
            </span>
          </template>

          <!-- Company -->
          <template #item.company_name="{ item }">
            <span class="text-body-2">{{ item.company_name }}</span>
          </template>

          <!-- Job Type -->
          <template #item.job_type="{ item }">
            <v-chip
              :color="getJobTypeColor(item.job_type)"
              x-small
              outlined
            >
              {{ getJobTypeText(item.job_type) }}
            </v-chip>
          </template>

          <!-- Source -->
          <template #item.source="{ item }">
            <v-chip
              :color="item.source === 'manual' ? 'primary' : 'grey'"
              x-small
              outlined
            >
              {{ item.source === 'manual' ? 'Manual' : 'Auto' }}
            </v-chip>
          </template>

          <!-- Date Range -->
          <template #item.date_range="{ item }">
            <div class="text-caption text--secondary">
              {{ formatDate(item.start_date) }} to {{ formatDate(item.end_date) }}
            </div>
          </template>

          <!-- Status -->
          <template #item.status="{ item }">
            <v-chip
              :color="getStatusColor(item.status)"
              x-small
              text-color="white"
            >
              {{ getStatusText(item.status) }}
            </v-chip>
          </template>

          <!-- Progress -->
          <template #item.progress="{ item }">
            <div class="d-flex align-center">
              <v-progress-linear
                :value="getProgress(item)"
                :color="getStatusColor(item.status)"
                height="8"
                rounded
                class="mr-2"
                style="min-width: 80px; max-width: 120px;"
              />
              <span class="text-caption text--secondary" style="min-width: 35px;">
                {{ getProgress(item) }}%
              </span>
            </div>
          </template>

          <!-- Created By -->
          <template #item.created_by="{ item }">
            <div class="text-caption text--secondary">
              {{ item.created_by || '-' }}
            </div>
          </template>

          <!-- Created At -->
          <template #item.created_at="{ item }">
            <div class="text-caption text--secondary">
              {{ formatDateTime(item.created_at) }}
            </div>
          </template>

          <!-- Updated At -->
          <template #item.completed_at="{ item }">
            <div class="text-caption text--secondary">
              {{ item.completed_at ? formatDateTime(item.completed_at) : '-' }}
            </div>
          </template>

          <!-- Actions -->
          <template #item.actions="{ item }">
            <v-tooltip bottom>
              <template #activator="{ on, attrs }">
                <v-btn
                  icon
                  small
                  v-bind="attrs"
                  v-on="on"
                  @click="viewJobDetails(item)"
                >
                  <v-icon small>
                    mdi-eye
                  </v-icon>
                </v-btn>
              </template>
              <span>View Details</span>
            </v-tooltip>
          </template>
        </v-data-table>
      </v-card>
    </v-container>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { LoggedInUser } from '~/models'
import ReplicaCompanyAutocomplete from '~/components/admin/ReplicaCompanyAutocomplete.vue'

interface ETLJob {
  uuid: string
  company_name: string
  status: string
  start_date: string
  end_date: string
  created_at: string
  started_at: string | null
  completed_at: string | null
  orders_processed: number
  order_items_processed: number
  error_message: string | null
  duration_ms: number | null
  log_summary: Record<string, number>
  performance_summary: Record<string, any>
  created_by: string | null
  source: string
  job_type: string
}

interface Statistics {
  total_jobs: number
  jobs_24h: number
  running_jobs: number
  failed_24h: number
  success_rate_7d: number
}

interface Pagination {
  page: number
  page_size: number
  total_pages: number
  total_count: number
  has_next: boolean
  has_previous: boolean
}

export default Vue.extend({
  components: {
    ReplicaCompanyAutocomplete
  },

  middleware: ['auth'],

  data () {
    return {
      loading: false,
      jobs: [] as ETLJob[],
      statistics: {
        total_jobs: 0,
        jobs_24h: 0,
        running_jobs: 0,
        failed_24h: 0,
        success_rate_7d: 0
      } as Statistics,
      pagination: {
        page: 1,
        page_size: 20,
        total_pages: 0,
        total_count: 0,
        has_next: false,
        has_previous: false
      } as Pagination,

      // Filters
      filters: {
        status: null as string | null,
        dateFrom: null as string | null,
        dateTo: null as string | null
      },
      dateFromMenu: false,
      dateToMenu: false,
      search: '',

      // Table
      tableOptions: {
        page: 1,
        itemsPerPage: 20,
        sortBy: ['created_at'],
        sortDesc: [true]
      },

      headers: [
        { text: 'Operation ID', value: 'uuid', sortable: false },
        { text: 'Company', value: 'company_name', sortable: false },
        { text: 'Job Type', value: 'job_type', sortable: true },
        { text: 'Source', value: 'source', sortable: true },
        { text: 'Date Range', value: 'date_range', sortable: false },
        { text: 'Status', value: 'status', sortable: true },
        { text: 'Progress', value: 'progress', sortable: false },
        { text: 'Created By', value: 'created_by', sortable: false },
        { text: 'Created At', value: 'created_at', sortable: true },
        { text: 'Updated At', value: 'completed_at', sortable: true },
        { text: 'Actions', value: 'actions', sortable: false }
      ],

      statusOptions: [
        { text: 'All', value: null },
        { text: 'Pending', value: 'pending' },
        { text: 'Running', value: 'running' },
        { text: 'Completed', value: 'completed' },
        { text: 'Failed', value: 'failed' }
      ],

      // Create ETL dialog state
      createDialog: false,
      createDateFromMenu: false,
      createDateToMenu: false,
      createForm: {
        dateFrom: null as string | null,
        dateTo: null as string | null
      },
      createLoading: false
    }
  },

  created () {
    // Check if user is staff or superuser
    const user = this.$auth.user as never as LoggedInUser
    if (!user.is_staff && !user.is_superuser) {
      this.$nuxt.error({ statusCode: 403, message: 'Access denied. Only staff or superuser accounts can view ETL jobs.' })
      return
    }

    this.loadJobs()
  },

  methods: {
    onTableOptionsChanged (options: any) {
      // Sync table options and fetch data
      this.tableOptions = options
      this.loadJobs()
    },

    resetToFirstPageAndLoad () {
      this.tableOptions.page = 1
      this.loadJobs()
    },

    async loadJobs () {
      this.loading = true

      try {
        const params = new URLSearchParams()
        params.append('page', this.tableOptions.page.toString())
        params.append('page_size', this.tableOptions.itemsPerPage.toString())

        if (this.filters.status) {
          params.append('status', this.filters.status)
        }

        if (this.filters.dateFrom) {
          params.append('date_from', this.filters.dateFrom + 'T00:00:00Z')
        }

        if (this.filters.dateTo) {
          params.append('date_to', this.filters.dateTo + 'T23:59:59Z')
        }

        // Note: backend does not support search yet; omit search param for now
        const response = await this.$axios.get(`/api/etl/jobs/?${params.toString()}`)

        this.jobs = response.data.results
        this.pagination = response.data.pagination
        this.statistics = response.data.statistics
      } catch (error) {
        this.$snackbar('error', 'Failed to load ETL jobs')
      } finally {
        this.loading = false
      }
    },

    getStatusColor (status: string): string {
      switch (status) {
        case 'completed': return 'success'
        case 'running': return 'warning'
        case 'failed': return 'error'
        case 'pending': return 'info'
        default: return 'grey'
      }
    },

    getStatusText (status: string): string {
      switch (status) {
        case 'completed': return 'Completed'
        case 'running': return 'Running'
        case 'failed': return 'Failed'
        case 'pending': return 'Pending'
        default: return status
      }
    },

    getJobTypeColor (jobType: string): string {
      switch (jobType) {
        case 'order': return 'blue'
        case 'video_record_log': return 'purple'
        case 'fixcase': return 'orange'
        default: return 'grey'
      }
    },

    getJobTypeText (jobType: string): string {
      switch (jobType) {
        case 'order': return 'Order'
        case 'video_record_log': return 'Video Log'
        case 'fixcase': return 'Fix Case'
        default: return jobType
      }
    },

    getProgress (item: ETLJob): number {
      switch (item.status) {
        case 'completed': return 100
        case 'running': return 50
        case 'failed': return 0
        case 'pending': return 0
        default: return 0
      }
    },

    formatDate (dateString: string): string {
      return new Date(dateString).toLocaleDateString()
    },

    formatDateTime (dateString: string): string {
      return new Date(dateString).toLocaleString()
    },

    viewJobDetails (job: ETLJob) {
      this.$router.push(`/etl/bulk-operations/${job.uuid}`)
    },

    isoStartOfDay (dateStr: string): string {
      if (!dateStr) { return '' }
      const d = new Date(dateStr + 'T00:00:00')
      return d.toISOString()
    },

    isoEndOfDay (dateStr: string): string {
      if (!dateStr) { return '' }
      const d = new Date(dateStr + 'T23:59:59')
      return d.toISOString()
    },

    async submitCreate () {
      if (!this.createForm.dateFrom || !this.createForm.dateTo) { return }
      this.createLoading = true
      try {
        const user = this.$auth.user as never as LoggedInUser
        const companyId = user?.company?.id

        const payload: any = {
          start_date: this.isoStartOfDay(this.createForm.dateFrom),
          end_date: this.isoEndOfDay(this.createForm.dateTo),
          company_id: companyId
        }

        const resp = await this.$axios.post('/api/etl/scheduler/bigquery-pipeline/', payload)
        const ids: string[] = (resp.data && resp.data.jobs_created) || []
        if (ids.length > 0) {
          const createdBy = user?.username || ''
          this.$router.push(`/etl/bulk-operations/${ids[0]}?created_by=${encodeURIComponent(createdBy)}`)
        } else {
          this.$snackbar('info', 'No job created')
        }
      } catch (e) {
        this.$snackbar('error', 'Failed to create ETL job')
      } finally {
        this.createLoading = false
        this.createDialog = false
      }
    }
  }
})
</script>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Add colored border to stat cards */
.stat-card:nth-child(1) {
  border-left-color: var(--v-primary-base);
}

.stat-card:nth-child(2) {
  border-left-color: var(--v-warning-base);
}

.stat-card:nth-child(3) {
  border-left-color: var(--v-error-base);
}

.stat-card:nth-child(4) {
  border-left-color: var(--v-success-base);
}

/* Make table rows clickable */
::v-deep .v-data-table tbody tr {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

::v-deep .v-data-table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Compact table cells */
::v-deep .v-data-table td,
::v-deep .v-data-table th {
  padding: 8px 12px !important;
}

/* Better spacing for chips */
::v-deep .v-chip {
  font-weight: 500;
}
</style>
