<template>
  <div>
    <!-- Page Title -->
    <v-row>
      <v-col cols="12">
        <v-row class="pa-3">
          <h2>Order Import</h2>
          <v-spacer />
        </v-row>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12">
        <h4>{{ $t('order-import.history') }}</h4>
        <order-import-progress-data-table
          :table-data.sync="table"
          :import-type="form.import_type"
        />

        <template v-if="$hasPerms('add_orderimportrequest')">
          <h4>{{ $t('order-import.order') }}</h4>
          <order-import-form
            ref="form"
            :form-data.sync="form"
            :loading="form.loading"
            @submit="upload()"
          />
        </template>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { saveAs } from 'file-saver'
import OrderImportForm from '~/components/order-import/OrderImportForm.vue'
import OrderImportProgressDataTable from '~/components/order-import/OrderImportProgressDataTable.vue'
import { Company, FEATURE_FLAGS, VDataTableOption, VForm } from '~/models'
import { getDynamicRESTPagination, getDynamicRESTSorting } from '~/utils/api'
export default Vue.extend({
  components: { OrderImportForm, OrderImportProgressDataTable },
  data () {
    return {
      // TODO: create interface for OrderImportRequest
      form: {
        file: null as File | null,
        import_type: null as string | null,
        loading: false,
        options: {
          do_not_update_tracking: false
        }
      },
      table: {
        meta: { total_results: 0, total_pages: 1, page: 1, per_page: 25 },
        options: {
          page: 1,
          itemsPerPage: 5,
          sortBy: ['create_date'],
          sortDesc: [true],
          groupBy: [],
          groupDesc: [],
          multiSort: false,
          mustSort: false
        } as VDataTableOption,
        import_requests: [] as any[]
      },
      loading: false
    }
  },

  watch: {
    'table.options': {
      handler () {
        if (!this.loading) {
          this.getOrderImportRequests()
        }
      },
      deep: true
    }
  },

  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.ORDER_CENTER)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }

    if (!this.$hasPerms('view_orderimportrequest')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }
  },

  mounted () {
    this.getOrderImportRequests()
  },

  methods: {
    async getOrderImportRequests () {
      const params: any = {
        ...getDynamicRESTPagination(this.table.options),
        ...getDynamicRESTSorting(this.table.options),
        include: ['create_by.*']
      }
      try {
        const res = await this.$axios.get('/api/importdata/resource/order-import-requests/', { params })
        this.table.import_requests = res.data.order_import_requests
        this.table.meta = res.data.meta
      } catch (error) {
        console.error(error)
      }
    },

    async upload () {
      const vform: VForm = this.$refs.form as unknown as VForm
      if (!vform.validate() || !this.form.file) {
        return
      }

      if (this.$auth.user?.is_staff) {
        const company = this.$auth.user?.company as Company
        while (true) {
          const msg = await this.$prompt({
            title: 'ยืนยันการนำเข้าคำสั่งซื้อโดย Staff',
            text: ('คุณแน่ใจหรือไม่ว่าต้องการนำเข้าข้อมูลคำสั่งซื้อของ<br> ' +
            `ร้านค้า <b style="text-transform: uppercase;color: #E74C3C;">"${company.name}"</b> โดยใช้บัญชีผู้ใช้ <b style="text-transform: uppercase;color: #E74C3C;">"${this.$auth.user.username}"</b><br><br>` +
            'หากแน่ใจกรุณาพิมพ์คำว่า <b>" ยืนยัน "</b> หรือ <b>" Confirm "</b> '),
            preset: 'text'
          })
          if (msg === null) { return }
          if (msg === 'ยืนยัน' || msg?.toLowerCase() === 'confirm') {
            break
          }
        }
      }

      const formData = new FormData()
      if (this.form.file) {
        formData.append('file', this.form.file)
      }
      formData.append('options', JSON.stringify(this.form.options))

      this.form.loading = true
      try {
        const res = await this.$axios.request({
          method: 'POST',
          url: `/api/importdata/orders/upload/${this.form.import_type}/`,
          data: formData,
          headers: { 'Content-Type': 'multipart/form-data' },
          responseType: 'blob'
        })
        res.data = JSON.parse(await res.data.text())
        this.table.import_requests.unshift(res.data)
        this.$snackbar('success', this.$t('order-import.being-import-data'))
      } catch (error: any) {
        console.error(error)

        const response = error.response

        if (response.status === 422) {
          this.$snackbar('error', this.$t('order-import.error'))
          this.$alert({
            theme: 'error',
            title: 'มีข้อผิดพลาดในไฟล์อัพโหลด',
            text: `
              <div>
                <p>กรุณา "ดาวน์โหลดไฟล์" เพื่อ ตรวจสอบและแก้ไขข้อผิดพลาดในไฟล์</p>
                <b>Tips</b>
                <ul>
                  <li>มองหา Cell ที่เป็นสีแดง</li>
                  <li>มองหาเครื่องหมาย !!!</li>
                </ul>
              </div>
            `,
            buttons: [
              {
                text: 'ดาวน์โหลดไฟล์',
                color: 'primary',
                onclick: () => {
                  saveAs(response.data, 'error.xlsx')
                  this.$store.dispatch('alert/hide')
                }
              }]
          })
        }

        if (response.status === 500) {
          this.$snackbar('error', this.$t('order-import.has-error-contact-admin'))
        }
      }
      this.form.loading = false
    }
  }

})
</script>
