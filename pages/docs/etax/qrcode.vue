<template>
  <v-container>
    <div class="d-flex justify-space-between align-center mb-4">
      <h1 class="text-h4">
        คู่มือการใช้งาน E-Tax QR Code V3 POS
      </h1>
      <div>
        <v-btn
          small
          outlined
          class="py-4"
          color="secondary"
          :to="'/docs/etax/qrcode-testtools'"
        >
          <v-icon left>
            mdi-tools
          </v-icon>
          Test Tools
        </v-btn>
      </div>
    </div>

    <!-- Documentation Section -->
    <div>
      <div class="documentation-content">
        <!-- Overview Section -->
        <div class="mb-6">
          <h2 class="text-h5 mb-3">
            🎯 ภาพรวม
          </h2>
          <p class="text-body-1">
            Dobybot E-Tax QR Code V3 เป็นระบบช่วยให้ลูกค้าสามารถขอใบกำกับภาษีอิเล็กทรอนิกส์ได้อย่างง่ายดาย โดยการสแกน QR Code ที่สร้างจากระบบ POS
          </p>

          <!-- Workflow Diagram -->
          <v-card outlined class="mb-4">
            <v-card-text>
              <h4 class="text-subtitle-1 mb-2">
                กระบวนการ
              </h4>
              <div>
                <v-img src="/flowcharts/etax-qr-flow.svg" max-height="800" contain />
              </div>
            </v-card-text>
          </v-card>
        </div>

        <!-- Prerequisites Section -->
        <div class="mb-6">
          <h3 class="text-h6 mb-3">
            📋 ข้อกำหนด / การตั้งค่าก่อนใช้งาน
          </h3>
          <v-list dense>
            <v-list-item>
              <v-list-item-icon>
                <v-icon color="primary">
                  mdi-office-building
                </v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title><strong>Company Code</strong></v-list-item-title>
                <v-list-item-subtitle>จะได้รับ company_code (CXX เช่น C27) เมื่อลงทะเบียนใช้งาน API</v-list-item-subtitle>
              </v-list-item-content>
            </v-list-item>
            <v-list-item>
              <v-list-item-icon>
                <v-icon color="primary">
                  mdi-key
                </v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title><strong>JWT Secret</strong></v-list-item-title>
                <v-list-item-subtitle>จะได้รับ JWT Secret Key สำหรับการสร้าง JWT Token เมื่อลงทะเบียนใช้งาน API</v-list-item-subtitle>
              </v-list-item-content>
            </v-list-item>
            <v-list-item>
              <v-list-item-icon>
                <v-icon color="primary">
                  mdi-barcode
                </v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title><strong>SKU</strong></v-list-item-title>
                <v-list-item-subtitle>ต้องตั้งค่า SKU ไว้ในฐานข้อมูล (สามารถตั้งค่าสินค้าได้ผ่าน API / Website / Import Excel)</v-list-item-subtitle>
              </v-list-item-content>
            </v-list-item>
          </v-list>
        </div>

        <!-- Workflow Section -->
        <div class="mb-6">
          <h3 class="text-h6 mb-3">
            🔄 กระบวนการ
          </h3>

          <!-- <h4 class="text-subtitle-1 mb-2">
                  กระบวนการ
                </h4> -->

          <!-- Step 1: Setup Products -->
          <v-card outlined class="mb-4">
            <v-card-text>
              <div class="text-h6">
                1. Setup ข้อมูลสินค้า และ SKU
              </div>
              <p>การออกเอกสาร E-Tax จะอ้างอิงชื่อสินค้าจาก SKU ที่ส่งมา ก่อน QRCode จะใช้งานได้ จำเป็นต้องทำการตั้งค่าสินค้าในระบบให้ครบก่อน</p>

              <h4 class="text-title">
                การ Setup ข้อมูลสินค้าสามารถทำได้ 2 วิธี:
              </h4>
              <v-list dense>
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-title><strong>1. สร้างสินค้าผ่าน API</strong></v-list-item-title>
                    <v-list-item-subtitle>
                      ดูรายละเอียดได้จาก
                      <a :href="`${backend_host}/openapi/schema/redoc/#tag/Product/operation/v1_products_create`" target="_blank">API Docs</a>
                    </v-list-item-subtitle>
                  </v-list-item-content>
                </v-list-item>
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-title><strong>2. สร้างสินค้าผ่านหน้าเว็บ / การนำเข้าไฟล์ Excel</strong></v-list-item-title>
                    <v-list-item-subtitle>
                      ดูรายละเอียดได้จาก <a href="/settings/product/barcode" target="_blank">Product Management</a>
                      (เข้าระบบด้วย Username/Password ที่ได้จากการลงทะเบียน)
                    </v-list-item-subtitle>
                  </v-list-item-content>
                </v-list-item>
              </v-list>

              <h4 class="mb-2 text-title">
                ตัวอย่างการสร้างสินค้าผ่าน API:
              </h4>
              <v-card outlined>
                <v-card-text>
                  <pre class="code-block"><code>curl -X POST "{{ backend_host }}/openapi/v1/products/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Token &lt;token&gt;" \
  -d '{
    "sku": "PROD-001",
    "barcode": "PROD-001",
    "name": "Product 001"
  }'</code></pre>
                </v-card-text>
              </v-card>

              <v-alert outlined type="info" dense class="mt-2">
                <strong>หมายเหตุ:</strong> สามารถดู <code>&lt;token&gt;</code> ได้จากหน้าเว็บ
                <a href="/settings/apidoc" target="_blank">Dobybot API</a>
              </v-alert>
            </v-card-text>

            <!-- Step 2 -->
            <v-card-text>
              <div class="text-h6">
                2. สร้าง JWT Token
              </div>
              <ul>
                <li>ใช้ Payload ตามโครงสร้างข้อมูลที่กำหนด</li>
                <li>ใช้ JWT Secret</li>
                <li>ใช้ algorithm HS256</li>
                <li>ดูเพื่มเติมได้ที่ <a href="https://jwt.io">jwt.io</a></li>
              </ul>

              <h4 class="mt-4">
                ตัวอย่าง Payload สำหรับสร้าง JWT Token
              </h4>

              <!-- Old Format -->
              <h5 class="text-subtitle-1 mb-2">
                🔄 รูปแบบเก่า (7 ฟิลด์) - ยังใช้งานได้ปกติ
              </h5>
              <v-card outlined class="mb-4">
                <v-card-text>
                  <pre class="code-block"><code>[
  "C27",                    // 0: company_code (ได้จากการลงทะเบียน)
  "SO-20231027-001",       // 1: doc_id (เลขเอกสาร)
  "2023-10-27",            // 2: order_date (YYYY-MM-DD)
  100,                     // 3: discountamount (ส่วนลดท้ายบิล)
  2800,                    // 4: amount (ยอดสุทธิ)
  3,                       // 5: vattype (2=ไม่รวม, 3=รวม)
  [                        // 6: items (รายการสินค้า)
    [
      "A001",              // sku
      10,                  // number (จำนวน)
      100,                 // pricepernumber (ราคาต่อหน่วย)
      900,                 // totalprice (ราคารวม)
      100,                 // discount (ส่วนลดรายการ)
      0                    // vat_percent (เปอร์เซ็นต์ภาษี)
    ]
  ]
]</code></pre>
                </v-card-text>
              </v-card>

              <!-- New Format -->
              <h5 class="text-subtitle-1 mb-2">
                ✨ รูปแบบใหม่ (10 ฟิลด์) - เพิ่มข้อมูลสาขา, หมายเหตุ, และเลข ABB
              </h5>
              <v-card outlined class="mb-4">
                <v-card-text>
                  <pre class="code-block"><code>[
  "C27",                    // 0: company_code (ได้จากการลงทะเบียน)
  "SO-20231027-001",       // 1: doc_id (เลขเอกสาร)
  "2023-10-27",            // 2: order_date (YYYY-MM-DD)
  100,                     // 3: discountamount (ส่วนลดท้ายบิล)
  2800,                    // 4: amount (ยอดสุทธิ)
  3,                       // 5: vattype (2=ไม่รวม, 3=รวม)
  "00001",                 // 6: branch (รหัสสาขา 5 หลัก - จำเป็น)
  "หมายเหตุเพิ่มเติม",        // 7: remark (หมายเหตุ - ไม่จำเป็น)
  "ABB123456",             // 8: abb_number (เลข ABB - ไม่จำเป็น)
  [                        // 9: items (รายการสินค้า - ย้ายจาก index 6)
    [
      "A001",              // sku
      10,                  // number (จำนวน)
      100,                 // pricepernumber (ราคาต่อหน่วย)
      900,                 // totalprice (ราคารวม)
      100,                 // discount (ส่วนลดรายการ)
      0                    // vat_percent (เปอร์เซ็นต์ภาษี)
    ]
  ]
]</code></pre>
                </v-card-text>
              </v-card>

              <!-- Migration Guide -->
              <v-alert outlined type="warning" dense class="mb-4">
                <strong>📝 คำแนะนำการย้าย:</strong>
                <ul class="mt-2 mb-0">
                  <li>ระบบ POS เก่าสามารถใช้รูปแบบ 7 ฟิลด์ต่อไปได้</li>
                  <li>ระบบ POS ใหม่แนะนำให้ใช้รูปแบบ 10 ฟิลด์เพื่อความสมบูรณ์ของข้อมูล</li>
                  <li>เมื่อใช้รูปแบบ 10 ฟิลด์ จำเป็นต้องระบุรหัสสาขา (branch)</li>
                </ul>
              </v-alert>
            </v-card-text>

            <!-- Step 3 -->
            <v-card-text>
              <div class="text-h6">
                3. สร้าง QR Code URL
              </div>

              นำ URL ต่อไปนี้มาต่อกับ JWT Token ที่ได้จากขั้นตอนที่ 2.

              <div class="mb-2">
                <strong>PROD:</strong>
                <v-card outlined class="mt-1">
                  <v-card-text>
                    <code>https://cloud.dobybot.com/v3/request-etax/?t={JWT_TOKEN}</code>
                  </v-card-text>
                </v-card>
              </div>
              <div>
                <strong>UAT:</strong>
                <v-card outlined class="mt-1">
                  <v-card-text>
                    <code>https://uat.dobybot.com/v3/request-etax/?t={JWT_TOKEN}</code>
                  </v-card-text>
                </v-card>
              </div>
            </v-card-text>

            <!-- Step 4 -->
            <v-card-text>
              <div class="text-h6">
                4. แสดง QR Code ให้ลูกค้าสแกน
              </div>
              พิมพ์ QR Code ออกมาท้ายใบเสร็จสำหรับให้ลูกค้าสแกนเพื่อขอใบกำกับภาษีอิเล็กทรอนิกส์ (ETax)
            </v-card-text>
          </v-card>

          <!-- Field Description -->
          <div>
            <!-- Main Fields Table -->
            <h4 class="text-subtitle-1 mb-2">
              ตารางฟิลด์หลัก
            </h4>
            <v-alert outlined type="info" dense class="mb-3">
              <strong>หมายเหตุ:</strong> ฟิลด์ที่มี * เป็นฟิลด์ใหม่ในรูปแบบ 10 ฟิลด์ เท่านั้น (รูปแบบ 7 ฟิลด์จะใช้ค่าเริ่มต้น)
            </v-alert>
            <v-data-table
              :headers="mainFieldHeaders"
              :items="mainFieldItems"
              class="mb-4"
              hide-default-footer
              disable-pagination
            >
              <template #item.require="{ item }">
                ✅
              </template>
            </v-data-table>

            <!-- Items Fields Table -->
            <h4 class="text-subtitle-1 mb-2">
              ตารางฟิลด์ Items (Array ใน Index 6 สำหรับรูปแบบเก่า หรือ Index 9 สำหรับรูปแบบใหม่)
            </h4>
            <v-data-table
              :headers="itemFieldHeaders"
              :items="itemFieldItems"
              class="mb-4"
              hide-default-footer
              disable-pagination
            >
              <template #item.require="{ item }">
                ✅
              </template>
            </v-data-table>
          </div>
        </div>
      </div>
    </div>
  </v-container>
</template>

<script>
import { getBackendHost } from '~/utils'

export default {
  name: 'EtaxQRCodeDocs',
  layout: 'blank',
  auth: 'guest',

  data () {
    return {
      backend_host: '',

      // Table headers and data
      mainFieldHeaders: [
        { text: 'Index', value: 'index' },
        { text: 'Field', value: 'field' },
        { text: 'Data Type', value: 'dataType' },
        { text: 'Require', value: 'require' },
        { text: 'Remark', value: 'remark' },
        { text: 'Example 1', value: 'example1' },
        { text: 'Example 2', value: 'example2' },
        { text: 'Example 3', value: 'example3' }
      ],
      mainFieldItems: [
        { index: 0, field: 'company_code', dataType: 'String', require: true, remark: 'CXX (ได้จากการลงทะเบียน)', example1: '"C27"', example2: '"C1"', example3: '"C999"' },
        { index: 1, field: 'doc_id', dataType: 'String', require: true, remark: 'เลขเอกสารไม่ซ้ำ', example1: '"SO-20231027-001"', example2: '"INV-2023-001"', example3: '"ORDER-001"' },
        { index: 2, field: 'order_date', dataType: 'String', require: true, remark: 'YYYY-MM-DD เท่านั้น', example1: '"2023-10-27"', example2: '"2023-12-31"', example3: '"2024-01-01"' },
        { index: 3, field: 'discountamount', dataType: 'Number', require: true, remark: 'ส่วนลดท้ายบิล (ทศนิยม 4 ตำแหน่ง)', example1: '100', example2: '0', example3: '250.5' },
        { index: 4, field: 'amount', dataType: 'Number', require: true, remark: 'ยอดสุทธิ (ทศนิยม 4 ตำแหน่ง)', example1: '2800', example2: '1500.75', example3: '999.99' },
        { index: 5, field: 'vattype', dataType: 'Number', require: true, remark: '2=ไม่รวมภาษี, 3=รวมภาษี', example1: '3', example2: '2', example3: '3' },
        { index: '6*', field: 'branch', dataType: 'String', require: false, remark: '🆕 รหัสสาขา 5 หลัก (จำเป็นในรูปแบบใหม่)', example1: '"00001"', example2: '"00000"', example3: '"99999"' },
        { index: '7*', field: 'remark', dataType: 'String', require: false, remark: '🆕 หมายเหตุเพิ่มเติม (ไม่จำเป็น)', example1: '"หมายเหตุ"', example2: '""', example3: '"Special order"' },
        { index: '8*', field: 'abb_number', dataType: 'String', require: false, remark: '🆕 เลข ABB (ไม่จำเป็น)', example1: '"ABB123456"', example2: '""', example3: '"REF789"' },
        { index: '6/9', field: 'items', dataType: 'Array', require: true, remark: 'รายการสินค้า (Index 6 รูปแบบเก่า, Index 9 รูปแบบใหม่)', example1: '[["A001",10,100,900,100,0]]', example2: '[["B001",5,200,1000,0,7]]', example3: '[["C001",1,500,500,0,7]]' }
      ],
      itemFieldHeaders: [
        { text: 'Index', value: 'index', sortable: false },
        { text: 'Field', value: 'field', sortable: false },
        { text: 'Data Type', value: 'dataType', sortable: false },
        { text: 'Require', value: 'require', sortable: false },
        { text: 'Remark', value: 'remark', sortable: false },
        { text: 'Example 1', value: 'example1', sortable: false },
        { text: 'Example 2', value: 'example2', sortable: false },
        { text: 'Example 3', value: 'example3', sortable: false }
      ],
      itemFieldItems: [
        { index: 0, field: 'sku', dataType: 'String', require: true, remark: 'รหัสสินค้า (ต้องมีในฐานข้อมูล)', example1: '"A001"', example2: '"COFFEE001"', example3: '"SHIRT-L"' },
        { index: 1, field: 'number', dataType: 'Number', require: true, remark: 'จำนวน (ทศนิยม 4 ตำแหน่ง)', example1: '10', example2: '2.5', example3: '1' },
        { index: 2, field: 'pricepernumber', dataType: 'Number', require: true, remark: 'ราคาต่อหน่วย (ทศนิยม 4 ตำแหน่ง)', example1: '100', example2: '50.25', example3: '999.99' },
        { index: 3, field: 'totalprice', dataType: 'Number', require: true, remark: 'ราคารวม = (pricepernumber × number) - discount', example1: '900', example2: '125.625', example3: '999.99' },
        { index: 4, field: 'discount', dataType: 'Number', require: true, remark: 'ส่วนลดรายการ (ทศนิยม 4 ตำแหน่ง)', example1: '100', example2: '0', example3: '50' },
        { index: 5, field: 'vat_percent', dataType: 'Number', require: true, remark: 'เปอร์เซ็นต์ภาษี (ทศนิยม 2 ตำแหน่ง)', example1: '0', example2: '7', example3: '10' }
      ]
    }
  },

  mounted () {
    this.backend_host = getBackendHost()
  }
}
</script>

<style scoped>
/* .documentation-content {
  max-height: 600px;
  overflow-y: auto;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.documentation-content h1,
.documentation-content h2,
.documentation-content h3 {
  margin-top: 24px;
  margin-bottom: 16px;
}

.documentation-content h1 {
  font-size: 1.5rem;
  font-weight: 500;
}

.documentation-content h2 {
  font-size: 1.25rem;
  font-weight: 500;
}

.documentation-content h3 {
  font-size: 1.1rem;
  font-weight: 500;
}

.documentation-content pre {
  background-color: #2d2d2d;
  color: #f8f8f2;
  padding: 16px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 16px 0;
}

.documentation-content code {
  background-color: #e0e0e0;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: 'Courier New', monospace;
}

.documentation-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
}

.documentation-content th,
.documentation-content td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.documentation-content th {
  background-color: #f2f2f2;
  font-weight: 500;
} */
</style>
