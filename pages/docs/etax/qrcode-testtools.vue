<template>
  <v-container>
    <div class="d-flex justify-space-between align-center mb-4">
      <h1 class="text-h4">
        🧪 E-Tax QR Code V3 Enhanced Test Tools
      </h1>
      <div>
        <v-btn
          small
          outlined
          class="py-4"
          color="primary"
          :to="'/docs/etax/qrcode'"
        >
          <v-icon left>
            mdi-book-open-variant
          </v-icon>
          Back to Documentation
        </v-btn>
      </div>
    </div>

    <!-- Loading State -->
    <v-card v-if="loading_config" outlined class="mb-6">
      <v-card-text class="text-center py-8">
        <v-progress-circular indeterminate color="primary" class="mb-4" />
        <div>Loading configuration...</div>
      </v-card-text>
    </v-card>

    <!-- Error State -->
    <v-alert
      v-if="config_error"
      type="error"
      class="mb-6"
      dismissible
      @input="config_error = ''"
    >
      <div class="d-flex justify-space-between align-center">
        <div>
          <strong>Configuration Error:</strong> {{ config_error }}
        </div>
        <v-btn
          small
          outlined
          color="error"
          :loading="loading_config"
          @click="loadConfiguration"
        >
          <v-icon left small>
            mdi-refresh
          </v-icon>
          Retry
        </v-btn>
      </div>
    </v-alert>

    <!-- Testing Tool Section -->
    <div v-if="!loading_config && !config_error" outlined class="testing-tool-section">
      <div>
        <v-row>
          <!-- Left Column: Form -->
          <v-col cols="12" md="6">
            <h3 class="mb-4">
              Test Payload Configuration
            </h3>

            <!-- Company Info Display -->
            <v-card outlined class="mb-4">
              <v-card-text>
                <div class="text-subtitle-2 mb-2">
                  Company Information
                </div>
                <div><strong>Company ID:</strong> {{ company_id }}</div>
                <div><strong>Company Code:</strong> {{ company_code }}</div>
              </v-card-text>
            </v-card>

            <!-- Format Toggle -->

            <v-card outlined class="mb-4">
              <v-card-text>
                <div class="text-subtitle-2 mb-3">
                  Payload Format
                </div>
                <v-radio-group v-model="payload_format" row>
                  <v-radio
                    label="7 fields"
                    value="old"
                    color="primary"
                  />
                  <v-radio
                    label="10 fields"
                    value="new"
                    color="success"
                  />
                </v-radio-group>
              </v-card-text>
            </v-card>

            <!-- JWT Secret -->
            <v-row>
              <v-col cols="12">
                <v-textarea
                  v-model="jwt_secret"
                  label="JWT Secret"
                  outlined
                  dense
                  :rules="[$rules.required]"
                  prepend-inner-icon="mdi-key"
                  readonly
                  rows="3"
                />
              </v-col>
            </v-row>

            <!-- JSON Payload Editor -->
            <v-textarea
              v-model="payload_json"
              label="Test Payload (JSON)"
              outlined
              rows="15"
              class="mb-4"
            />

            <!-- Action Buttons -->
            <v-row>
              <v-col cols="12" md="6">
                <v-btn
                  color="primary"
                  block
                  :loading="generating"
                  @click="generateQRCode"
                >
                  <v-icon left>
                    mdi-qrcode
                  </v-icon>
                  Generate QR Code
                </v-btn>
              </v-col>
              <v-col cols="12" md="6">
                <v-btn
                  color="secondary"
                  block
                  outlined
                  @click="resetToExample"
                >
                  <v-icon left>
                    mdi-refresh
                  </v-icon>
                  Reset to Example
                </v-btn>
              </v-col>
            </v-row>

            <!-- Validation Messages -->
            <v-alert
              v-if="validation_error"
              type="error"
              class="mt-4"
              dense
            >
              {{ validation_error }}
            </v-alert>
          </v-col>

          <!-- Right Column: Results -->
          <v-col cols="12" md="6">
            <h3 class="mb-4">
              Generated Results
            </h3>

            <!-- JWT Token -->
            <v-textarea
              v-if="generated_token"
              :value="generated_token"
              label="Generated JWT Token"
              outlined
              readonly
              rows="4"
              class="mb-4"
              append-icon="mdi-content-copy"
              @click:append="copyToClipboard(generated_token)"
            />

            <!-- QR Code URL -->
            <v-text-field
              v-if="qr_code_url"
              :value="qr_code_url"
              label="QR Code URL"
              outlined
              readonly
              class="mb-4"
              append-icon="mdi-content-copy"
              @click:append="copyToClipboard(qr_code_url)"
            />

            <!-- QR Code Image -->
            <div v-if="qr_code_image_url" class="text-center mb-4">
              <h4 class="mb-2">
                QR Code
              </h4>
              <v-img
                :src="qr_code_image_url"
                max-width="200"
                class="mx-auto"
                alt="Generated QR Code"
              />
            </div>

            <!-- Test Instructions -->
            <v-alert
              v-if="qr_code_url"
              type="info"
              class="mt-4"
              dense
            >
              <strong>Next Steps:</strong><br>
              1. Copy the URL above and test in browser<br>
              2. Scan the QR code with mobile device<br>
              3. Verify the order is created correctly
            </v-alert>
          </v-col>
        </v-row>
      </div>
    </div>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { copyToClipboard } from '~/api/utils'

export default Vue.extend({
  name: 'EtaxQRCodeTestTools',
  middleware: 'auth', // Require authentication

  data () {
    return {
      // Configuration loading
      loading_config: true,
      config_error: '',

      // Company data
      company_id: '',
      company_code: '',
      jwt_secret: '',

      // Form data
      payload_json: '',
      payload_format: 'old', // 'old' or 'new'
      form_branch: '00000',
      form_remark: '',
      form_abb_number: '',

      // Generated results
      generated_token: '',
      qr_code_url: '',
      qr_code_image_url: '',

      // UI state
      generating: false,
      validation_error: '',
      show_copy_success: false,

      // Example payloads
      example_payload_old: {
        data: [
          'C475',
          'TEST-2025-01-12-001',
          '2025-01-12',
          50,
          900,
          3,
          [
            [
              'TEST_SKU_001',
              2,
              500,
              950,
              50,
              7
            ]
          ]
        ]
      },
      example_payload_new: {
        data: [
          'C475',
          'TEST-2025-01-12-002',
          '2025-01-12',
          50,
          900,
          3,
          '00000',
          'Test order from enhanced format',
          'ABB123456',
          [
            [
              'TEST_SKU_001',
              2,
              500,
              950,
              50,
              7
            ]
          ]
        ]
      }
    }
  },

  head () {
    return {
      title: 'Dobybot.com - E-Tax QR Code Test Tools'
    }
  },

  watch: {
    payload_format () {
      this.resetToExample()
    }
  },

  async mounted () {
    await this.loadConfiguration()
  },

  methods: {
    async loadConfiguration () {
      try {
        this.loading_config = true
        this.config_error = ''

        // Check permissions first
        if (!this.$hasPerms('change_settingvalue')) {
          throw new Error('You do not have permission to access test tools. Please contact your administrator.')
        }

        // Fetch JWT secret and company info from the API
        const response = await this.$axios.get('/api/etax/easy-order/token3/config/')

        this.company_id = response.data.company_id
        this.company_code = response.data.company_code
        this.jwt_secret = response.data.jwt_secret

        if (!this.jwt_secret) {
          throw new Error('JWT Secret (EASY_ETAX_TOKEN) is not configured for your company. Please configure it in company settings first.')
        }

        // Show success message when configuration is loaded successfully
        if (!this.config_error) {
          this.$snackbar('success', 'Configuration loaded successfully!')
        }

        // Initialize with example payload using company data
        this.resetToExample()
      } catch (error: any) {
        this.config_error = error.response?.data?.detail || error.message || 'Failed to load configuration'
      } finally {
        this.loading_config = false
      }
    },

    resetToExample () {
      // Choose example based on format
      const example = this.payload_format === 'new'
        ? { ...this.example_payload_new }
        : { ...this.example_payload_old }

      if (this.company_code) {
        example.data[0] = this.company_code
      }

      // Update date to today
      const today = new Date().toISOString().split('T')[0]
      example.data[2] = today

      if (this.payload_format === 'new') {
        example.data[1] = `TEST-${today}-002`
        // Update form fields from example
        this.form_branch = String(example.data[6])
        this.form_remark = String(example.data[7])
        this.form_abb_number = String(example.data[8])
      } else {
        example.data[1] = `TEST-${today}-001`
      }

      this.payload_json = JSON.stringify(example, null, 2)
      this.clearResults()
    },

    async generateQRCode () {
      this.generating = true
      this.validation_error = ''
      this.clearResults()

      try {
        // Validate payload
        const payload = {
          payload: JSON.parse(this.payload_json),
          jwt_secret: this.jwt_secret
        }
        // Generate JWT token server-side
        const response = await this.$axios.post('/api/etax/easy-order/token3/generate/', payload)

        this.generated_token = response.data.token
        this.qr_code_url = `${window.location.protocol}//${window.location.host}/v3/request-etax/?t=${this.generated_token}`

        // Generate QR code image using the barcode service
        this.qr_code_image_url = `https://go-barcode-yuo4mnnlaa-as.a.run.app/qrcode/?data=${encodeURIComponent(this.qr_code_url)}&size=200`
      } catch (error: any) {
        this.validation_error = error.response?.data || error.message || 'Failed to generate QR code'
      } finally {
        this.generating = false
      }
    },

    clearResults () {
      this.generated_token = ''
      this.qr_code_url = ''
      this.qr_code_image_url = ''
    },

    copyToClipboard (text) {
      copyToClipboard(text)
      this.$snackbar('success', this.$t('copied'))
    }
  }
})
</script>
