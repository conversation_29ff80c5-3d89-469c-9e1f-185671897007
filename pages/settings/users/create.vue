<template>
  <v-row justify="center">
    <v-col cols="12">
      <h2>{{ $t('users.add-user') }}</h2>
      <v-divider />

      <user-form
        ref="form"
        class="pt-3"
        :form-data.sync="form"
        :username-suffix="$auth.user.company.account_suffix"
        :error="error"
        @submit="submit()"
      />
      <v-row class="pa-3">
        <h2 />
        <v-spacer />
        <v-btn data-test="submit-button" color="success" @click="submit()">
          บันทึก
        </v-btn>
      </v-row>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import UserForm from '~/components/settings/users/UserForm.vue'
import { User } from '~/models'
export default Vue.extend({
  components: {
    UserForm
  },

  middleware: ['auth'],

  data () {
    return {
      form: {
        id: 0,
        username: '',
        password: '',
        confirm: '',
        first_name: '',
        last_name: '',
        user_permissions: [] as string[]
      } as User,

      error: {
        detail: '',
        username: '',
        password: ''
      }
    }
  },

  created () {
    if (!this.$hasPerms('add_user')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }
  },

  methods: {
    async submit () {
      console.log(!(this.$refs.form as any).validate())
      if (!(this.$refs.form as any).validate()) {
        return
      }

      try {
        await this.$store.dispatch('user/create', this.form)
        this.$router.replace({ path: '/settings/users/' })
      } catch (e:any) {
        this.error = e.response.data
      }
    }
  }
})
</script>
