<template>
  <v-row wrap justify="center">
    <v-col cols="12">
      <h2>{{ $t('users.edit') }}</h2>
      <v-divider />

      <user-form
        ref="form"
        class="pt-3"
        :form-data.sync="form"
        :hide-password.sync="hide_password"
        :disabled="['username']"
        :error="error"
        @submit="submit()"
      />
      <v-row class="px-3 mt-4" justify="space-between">
        <v-btn v-if="$hasPerms('delete_user')" width="100" color="error" @click="deleteUser()">
          {{ $t('users.delete-user') }}
        </v-btn>
        <v-spacer />
        <v-btn width="200" color="success" @click="submit()">
          {{ $t('users.save') }}
        </v-btn>
      </v-row>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import UserForm from '~/components/settings/users/UserForm.vue'
import { User } from '~/models'
export default Vue.extend({
  components: {
    UserForm
  },

  middleware: ['auth'],

  data () {
    return {
      form: {
        id: 0,
        username: '',
        password: '',
        confirm: '',
        first_name: '',
        last_name: '',
        user_permissions: [] as string[]
      } as User,
      error: {
        detail: '',
        username: '',
        password: ''
      },
      hide_password: true
    }
  },

  head () {
    return {
      title: 'Dobybot.com - Create User'
    }
  },

  created () {
    if (!this.$hasPerms('change_user')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }

    (async () => {
      const res = await this.$axios.get(`/api/users/resource/users/${this.$route.params.id}/`, {
        params: {
          exclude: ['groups', 'company', 'group_permissions', 'devices', 'groups']
        }
      })
      this.form = { ...this.form, ...res.data.user }
    })()
  },

  methods: {
    async submit () {
      if (!(this.$refs.form as any).validate()) {
        return
      }

      try {
        const payload = {
          id: this.form.id,
          first_name: this.form.first_name,
          last_name: this.form.last_name,
          user_permissions: this.form.user_permissions,
          password: undefined as string | undefined
        } as User

        if (this.form.password) {
          payload.password = this.form.password
        }
        await this.$axios.put(`/api/users/resource/users/${this.form.id}/`, payload)
        await this.$auth.fetchUser()
        this.$router.replace({ path: '/settings/users/' })
      } catch (e:any) {
        this.error = e.response.data
      }
    },

    async deleteUser () {
      if (!confirm(this.$t('users.confirm-to-delete-user'))) {
        return
      }

      try {
        await this.$axios.delete(`/api/users/resource/users/${this.form.id}/`)
        this.$router.replace({ path: '/settings/users/' })
      } catch (error) {
        console.error(error)
      }
    }
  }
})
</script>
