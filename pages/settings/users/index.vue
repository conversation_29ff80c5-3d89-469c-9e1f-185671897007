<template>
  <v-row>
    <v-col cols="12">
      <v-row class="pa-3">
        <h2>{{ $t('users.manage-users') }}</h2>
        <v-spacer />
        <v-btn v-if="$hasPerms('add_user')" data-test="add-user" color="success" small :to="{path: '/settings/users/create'}">
          {{ $t('users.add-user') }}
        </v-btn>
      </v-row>
      <v-divider />
    </v-col>
    <v-col cols="12">
      <user-filter-form :form-data.sync="filter" />
    </v-col>
    <v-col cols="12">
      <user-data-table
        :items="table.users"
        :options.sync="table.options"
        :server-items-length="table.meta.total_results"
        :loading="loading"
        :permissions="permissions"
        :footer-props="{'items-per-page-options': [5, 10, 25, 50, 100]}"
        @bring-user-back="bringUserBack($event)"
      />
    </v-col>
  </v-row>
</template>

<script lang="ts">
import _ from 'lodash'
import Vue from 'vue'
import UserDataTable from '~/components/settings/users/UserDataTable.vue'
import UserFilterForm from '~/components/settings/users/UserFilterForm.vue'
import { PagePermission, Permission, VDataTableOption } from '~/models'
import { getDynamicRESTPagination, getDynamicRESTSorting } from '~/utils/api'

interface UserFilter {
  search: string
  permissions: string[]
  show_delete_user:boolean
}

export interface User {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
    devices?: { [key: string]: Device };
    user_permissions?: any[];
    group_permissions? : string[];
}

export interface Device {
    os: string;
    device: string;
    browser: string;
    login_at: number;
}

export default Vue.extend({
  components: { UserDataTable, UserFilterForm },

  middleware: ['auth'],

  data () {
    return {
      filter: {
        search: '',
        permissions: [] as string[],
        show_delete_user: false
      } as UserFilter,
      table: {
        meta: {
          total_results: 1,
          total_pages: 1,
          page: 1,
          per_page: 25
        },
        options: {
          page: 1,
          itemsPerPage: 25,
          sortBy: [],
          sortDesc: [true],
          groupBy: [],
          groupDesc: [],
          multiSort: false,
          mustSort: false
        } as VDataTableOption,
        users: [] as any as User
      },
      loading: false,
      permissions: [] as Permission[],

      debouncedGetUsers: null as Function | null
    }
  },

  head () {
    return {
      title: 'Dobybot.com - User Management'
    }
  },

  watch: {
    filter: {
      handler () {
        if (this.debouncedGetUsers) {
          this.table.options.page = 1
          this.debouncedGetUsers()
        }
      },
      deep: true
    },
    'table.options': {
      handler () {
        this.getUsers(this.filter, this.table.options)
      }
    }
  },

  async created () {
    if (!this.$hasPerms('view_user')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
      return
    }

    // get user
    this.debouncedGetUsers = _.debounce(() => { this.getUsers(this.filter, this.table.options) }, 500)

    // get permission list
    const res = await this.$axios.get('/api/users/page-permission/')
    res.data.forEach((permission: PagePermission) => {
      permission.permissions.forEach((p :Permission) => this.permissions.push(p))
    })
  },
  methods: {
    async getUsers (filters: UserFilter, options: VDataTableOption) {
      this.loading = true

      try {
        const config: any = {
          params: {
            ...getDynamicRESTPagination(options),
            ...getDynamicRESTSorting(options),
            'filter{is_active}': true,
            user_permissions: this.filter.permissions
          }
        }

        if (filters.search) {
          config.params.search = filters.search
        }
        if (filters.show_delete_user) {
          config.params['filter{is_active}'] = false
        }

        const res = await this.$axios.get('/api/users/resource/users/', config)
        this.table.users = res.data.users
        this.table.meta = res.data.meta
      } catch (error) {
        console.error(error)
      }

      this.loading = false
    },
    async bringUserBack (id:number) {
      console.log(id)
      await this.$axios.patch(`/api/users/resource/users/${id}/`, { is_active: true })

      this.getUsers(this.filter, this.table.options)
    }
  }
})
</script>
