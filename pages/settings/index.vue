<!-- eslint-disable vue/html-end-tags -->

<template>
  <setting-template ref="base" :title="$t('settings.general-setting')">
    <template #overview>
      <!--  -->
    </template>

    <template #form="{ settings, update }">
      <!-- Supervisor Password -->
      <v-row dense no-gutters>
        <v-col cols="12">
          <h2>{{ $t("settings.setting-supervisor-password") }}</h2>
        </v-col>
        <v-col cols="12">
          <ul class="my-3">
            <li>ใช้ปลดล็อกตั้งค่าขั้นสูงในหน้า Record</li>
            <li>
              ใช้ยืนยันการบันทึกวิดีโอ กรณี คำสั่งซื้อมี FixCase / หมายเหตุ /
              อัดวิดีโอซ้ำ
            </li>
            <li>
              ใช้ยืนยันการบันทึกวิดีโอ กรณี สเเกนสินค้ารายชิ้นไม่ครบตามจำนวน
            </li>
          </ul>
          <v-text-field
            hide-defails
            filled
            :label="$t('settings.confirm-record-password')"
            :value="settings.company.SUPERVISOR_PASSWORD"
            data-test="v-text-field-supervisor-password"
            @change="update({ SUPERVISOR_PASSWORD: $event })"
          />
        </v-col>
      </v-row>

      <!-- Video Recording -->
      <v-row dense no-gutters class="mt-4 pt-4 pb-4">
        <v-col cols="12">
          <h2>{{ $t("settings.record-setting") }}</h2>
        </v-col>

        <v-col cols="12">
          <v-checkbox
            hide-details
            :label="$t('settings.record-piece-scan-mode-enable')"
            :input-value="settings.company.RECORD_PIECE_SCAN_MODE_ENABLE"
            disabled
            @change="update({ RECORD_PIECE_SCAN_MODE_ENABLE: $event })"
          />
          <div class="text-help">
            <div>
              กรุณาติดต่อ
              <a
                href="https://lin.ee/012QqmH"
                class="teal--text"
              >LINE @dobybot</a>
              เพื่อเปิดใช้งานฟีเจอร์นี้
            </div>
          </div>
        </v-col>

        <v-col cols="12">
          <v-checkbox
            hide-details
            :label="
              $t('settings.stop-uncomplete-record-require-supervisor-password')
            "
            :input-value="
              settings.company.STOP_RECORD_REQUIRE_COMPLETED_SCAN_LOG
            "
            @change="update({ STOP_RECORD_REQUIRE_COMPLETED_SCAN_LOG: $event })"
          />
          <div class="text-help">
            <div>
              ก่อนหยุดอัดวิดีโอ
              ระบบจะแจ้งเตือนหากพบว่ายังทำการสแกนสินค้าไม่ครบตามจำนวนในออเดอร์
            </div>
          </div>
        </v-col>

        <v-col cols="12">
          <v-checkbox
            hide-details
            :label="$t('settings.confirm-record-password-enable')"
            :input-value="settings.company.CONFIRM_RECORD_PASSWORD_ENABLE"
            @change="update({ CONFIRM_RECORD_PASSWORD_ENABLE: $event })"
          />
          <div class="text-help">
            <div>
              ก่อนเริ่มอัดวิดีโอ ระบบจะทำการแจ้งเตือนหากพบว่าออเดอร์มี การเปิด
              FixCase / หมายเหตุ / อัดวิดีโอซ้ำ
            </div>
          </div>
          <div class="pl-8">
            <v-checkbox
              hide-details
              dense
              label="ไม่พบหมายเลขคำสั่งซื้อ"
              value="ORDER_NOT_FOUND"
              :disabled="!settings.company.CONFIRM_RECORD_PASSWORD_ENABLE"
              :input-value="settings.company.CONFIRM_RECORD_PASSWORD_CONFIG"
              @change="update({ CONFIRM_RECORD_PASSWORD_CONFIG: $event })"
            />
            <v-checkbox
              hide-details
              dense
              label="FixCase"
              value="FIXCASE"
              :disabled="!settings.company.CONFIRM_RECORD_PASSWORD_ENABLE"
              :input-value="settings.company.CONFIRM_RECORD_PASSWORD_CONFIG"
              @change="update({ CONFIRM_RECORD_PASSWORD_CONFIG: $event })"
            />
            <v-checkbox
              hide-details
              dense
              label="อัดวิดีโอซ้ำ"
              value="DUPLICATE_RECORD"
              :disabled="!settings.company.CONFIRM_RECORD_PASSWORD_ENABLE"
              :input-value="settings.company.CONFIRM_RECORD_PASSWORD_CONFIG"
              @change="update({ CONFIRM_RECORD_PASSWORD_CONFIG: $event })"
            />
            <v-checkbox
              hide-details
              dense
              label="หมายเหตุ"
              persistent-hint
              value="REMARK"
              :disabled="!settings.company.CONFIRM_RECORD_PASSWORD_ENABLE"
              :input-value="settings.company.CONFIRM_RECORD_PASSWORD_CONFIG"
              @change="update({ CONFIRM_RECORD_PASSWORD_CONFIG: $event })"
            />
            <div class="text-help">
              ถ้า <b>เปิด</b> ตัวเลือกนี้ -
              บังคับให้กรอกรหัสผ่านหัวหน้างานทุกหมายเหตุ<br>
              ถ้า <b style="margin-right: 4px">ปิด</b> ตัวเลือกนี้ -
              การใส่เครื่องหมาย ! (อัศเจรีย์) นำหน้าหมายเหตุ
              จะเป็นการบังคับให้กรอกรหัสผ่านหัวหน้างาน
            </div>
          </div>
        </v-col>

        <v-col cols="12">
          <v-checkbox
            hide-details
            :label="$t('settings.add-item-barcode-require-supervisor-password')"
            :input-value="
              settings.company.ADD_ITEM_BARCODE_REQUIRE_SUPERVISOR_PASSWORD
            "
            @change="
              update({ ADD_ITEM_BARCODE_REQUIRE_SUPERVISOR_PASSWORD: $event })
            "
          />
          <div class="text-help">
            <div>
              ก่อนปรับจำนวนการแพ็คโดยการคลิ๊ก
              ระบบจะบังคับให้กรอกรหัสผ่านหัวหน้างาน
            </div>
          </div>
          <div class="text-help">
            <v-img src="https://storage.googleapis.com/dobybot-public-bucket/example/record-order-item.png" style="border: 1px solid black; max-width:400px" />
          </div>
        </v-col>

        <v-col cols="12">
          <v-checkbox
            hide-details
            :label="$t('settings.record-voided-order-require-supervisor-password')"
            :input-value="
              settings.company.RECORD_VOIDED_ORDER_REQUIRE_SUPERVISOR_PASSWORD
            "
            @change="
              update({ RECORD_VOIDED_ORDER_REQUIRE_SUPERVISOR_PASSWORD: $event })
            "
          />
          <div class="text-help">
            <div>
              ก่อนอัดวิดีโอจากออเดอร์ที่มีสถานะเป็น Voided
              ระบบจะบังคับให้กรอกรหัสผ่านหัวหน้างาน
            </div>
          </div>
        </v-col>

        <v-col cols="12">
          <v-checkbox
            :label="$t('users.share-google-drive')"
            hide-details
            :input-value="settings.company.POST_RECORD_ACTION_SHARE_VIDEO_LINK"
            @change="update({ POST_RECORD_ACTION_SHARE_VIDEO_LINK: $event })"
          />
          <div class="text-help">
            <div>
              หลังจากอัดวิดีโอเสร็จ ระบบจะทำการแชร์ลิงค์วิดีโออัตโนมัติ
              ทำให้สามารถเปิดดูได้ทันที
            </div>
            <ul>
              <li>
                หากต้องการส่งลิงค์วิดีโอทาง SMS / Chat ควร
                <b>เปิด</b> การแชร์ลิงค์
              </li>
              <li>
                หากไม่ต้องการให้ผู้อื่นดูวิดีโอโดยไม่ได้รับอนุญาติ ให้
                <b>ปิด</b> การแชร์ลิงค์
              </li>
            </ul>
          </div>
        </v-col>

        <v-col cols="12">
          <v-checkbox
            label="สร้างออเดอร์จากชื่อวิดีโอ"
            hide-details
            :input-value="settings.company.POST_RECORD_ACTION_CREATE_ORDER"
            @change="update({ POST_RECORD_ACTION_CREATE_ORDER: $event })"
          />
          <div class="text-help">
            <div>
              หลังจากอัดวิดีโอเสร็จ ระบบจะทำการสร้างออเดอร์อัตโนมัติจากโดยดึง
              หมายเลขออเดอร์, เบอร์โทร, หมายเลข tracking จากชื่อวิดีโอ
            </div>
            <div>
              ใช้สำหรับอัดวิดีโอและส่ง sms อย่างรวดเร็วโดยไม่ต้อง sync
              ข้อมูลออเดอร์เข้ามาใน Order Center ก่อน
            </div>
          </div>
        </v-col>

        <v-col
          v-if="settings.company.POST_RECORD_ACTION_CREATE_ORDER"
          cols="12"
        >
          <v-text-field
            class="ml-8 mt-2"
            style="font-family: monospace, monospace"
            label="Regular Expression สำหรับดึงข้อมูลจากชื่อวิดีโอ"
            :value="settings.company.POST_RECORD_ACTION_CREATE_ORDER_PATTERN"
            @change="
              update({ POST_RECORD_ACTION_CREATE_ORDER_PATTERN: $event })
            "
          />
          <div class="text-help">
            <b>ตัวอย่าง</b><br>
            <ul>
              <li>
                <code>^=(?&lt;order_customerphone&gt;0[0-9]{9})$</code>
                กรอกเครื่องหมาย = ตามด้วยเบอร์โทร 10 หลัก
              </li>
              <li>
                <code>^(?&lt;order_customerphone&gt;0[0-9]{9})$</code>
                กรอกเบอร์โทร 10 หลัก
              </li>
              <li>
                <code>^(?&lt;order_number&gt;[a-zA-Z0-9-_/.\s]+)\/(?&lt;order_customerphone&gt;0[0-9]{9})$</code>
                กรอก หมายเลขออเดอร์ / เบอร์โทร
              </li>
              <li>
                <code>^(?&lt;order_trackingno&gt;[a-zA-Z0-9-_/.\s]+)\/(?&lt;order_customerphone&gt;0[0-9]{9})$</code>
                กรอก หมายเลข tracking / เบอร์โทร
              </li>
            </ul>
          </div>
        </v-col>

        <v-col cols="12">
          <v-checkbox
            hide-details
            :label="$t('settings.order-status')"
            :input-value="settings.company.POST_RECORD_ACTION_READY_TO_SHIP"
            @change="update({ POST_RECORD_ACTION_READY_TO_SHIP: $event })"
          />
          <div class="text-help">
            <div>
              หลังจากอัดวิดีโอเสร็จ ระบบจะปรับสถานะออเดอร์เป็น Ready to ship
              อัตโนมัติ
            </div>
            <ul>
              <li>แก้ไขเครื่องหมายเป็นติ๊กถูกในหน้า Order Center</li>
              <li>ปรับสถานะออเดอร์ที่มาจาก VRich เป็น Checkout</li>
              <li>
                ทำการส่งลิงค์วิดีโอผ่าน Chat / SMS (ดูเพิ่มเติมที่
                <router-link :to="{ path: '/settings/sms' }">
                  ตั้งค่า Chat / SMS
                </router-link>)
              </li>
            </ul>
          </div>
        </v-col>
      </v-row>

      <!-- Record Serial No. -->
      <v-row dense no-gutter class="mt-4 pt-4 pb-4">
        <v-col cols="12">
          <h2>ตั้งค่าระบบ Serial No.</h2>
        </v-col>

        <v-col cols="6">
          <v-select
            hide-details
            filled
            label="ตั้งค่ารูปแบบการสแกน Serial No"
            :items="serialNoModes"
            item-text="title"
            item-value="code"
            :value="settings.company.RECORD_SCAN_SERIAL_NO_MODE"
            @change="update({ RECORD_SCAN_SERIAL_NO_MODE: $event })"
          />
        </v-col>
      </v-row>

      <!-- Record Tags -->
      <v-row dense no-gutter class="mt-4 pt-4 pb-4">
        <v-col cols="12">
          <h2>ตั้งค่า Tag สำหรับวิดีโอ</h2>
          <div>
            กำหนด Barcode และ Tag
            ที่ต้องการบันทึกลงในหมายเหตุระหว่างการบันทึกวิดิโอ
            <ul>
              <li>ใช้ในการบันทึก "ขนาดของกล่อง" ที่ใช้ในการแพ็คแต่ละออเดอร์</li>
              <li>
                ใช้ในการบันทึก "ความยาก/ง่าย" ของการแพ็ค (สำหรับช่วยคิดค่าแรง)
                ของแต่ละออเดอร์
              </li>
            </ul>
          </div>
        </v-col>
        <v-col cols="6">
          <record-tag-combo-box
            :value="settings.company.RECORD_TAGS"
            @change="update({ RECORD_TAGS: $event })"
          />
        </v-col>
      </v-row>

      <!-- Image Capture Setting -->
      <v-row dense no-gutter class="mt-4 pt-4 pb-4">
        <v-col cols="12">
          <h2>{{ $t("image-capture-superviser-setting") }}</h2>
        </v-col>
        <v-col cols="12">
          <v-checkbox
            class="mt-0"
            hide-details
            :label="$t('image-capture-superviser-setting-lable')"
            :input-value="
              settings.company.CONFIRM_IMAGE_CAPTURE_PASSWORD_ENABLE
            "
            @change="update({ CONFIRM_IMAGE_CAPTURE_PASSWORD_ENABLE: $event })"
          />
          <div class="text-help">
            <div>{{ $t("image-capture-superviser-setting-description") }}</div>
          </div>
        </v-col>

        <!-- TODO: Translation -->
        <v-col cols="12">
          <v-checkbox
            class="mt-0"
            hide-details
            :label="$t('users.share-image-google-drive')"
            :input-value="settings.company.POST_IMAGE_CAPTURE_SHARE_IMAGE_LINK"
            @change="update({ POST_IMAGE_CAPTURE_SHARE_IMAGE_LINK: $event })"
          />
          <div class="text-help">
            <div>{{ $t("image-capture-share-image-setting-description") }}</div>
          </div>
        </v-col>
      </v-row>

      <!-- Weight scale unit -->
      <v-row dense no-gutter class="mt-4 pt-4 pb-4">
        <v-col cols="12">
          <h2>{{ $t("settings.weight-scale") }}</h2>
        </v-col>
        <v-col cols="6">
          <v-select
            hide-details
            filled
            :label="$t('weight-scale-unit')"
            :value="settings.company.WEIGHT_SCALE_PREFER_UNIT"
            :items="['g', 'kg', 'auto']"
            @change="update({ WEIGHT_SCALE_PREFER_UNIT: $event })"
          />
          <div class="text-help pl-1 mt-2">
            <div>หน่วยของน้ำหนักที่แสดงในวิดีโอและสติกเกอร์น้ำหนัก</div>
            <ul>
              <li>g ใช้หน่วยกรัม ทศนิยม 1 ตำแหน่ง</li>
              <li>kg ใช้หน่วยกิโลกรัม ทศนิยม 2 ตำแห่นง</li>
              <li>
                auto เปลี่ยนหน่วยเป็นกิโลกรัมอัตโนมัติเมื่อน้ำหนักเกิน 1000 กรัม
              </li>
            </ul>
          </div>
        </v-col>
      </v-row>
      <!-- Alerting -->
      <v-row dense no-gutters class="mt-4 pt-4 pb=4">
        <v-col cols="12">
          <h2>{{ $t("settings.settings-notification") }}</h2>
        </v-col>
        <v-col cols="12">
          <v-text-field
            :label="$t('settings.vdo')"
            :hint="$t('settings.fill-0')"
            type="number"
            filled
            :rules="[$rules.required]"
            :value="settings.company.LOW_RECORD_CREDIT_WARNING_AMOUNT"
            @change="
              update({ LOW_RECORD_CREDIT_WARNING_AMOUNT: Number($event) })
            "
          />
        </v-col>
        <v-col cols="12">
          <v-text-field
            :label="$t('settings.sms-sms-notification')"
            :hint="$t('settings.fill-0')"
            type="number"
            filled
            :rules="[$rules.required]"
            :value="settings.company.LOW_SMS_CREDIT_WARNING_AMOUNT"
            @change="update({ LOW_SMS_CREDIT_WARNING_AMOUNT: Number($event) })"
          />
        </v-col>
      </v-row>
    </template>
  </setting-template>
</template>

<script lang="ts">
import Vue from 'vue'
import RecordTagComboBox from '~/components/settings/record/RecordTagComboBox.vue'
import SettingTemplate from '~/components/settings/SettingTemplate.vue'
import ConfirmExit from '~/mixins/confirm-exit'

export default Vue.extend({
  components: {
    SettingTemplate,
    RecordTagComboBox
  },

  mixins: [ConfirmExit],
  middleware: ['auth'],

  data () {
    return {
      serialNoModes: [
        { code: 'NONE', title: 'ปิดการใช้งาน' },
        { code: 'SERIAL_NO_ONLY', title: 'สแกนเฉพาะ Serial No เท่านั้น' },
        { code: 'SERIAL_NO_AND_SKU', title: 'สแกน Serial No และ SKU' }
      ]
    }
  },

  methods: {
    hasUnsavedChanges () {
      return (this.$refs.base as never as any).hasUnsavedChanges()
    }

    // async connectOnlineShopingPlatform (platform: 'lazada'|'shopee'|'tiktok_shop') {
    //   const seller_auth_url = (await this.$axios.get('/api/companies/dobybot-connect/seller/auth/', { params: { platform } })).data
    //   window.open(seller_auth_url)
    // }

    // ...mapActions('settings', {
    //   saveSettings: 'saveSettings'
    // }),

    // ...mapMutations('settings', {
    //   update: 'setCompanySetting'
    // })
  }
})
</script>

<style scoped>
.text-help {
  padding-left: 33px;
  font-size: 0.85rem;
  color: #555555;
}
</style>
