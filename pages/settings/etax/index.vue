<template>
  <setting-template ref="base" :title="$t('etax.title')">
    <template #form="{ settings, update }">
      <v-row dense no-gutters class="mt-4 pt-4 pb-4">
        <v-col class="mb-2" cols="12">
          <h2>{{ $t("etax.seller_info.title") }}</h2>
        </v-col>
        <v-col cols="12">
          <v-text-field
            hide-details
            readonly
            class="mt-2"
            filled
            :label="$t('etax.seller_info.company_name')"

            :value="settings.company.ETAX_SELLER.name"
            @change="updateETaxSeller({ name: $event })"
          />
        </v-col>
        <v-col cols="12">
          <v-text-field
            hide-details
            class="mt-2"
            readonly
            filled

            :label="$t('etax.seller_info.tax_id')"
            :value="settings.company.ETAX_SELLER.tax_id"
            @change="updateETaxSeller({ tax_id: $event })"
          />
        </v-col>
        <v-col cols="6">
          <v-text-field
            hide-details
            readonly
            class="mt-2 mr-2"
            filled
            :label="$t('etax.seller_info.branch_name')"
            placeholder="สำนักงานใหญ่"

            :value="settings.company.ETAX_SELLER.seller_branch_name"
            @change="updateETaxSeller({ seller_branch_name: $event })"
          />
        </v-col>
        <v-col cols="6">
          <v-text-field
            hide-details
            class="mt-2"
            readonly
            filled
            :label="$t('etax.seller_info.branch_code')"

            placeholder="00000"
            :value="settings.company.ETAX_SELLER.seller_branch"
            @change="updateETaxSeller({ seller_branch: $event })"
          />
        </v-col>
        <v-col cols="12">
          <v-textarea
            hide-details
            class="mt-2"
            readonly
            filled
            :label="$t('etax.seller_info.address')"

            :value="settings.company.ETAX_SELLER.address"
            @change="updateETaxSeller({ address: $event })"
          />
        </v-col>
        <v-col cols="">
          <v-text-field
            hide-details
            class="mt-2 mr-2"
            readonly
            filled
            :label="$t('etax.seller_info.post_code')"
            :value="settings.company.ETAX_SELLER.post_code"

            @change="updateETaxSeller({ post_code: $event })"
          />
        </v-col>
        <v-col cols="6">
          <v-text-field
            hide-details
            class="mt-2"
            readonly
            filled
            :label="$t('etax.seller_info.phone')"
            :value="settings.company.ETAX_SELLER.phone_number"

            @change="updateETaxSeller({ phone_number: $event })"
          />
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12" class="pb-0">
          <h2>{{ $t("etax.action.title") }}</h2>
        </v-col>
        <v-col cols="12" class="py-0">
          <v-checkbox
            hide-details
            dense
            :label="$t('etax.action.receipt_show_etax_button')"
            :input-value="settings.company.RECEIPT_SHOW_ETAX_BUTTON"
            @change="update({ RECEIPT_SHOW_ETAX_BUTTON: $event })"
          />
        </v-col>
        <v-col cols="12" class="py-0">
          <v-checkbox
            hide-details
            dense
            :label="$t('etax.action.show_qr')"
            :input-value="settings.company.ETAX_ENABLE_PICKSLIP_QR_CODE"
            @change="update({ ETAX_ENABLE_PICKSLIP_QR_CODE: $event })"
          />
          <p class="text-help">
            แสดง QR Code บนใบจัดสินค้า
            เพื่อให้ลูกค้าสแกนและเข้าถึงใบกำกับภาษีอิเล็กทรอนิกส์ได้สะดวก
          </p>
        </v-col>
        <v-col cols="12" class="py-0">
          <v-checkbox
            hide-details
            dense
            :label="$t('etax.action.allow_received')"
            :input-value="settings.company.ETAX_ALLOW_ON_ORDER_RECEIVED"
            @change="update({ ETAX_ALLOW_ON_ORDER_RECEIVED: $event })"
          />
          <p class="text-help">
            อนุญาตให้ออกใบกำกับภาษีอิเล็กทรอนิกส์ (e-Tax)
            ได้เมื่อสถานะคำสั่งซื้อเปลี่ยนเป็น "ได้รับสินค้าแล้ว" (เฉพาะ Lazada และ Shopee)
          </p>
        </v-col>
        <v-col cols="12" class="py-0">
          <v-checkbox
            hide-details
            dense
            :label="$t('etax.auto-e-tax-platform-label')"
            :input-value="
              settings.company.ETAX_AUTO_CREATE_WHEN_PLATFORM_REQUEST
            "
            @change="
              update({ ETAX_AUTO_CREATE_WHEN_PLATFORM_REQUEST: $event })
            "
          />
          <p class="text-help">
            {{ $t('etax.auto-e-tax-description') }}
          </p>
          <div v-if="settings.company.ETAX_AUTO_CREATE_WHEN_PLATFORM_REQUEST" class="text-help">
            <div style="width: 420px;" class="mb-2">
              <date-picker
                label="วันที่เริ่มต้น"
                filled
                hide-details
                persistent-hint
                :value="settings.company.ETAX_AUTO_START_DATE"
                @input="update({ ETAX_AUTO_START_DATE: $event || '' })"
              />
              <div class="pt-1">
                ระบบจะออกใบกำกับอัตโนมัติเฉพาะออเดอร์ที่ถูกสร้างหลังจากวันที่เริ่มต้นนี้ หากไม่กำหนดจะใช้เป็นวันที่ 1 ของเดือนปัจจุบัน
              </div>
            </div>
          </div>
        </v-col>
        <v-col cols="12" class="py-0">
          <v-checkbox
            hide-details
            dense
            :label="$t('etax.action.auto_cancel')"
            :input-value="
              settings.company.ETAX_CANCEL_ON_ORDER_CANCELLED_OR_RETURNED
            "
            @change="
              update({ ETAX_CANCEL_ON_ORDER_CANCELLED_OR_RETURNED: $event })
            "
          />
          <p class="text-help">
            ยกเลิกใบกำกับภาษีอิเล็กทรอนิกส์ (e-Tax)
            โดยอัตโนมัติเมื่อคำสั่งซื้อถูกยกเลิกหรือมีการคืนสินค้า
          </p>
          <div
            v-if="settings.company.ETAX_CANCEL_ON_ORDER_CANCELLED_OR_RETURNED"
            cols="12"
            class="text-help"
          >
            <div class="d-flex">
              <div style="width: 420px;">
                <v-select
                  filled
                  :value="
                    settings.company.ETAX_AUTO_CANCEL_MODE
                  "
                  :items="[
                    { text: 'ยกเลิกใบกำกับภาษีอิเล็กทรอนิกส์ (e-Tax)', value: 'cancel' },
                    { text: 'สร้างใบลดหนี้อัตโนมัติ', value: 'credit_note' }
                  ]"
                  label="* ก่อนที่จะส่งเอกสารเข้ากรมสรรพกร"
                  persistent-hint
                  @change="
                    update({ ETAX_AUTO_CANCEL_MODE: $event })
                  "
                />
              </div>

              <div style="width: 420px;" class="ps-2">
                <v-select
                  filled
                  :value="
                    settings.company.ETAX_AUTO_CANCEL_MODE_AFTER_CUTOFF
                  "
                  :items="[
                    { text: 'ไม่ดำเนินการใด ๆ', value: 'do_nothing' },
                    { text: 'สร้างใบลดหนี้อัตโนมัติ', value: 'credit_note' }
                  ]"
                  label="* หลังจากที่จะส่งเอกสารเข้ากรมสรรพกรไปแล้ว"
                  @change="
                    update({ ETAX_AUTO_CANCEL_MODE_AFTER_CUTOFF: $event })
                  "
                />
              </div>
            </div>
          </div>
        </v-col>

        <v-col cols="12" class="pb-0">
          <v-checkbox
            hide-details
            dense
            :label="$t('etax.action.zort_auto_tag')"
            :input-value="
              settings.company.ETAX_AUTO_ZORT_TAG
            "
            @change="
              update({ ETAX_AUTO_ZORT_TAG: $event })
            "
          />
          <p class="text-help">
            สำหรับร้านที่เชื่อมต่อ Zort ระบบจะเพิ่ม tag ใน order Zort อัตโนมัติ เมื่อสร้าง ETax หรือยกเลิก
          </p>
        </v-col>
        <v-col cols="12" class="pb-0">
          <h2>ตั้งค่าเมื่อการเงินของออเดอร์มีการเปลี่ยนแปลง</h2>
          <p class="help-text">
            กำหนดการกระทำต่อเอกสารหากการเงินของออเดอร์มีการเปลี่ยนแปลง
          </p>

          <v-radio-group
            class="mt-0"
            mandatory
            :value="settings.company.ETAX_AUTO_ACTION_WHEN_ORDER_CHANGE"
            @change="update({ ETAX_AUTO_ACTION_WHEN_ORDER_CHANGE: $event })"
          >
            <v-radio label="ไม่ดำเนินการใด ๆ" value="do_nothing" />
            <v-radio
              label="ยกเลิกใบกำกับภาษีอิเล็กทรอนิกส์ (e-Tax)"
              value="cancel"
            />
            <v-radio label="ยกเลิกใบภาษีอิเล็กทรอนิกส์เก่าและสร้างใบใหม่อัตโนมัต" value="cancel_and_renew" />
          </v-radio-group>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12">
          <h2>ตั้งค่าวันที่อ้างอิงในการสร้างเอกสาร e-Tax</h2>
          <div class="help-text">
            กำหนดวันที่ออกใบกำกับภาษี (Issue Date) อิเล็กทรอนิกส์
          </div>
        </v-col>
        <v-col cols="12" class="pt-0">
          <v-radio-group
            class="mt-0"
            mandatory
            :value="settings.company.ETAX_DOC_CREATE_DATE"
            @change="update({ ETAX_DOC_CREATE_DATE: $event })"
          >
            <v-radio label="วันที่ลูกค้าสั่งซื้อ" value="order_date" />
            <v-radio
              label="วันที่สินค้าพร้อมจัดส่ง"
              value="ready_to_ship_date"
            />
            <v-radio label="วันที่ออกเอกสาร" value="create_doc_date" />
          </v-radio-group>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12">
          <h2>{{ $t('etax.setup-qr-code') }}</h2>
        </v-col>
        <v-col cols="6">
          <v-row>
            <v-col cols="12" class="pt-0">
              <v-text-field
                hide-details
                :label="$t('etax.header-message')"
                class="mt-2"
                filled
                :value="settings.company.ETAX_HEADER_MESSAGE_BILL"
                @change="update({ ETAX_HEADER_MESSAGE_BILL: $event })"
              />
            </v-col>
            <v-col cols="12" class="pt-0">
              <v-text-field
                hide-details
                :label="$t('etax.footer-message')"
                class="mt-2"
                filled
                :value="settings.company.ETAX_FOOTER_MESSAGE_BILL"
                @change="update({ ETAX_FOOTER_MESSAGE_BILL: $event })"
              />
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="6">
          <div style="max-width: 450px;">
            <div class="text-h5 font-weight-bold d-flex justify-center mb-1">
              {{ $t('example') }}
            </div>
            <div style="border: 2px solid #e0e0e0; padding: 10px;">
              <e-tax-qr-slip
                :header="settings.company.ETAX_HEADER_MESSAGE_BILL"
                :footer="settings.company.ETAX_FOOTER_MESSAGE_BILL"
              />
            </div>
          </div>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12">
          <h2>{{ $t("etax.sms_message_header") }}</h2>
        </v-col>
        <v-col cols="12" md="6">
          <v-textarea

            class="mt-2"
            filled
            :counter="75"
            :label="$t('etax.sms_message_input')"
            :rules="[$rules.includesValue('{sms_etax_link}'),$rules.maxlength(75)]"
            :value="settings.company.ETAX_SMS_MESSAGE"
            @change="update({ ETAX_SMS_MESSAGE: $event })"
          />
        </v-col>
        <v-col cols="12" md="6" class="px-1 pt-2">
          <div>
            {{ $t("settings.variable-explanation") }}
            <ul>
              <li>{{ $t('etax.sms_link') }}</li>
            </ul>
          </div>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12">
          <h2>ตั้งค่าหน้าขอใบกำกับภาษี e-Tax</h2>
        </v-col>
        <v-col cols="12" md="6">
          <v-textarea
            class="mt-2"
            filled
            label="ข้อความหากลูกค้่าต้องการแก้ไขใบกำกับภาษี"
            :value="settings.company.ETAX_CUSTOMER_EDIT_MESSAGE"
            @change="update({ ETAX_CUSTOMER_EDIT_MESSAGE: $event })"
          />
          <v-textarea
            class="mt-2"
            filled
            label="ข้อความต้อนรับ"
            :value="settings.company.ETAX_WELCOME_MESSAGE"
            @change="update({ ETAX_WELCOME_MESSAGE: $event })"
          />
          <v-textarea
            class="mt-2"
            filled
            label="ข้อความแจ้งเตือนเมื่อลูกค้าขอใบกำกับภาษีเลยวันที่กำหนด"
            :value="settings.company.ETAX_ALERT_OVERDUE_MESSAGE"
            @change="update({ ETAX_ALERT_OVERDUE_MESSAGE: $event })"
          />
        </v-col>
        <v-col cols="12" md="6" class="px-1 pt-2">
          <div style="max-width: 600px;" class="text-center">
            <div class="text-h5 font-weight-bold mb-1 text-center">
              {{ $t('example') }}
            </div>
            <v-img src="/help/example-etax-welcome-message2.png" contain />
          </div>
          <!--  -->
        </v-col>
      </v-row>
      <!-- <v-row>
        <v-col cols="12" class="py-2">
          <h2>ตั้งค่าการส่งลิงค์ e-Tax ไปในแชทของ Lazada และ Shopee</h2>
          <div class="help-text">
            ตั้งค่าการส่งลิงค์ e-Tax ไปในแชทตามสถานะของออเดอร์
          </div>
        </v-col>
        <v-col cols="12" class="py-0">
          <v-checkbox
            hide-details
            dense
            label="ส่งลิงค์ e-Tax ผ่านช่องแชทแพลตฟอร์ม Lazada และ Shopee อัตโนมัติ"
            :input-value="settings.company.ETAX_ALLOW_SEND_TO_PLATFORM_CHAT"
            @change="update({ ETAX_ALLOW_SEND_TO_PLATFORM_CHAT: $event })"
          />
          <p class="text-help">
            ให้ระบบแจ้งลูกค้า ไปทางแซทแพลทฟอร์ม เพื่อให้ลูกค้าสามารถกดลิ้งค์ขอ e-Tax ได้
          </p>
        </v-col>

        <v-col cols="12 py-0 ">
          <v-radio-group

            class="mt-0"
            mandatory
            :value="settings.company.ETAX_SEND_LINK_TRIGGER"
            @change="update({ ETAX_SEND_LINK_TRIGGER: $event })"
          >
            <div>
              <v-radio
                class="mt-0"
                label="เมื่อคำสั่งซื้อเปลี่ยนเป็นสถานะ 'พร้อมส่ง'"
                value="ready_to_ship"
              />
            </div>

            <v-radio
              class="mt-2"
              label="เมื่อคำสั่งซื้อเปลี่ยนเป็นสถานะ 'รับสินค้าแล้ว' "
              value="order_received"
            />
          </v-radio-group>
        </v-col>
      </v-row> -->
    </template>
  </setting-template>
</template>

<script lang="ts">
import Vue from 'vue'
import ETaxQrSlip from '~/components/etax-invoice/ETaxQrSlip.vue'
import DatePicker from '~/components/global/DatePicker.vue'
import SettingTemplate from '~/components/settings/SettingTemplate.vue'
import ConfirmExit from '~/mixins/confirm-exit'
import { SettingState } from '~/store/settings'

export default Vue.extend({
  components: {
    SettingTemplate,
    ETaxQrSlip,
    DatePicker
  },

  mixins: [ConfirmExit],
  middleware: ['auth'],

  data () {
    return {
      form_data: {
        name: '',
        address: '',
        post_code: '',
        phone_number: '',
        tax_id: '',
        seller_branch: '',
        seller_branch_name: '',

        // DoaInfo properties
        api_host: '',
        api_token: '',
        branch_id: '',
        type_tax: '',
        tenant_code: '',
        tenant_id: ''
      },
      example_chat_messages: [
        {
          title: 'ข้อความรูปแบบที่ 1',
          text: 'ออเดอร์ {order_number} ของคุณได้ทำการแพคเสร็จแล้ว สามารถคลิกลิงค์นี้เพื่อออกใบกำกับภาษีของออเดอร์นี้ได้เลยค่ะ{etax_link}'
        },
        {
          title: 'ข้อความรูปแบบที่ 2',
          text: 'ออเดอร์ {order_number} ของคุณได้ทำการจัดส่งสำเร็จแล้ว สามารถคลิกลิงค์นี้เพื่อออกใบกำกับภาษีของออเดอร์นี้ได้เลยค่ะ{etax_link}'
        }
      ]
    }
  },
  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },
  methods: {
    hasUnsavedChanges () {
      return (this.$refs.base as never as any).hasUnsavedChanges()
    },

    updateFormData (event: FormData) {
      this.form_data = { ...this.form_data, ...event }
      this.updateETaxSeller(event)
    },
    updateETaxSeller (event: object) {
      const etax_seller = { ...this.settings.company.ETAX_SELLER, ...event }
      const base = this.$refs.base as never as any
      base.update({ ETAX_SELLER: etax_seller })
    },
    hasTrue (obj: object) {
      return Object.values(obj).some(x => x)
    }
  }
})
</script>

<style scoped>
.text-help {
  padding-left: 33px;
  font-size: 0.85rem;
  color: #555555;
}
</style>
