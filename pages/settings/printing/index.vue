<template>
  <setting-template ref="base" :title="$t('settings.setting-printing')">
    <template #overview>
      Dobybot Cloud Print เป็นระบบสั่งพิมพ์ใบจัดสินค้าอัตโนมัติเมื่อมีคำสั่งซื้อเข้ามาในระบบ<br>
      กรุณาติดต่อ <a href="https://lin.ee/HZOUXJg">line@dobybot</a> เพื่อเปิดใช้งานระบบนี้
    </template>

    <template #form="{ settings, update }">
      <div v-package="$package.FULL_INTEGRATION">
        <!-- PrintNode API KEY -->
        <v-row v-if="$auth.user.is_staff" dense class="mt-4 pt-4 px-1">
          <v-col sm="12">
            <h2>{{ $t('settings.setting-API') }}</h2>
            <div>
              เชื่อมต่อเครื่องพิมพ์ และ พิมพ์เอกสารต่าง ๆ ติดต่อ Dobybot Support เพื่อขอ API Key
            </div>
          </v-col>

          <v-col sm="12">
            <v-text-field
              label="API Key"
              filled
              hide-details
              :value="settings.company.PRINTNODE_API_KEY"
              @change="update({ PRINTNODE_API_KEY: $event })"
            />
          </v-col>
        </v-row>

        <!-- Pick Order Printing -->
        <v-row dense class="mt-4 pt-4 px-1">
          <v-col sm="12">
            <h2>{{ $t('settings.setting-packing-slip') }}</h2>
            <div>
              ระบบจะส่งคำสั่งพิมพ์ใบจัดของอัตโนมัติเมื่อมีคำสั่งซื้อใหม่เข้ามา
              <!-- <b>ต้องตั้งค่า PrintNode API Key ก่อนจึงจะใช้งานได้</b> -->
            </div>
          </v-col>
          <v-col cols="12">
            <v-checkbox
              class="mt-0"
              :label="$t('settings.order')"
              hide-details
              :disabled="!settings.company.PRINTNODE_API_KEY"
              :input-value="settings.company.PICKORDER_AUTO_PRINT_ENABLE"
              @change="update({ PICKORDER_AUTO_PRINT_ENABLE: $event })"
            />
          </v-col>
          <v-col sm="6">
            <printnode-printer-select
              filled
              hide-details
              :disabled="
                !settings.company.PRINTNODE_API_KEY
                  || !settings.company.PICKORDER_AUTO_PRINT_ENABLE
              "
              :value="settings.company.PRINTNODE_PICKORDER_PRINTER_ID"
              @input="update({PRINTNODE_PICKORDER_PRINTER_ID: $event })"
            />
          </v-col>
          <v-col sm="6">
            <v-select
              :label="$t('settings.paper')"
              filled
              hide-details
              :items="['a5', '80mm']"
              :disabled="
                !settings.company.PRINTNODE_API_KEY
                  || !settings.company.PICKORDER_AUTO_PRINT_ENABLE
              "
              :value="settings.company.PRINTNODE_PICKORDER_PRINTER_TYPE"
              @change="update({ PRINTNODE_PICKORDER_PRINTER_TYPE: $event })"
            />
          </v-col>
          <v-col sm="6">
            <date-picker
              filled
              label="วันที่เริ่มพิมพ์"
              hint="ระบบจะพิมพ์ใบจัดของเฉพาะคำสั่งซื้อที่สร้างหลังวันที่นี้"
              persistent-hint
              clearable
              :value="settings.company.PICKORDER_PRINT_START_DATE"
              :disabled="
                !settings.company.PRINTNODE_API_KEY
                  || !settings.company.PICKORDER_AUTO_PRINT_ENABLE
              "
              @input="update({ PICKORDER_PRINT_START_DATE: $event || '' })"
            />
          </v-col>
        </v-row>
        <v-row dense class="mt-5 px-1">
          <v-col cols="12" class="px-1">
            <div>
              <div class="font-weight-bold">
                ปิดการ พิมพ์ใบจัดของ เฉพาะร้านค้า
              </div>
              ระบบจะไม่พิมพ์ใบจัดของจากร้านค้าที่เลือก
            </div>
          </v-col>
          <v-col cols="12">
            <v-autocomplete
              label="เลือกร้านค้าที่ปิดการพิมพ์ใบจัดของ"
              class="mt-2"
              filled
              multiple
              chips
              :disabled="
                !settings.company.PRINTNODE_API_KEY
                  || !settings.company.PICKORDER_AUTO_PRINT_ENABLE
              "
              :items="shops"
              :value="settings.company.PICKORDER_STORE_BLOCKED_LIST"
              @change="update({ PICKORDER_STORE_BLOCKED_LIST: $event })"
            />
          </v-col>
        </v-row>

        <!-- Pick Order Footer HTML -->
        <v-row dense class="mt-4 pt-4 px-1">
          <v-col cols="12">
            <h2>ตั้งค่าท้ายใบจัดของ (Pick Order Footer)</h2>
            <div>กำหนด HTML สำหรับแสดงที่ท้ายใบจัดของ เช่น ข้อความขอบคุณ ข้อมูลติดต่อ หรือคำแนะนำพิเศษ</div>
          </v-col>

          <v-col cols="12" class="mt-3">
            <h4>ตั้งค่าท้ายใบจัดของ (HTML)</h4>
            <prism-editor
              :value="settings.company.PICK_ORDER_FOOTER_HTML"
              lang="html"
              placeholder="<p>ใส่ HTML สำหรับท้ายใบจัดของที่นี่...</p>"
              @input="update({ PICK_ORDER_FOOTER_HTML: $event })"
            />
          </v-col>
        </v-row>

        <!-- Receipt / Tax Invoice printing -->
        <v-row dense class="mt-4 pt-4 px-1">
          <v-col cols="12">
            <h2>ตั้งค่าการพิมพ์ใบเสร็จ / ใบกำกับภาษีแบบย่อ</h2>
            <div>เมื่อเริ่มอัดวิดีโอ ระบบจะทำการพิมพ์ใบเสร็จ / ใบกำกับภาษีแบบย่อ อัตโนมัติ</div>
          </v-col>

          <v-col cols="12">
            <v-radio-group
              class="mt-0"
              mandatory
              :value="settings.company.PRINT_RECEIPT_TAX_INVOICE_2_COPY"
              @change="update({ PRINT_RECEIPT_TAX_INVOICE_2_COPY: $event })"
            >
              <v-radio
                label="พิมพ์ต้นฉบับเท่านั้น"
                :value="false"
              />
              <v-radio
                label="พิมพ์ต้นฉบับและสำเนา"
                :value="true"
              />
            </v-radio-group>
          </v-col>
        </v-row>

        <!-- Airway Bill Printing -->
        <v-row dense class="mt-4 pt-4 px-1">
          <v-col cols="12">
            <h2>{{ $t('settings.setting-print-airway-bill') }}</h2>
            <div>เมื่อเริ่มอัดวิดีโอ ระบบจะทำการพิมพ์ใบ Airway Bill อัตโนมัติ เพื่อจะได้ปิดกล่องได้อย่างรวดเร็วและไร้ข้อผิดพลาด</div>
          </v-col>

          <v-col cols="12" class="mt-5">
            <h4>Platform Airway Bill</h4>
            <div>ดาวน์โหลด Airway Bill จาก Lazada/Shopee/NocNoc/TikTok อัตโนมัติ</div>
            <div v-if="shopee_shops.length + lazada_shops.length + nocnoc_shops.length + tiktok_shops.length === 0">
              <router-link :to="{path: '/settings/marketplace'}">
                กรุณาตั้งค่าการเชื่อมต่อ Marketplace ก่อน
              </router-link>
            </div>
            <div v-if="shopee_shops.length > 0" style="max-width: 500px;">
              <marketplace-airway-bill-setting-table
                platform="Shopee"
                class="mt-3"
                :items="shopee_shops"
                :value="settings.company.SHOPEE_API"
                @input="update({SHOPEE_API: $event})"
              />
            </div>
            <div v-if="lazada_shops.length > 0" style="max-width: 500px;">
              <marketplace-airway-bill-setting-table
                platform="Lazada"
                class="mt-3"
                :items="lazada_shops"
                :value="settings.company.LAZADA_API"
                @input="update({LAZADA_API: $event})"
              />
            </div>
            <div v-if="tiktok_shops.length > 0" style="max-width: 500px;">
              <marketplace-airway-bill-setting-table
                platform="TikTok"
                class="mt-3"
                :items="tiktok_shops"
                :value="settings.company.TIKTOK_SHOP_API"
                @input="update({TIKTOK_SHOP_API: $event})"
              />
            </div>
            <div v-if="tiktok_v2_shops.length > 0" style="max-width: 500px;">
              <marketplace-airway-bill-setting-table
                platform="TikTok Shop V2"
                class="mt-3"
                :items="tiktok_v2_shops"
                :value="settings.company.TIKTOK_SHOP_V2_API"
                @input="update({TIKTOK_SHOP_V2_API: $event})"
              />
            </div>
            <div v-if="nocnoc_shops.length > 0" style="max-width: 500px;">
              <marketplace-airway-bill-setting-table
                platform="NocNoc"
                class="mt-3"
                :items="nocnoc_shops"
                :value="settings.company.NOCNOC_API"
                @input="update({NOCNOC_API: $event})"
              />
            </div>
            <div v-if="linemyshop_shops.length > 0" style="max-width: 500px;">
              <marketplace-airway-bill-setting-table
                platform="LineMyShop"
                class="mt-3"
                :items="linemyshop_shops"
                :value="settings.company.LINE_MY_SHOP_API"
                @input="update({LINE_MY_SHOP_API: $event})"
              />
            </div>
          </v-col>

          <v-col class="mt-5">
            <h4>Custom Airway Bill</h4>
            <div>Airway Bill แบบกำหนดเอง</div>
            <div>สร้าง Airway Bill อัตโนมัติสำหรับคำสั่งซื้อที่ไม่มี Airway Bill</div>
            <v-select
              class="mt-2"
              label="รายชื่อร้านค้าที่ใช้ Airway Bill แบบกำหนดเอง"
              multiple
              filled
              hide-details
              :items="shops"
              :value="settings.company.DOBYBOT_AIRWAYBILL_SHOP_LIST"
              @change="update({ DOBYBOT_AIRWAYBILL_SHOP_LIST: $event })"
            />
            <v-text-field
              filled
              label="ชื่อผู้ส่ง"
              class="mt-2"
              hide-details=""
              :value="settings.company.DOBYBOT_AIRWAYBILL_SENDER_NAME"
              @change="update({ DOBYBOT_AIRWAYBILL_SENDER_NAME: $event })"
            />
            <v-text-field
              filled
              label="เบอร์โทรผู้ส่ง"
              class="mt-2"
              hide-details=""
              :value="settings.company.DOBYBOT_AIRWAYBILL_SENDER_PHONE"
              @change="update({ DOBYBOT_AIRWAYBILL_SENDER_PHONE: $event })"
            />
            <v-textarea
              filled
              label="ที่อยู่ผู้ส่ง"
              class="mt-2 mb-4"
              hide-details=""
              :value="settings.company.DOBYBOT_AIRWAYBILL_SENDER_ADDRESS"
              @change="update({ DOBYBOT_AIRWAYBILL_SENDER_ADDRESS: $event })"
            />

            <h4>ตั้งค่าท้ายใบ Airway Bill (HTML)</h4>
            <div>
              สามารถใช้ตัวแปรต่อไปนี้ใน HTML ได้
              <ul>
                <li>{{ $t('settings.html-customer-name') }}</li>
                <li>{{ $t('settings.html-order-number') }}</li>
                <li>{{ $t('settings.html-tracking_no-tracking') }}</li>
              </ul>
            </div>
            <prism-editor
              :value="settings.company.DOBYBOT_AIRWAYBILL_FOOTER"
              @input="update({ DOBYBOT_AIRWAYBILL_FOOTER: $event })"
            />
          </v-col>
          <v-col style="max-width: 400px; padding-top: 80px;">
            <div class="text-center">
              ตัวอย่าง
            </div>
            <v-img src="https://storage.googleapis.com/dobybot-public-bucket/example/custom-airway-bill-example.png" style="border: 1px solid black;" />
          </v-col>
        </v-row>
      </div>
    </template>
  </setting-template>
</template>

<script lang="ts">
import Vue from 'vue'
import SettingTemplate from '~/components/settings/SettingTemplate.vue'
import PrintnodePrinterSelect from '~/components/printers/PrintnodePrinterSelect.vue'
import ConfirmExit from '~/mixins/confirm-exit'
import MarketplaceAirwayBillSettingTable from '~/components/settings/printing/MarketplaceAirwayBillSettingTable.vue'
import { FEATURE_FLAGS, Marketplace } from '~/models'
import PrismEditor from '~/components/editor/PrismEditor.vue'
import DatePicker from '~/components/global/DatePicker.vue'
export default Vue.extend({
  components: {
    SettingTemplate,
    PrintnodePrinterSelect,
    MarketplaceAirwayBillSettingTable,
    PrismEditor,
    DatePicker
  },

  mixins: [
    ConfirmExit
  ],

  middleware: ['auth'],

  data () {
    return {
      shops: [] as string[],
      marketplaces: [] as Marketplace[]
    }
  },

  computed: {
    shopee_shops (): Marketplace[] {
      return this.marketplaces.filter(x => x.marketplace === 'shopee')
    },

    lazada_shops (): Marketplace[] {
      return this.marketplaces.filter(x => x.marketplace === 'lazada')
    },

    tiktok_shops (): Marketplace[] {
      return this.marketplaces.filter(x => x.marketplace === 'tiktok_shop')
    },

    tiktok_v2_shops (): Marketplace[] {
      return this.marketplaces.filter(x => x.marketplace === 'tiktok_shop_v2')
    },

    nocnoc_shops (): Marketplace[] {
      return this.marketplaces.filter(x => x.marketplace === 'nocnoc')
    },

    linemyshop_shops (): Marketplace[] {
      return this.marketplaces.filter(x => x.marketplace === 'line_my_shop')
    }
  },

  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.PRINTING)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }
  },

  mounted () {
    this.getShopList()
    this.getMarketplaces()
  },

  methods: {
    async getShopList () {
      this.shops = (await this.$axios.get('/api/companies/choices/saleschannel_choices/')).data
    },

    hasUnsavedChanges () {
      return (this.$refs.base as never as any).hasUnsavedChanges()
    },

    async getMarketplaces () {
      const marketplaces: Marketplace[] = (await this.$axios.get('/api/companies/marketplaces/')).data
      this.marketplaces = marketplaces
    }
  }
})
</script>
