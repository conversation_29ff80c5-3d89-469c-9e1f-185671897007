<!-- eslint-disable vue/html-end-tags -->

<template>
  <setting-template ref="base" :title="$t('settings.shipping-setting')">
    <template #overview>
      <!--  -->
    </template>

    <template #form="{ settings, update }">
      <v-row>
        <v-col cols="12">
          <h2>{{ $t('setting-shipping-title') }} </h2>
          <v-card
            max-width="500"
            outlined
            :loading="loading"
          >
            <v-card-title>
              <v-avatar>
                <img
                  src="/Flash.png"
                  alt="John"
                >
              </v-avatar>
              <span class="ms-2">Flash Express</span>

              <v-spacer />

              <v-btn
                class="mx-1"
                color="primary"
                :loading="loading"
                @click.prevent="linkFlashAccount()"
              >
                {{ $t('setiing-shipping-flash-connect-button') }}
              </v-btn>
              <v-btn
                color="success"
                :loading="loading"
                @click.stop="testLinkFlashAccount()"
              >
                {{ $t('setting-shipping-flash-test-button') }}
              </v-btn>
            </v-card-title>

            <v-card-subtitle>
              <div class="my-3">
                <div v-show="loading" class="body-1">
                  {{ $t('setting-shipping-flash-connecting') }}
                </div>
                <div v-show="login_status == 'ALREADY_LOGIN'" class="green--text" color="success">
                  {{ $t('setting-shipping-flash-connected') }}
                  <v-icon color="green">
                    mdi-check-circle
                  </v-icon>
                </div>
                <div v-show="login_status == 'NOT_LOGGED_YET'" class="red--text my-auto" color="red">
                  {{ $t('setting-shipping-flash-not_connected') }}
                  <v-icon color="red">
                    mdi-close-circle
                  </v-icon>
                </div>
              </div>
            </v-card-subtitle>
          </v-card>

          <v-card text="test data" variant="outlined" />
        </v-col>
      </v-row>

      <v-row class="mt-8 mb-4">
        <v-col cols="12">
          <h2> {{ $t('shipping-setting-flash-auto-tracking') }} </h2>
          <v-checkbox
            class="mt-0"
            hide-details
            :label="$t('setting-shipping-flash-auto-tracking-label')"
            :input-value="settings.company.AUTO_GENERATE_TRACKINGNO_ENABLE"
            @change="update({ AUTO_GENERATE_TRACKINGNO_ENABLE: $event })"
          />
          <div class="text-help">
            <div> {{ $t('setting-shipping-flash-auto-tracking-help') }}</div>
          </div>
        </v-col>

        <v-col cols="6" class="pl-11">
          <v-autocomplete
            :label="$t('setting-shipping-flash-select-shipping-channel')"
            multiple
            filled
            chips
            hide-details
            :disabled="!settings.company.AUTO_GENERATE_TRACKINGNO_ENABLE"
            :items="shops"
            :value="settings.company.AUTO_GENERATE_TRACKINGNO_SHOPS"
            @change="update({ AUTO_GENERATE_TRACKINGNO_SHOPS: $event })"
          />
          <div class="text-help pl-1 mt-2">
            <div>{{ $t('setting-shipping-flash-select-shipping-channel-help') }}</div>
          </div>
        </v-col>
        <v-col cols="6" />

        <v-col cols="6" class="pl-11">
          <v-select
            hide-details
            filled
            :label="$t('setting-shipping-flash-select-shipping_service')"
            :value="settings.company.AUTO_GENERATE_TRACKINGNO_PROVIDER"
            :disabled="!settings.company.AUTO_GENERATE_TRACKINGNO_ENABLE"
            :items="courier"
            item-text="full_name"
            item-value="code"
            @change="update({ AUTO_GENERATE_TRACKINGNO_PROVIDER: $event })"
          />
          <div class="text-help pl-1 mt-2">
            <div>{{ $t('setting-shipping-flash-select-shipping-help') }}</div>
          </div>
        </v-col>

        <v-col cols="6" class="text-help pl-2">
          <div>
            หากต้องการใช้ผู้ให้บริการขนส่งมากกว่า 1 รายการ<br>
            ท่านสามารถกำหนดผู้ให้บริการขนส่งในไฟล์ Excel Import (001-full) ได้ในคอลัมน์ <code>shippingchannel</code>
          </div>
          <ul>
            <li>DBB_FLASH</li>
            <li>DBB_KERRY</li>
            <li>DBB_THAIPOST_EMS</li>
            <li>DBB_THAIPOST_REG</li>
          </ul>
        </v-col>
      </v-row>

      <v-row>
        <v-col cols="12">
          <h2>{{ $t('setting-shipping-flash-shipping-address') }}</h2>
        </v-col>

        <v-col cols="6" class="mt-3">
          <v-text-field
            filled
            :label="$t('setting-shipping-sender_name')"
            class="mt-2"
            hide-details
            :value="settings.company.SHIPPING_SENDER.name"
            @change="updateSenderAddress({ name: $event })"
          />

          <v-text-field
            filled
            :label="$t('setting-shipping-sender-phone')"
            class="mt-2"
            hide-details
            :value="settings.company.SHIPPING_SENDER.phone"
            @change="updateSenderAddress({ phone: $event })"
          />
          <v-textarea
            filled
            rows="3"
            :label="$t('setting-shipping-sender-address')"
            class="mt-2 mb-4"
            hide-details
            :value="settings.company.SHIPPING_SENDER.address"
            @change="updateSenderAddress({ address: $event })"
          />
          <form-address
            :province="form_data.province"
            :district="form_data.district"
            :subdistrict="form_data.subdistrict"
            :postcode="form_data.postcode"
            @update:address_detail="updateFormData($event)"
          />
        </v-col>
        <v-col style="max-width: 440px;">
          <div class="text-center">
            {{ $t('example') }}
          </div>
          <v-img src="https://storage.googleapis.com/dobybot-public-bucket/example/flash-airwaylbill-example.png" style="border: 1px solid black;" />
        </v-col>
      </v-row>
    </template>
  </setting-template>
</template>

<script lang="ts">
import Vue from 'vue'
import _ from 'lodash'
import { FEATURE_FLAGS } from '~/models'
import { SettingState } from '~/store/settings'
import ConfirmExit from '~/mixins/confirm-exit'
import SettingTemplate from '~/components/settings/SettingTemplate.vue'
import FormAddress from '~/components/settings/shipping/FormAddress.vue'

interface FormData {
  province: string | null;
  province_en: string | null;
  district: string | null;
  district_en: string | null;
  subdistrict: string | null;
  subdistrict_en: string | null;
  postcode: string | null;
}

export default Vue.extend({
  components: {
    SettingTemplate,
    FormAddress
  },

  mixins: [ConfirmExit],
  middleware: ['auth'],

  data () {
    return {
      shops: [] as string[],
      form_data: {
        province: '',
        district: '',
        subdistrict: '',
        province_en: '',
        district_en: '',
        subdistrict_en: '',
        postcode: ''
      } as FormData | null,
      courier: [
        { full_name: 'Flash Express', code: 'DBB_FLASH' },
        { full_name: 'Kerry Express', code: 'DBB_KERRY' },
        { full_name: 'ไปรษณีย์ไทย (EMS)', code: 'DBB_THAIPOST_EMS' },
        { full_name: 'ไปรษณีย์ไทย (ลงทะเบียน)', code: 'DBB_THAIPOST_REG' }
      ] as object[],
      items: [] as object[],
      search: '' as string,
      debouncedGetAddress: null as Function |null,
      shipping_sender: {} as Object,
      loading: false,
      login_status: '' as '' | 'ALREADY_LOGIN' | 'NOT_LOGGED_YET'
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },

  mounted () {
    this.getShopList()

    setTimeout(() => {
      if (this.settings.company.SHIPPING_SENDER) {
        this.form_data = {
          province: this.settings.company.SHIPPING_SENDER.province,
          district: this.settings.company.SHIPPING_SENDER.district,
          subdistrict: this.settings.company.SHIPPING_SENDER.subdistrict,
          province_en: this.settings.company.SHIPPING_SENDER.province_en,
          district_en: this.settings.company.SHIPPING_SENDER.district_en,
          subdistrict_en: this.settings.company.SHIPPING_SENDER.subdistrict_en,
          postcode: this.settings.company.SHIPPING_SENDER.postcode
        }
      }
    }, 500)
  },

  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.SHIPPING)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }

    this.debouncedGetAddress = _.debounce(() => {
      this.getAddress()
    }, 500)
  },

  methods: {

    hasUnsavedChanges () {
      const base = this.$refs.base as never as any
      return base.hasUnsavedChanges()
    },

    updateFormData (event: FormData) {
      this.form_data = { ...this.form_data, ...event }
      this.updateSenderAddress(event)
    },

    updateSenderAddress (event : object) {
      const shipping_sender = { ...this.settings.company.SHIPPING_SENDER, ...event }
      const base = this.$refs.base as never as any
      base.update({ SHIPPING_SENDER: shipping_sender })
    },

    async getShopList () {
      this.shops = (await this.$axios.get('/api/companies/choices/saleschannel_choices/')).data
    },

    async getAddress () {
      const params = {} as any
      params.search = this.search
      this.items = (await this.$axios.get('/api/thaiaddress/search/', { params })).data
    },

    async linkFlashAccount () {
      try {
        const res = await this.$axios.post('/api/shipping/flash/link-account/')
        const url = res.data.url
        window.open(url)
      } catch (error) {
        console.error(error)
      }
    },

    async testLinkFlashAccount () {
      this.loading = true
      try {
        await this.$axios.post('/api/shipping/flash/test-link-account/')
        this.login_status = 'ALREADY_LOGIN'
        this.$snackbar('success', 'ล็อกอินเเล้ว')
      } catch (error) {
        this.$snackbar('error', 'ยังไม่ได้ล็อกอิน')
        this.login_status = 'NOT_LOGGED_YET'
        console.error(error)
      } finally {
        this.loading = false
      }
    },

    highlight (text: string, search: string) {
      const index = text.indexOf(search)
      if (index >= 0) {
        text = (text.substring(0, index) +
          "<span style='background-color: yellow;'>" +
          text.substring(index, index + search.length) +
          '</span>' +
          text.substring(index + search.length)
        )
        return text
      }

      return text
    }

  }
})
</script>

<style scoped>
/* .text-help {
  padding-left: 33px;
  font-size: 0.85rem;
  color: #555555;
} */
</style>
