<template>
  <div />
</template>

<script lang="ts">
import Vue from 'vue'
import { SettingState } from '~/store/settings'
// import LazadaSettingDialog from '~/components/settings/marketplaces/lazada/LazadaSettingDialog.vue'

export default Vue.extend({
  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },

  created () {
    if (this.settings.company.DOBYBOT_CONNECT_VERSION === 1) {
      this.$router.replace('/settings/marketplace/v1')
    } else {
      this.$router.replace('/settings/marketplace/v2')
    }
  }
})
</script>
