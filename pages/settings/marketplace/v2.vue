<template>
  <setting-template ref="base" :title="$t('settings.setting-market-v2')">
    <template #overview>
      <v-row>
        <v-col cols="8">
          <div>ตั้งค่าการเชื่อมต่อ Marketplace และ บริการอื่น ๆ</div>
          <div v-if="$auth.user.is_staff">
            Company UUID: {{ $auth.user.company.uuid }}
          </div>
        </v-col>
        <v-col cols="4" class="text-right">
          <v-btn color="primary" @click="showMarketplaceConnectDialog()">
            เพิ่มการเชื่อมต่อ
          </v-btn>
        </v-col>
      </v-row>
      <!-- <div>
        เปลี่ยนไปใช้งาน
        <a @click.prevent="switchToV1()">
          Marketplace V1
        </a>
      </div> -->
    </template>

    <template #form="{ settings, update }">
      <v-row wrap class="mt-5">
        <v-col
          v-for="mp in marketplaces"
          :key="mp.marketplace + mp.seller_id"
          cols="12"
          sm="12"
          md="6"
          lg="4"
        >
          <shop-setting-card
            v-if="mp.marketplace === 'shopee'"
            :shop="mp"
            :shop-setting="mp.settings"
            :features="['sync_order', 'airway_bill', 'chat', 'disconnect']"
            @update:shop-setting="
              update(
                {
                  SHOPEE_API: {
                    ...settings.company.SHOPEE_API,
                    [mp.seller_id]: $event,
                  },
                },
                true
              )
            "
            @reconnect="connectOnlineShopingPlatform('shopee')"
            @click:disconnect="disconnectShop('shopee', mp.seller_id)"
          />
          <shop-setting-card
            v-if="mp.marketplace === 'lazada'"
            :shop="mp"
            :features="[
              'sync_order',
              'airway_bill',
              'chat',
              'ready_to_ship',
              'disconnect',
            ]"
            :shop-setting="mp.settings"
            @update:shop-setting="
              update(
                {
                  LAZADA_API: {
                    ...settings.company.LAZADA_API,
                    [mp.seller_id]: $event,
                  },
                },
                true
              )
            "
            @reconnect="connectOnlineShopingPlatform('lazada')"
            @click:disconnect="disconnectShop('lazada', mp.seller_id)"
          />
          <shop-setting-card
            v-if="mp.marketplace === 'tiktok_shop'"
            :shop="mp"
            :features="['sync_order', 'airway_bill', 'disconnect']"
            :shop-setting="mp.settings"
            @update:shop-setting="
              update(
                {
                  TIKTOK_SHOP_API: {
                    ...settings.company.TIKTOK_SHOP_API,
                    [mp.seller_id]: $event,
                  },
                },
                true
              )
            "
            @reconnect="connectOnlineShopingPlatform('tiktok_shop_v2')"
            @click:disconnect="disconnectShop('tiktok_shop', mp.seller_id)"
          />
          <shop-setting-card
            v-if="mp.marketplace === 'tiktok_shop_v2'"
            :shop="mp"
            :features="['sync_order', 'airway_bill', 'disconnect']"
            :shop-setting="mp.settings"
            @update:shop-setting="
              update(
                {
                  TIKTOK_SHOP_V2_API: {
                    ...settings.company.TIKTOK_SHOP_V2_API,
                    [mp.seller_id]: $event,
                  },
                },
                true
              )
            "
            @reconnect="connectOnlineShopingPlatform('tiktok_shop_v2')"
            @click:disconnect="disconnectShop('tiktok_shop_v2', mp.seller_id)"
          />
          <shop-setting-card
            v-if="mp.marketplace === 'nex_gen_commerce'"
            :shop="mp"
            :features="['sync_order', 'disconnect']"
            :shop-setting="mp.settings"
            @update:shop-setting="
              update(
                {
                  NEX_GEN_COMMERCE_API: {
                    ...settings.company.NEX_GEN_COMMERCE_API,
                    [mp.seller_id]: $event,
                  },
                },
                true
              )
            "
            @click:disconnect="disconnectShop('nex_gen_commerce', mp.seller_id)"
          />
          <shop-setting-card
            v-if="mp.marketplace === 'zort_v2'"
            :shop="mp"
            :features="['sync_order', 'setting', 'disconnect']"
            :shop-setting="mp.settings"
            @click:setting="dialogs.zort_setting_dialog = true"
            @update:shop-setting="
              update(
                { ZORT_API_V2: { ...settings.company.ZORT_API_V2, ...$event } },
                true
              )
            "
            @click:disconnect="disconnectShop('zort_v2', mp.seller_id)"
          >
            <template #dialog>
              <zort-setting-dialog
                v-model="dialogs.zort_setting_dialog"
                :zort="mp"
                @update:zort="
                  update($event, true).then(() => getMarketplaces())
                "
                @update:webhooks="update($event, true)"
                @update:settings="update($event)"
              />
            </template>
          </shop-setting-card>
          <shop-setting-card
            v-if="mp.marketplace === 'nocnoc'"
            :shop="mp"
            :features="[
              'sync_order',
              'airway_bill',
              'ready_to_ship',
              'disconnect',
            ]"
            :shop-setting="mp.settings"
            @update:shop-setting="
              update(
                {
                  NOCNOC_API: {
                    ...settings.company.NOCNOC_API,
                    [mp.seller_id]: $event,
                  },
                },
                true
              )
            "
            @click:disconnect="disconnectShop('nocnoc', mp.seller_id)"
          />
          <shop-setting-card
            v-if="mp.marketplace === 'line_my_shop'"
            :shop="mp"
            :features="['sync_order', 'airway_bill', 'disconnect']"
            :shop-setting="mp.settings"
            @update:shop-setting="
              update(
                {
                  LINE_MY_SHOP_API: {
                    ...settings.company.LINE_MY_SHOP_API,
                    [mp.seller_id]: $event,
                  },
                },
                true
              )
            "
            @click:disconnect="disconnectShop('line_my_shop', mp.seller_id)"
          />
          <shop-setting-card
            v-if="mp.marketplace === 'woo_commerce'"
            :shop="mp"
            :features="['sync_order', 'disconnect']"
            :shop-setting="mp.settings"
            @update:shop-setting="
              update(
                {
                  WOO_COMMERCE_API: {
                    ...(settings.company.WOO_COMMERCE_API || {}),
                    [mp.seller_id]: $event,
                  },
                },
                true
              )
            "
            @reconnect="connectOnlineShopingPlatform('woo_commerce')"
            @click:disconnect="disconnectShop('woo_commerce', mp.seller_id)"
          />
        </v-col>
      </v-row>

      <noc-noc-connect-dialog
        v-model="dialogs.nocnoc_connect_dialog"
        @connected="onNocNocShopConnected()"
      />

      <line-my-shop-connect-dialog
        v-model="dialogs.linemyshop_connect_dialog"
        @connected="onLineMyShopConnected()"
      />
      <nex-gen-commerce-connect-dialog
        v-model="dialogs.nexgencommerce_connect_dialog"
        @connected="onNexGenCommerceConnected()"
      />

      <woo-commerce-connect-dialog
        v-model="dialogs.woocommerce_connect_dialog"
        @connected="onWooCommerceConnected()"
      />

      <v-dialog v-model="dialogs.zort_connect_dialog_v2" max-width="600">
        <zort-connect-form-v2
          @success="
            dialogs.zort_connect_dialog_v2 = false;
            getMarketplaces();
          "
        />
      </v-dialog>
      <marketplace-connect-dialog
        v-model="dialogs.marketplace_connect_dialog"
        :version="2"
        @connect="connectOnlineShopingPlatform($event)"
        @connect:nocnoc="showNocNocConnectDialog()"
        @connect:linemyshop="showLineMyShopConnectDialog()"
        @connect:woo-commerce="showWooCommerceConnectDialog()"
        @connect:zort-v2="showZortV2ConnectDialog()"
        @connect:nexgencommerce="showNexGenCommerceConnectDialog()"
        @refresh="getMarketplaces()"
      />
    </template>
  </setting-template>
</template>

<script lang="ts">
import Vue from 'vue'
import SettingTemplate from '~/components/settings/SettingTemplate.vue'
import MarketplaceConnectDialog from '~/components/settings/marketplaces/MarketplaceConnectDialog.vue'
import ShopSettingCard from '~/components/settings/marketplaces/ShopSettingCard.vue'
import { openPopupWindow } from '~/utils/popup'
import { wait } from '~/utils/timer'
import NocNocConnectDialog from '~/components/settings/marketplaces/NocNocConnectDialog.vue'
import { FEATURE_FLAGS, Marketplace } from '~/models'
import { SettingState } from '~/store/settings'
import ZortConnectFormV2 from '~/components/settings/marketplaces/ZortConnectFormV2.vue'
import ZortSettingDialog from '~/components/settings/marketplaces/zort/ZortSettingDialog.vue'
import LineMyShopConnectDialog from '~/components/settings/marketplaces/LineMyShopConnectDialog.vue'
import NexGenCommerceConnectDialog from '~/components/settings/marketplaces/NexGenCommerceConnectDialog.vue'
import WooCommerceConnectDialog from '~/components/settings/marketplaces/WooCommerceConnectDialog.vue'

export default Vue.extend({
  components: {
    SettingTemplate,
    MarketplaceConnectDialog,
    NocNocConnectDialog,
    ShopSettingCard,
    ZortConnectFormV2,
    ZortSettingDialog,
    LineMyShopConnectDialog,
    NexGenCommerceConnectDialog,
    WooCommerceConnectDialog
  },

  data () {
    return {
      marketplaces: [] as Marketplace[],
      dialogs: {
        marketplace_connect_dialog: false,
        nocnoc_connect_dialog: false,
        zort_connect_dialog_v2: false,
        zort_setting_dialog: false,
        linemyshop_connect_dialog: false,
        nexgencommerce_connect_dialog: false,
        woocommerce_connect_dialog: false
      }
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },

  mounted () {
    this.getMarketplaces();

    (async () => {
      await this.$store.dispatch('settings/loadFromServer')
      if (this.settings.company.DOBYBOT_CONNECT_VERSION === 1) {
        this.$router.replace('/settings/marketplace/v1')
      }
    })()
  },

  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.ORDER_CENTER)) {
      this.$nuxt.error({
        statusCode: 400,
        message: this.$t('error.feature_flag')
      })
    }
  },

  methods: {
    async getMarketplaces () {
      const shops: Marketplace[] = (
        await this.$axios.get('/api/companies/marketplaces/')
      ).data
      this.marketplaces = shops.filter(x => !x.marketplace.endsWith('_chat'))
    },

    async switchToV1 () {
      if (
        !(await this.$confirm({
          title: 'ยืนยันการเปลี่ยนแปลง',
          text: 'เมื่อเปลี่ยนกลับไปใช้ V1 แล้วจะต้องทำการเชื่อมต่อร้านค้าต่าง ๆ อีกรอบ',
          theme: 'warning'
        }))
      ) {
        return
      }

      try {
        await this.$axios.post('/api/companies/dobybot-connect/switch-to-v1/')
        await this.$store.dispatch('settings/loadFromServer')
        this.$snackbar('success', 'Switched to v1 successfully')
        this.$router.replace('/settings/marketplace/v1')
      } catch (error: any) {
        console.error(error)
      }
    },

    showMarketplaceConnectDialog () {
      this.dialogs.marketplace_connect_dialog = true
    },

    async connectOnlineShopingPlatform (
      platform: 'lazada' | 'shopee' | 'tiktok_shop' | 'tiktok_shop_v2' | 'woo_commerce'
    ) {
      const seller_auth_url = (
        await this.$axios.get('/api/companies/dobybot-connect/seller/auth/', {
          params: { platform }
        })
      ).data
      openPopupWindow(seller_auth_url, 1200, 760, async () => {
        this.dialogs.marketplace_connect_dialog = false

        this.$loader(true)
        await wait(1000)
        await this.getMarketplaces()
        this.$loader(false)
      })
    },

    showNocNocConnectDialog () {
      this.dialogs.marketplace_connect_dialog = false
      this.dialogs.nocnoc_connect_dialog = true
    },

    showZortV2ConnectDialog () {
      this.dialogs.marketplace_connect_dialog = false
      this.dialogs.zort_connect_dialog_v2 = true
    },

    showLineMyShopConnectDialog () {
      this.dialogs.marketplace_connect_dialog = false
      this.dialogs.linemyshop_connect_dialog = true
    },
    showNexGenCommerceConnectDialog () {
      this.dialogs.marketplace_connect_dialog = false
      this.dialogs.nexgencommerce_connect_dialog = true
    },

    showWooCommerceConnectDialog () {
      this.dialogs.marketplace_connect_dialog = false
      this.dialogs.woocommerce_connect_dialog = true
    },

    async onNocNocShopConnected () {
      this.dialogs.nocnoc_connect_dialog = false

      this.$loader(true)
      await this.getMarketplaces()
      this.$loader(false)
    },

    async onLineMyShopConnected () {
      this.dialogs.linemyshop_connect_dialog = false

      this.$loader(true)
      await this.getMarketplaces()
      this.$loader(false)
    },

    async onNexGenCommerceConnected () {
      this.dialogs.nexgencommerce_connect_dialog = false

      this.$loader(true)
      await this.getMarketplaces()
      this.$loader(false)
    },

    async disconnectShop (marketplace: string, seller_id: string) {
      if (
        !(await this.$confirm({
          text: 'Are you sure you want to disconnect?',
          theme: 'warning'
        }))
      ) {
        return
      }

      try {
        await this.$axios.post(
          '/api/companies/dobybot-connect/seller/disconnect/',
          {
            marketplace,
            seller_id
          }
        )

        // Reload markeplace list
        this.$loader(true)
        await this.getMarketplaces()
        this.$loader(false)
      } catch (error: any) {
        console.error(error)
        // TODO: display error
      }
    },

    async onWooCommerceConnected () {
      this.dialogs.woocommerce_connect_dialog = false
      this.$loader(true)
      await this.getMarketplaces()
      this.$loader(false)
    }
  }
})
</script>
