
<template>
  <setting-template ref="base" :title="$t('settings.setting-market')">
    <template #overview>
      <v-alert v-if="$auth.user.is_staff" outlined text color="warning">
        <v-row class="pa-3" justify="space-between">
          <div class="my-auto">
            เปลี่ยนไปใช้งาน <a @click.prevent="showSwitchToV2Dialog()">Marketplace V2</a> เพื่อใช้งานฟีเจอร์ใหม่ ๆ และประสิทธิภาพที่ดีขึ้น
          </div>
        </v-row>
      </v-alert>

      <v-row>
        <v-col cols="8">
          <div>
            ตั้งค่าการเชื่อมต่อ Marketplace และ บริการอื่น ๆ
          </div>
          <div v-if="$auth.user.is_staff">
            Company UUID: {{ $auth.user.company.uuid }}
          </div>
        </v-col>
        <v-col cols="4" class="text-right">
          <v-btn color="primary" @click="addMarketplace()">
            เพิ่มการเชื่อมต่อ
          </v-btn>
        </v-col>
      </v-row>
    </template>

    <template #form="{ settings, update }">
      <v-row wrap class="mt-5">
        <v-col
          v-for="mp in marketplaces"
          :key="mp.marketplace + mp.seller_id"
          cols="12"
          sm="12"
          md="6"
          lg="4"
        >
          <shop-setting-card
            v-if="mp.marketplace === 'shopee'"
            :shop="mp"
            :shop-setting="mp.settings"
            @update:shop-setting="update({ SHOPEE_API: {...settings.company.SHOPEE_API, [mp.seller_id]: $event} }, true)"
            @reconnect="connectOnlineShopingPlatform('shopee')"
          />
          <shop-setting-card
            v-if="mp.marketplace === 'lazada'"
            :shop="mp"
            :features="['sync_order', 'airway_bill', 'chat', 'ready_to_ship']"
            :shop-setting="mp.settings"
            @update:shop-setting="update({ LAZADA_API: {...settings.company.LAZADA_API, [mp.seller_id]: $event} }, true)"
            @reconnect="connectOnlineShopingPlatform('lazada')"
          />
          <shop-setting-card
            v-if="mp.marketplace === 'tiktok_shop'"
            :shop="mp"
            :features="['sync_order', 'airway_bill']"
            :shop-setting="mp.settings"
            @update:shop-setting="update({ TIKTOK_SHOP_API: {...settings.company.TIKTOK_SHOP_API, [mp.seller_id]: $event} }, true)"
            @reconnect="connectOnlineShopingPlatform('tiktok_shop')"
          />
          <shop-setting-card
            v-if="mp.marketplace === 'zort_v1'"
            :shop="mp"
            :features="['sync_order', 'sync_product']"
            :shop-setting="{sync_order: settings.company.AUTO_SYNC_ORDER_ENABLE}"
            :auto-persist="false"
            @update:shop-setting="update({ AUTO_SYNC_ORDER_ENABLE: $event.sync_order })"
            @click:sync-product="syncZortProducts()"
          />
          <shop-setting-card
            v-if="mp.marketplace === 'zort_v2'"
            :shop="mp"
            :features="['sync_order', 'setting']"
            :shop-setting="mp.settings"
            @click:setting="dialogs.zort_setting_dialog = true"
            @update:shop-setting="update({ ZORT_API_V2: {...settings.company.ZORT_API_V2, ...$event} }, true)"
          >
            <template #dialog>
              <zort-setting-dialog
                v-model="dialogs.zort_setting_dialog"
                :zort="mp"
                @update:zort="update($event, true).then(() => getMarketplaces());"
                @update:webhooks="update($event, true)"
                @update:settings="update($event)"
              />
            </template>
          </shop-setting-card>
          <shop-setting-card
            v-if="mp.marketplace === 'shipnity'"
            :shop="mp"
            :features="['sync_order']"
            :shop-setting="settings.company.SHIPNITY_API"
            @update:shop-setting="update({ SHIPNITY_API: {...settings.company.SHIPNITY_API, ...$event} }, true)"
          />
        </v-col>
      </v-row>

      <!-- <v-row dense class="mt-4 pt-4 px-1">
        <v-col cols="12">
          <marketplace-table :items="marketplaces" />
        </v-col>
      </v-row> -->

      <marketplace-connect-dialog
        v-model="dialogs.marketplace_connect_dialog"
        @connect="connectOnlineShopingPlatform($event)"
        @connect:zort-v1="connectZortV1()"
        @connect:zort-v2="connectZortV2()"
        @connect:shipnity="connectShinity()"
        @refresh="getMarketplaces()"
      />

      <!-- <lazada-setting-dialog
        v-model="dialogs.lazada_setting_dialog.show"
        :shop="dialogs.lazada_setting_dialog.shop"
      /> -->
      <v-dialog v-model="dialogs.zort_connect_dialog_v1" max-width="600">
        <zort-connect-form-v1
          :form-data="{
            ZORT_STORENAME: settings.company.ZORT_STORENAME,
            ZORT_API_KEY: settings.company.ZORT_API_KEY,
            ZORT_API_SECRET: settings.company.ZORT_API_SECRET,
          }"
          @update:form-data="update($event)"
          @submit="dialogs.zort_connect_dialog_v1 = false"
        />
      </v-dialog>

      <v-dialog v-model="dialogs.zort_connect_dialog_v2" max-width="600">
        <zort-connect-form-v2
          @success="dialogs.zort_connect_dialog_v2 = false; getMarketplaces()"
        />
      </v-dialog>

      <v-dialog v-model="dialogs.shipnity_connect_dialog" max-width="600">
        <shipnity-connect-form
          @success="dialogs.shipnity_connect_dialog = false; getMarketplaces()"
        />
      </v-dialog>

      <switch-to-marketplace-v2-dialog v-model="dialogs.switch_to_v2_dialog" />
    </template>
  </setting-template>
</template>

<script lang="ts">
import Vue from 'vue'
import { wait } from '~/utils/timer'
import MarketplaceConnectDialog from '~/components/settings/marketplaces/MarketplaceConnectDialog.vue'
import ShipnityConnectForm from '~/components/settings/marketplaces/ShipnityConnectForm.vue'
import ShopSettingCard from '~/components/settings/marketplaces/ShopSettingCard.vue'
import ZortConnectFormV1 from '~/components/settings/marketplaces/ZortConnectFormV1.vue'
import ZortConnectFormV2 from '~/components/settings/marketplaces/ZortConnectFormV2.vue'
import SettingTemplate from '~/components/settings/SettingTemplate.vue'
import ConfirmExit from '~/mixins/confirm-exit'
import { FEATURE_FLAGS, Marketplace } from '~/models'
import { openPopupWindow } from '~/utils/popup'
import ZortSettingDialog from '~/components/settings/marketplaces/zort/ZortSettingDialog.vue'
import SwitchToMarketplaceV2Dialog from '~/components/settings/marketplaces/SwitchToMarketplaceV2Dialog.vue'
import { SettingState } from '~/store/settings'
// import LazadaSettingDialog from '~/components/settings/marketplaces/lazada/LazadaSettingDialog.vue'

export default Vue.extend({
  components: {
    SettingTemplate,
    MarketplaceConnectDialog,
    ShopSettingCard,
    ZortConnectFormV1,
    ZortConnectFormV2,
    ShipnityConnectForm,
    ZortSettingDialog,
    SwitchToMarketplaceV2Dialog
    // LazadaSettingDialog
  },

  mixins: [ConfirmExit],
  middleware: ['auth'],

  data () {
    return {
      marketplaces: [] as Marketplace[],
      dialogs: {
        marketplace_connect_dialog: false,
        zort_connect_dialog_v1: false,
        zort_connect_dialog_v2: false,
        shipnity_connect_dialog: false,
        zort_setting_dialog: false,
        switch_to_v2_dialog: false
      }
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },

  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.ORDER_CENTER)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }
  },

  mounted () {
    if (this.settings.company.DOBYBOT_CONNECT_VERSION === 2) {
      this.$router.replace('/settings/marketplace/v2')
    }

    this.getMarketplaces()
  },

  methods: {
    hasUnsavedChanges () {
      return (this.$refs.base as never as any).hasUnsavedChanges()
    },

    addMarketplace () {
      this.dialogs.marketplace_connect_dialog = true
    },

    async getMarketplaces () {
      const shops: Marketplace[] = (await this.$axios.get('/api/companies/marketplaces/')).data
      this.marketplaces = shops.filter(x => !x.marketplace.endsWith('_chat'))
    },

    async connectOnlineShopingPlatform (platform: 'lazada'|'shopee'|'tiktok_shop') {
      const seller_auth_url = (await this.$axios.get('/api/companies/dobybot-connect/seller/auth/', { params: { platform } })).data
      openPopupWindow(seller_auth_url, 1200, 760, async () => {
        this.dialogs.marketplace_connect_dialog = false

        this.$loader(true)
        await wait(1000)
        await this.getMarketplaces()
        this.$loader(false)
      })
    },

    connectZortV1 () {
      this.dialogs.marketplace_connect_dialog = false
      this.dialogs.zort_connect_dialog_v1 = true
    },

    connectZortV2 () {
      this.dialogs.marketplace_connect_dialog = false
      this.dialogs.zort_connect_dialog_v2 = true
    },

    connectShinity () {
      this.dialogs.marketplace_connect_dialog = false
      this.dialogs.shipnity_connect_dialog = true
    },

    async syncZortProducts () {
      this.$loader(true)
      try {
        const res = await this.$axios.post('/api/picking/zort/sync-products/')
        this.$alert({
          title: 'Sync ข้อมูลสินค้าสำเร็จ',
          text: `Sync ข้อมูลสินค้าสำเร็จ พบสินค้าทั้งหมด ${res.data.count} รายการ`,
          theme: 'success'
        })
      } catch (error) {
        console.error(error)
        this.$snackbar('error', this.$t('error.unknown'))
      }
      this.$loader(false)
    },

    showSwitchToV2Dialog () {
      this.dialogs.switch_to_v2_dialog = true
    }
  }
})
</script>
