<template>
  <setting-template ref="base" :title="$t('settings.setting-receipt')">
    <template #overview>
      <!--  -->
      ตั้งค่าใบเสร็จที่ลูกค้ามองเห็นเมื่อคลิกลิงค์ที่ได้รับทาง SMS / Chat ต่าง ๆ
    </template>

    <template #form="{ settings, update }">
      <v-row v-package="$package.FULL_INTEGRATION">
        <!-- Form -->
        <v-col cols="8">
          <!-- Show / Hide receipt section -->
          <v-row>
            <v-col cols="12">
              <v-checkbox
                :label="$t('receipt.show-packing-video')"
                hide-details
                disabled
                :input-value="true"
              />
              <v-checkbox
                :label="$t('receipt.show-receipt')"
                hide-details
                :input-value="!settings.company.HIDE_RECEIPT"
                @change="update({ HIDE_RECEIPT: !$event })"
              />
            </v-col>
          </v-row>

          <!-- ตั้งค่าหัวบิล -->
          <v-row dense class="mt-4 pt-4">
            <v-col cols="12">
              <h2>{{ $t('settings.setting-top') }}</h2>
            </v-col>
            <v-col cols="12">
              <v-textarea
                :label="$t('settings.company')"
                filled
                hide-details
                rows="1"
                auto-grow
                :disabled="settings.company.HIDE_RECEIPT"
                :value="settings.company.COMPANY_NAME"
                @change="update({ COMPANY_NAME: $event })"
              />
            </v-col>
            <v-col cols="12">
              <v-text-field
                :label="$t('settings.admin-phone-number')"
                filled
                hide-details
                :value="settings.company.COMPANY_ADMIN_TEL"
                @change="update({ COMPANY_ADMIN_TEL: $event })"
              />
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                :label="$t('settings.admin-line-name')"
                filled
                hide-details
                :value="settings.company.COMPANY_ADMIN_LINE_NAME"
                @change="update({ COMPANY_ADMIN_LINE_NAME: $event })"
              />
            </v-col>
            <v-col cols="12" md="6">
              <v-text-field
                :label="$t('settings.admin-line-qrcode-url')"
                filled
                hide-details
                :value="settings.company.COMPANY_ADMIN_LINE_URL"
                @change="update({ COMPANY_ADMIN_LINE_URL: $event })"
              />
            </v-col>
            <v-col cols="12">
              <v-textarea
                :label="$t('settings.company-address')"
                filled
                hide-details
                rows="2"
                :disabled="settings.company.HIDE_RECEIPT"
                :value="settings.company.COMPANY_ADDRESS"
                @change="update({ COMPANY_ADDRESS: $event })"
              />
            </v-col>
          </v-row>

          <v-row dense class="mt-4 pt-4">
            <v-col cols="12">
              <h2>{{ $t('settings.setting-footer') }}</h2>
            </v-col>
            <v-col cols="12">
              <b>HTML</b>
              <prism-editor
                :value="settings.company.RECEIPT_FOOTER"
                @input="update({ RECEIPT_FOOTER: $event })"
              />
            </v-col>
          </v-row>
        </v-col>

        <!-- Preview -->
        <v-col cols="4">
          <h2 class="text-center">
            ตัวอย่าง
          </h2>
          <div style="max-height: 720px; max-width: 420px; border: 1px solid #eeeeee; overflow: scroll;">
            <v-container style="max-width: 650px;">
              <!-- Brand Logo -->
              <v-row class="no-print" wrap>
                <v-col cols="12" class="text-center">
                  <v-img :src="logo_url" max-height="60px" contain />
                  <h3>
                    WAREHOURSE SMART RECORD SYSTEM
                  </h3>
                  <div v-if="!settings.company.CUSTOM_BRAND">
                    {{ $t('public.interest-system') }} <a href="https://lin.ee/012QqmH" class="teal--text">LINE @dobybot</a>
                  </div>
                </v-col>
              </v-row>

              <!-- Video Player -->
              <v-row class="no-print px-0">
                <v-col cols="12" class="px-0">
                  <div class="text-center mx-auto">
                    <div class="text-center black white--text" style="font-size: 18px; cursor: pointer;">
                      {{ $t('public.order-number', ['ORDER000001']) }}
                    </div>
                    <div>
                      <v-img src="/help/video-player.png" />
                    </div>
                  </div>

                  <div class="text-center mt-2">
                    <v-btn
                      small
                      outlined
                      color="primary"
                      rounded
                    >
                      <v-icon left>
                        mdi-download
                      </v-icon>
                      {{ $t('public.download-video') }}
                    </v-btn>
                  </div>

                  <h3 v-if="settings.company.COMPANY_ADMIN_TEL || (settings.company.COMPANY_ADMIN_LINE_NAME && settings.company.COMPANY_ADMIN_LINE_URL)" class="text-center mt-4">
                    {{ $t('public.found-package-not-correct') }}
                  </h3>
                  <div v-if="settings.company.COMPANY_ADMIN_TEL" class="text-center mt-2">
                    <v-btn :href="`tel:${settings.company.COMPANY_ADMIN_TEL}`" x-large color="warning" rounded>
                      <v-icon left>
                        mdi-phone
                      </v-icon>
                      {{ $t('public.click-here-to-call-to-shop') }} <br>{{ settings.company.COMPANY_ADMIN_TEL }}
                    </v-btn>
                  </div>

                  <div v-if="settings.company.COMPANY_ADMIN_LINE_NAME && settings.company.COMPANY_ADMIN_LINE_URL" class="text-center mt-2">
                    <v-btn
                      :href="settings.company.COMPANY_ADMIN_LINE_URL"
                      target="_blank"
                      x-large
                      color="#40B566"
                      outlined
                      rounded
                    >
                      <v-img src="/logo/LINE_Brand_icon.png" width="24" height="24" class="mr-2" />
                      {{ $t('line-click-here-to-add-friend') }} <br>{{ settings.company.COMPANY_ADMIN_LINE_NAME }}
                    </v-btn>
                  </div>
                </v-col>
              </v-row>

              <!-- Receipt -->
              <v-row v-if="!settings.company.HIDE_RECEIPT" wrap>
                <v-col cols="12" class="px-0">
                  <div
                    class="text-center teal darken-1 white--text title"
                    style="padding-top: 8px; height: 40px"
                  >
                    {{ $t('public.receipt') }}
                  </div>
                </v-col>
                <v-col cols="12" class="pt-0">
                  <h2>{{ settings.company.COMPANY_NAME }}</h2>
                  <div>{{ settings.company.COMPANY_ADDRESS }}</div>
                  <div style="height: 10px;" />

                  <v-row class="my-1 px-2" justify="space-between">
                    <span class="my-auto">{{ $t('date') }}</span>
                    <span style="font-size: 18px;">{{ $moment().format('DD/MM/YYYY') }}</span>
                  </v-row>
                  <v-row class="my-1 px-2" justify="space-between">
                    <span class="my-auto">{{ $t('public.order-num') }}</span>
                    <span style="font-size: 18px;">
                      ORDER000001
                      <a class="no-print copy teal--text" href="#">{{ $t('public.copy') }}</a>
                    </span>
                  </v-row>
                  <v-row class="my-1 px-2" justify="space-between">
                    <span class="my-auto">{{ $t('public.delivery') }}</span>
                    <span style="font-size: 18px;">Kerry Express</span>
                  </v-row>
                  <v-row class="my-1 px-2" justify="space-between">
                    <span class="my-auto">{{ $t('public.parcel') }}</span>
                    <span style="font-size: 18px;">
                      TRACKING00001
                      <a class="no-print copy teal--text" href="#">{{ $t('public.copy') }}</a>
                    </span>
                  </v-row>
                </v-col>

                <v-col cols="12" class="pt-3 px-0">
                  <v-simple-table class="table">
                    <thead class="teal lighten-4">
                      <th class="text-left pl-2">
                        {{ $t('public.list') }}
                      </th>
                      <th>{{ $t('public.amount') }}</th>
                      <th class="text-right pr-2">
                        {{ $t('public.price') }}
                      </th>
                    </thead>
                    <tbody>
                      <tr v-for="(item, i) in [{ name: 'ตัวอย่างสินค้า 1', number: 2, totalprice: 200 }]" :key="i">
                        <td class="pl-2">
                          {{ item.name }}
                        </td>
                        <td class="pr-2 text-right">
                          {{ item.number }}
                        </td>
                        <td class="pr-2 text-right">
                          {{ item.totalprice.toFixed(2) }}
                        </td>
                      </tr>
                    </tbody>
                    <tfoot>
                      <tr>
                        <th class="text-right">
                          {{ $t('public.freight') }}
                        </th>
                        <td />
                        <td class="pr-2 text-right">
                          25.00
                        </td>
                      </tr>
                      <tr>
                        <th class="text-right">
                          {{ $t('public.discount') }}
                        </th>
                        <td />
                        <td class="pr-2 text-right red lighten-5 red--text text-ligthen-3">
                          0.00
                        </td>
                      </tr>
                      <tr>
                        <th class="text-right">
                          {{ $t('public.total-payment') }}
                        </th>
                        <td class="pr-2 text-right">
                          <!-- {{ order_total_number }} -->
                        </td>
                        <td class="pr-2 text-right">
                          225.00
                        </td>
                      </tr>
                    </tfoot>
                  </v-simple-table>
                </v-col>

                <v-col cols="12" class="no-print text-center">
                  <v-btn large outlined width="200">
                    <v-icon left>
                      mdi-printer
                    </v-icon>
                    {{ $t('public.print') }}
                  </v-btn>
                </v-col>
              </v-row>

              <!-- Tracking No. -->
              <v-row class="no-print">
                <v-col cols="12" class="px-0">
                  <div class="teal darken-1 white--text text-center" style="font-size: 18px;">
                    {{ $t('public.shipping-number', ['Kerry Express']) }}

                    <div>
                      <span style="font-size: 22px;">
                        TRACKING00001
                      </span>
              &nbsp;
                      <v-btn x-small style="position: relative; top: -2px;">
                        {{ $t('public.copy') }}
                      </v-btn>
                    </div>
                  </div>
                </v-col>
              </v-row>

              <!-- Ads -->
              <v-row class="no-print">
                <v-col cols="12">
                  <div v-html="settings.company.RECEIPT_FOOTER" />
                </v-col>
              </v-row>
            </v-container>
          </div>
        </v-col>
      </v-row>
    </template>
  </setting-template>
</template>

<script lang="ts">
import Vue from 'vue'
import SettingTemplate from '~/components/settings/SettingTemplate.vue'
import ConfirmExit from '~/mixins/confirm-exit'
import PrismEditor from '~/components/editor/PrismEditor.vue'
import { Company, LoggedInUser } from '~/models'

export default Vue.extend({
  components: {
    SettingTemplate,
    PrismEditor
  },

  mixins: [ConfirmExit],
  middleware: ['auth'],

  data () {
    return {
    }
  },

  computed: {
    logo_url (): string {
      const user = this.$auth.user as never as LoggedInUser
      const company = user.company as Company
      if (company && company.receipt_logo) {
        return company.receipt_logo
      }
      return '/logo-with-text.png'
    }
  },

  mounted () {
    // this.getShopList()
  },

  methods: {
    hasUnsavedChanges () {
      return (this.$refs.base as never as any).hasUnsavedChanges()
    }
  }
})
</script>

<style scoped>
.app {
  font-family: kanit;
}

a.copy {
  text-decoration: none;
}
</style>
