<template>
  <div>
    <h1>Print Barcode</h1>
    <v-divider class="mb-3" />
    <v-row>
      <v-col cols="12" md="6">
        <v-row class="ms-1" align="center">
          <v-text-field
            v-model="code"
            :label="$t('barcode')"
            hint="ถ้าเป็นภาษาไทยสามารถเลือกเป็น QRCode เท่านั้น"
          />
          <v-checkbox
            v-model="barcode_run_number"
            :disabled="!code"
            label="Run number"
          />
        </v-row>

        <v-row class="ms-1" align="center">
          <v-text-field
            v-model="name"
            :label="$t('name')"
            hint="ถ้าเป็นภาษาไทยสามารถเลือกเป็น QRCode เท่านั้น"
          />
          <v-checkbox
            v-model="name_run_number"
            :disabled="!name"
            label="Run number"
          />
        </v-row>

        <v-select v-model="mode" class="mt-5" :label="$t('barcode-type')" :items="mode_choices" />

        <v-btn @click="generateBarcode()">
          GENERATE BARCODE
        </v-btn>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { FEATURE_FLAGS } from '~/models'

export default Vue.extend({
  data () {
    return {
      name: '',
      mode: 'Barcode',
      code: '',
      name_run_number: false,
      barcode_run_number: false
    }
  },

  computed: {
    mode_choices () {
      if (this.code.match(/[\u0E01-\u0E5B]/g)) {
        return ['QRCode']
      }
      return ['Barcode', 'QRCode']
    }
  },

  watch: {
    code (val) {
      // check if the code is in Thai language then change the mode to QRCode
      const isThai = val.match(/[\u0E01-\u0E5B]/g)
      if (isThai) {
        this.mode = 'QRCode'
      }

      if (val === '') {
        this.barcode_run_number = false
      }
    },
    name (val) {
      if (val === '') {
        this.name_run_number = false
      }
    }
  },
  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.PRODUCT_MANAGEMENT)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }
  },

  methods: {
    generateBarcode () {
      const baseURL = this.$axios.defaults.baseURL
      const mode = this.mode.toLowerCase()
      window.open(`${baseURL}/printing/barcode-page/?barcode=${this.code}&mode=${mode}&name=${this.name}&name_run_number=${this.name_run_number}&barcode_run_number=${this.barcode_run_number}`, '_blank')
    }
  }
})
</script>
