<template>
  <div>
    <h1>Product Serial No. Setting</h1>
    <v-divider class="mb-3" />

    <div>
      <serial-no-upload-form ref="form" :form-data.sync="form" @submit="uploadSerialNoFile()" />
    </div>

    <v-divider class="my-5" />

    <div>
      <v-row class="px-2 pt-3">
        <h3>{{ $t('public.list') }}</h3>
        <v-spacer />

        <v-btn small outlined color="primary" class="mr-1" @click="exportSerialNoXlsx()">
          <v-icon left>
            mdi-microsoft-excel
          </v-icon>
          Export XLSX
        </v-btn>
      </v-row>

      <serial-no-data-table
        :serial-nos="serial_nos"
        :table.sync="table"
        :search.sync="table.search"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { saveAs } from 'file-saver'
import Vue from 'vue'
import { debounce } from 'lodash'
import SerialNoUploadForm from '~/components/products/SerialNoUploadForm.vue'
import SerialNoDataTable from '~/components/products/SerialNoDataTable.vue'
import { FEATURE_FLAGS, VDataTableOption, VForm } from '~/models'
import { getDynamicRESTPagination, getDynamicRESTSorting } from '~/utils'
import { SettingState } from '~/store/settings'
export default Vue.extend({
  components: {
    // ProductUploadForm,
    SerialNoUploadForm,
    SerialNoDataTable
  },
  data () {
    return {
      table: {
        loading: false,
        meta: {
          total_results: 0,
          total_pages: 1,
          page: 1,
          per_page: 10
        },
        search: '',
        options: {
          page: 1,
          itemsPerPage: 10,
          sortBy: [],
          sortDesc: [false],
          groupBy: [],
          groupDesc: [],
          multiSort: false,
          mustSort: false
        } as VDataTableOption
      },
      serial_nos: [] as any[],
      form: {
        file: null as File | null,
        errors: []
      },
      debounceGetSerialNos: null as Function | null
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },

  watch: {
    'table.options': {
      handler () {
        if (!this.table.loading) {
          this.getSerialNos()
        }
      },
      deep: true
    },

    'table.search': {
      handler () {
        if (this.debounceGetSerialNos) {
          this.debounceGetSerialNos()
        }
      }
    }
  },

  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.PRODUCT_MANAGEMENT)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }
    this.getSerialNos()
    this.debounceGetSerialNos = debounce(() => {
      this.table.options.page = 1
      this.getSerialNos()
    }, 500)
  },

  methods: {
    async getSerialNos () {
      const params: any = {
        ...getDynamicRESTPagination(this.table.options),
        ...getDynamicRESTSorting(this.table.options)
      }

      if (this.table.search) {
        params.search = this.table.search
      }

      this.table.loading = true
      try {
        const result = await this.$axios.$get('/api/picking/resource/serial-no/', { params })
        this.serial_nos = result.product_serial_nos
        this.table.meta = result.meta
      } catch (error) {
        console.error(error)
      }
      this.table.loading = false
    },

    async uploadSerialNoFile () {
      const form = this.$refs.form as never as VForm
      if (!form.validate()) {
        return
      }

      this.form.errors = []
      const formData = new FormData()
      if (this.form.file) {
        formData.append('file', this.form.file)
      }

      this.$loader(true)
      try {
        await this.$axios.request({
          method: 'POST',
          url: '/api/picking/products/serial-no/import/',
          data: formData,
          headers: { 'Content-Type': 'multipart/form-data' }
        })
        this.$snackbar('success', this.$t('home.upload-success'))
        this.getSerialNos()
      } catch (error: any) {
        console.error(error)
        if (error.response) {
          if (error.response.status === 400) {
            this.$snackbar('error', this.$t('error.please_fix_form_error'))
            this.form.errors = error.response.data
          }
        } else {
          this.$snackbar('error', this.$t('error.unknown'))
        }
      }
      this.$loader(false)
    },

    async exportSerialNoXlsx () {
      this.$loader(true)
      try {
        const response = await this.$axios.get('/api/picking/products/serial-no/export/', { responseType: 'blob' })
        saveAs(response.data, 'serial_numbers.xlsx')
      } catch (error) {
        console.error(error)
      }
      this.$loader(false)
    }

  }
})
</script>
