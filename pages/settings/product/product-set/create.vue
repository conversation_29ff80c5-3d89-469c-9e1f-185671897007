<template>
  <div>
    <h1>Create Product Set</h1>
    <v-divider class="my-5" />

    <product-set-form ref="form" :form-data.sync="form" :errors="errors" />

    <v-row class="mt-5">
      <v-col cols="12" sm="3" md="3" />
      <v-col cols="12" sm="9" md="7" class="text-right">
        <v-btn data-test="v-btn-product-set-save" color="success" @click="createProductSet()">
          {{ $t('save') }}
        </v-btn>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import ProductSetForm from '@/components/product-set/ProductSetForm.vue'
import { FEATURE_FLAGS, ProductSet, VForm } from '~/models'

export default Vue.extend({
  components: {
    ProductSetForm
  },

  data () {
    return {
      form: {
        sku: '',
        name: '',
        products: [],
        change_logs: [],
        is_deleted: false
      } as ProductSet,
      errors: {}
    }
  },

  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.PRODUCT_MANAGEMENT)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }
  },

  methods: {
    async createProductSet () {
      const form = this.$refs.form as never as VForm
      if (!form.validate()) {
        return
      }

      try {
        await this.$axios.post('/api/picking/resource/product-set/', this.form)
        this.$snackbar('success', this.$t('snackbar.save_success'))
        this.$router.push('/settings/product/product-set/')
      } catch (err: any) {
        console.log(err)

        if (err.response) {
          this.errors = err.response.data
        }
      }
    }
  }

})
</script>

<style>

</style>
