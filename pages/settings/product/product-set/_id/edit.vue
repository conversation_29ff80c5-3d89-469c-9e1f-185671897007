<template>
  <div>
    <h1>Edit Product Set</h1>
    <v-divider class="my-5" />

    <product-set-form v-if="product_set" ref="form" :form-data.sync="product_set" :errors="errors" />

    <v-row class="mt-5">
      <v-col cols="12" sm="3" md="3" />
      <v-col cols="12" sm="9" md="7" class="text-right">
        <v-btn data-test="v-btn-save-and-update" color="success" @click="updateProductSet()">
          {{ $t('save') }}
        </v-btn>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import ProductSetForm from '~/components/product-set/ProductSetForm.vue'
import { FEATURE_FLAGS, ProductSet, VForm } from '~/models'

export default Vue.extend({
  components: { ProductSetForm },

  data () {
    return {
      product_set: null as ProductSet | null,
      errors: {}
    }
  },

  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.PRODUCT_MANAGEMENT)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }
  },

  mounted () {
    this.getProductSet(this.$route.params.id)
  },

  methods: {
    async getProductSet (id: string) {
      const res = await this.$axios.get(`/api/picking/resource/product-set/${id}/`)
      this.product_set = res.data.product_set
    },

    async updateProductSet () {
      const form = this.$refs.form as never as VForm
      if (!form.validate()) {
        return
      }

      if (!this.product_set) {
        return
      }

      try {
        await this.$axios.put(`/api/picking/resource/product-set/${this.product_set.id}/`, this.product_set)
        this.$router.push('/settings/product/product-set/')
        this.$snackbar('success', this.$t('snackbar.save_success'))
      } catch (err: any) {
        console.error(err)

        if (err.response) {
          this.errors = err.response.data
        }
      }
    }
  }
})
</script>

<style>

</style>
