<template>
  <div>
    <h1>Product Set</h1>
    <v-divider class="mb-3" />

    <div>
      <product-set-upload-form ref="form" :form-data.sync="form" @submit="uploadProductSetFile()" />
    </div>

    <v-divider class="my-5" />

    <v-row class="px-2 pt-3">
      <h3>รายการสินค้าจัดเซ็ต</h3>
      <v-spacer />

      <v-btn small outlined color="primary" class="mr-1" @click="exportProductSetXlsx()">
        <v-icon left>
          mdi-microsoft-excel
        </v-icon>
        Export XLSX
      </v-btn>

      <v-btn data-test="v-btn-create-product-set" small color="primary" :to="{path: '/settings/product/product-set/create'}">
        {{ $t('create-product-set') }}
      </v-btn>
    </v-row>

    <product-set-data-table
      :product-set="product_set"
      @click-edit="handleClickEdit($event)"
      @click-delete="handleClickDelete($event)"
      @click-toggle-delete="handleClickToggleDelete($event)"
    />
  </div>
</template>

<script lang="ts">
import saveAs from 'file-saver'
import Vue from 'vue'
import ProductSetDataTable from '~/components/product-set/ProductSetDataTable.vue'
import ProductSetUploadForm from '~/components/product-set/ProductSetUploadForm.vue'
import { FEATURE_FLAGS, ProductSet, VForm } from '~/models'

export default Vue.extend({
  components: { ProductSetDataTable, ProductSetUploadForm },
  data () {
    return {
      form: {
        file: null as File | null,
        errors: []
      },
      product_set: [] as ProductSet[]
    }
  },
  mounted () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.PRODUCT_MANAGEMENT)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }
    this.getProductSet()
  },
  methods: {
    async getProductSet () {
      const params = {
        include: ['create_by.*', 'update_by.*'],
        exclude: ['change_logs']
      }
      const res = await this.$axios.get('/api/picking/resource/product-set/', { params })
      this.product_set = res.data.product_sets
    },

    handleClickEdit (item: ProductSet) {
      this.$router.push(`/settings/product/product-set/${item.id}/edit`)
    },

    async handleClickDelete (item: ProductSet) {
      const confirm = await this.$confirm({
        title: 'Delete Product Set',
        text: `Are you sure you want to delete ${item.name}?`,
        theme: 'error'
      })

      if (!confirm) {
        return
      }

      try {
        await this.$axios.delete(`/api/picking/resource/product-set/${item.id}/`)
        this.product_set = this.product_set.filter(p => p.id !== item.id)
      } catch (err) {
        console.log(err)
      }
    },

    async handleClickToggleDelete (item: ProductSet) {
      try {
        await this.$axios.patch(`/api/picking/resource/product-set/${item.id}/`, {
          is_deleted: !item.is_deleted
        })
        const index = this.product_set.findIndex(p => p.id === item.id)
        this.product_set[index].is_deleted = !item.is_deleted
      } catch (err) {
        console.log(err)
      }
    },

    async uploadProductSetFile () {
      const form = this.$refs.form as never as VForm
      if (!form.validate()) {
        return
      }

      this.form.errors = []
      const formData = new FormData()
      if (this.form.file) {
        formData.append('file', this.form.file)
      }

      try {
        await this.$axios.request({
          method: 'POST',
          url: '/api/picking/product-sets/import/',
          data: formData,
          headers: { 'Content-Type': 'multipart/form-data' }
        })
        this.getProductSet()
        this.$snackbar('success', this.$t('home.upload-success'))
      } catch (error: any) {
        if (error.response) {
          if (error.response.status === 400) {
            this.$snackbar('error', this.$t('error.please_fix_form_error'))
            this.form.errors = error.response.data
            return
          }
        }

        console.error(error)
        this.$snackbar('error', this.$t('error.unknown'))
      }
    },

    async exportProductSetXlsx () {
      this.$loader(true)
      try {
        const response = await this.$axios.get('/api/picking/product-sets/export/', { responseType: 'blob' })
        saveAs(response.data, 'product-sets.xlsx')
      } catch (error) {
        console.error(error)
      }
      this.$loader(false)
    }

  }
})
</script>

<style>

</style>
