<template>
  <div>
    <h1>Product SKU / Barcode Setting</h1>
    <v-divider class="mb-3" />

    <div>
      <product-upload-form
        ref="form"
        :form-data.sync="form"
        @submit="uploadProductFile()"
      />
    </div>

    <v-divider class="my-5" />

    <div>
      <v-row class="px-2 pt-3">
        <h3>{{ $t("public.list") }}</h3>
        <v-spacer />

        <v-btn
          small
          outlined
          color="primary"
          class="mr-1"
          @click="exportProductXlsx()"
        >
          <v-icon left>
            mdi-microsoft-excel
          </v-icon>
          Export XLSX
        </v-btn>
        <v-btn
          data-test="product-barcode-setting-add-barcode"
          small
          color="primary"
          @click="product_create_dialog.show = true"
        >
          <v-icon left>
            mdi-plus
          </v-icon>
          {{ $t('add-product') }}
        </v-btn>
        <sync-products-menu
          v-if="settings.company.DOBYBOT_CONNECT_VERSION == 2"
          @reset="getProducts()"
        />
      </v-row>

      <product-data-table
        :products="products"
        :table.sync="table"
        :search.sync="table.search"
        @click-delete="deleteProduct($event)"
        @click-edit="
          (product) => {
            product_update_dialog.product = product;
            product_update_dialog.show = true;
          }
        "
        @click-print-barcode="printBarcode('barcode', $event)"
        @click-print-qrcode="printBarcode('qrcode', $event)"
      />

      <product-create-dialog
        v-model="product_create_dialog.show"
        @created="onProductCreated($event)"
      />
      <product-update-dialog
        v-model="product_update_dialog.show"
        :product="product_update_dialog.product"
        @updated="onProductUpdated($event)"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { saveAs } from 'file-saver'
import Vue from 'vue'
import { debounce } from 'lodash'
import ProductCreateDialog from '~/components/products/ProductCreateDialog.vue'
import ProductDataTable from '~/components/products/ProductDataTable.vue'
import ProductUpdateDialog from '~/components/products/ProductUpdateDialog.vue'
import ProductUploadForm from '~/components/products/ProductUploadForm.vue'
import SyncProductsMenu from '~/components/products/SyncProductsMenu.vue'
import { FEATURE_FLAGS, Product, VDataTableOption, VForm } from '~/models'
import { getDynamicRESTPagination, getDynamicRESTSorting } from '~/utils'
import { SettingState } from '~/store/settings'
export default Vue.extend({
  components: {
    ProductUploadForm,
    ProductDataTable,
    ProductCreateDialog,
    ProductUpdateDialog,
    SyncProductsMenu
  },
  data () {
    return {
      table: {
        loading: false,
        meta: {
          total_results: 0,
          total_pages: 1,
          page: 1,
          per_page: 10
        },
        search: '',
        options: {
          page: 1,
          itemsPerPage: 10,
          sortBy: ['sku'],
          sortDesc: [false],
          groupBy: [],
          groupDesc: [],
          multiSort: false,
          mustSort: false
        } as VDataTableOption
      },
      products: [] as any[],
      form: {
        file: null as File | null,
        errors: []
      },

      product_create_dialog: {
        show: false
      },
      product_update_dialog: {
        show: false,
        product: null as Product | null
      },
      debounceGetProducts: null as Function | null
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },

  watch: {
    'table.options': {
      handler () {
        if (!this.table.loading) {
          this.getProducts()
        }
      },
      deep: true
    },

    'table.search': {
      handler () {
        if (this.debounceGetProducts) {
          this.debounceGetProducts()
        }
      }
    }
  },

  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.PRODUCT_MANAGEMENT)) {
      this.$nuxt.error({
        statusCode: 400,
        message: this.$t('error.feature_flag')
      })
    }
    this.getProducts()
    this.debounceGetProducts = debounce(() => {
      this.table.options.page = 1
      this.getProducts()
    }, 500)
  },

  methods: {
    async getProducts () {
      const params: any = {
        ...getDynamicRESTPagination(this.table.options),
        ...getDynamicRESTSorting(this.table.options)
      }

      if (this.table.search) {
        params.search = this.table.search
      }

      this.table.loading = true
      try {
        const result = await this.$axios.$get(
          '/api/picking/resource/products/',
          { params }
        )
        this.products = result.products

        this.table.meta = result.meta
      } catch (error) {
        console.error(error)
      }
      this.table.loading = false
    },

    async deleteProduct (product: Product) {
      const confirm = await this.$confirm({
        title: this.$t('confirm.confirm-delete'),
        text: 'เมื่อลบแล้วไม่สามารถกูคืนได้ ท่านต้องการลบรายการนี้ใช่หรือไม่',
        theme: 'error'
      })

      if (!confirm) {
        return
      }

      try {
        await this.$axios.$delete(
          `/api/picking/resource/products/${product.id}/`
        )
        this.products = this.products.filter(
          (p: Product) => p.id !== product.id
        )
      } catch (error) {
        console.error(error)
        this.$snackbar('error', this.$t('error.unknown'))
      }
    },

    async uploadProductFile () {
      const form = this.$refs.form as never as VForm
      if (!form.validate()) {
        return
      }

      this.form.errors = []
      const formData = new FormData()
      if (this.form.file) {
        formData.append('file', this.form.file)
      }

      this.$loader(true)
      try {
        await this.$axios.request({
          method: 'POST',
          url: '/api/picking/products/import/',
          data: formData,
          headers: { 'Content-Type': 'multipart/form-data' }
        })
        this.$snackbar('success', this.$t('home.upload-success'))
        this.getProducts()
      } catch (error: any) {
        console.error(error)
        if (error.response) {
          if (error.response.status === 400) {
            const err_data = error.response.data
            if (err_data.unexpected_headers || err_data.expected_headers) {
              const headersList = `
              <p>กรุณาตรวจสอบความถูกต้องของหัวคอลัมน์ในไฟล์ของคุณ หรือ <a href="https://storage.googleapis.com/dobybot-public-bucket/example/importdata/003-example_sku_barcode_map_v2.xlsx" download>ดาวน์โหลดไฟล์ตัวอย่าง</a>เพื่อดูรูปแบบที่ถูกต้อง</p>
              <br/>
              <div style="display: flex; flex-wrap: wrap; justify-content: space-between;">
                <div>
                  <p style="color: red"><u>หัวคอลัมน์ที่ไม่ถูกต้อง</u></p>
                  <ul>
                    ${err_data.unexpected_headers.map((header: any) => `<li>${header}</li>`).join('')}
                  </ul>
                </div>
                <div>
                  <p style="color: green"><u>รูปแบบหัวคอลัมน์ที่ถูกต้อง</u></p>
                  <ul>
                    <li>sku (Required)</li> 
                    <li>name</li>
                    <li>price </li>
                    <li>width</li>
                    <li>length</li>
                    <li>weight</li>
                    <li>height</li>
                    <li>vat_percent</li>
                    <li>show_qrcode_on_record_page</li>
                  </ul>
                </div>
              <div>
            `
              this.$alert({
                title: 'ระบบพบปัญหากับหัวคอลัมน์',
                text: headersList, // Use html instead of text to render HTML content
                theme: 'error'
              })
            } else {
              this.$snackbar('error', this.$t('error.please_fix_form_error'))
              this.form.errors = err_data
            }
          }
        } else {
          this.$snackbar('error', this.$t('error.unknown'))
        }
      }
      this.$loader(false)
    },

    async exportProductXlsx () {
      this.$loader(true)
      try {
        const response = await this.$axios.get(
          '/api/picking/products/export/',
          { responseType: 'blob' }
        )
        saveAs(response.data, 'products.xlsx')
      } catch (error) {
        console.error(error)
      }
      this.$loader(false)
    },

    onProductUpdated (product: Product) {
      this.product_update_dialog.show = false

      const index = this.products.findIndex(
        (p: Product) => p.id === product.id
      )
      this.products.splice(index, 1, product)
    },

    onProductCreated (product: Product) {
      this.products.unshift(product)
      this.table.meta.total_results++
    },

    printBarcode (mode: string, product: Product) {
      const baseURL = this.$axios.defaults.baseURL
      window.open(
        `${baseURL}/printing/barcode-page/?barcode=${product.barcode}&mode=${mode}&name=${product.name}`,
        '_blank'
      )
    }
  }
})
</script>
