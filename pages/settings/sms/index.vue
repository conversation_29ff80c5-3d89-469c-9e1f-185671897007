<template>
  <setting-template ref="base" :title="$t('settings.setting-sms')">
    <template #overview>
      ตั้งค่าการส่งลิงค์วิดีโอผ่าน SMS / Email / Facebook Chat / Shopee Chat /
      <PERSON><PERSON><PERSON>t
    </template>

    <template #form="{ settings, update }">
      <!-- SMS -->
      <v-row
        v-package="$package.FULL_INTEGRATION"
        dense
        no-gutters
        class="mt-4 pt-4"
      >
        <v-col cols="12">
          <h2>SMS</h2>
          <div>{{ $t("settings.sms-support") }}</div>
        </v-col>
        <v-col cols="12">
          <v-text-field
            :label="$t('settings.name-sender')"
            append-icon="mdi-lock"
            filled
            persistent-hint
            disabled
            hide-details
            :value="settings.company.SMS_SENDER"
          />
        </v-col>
      </v-row>

      <!-- Send SMS Ready to ship -->
      <v-row v-package="$package.FULL_INTEGRATION" dense no-gutters>
        <v-col cols="12">
          <v-checkbox
            :label="$t('settings.send-link-status')"
            hide-details
            :input-value="settings.company.POST_RECORD_ACTION_SEND_SMS"
            @change="update({ POST_RECORD_ACTION_SEND_SMS: $event })"
          />
        </v-col>
        <v-col cols="12" md="6" class="px-1 pt-2 pl-8">
          <v-textarea
            id="textarea-ready-to-ship-message"
            :label="$t('settings.sms-notification')"
            filled
            rows="4"
            persistent-hint
            :counter="70"
            :rules="[$rules.maxlength(70)]"
            :value="settings.company.SMS_READY_TO_SHIP_MESSAGE"
            @change="update({ SMS_READY_TO_SHIP_MESSAGE: $event })"
          />
        </v-col>
        <v-col cols="12" md="6" class="px-1 pt-2">
          <div>
            <!-- 70 ตัวอักษรเท่ากับ 1 sms credit<br> -->
            {{ $t("settings.variables-sms") }}
            <ul>
              <li>{{ $t("settings.customer-name") }}</li>
              <li>{{ $t("settings.order-number") }}</li>
              <li>{{ $t("settings.tracking_no-tracking") }}</li>
              <li>{{ $t("settings.receipt_link-shorten-url") }}</li>
              <li>{{ $t("settings.shipping_channel") }}</li>
              <li>{{ $t("settings.sms-shop-name") }}</li>
            </ul>
          </div>
        </v-col>
      </v-row>

      <v-row
        v-if="settings.company.POST_RECORD_ACTION_SEND_SMS"
        v-package="$package.FULL_INTEGRATION"
        dense
        no-gutters
        class="pl-8"
      >
        <div class="px-1">
          <div class="font-weight-bold">
            ปิดการส่ง SMS เฉพาะร้านค้า
          </div>
          ระบบจะไม่ส่ง SMS Ready To Ship
          หาลูกค้าที่ซื้อของผ่านทางร้านค้าที่เลือก (SMS FixCase ยังส่งตามปกติ)
        </div>
        <v-col cols="12" class="mt-1 px-1">
          <v-autocomplete
            label="เลือกร้านค้าที่ปิดการส่ง SMS"
            class="mt-2"
            filled
            multiple
            chips
            :items="saleschannel_choices"
            :value="settings.company.SALES_CHANNEL_SMS_BLOCKED_LIST"
            @change="update({ SALES_CHANNEL_SMS_BLOCKED_LIST: $event })"
          />
        </v-col>
      </v-row>

      <v-row
        v-if="$isPim()"
        v-package="$package.FULL_INTEGRATION"
        dense
        no-gutters
      >
        <v-col cols="12">
          <v-checkbox
            :label="$t('settings.sms-ready-to-ship')"
            hide-details
            :input-value="settings.company.MULTI_PACKAGE_READY_TO_SHIP_SEND_SMS"
            @change="update({ MULTI_PACKAGE_READY_TO_SHIP_SEND_SMS: $event })"
          />
        </v-col>
        <v-col cols="12" md="6" class="px-1 pt-2 pl-8">
          <v-textarea
            :label="$t('settings.ready-to-ship')"
            filled
            rows="3"
            persistent-hint
            :counter="100"
            :rules="[$rules.maxlength(100)]"
            :value="settings.company.SMS_MULTI_PACKAGE_MESSAGE"
            @change="update({ SMS_MULTI_PACKAGE_MESSAGE: $event })"
          />
        </v-col>
        <v-col cols="12" md="6" class="px-1 pt-2">
          <div>
            <ul>
              <li>{{ $t("settings.customer-name") }}</li>
              <li>{{ $t("settings.store-company") }}</li>
              <li>{{ $t("settings.order_number") }}</li>
              <li>{{ $t("settings.tracking_no-tracking") }}</li>
              <li>{{ $t("settings.total-packages") }}</li>
            </ul>
          </div>
        </v-col>
      </v-row>

      <!-- Send SMS Fix Case -->
      <v-row v-package="$package.FULL_INTEGRATION" dense no-gutters>
        <v-col cols="12">
          <v-checkbox
            :label="$t('settings.sms-fix-case')"
            hide-details
            :input-value="settings.company.FIXCASE_SEND_OPEN_MESSAGE"
            @change="update({ FIXCASE_SEND_OPEN_MESSAGE: $event })"
          />
        </v-col>
        <v-col cols="12" md="6" class="px-1 pt-2 pl-8">
          <v-textarea
            :label="$t('settings.sms-fixcase-customer')"
            filled
            rows="3"
            persistent-hint
            :rules="[$rules.maxlength(120)]"
            :counter="120"
            :value="settings.company.SMS_FIXCASE_OPEN_MESSAGE"
            @change="update({ SMS_FIXCASE_OPEN_MESSAGE: $event })"
          />
        </v-col>
        <v-col cols="12" md="6" class="px-1 pt-2">
          <div>
            {{ $t("settings.variables-sms") }}
            <ul>
              <li>{{ $t("settings.customer-name") }}</li>
              <li>{{ $t("settings.store-company") }}</li>
              <li>{{ $t("settings.order_number") }}</li>
            </ul>
          </div>
        </v-col>

        <v-col cols="12">
          <v-checkbox
            :label="$t('sms-fix-case-closed')"
            hide-details
            :input-value="settings.company.FIXCASE_SEND_CLOSE_MESSAGE"
            @change="update({ FIXCASE_SEND_CLOSE_MESSAGE: $event })"
          />
        </v-col>
        <v-col cols="12" md="6" class="px-1 pt-2 pl-8">
          <v-textarea
            :label="$t('sms-fixcase-sms-to-customer')"
            filled
            rows="3"
            persistent-hint
            :rules="[$rules.maxlength(120)]"
            :counter="120"
            :value="settings.company.SMS_FIXCASE_CLOSE_MESSGE"
            @change="update({ SMS_FIXCASE_CLOSE_MESSGE: $event })"
          />
        </v-col>
        <v-col cols="12" md="6" class="px-1 pt-2">
          <div>
            {{ $t("settings.variables-sms") }}
            <ul>
              <li>{{ $t("settings.customer-name") }}</li>
              <li>{{ $t("settings.store-company") }}</li>
              <li>{{ $t("settings.order_number") }}</li>
              <li>{{ $t("settings.fixcase-close-message") }}</li>
            </ul>
          </div>
        </v-col>
      </v-row>

      <!-- Line Official Notification (LON) -->
      <div v-package="$package.FULL_INTEGRATION" class="mt-4 pt-4">
        <v-row dense no-gutters>
          <v-col cols="12">
            <h2>Line Official Notification (LON)</h2>
            <div>
              ส่ง วิดีโอ/ใบเสร็จ ผ่านบริการ Line Official Notification
              ด้วยเบอร์โทรศัพท์มือถือ
              <!-- <a href="https://www.dobybot.com/ordernotice" target="_blank">เพิ่มเติม</a> -->
              <div>
                กรุณาติดต่อ
                <a
                  href="https://lin.ee/012QqmH"
                  class="teal--text"
                >LINE @dobybot</a>
                เพื่อเปิดใช้งานฟีเจอร์นี้
              </div>
            </div>
            <!-- <v-img src="https://lon.ants.co.th/img/templateImages/UPDATE_ORDER_STATUS.png" max-width="200" class="ml-8 my-4" /> -->
            <v-col cols="12" md="6">
              <v-img class="ml-8 my-4" src="/lon/lonheader.png" />
            </v-col>
          </v-col>
        </v-row>

        <!-- LON Ready To Ship Message -->
        <v-row dense no-gutters>
          <v-col cols="12">
            <v-checkbox
              hide-details
              disabled
              :label="$t('settings.send-ready-to-ship-lon-message')"
              :input-value="settings.company.POST_RECORD_ACTION_SEND_LON"
              @change="update({ POST_RECORD_ACTION_SEND_LON: $event })"
            />
          </v-col>
          <v-col cols="12" md="6" class="px-1 pt-2 pl-8">
            <v-textarea
              id="textarea-ready-to-ship-message"
              :label="$t('settings.lon-remark-message')"
              filled
              rows="3"
              :value="settings.company.LON_READY_TO_SHIP_REMARK_MESSAGE"
              disabled
              hide-details=""
              @change="update({ LON_READY_TO_SHIP_REMARK_MESSAGE: $event })"
            />
            <v-text-field
              label="More Detail... (Link)"
              filled
              class="mt-2"
              disabled
              :value="settings.company.LON_MORE_DETAIL_LINK"
              @change="update({ LON_MORE_DETAIL_LINK: $event })"
            />
          </v-col>
          <v-col cols="12" md="6" class="px-1 pt-2">
            <div>
              <!-- 70 ตัวอักษรเท่ากับ 1 sms credit<br> -->
              {{ $t("settings.variables-sms") }}
              <ul>
                <li>{{ $t("settings.customer-name") }}</li>
                <li>{{ $t("settings.order-number") }}</li>
                <li>{{ $t("settings.tracking_no-tracking") }}</li>
                <li>{{ $t("settings.shipping_channel") }}</li>
              </ul>
            </div>
          </v-col>
        </v-row>

        <!-- LON FixCase Open -->
        <v-row dense no-gutters>
          <v-col cols="12">
            <v-checkbox
              hide-details
              disabled
              :label="$t('settings.send-lon-open-fix-case')"
              :input-value="settings.company.FIXCASE_LON_SEND_OPEN_MESSAGE"
              @change="update({ FIXCASE_LON_SEND_OPEN_MESSAGE: $event })"
            />
          </v-col>
          <v-col cols="12" md="6" class="px-1 pt-2 pl-8">
            <v-textarea
              disabled
              filled
              rows="3"
              persistent-hint
              :value="settings.company.LON_FIXCASE_OPEN_MESSAGE"
              @change="update({ LON_FIXCASE_OPEN_MESSAGE: $event })"
            />
          </v-col>
          <v-col cols="12" md="6" class="px-1 pt-2">
            <div>
              {{ $t("settings.variables-sms") }}
              <ul>
                <li>{{ $t("settings.customer-name") }}</li>
                <li>{{ $t("settings.store-company") }}</li>
                <li>{{ $t("settings.order_number") }}</li>
              </ul>
            </div>
          </v-col>
        </v-row>

        <!-- LON FixCase Close -->
        <v-row dense no-gutters>
          <v-col cols="12">
            <v-checkbox
              disabled
              hide-details
              :label="$t('settings.send-lon-close-fix-case')"
              :input-value="settings.company.FIXCASE_LON_SEND_CLOSE_MESSAGE"
              @change="update({ FIXCASE_LON_SEND_CLOSE_MESSAGE: $event })"
            />
          </v-col>
          <v-col cols="12" md="6" class="px-1 pt-2 pl-8">
            <v-textarea
              filled
              rows="3"
              persistent-hint
              disabled
              :value="settings.company.LON_FIXCASE_CLOSE_MESSAGE"
              @change="update({ LON_FIXCASE_CLOSE_MESSAGE: $event })"
            />
          </v-col>
          <v-col cols="12" md="6" class="px-1 pt-2">
            <div>
              {{ $t("settings.variables-sms") }}
              <ul>
                <li>{{ $t("settings.customer-name") }}</li>
                <li>{{ $t("settings.store-company") }}</li>
                <li>{{ $t("settings.order_number") }}</li>
                <li>{{ $t("settings.fixcase-close-message") }}</li>
              </ul>
            </div>
          </v-col>
        </v-row>
      </div>

      <!-- Facebook Chat -->
      <v-row
        v-package="$package.FULL_INTEGRATION"
        dense
        no-gutters
        class="mt-4 pt-4"
      >
        <v-col cols="12">
          <h2>Facebook Chat (VRich)</h2>
          <div>
            สำหรับคำสั่งซื้อที่มาจาก VRich ส่งข้อความทาง Facebook Chat
            <ul>
              <li>ต้องเชื่อมต่อ VRich และตั้งค่าให้ถูกต้อง</li>
              <li>
                ใช้ข้อความเดียวกับ
                <a @click.prevent="scrollTo('#textarea-ready-to-ship-message')">
                  {{ $t("settings.sms-notification") }}
                </a>
              </li>
              <li>สามารถส่งข้อความทั้งทาง SMS และ Facebook ได้พร้อมกัน</li>
            </ul>
          </div>
        </v-col>
        <v-col cols="12">
          <v-checkbox
            :disabled="!settings.company.VRICH_HOST"
            :label="$t('settings.facebook-messenger')"
            :input-value="
              settings.company.VRICH_READY_TO_SHIP_SEND_FACEBOOK_MESSAGE
            "
            @change="
              update({ VRICH_READY_TO_SHIP_SEND_FACEBOOK_MESSAGE: $event })
            "
          />
        </v-col>
      </v-row>

      <!-- Shopee Chat -->
      <v-row
        v-package="$package.FULL_INTEGRATION"
        dense
        no-gutters
        class="mt-4 pt-4"
      >
        <v-col cols="12">
          <h2>Shopee Chat</h2>
          <div>
            สำหรับคำสั่งซื้อที่มาจาก Shopee ส่งข้อความทาง Shopee Chat
            เมื่อสถานะออเดอร์เปลี่ยนเป็น Ready to ship
            <ul>
              <li>ต้องเชื่อมต่อ "ร้านค้าของท่าน" เข้ากับ Shopee Chat API</li>
              <li>
                ในลิงค์ที่ส่งผ่าน Shopee Chat API
                จะไม่สามารถแสดงโฆษณาท้ายใบเสร็จได้
              </li>
            </ul>
          </div>
        </v-col>
        <v-col cols="12" class="pt-3">
          <chat-api-setting-table
            :items="shopee_shops"
            :value="settings.company.SHOPEE_CHAT_API"
            @input="update({ SHOPEE_CHAT_API: $event })"
            @click-reconnect="connectShopeeChatAPI()"
          >
            <template #footer.prepend>
              <v-btn
                small
                outlined
                color="primary"
                @click="connectShopeeChatAPI()"
              >
                เชื่อมต่อ Shopee Chat API
              </v-btn>
            </template>
          </chat-api-setting-table>
        </v-col>
        <v-col cols="12" md="6" class="px-1 pt-3">
          <v-select
            filled
            :label="$t('settings.shopee-notification')"
            :rules="[
              $rules.required_if(hasTrue(settings.company.SHOPEE_CHAT_API)),
            ]"
            :items="example_chat_messages"
            :value="settings.company.SHOPEE_READY_TO_SHIP_MESSAGE"
            item-value="text"
            @change="update({ SHOPEE_READY_TO_SHIP_MESSAGE: $event })"
          >
            <template #selection="props">
              <div class="py-2">
                {{ props.item.title }}
              </div>
            </template>
            <template #item="props">
              <div class="py-2 message-container" style="max-width: 600px">
                <h4>{{ props.item.title }}</h4>
                <div style="white-space: pre-line" class="message-content">
                  {{ props.item.text }}
                </div>
              </div>
            </template>
          </v-select>
          <div class="message-container">
            <h4>ตัวอย่างข้อความ</h4>
            <span style="white-space: pre-line" class="message-content">
              {{ settings.company.SHOPEE_READY_TO_SHIP_MESSAGE }}
            </span>
          </div>
        </v-col>
        <v-col cols="12" md="6" class="px-1 pt-2">
          <div>
            {{ $t("settings.variable-explanation") }}
            <ul>
              <li>{{ $t("settings.customer-name") }}</li>
              <li>{{ $t("settings.order-number") }}</li>
              <li>{{ $t("settings.tracking_no-tracking") }}</li>
              <li>{{ $t("settings.receipt_link-shorten-url") }}</li>
              <li>{{ $t("settings.shipping_channel") }}</li>
            </ul>
          </div>
        </v-col>
      </v-row>

      <!-- Lazada Chat -->
      <v-row
        v-package="$package.FULL_INTEGRATION"
        dense
        no-gutters
        class="mt-4 pt-4"
      >
        <v-col cols="12">
          <h2>Lazada Chat</h2>
          <div>
            สำหรับคำสั่งซื้อที่มาจาก Lazada ส่งข้อความทาง Lazada Chat
            เมื่อสถานะออเดอร์เปลี่ยนเป็น Ready to ship
            <ul>
              <li>ต้องเชื่อมต่อ "ร้านค้าของท่าน" เข้ากับ Lazada Chat API</li>
              <li>
                ในลิงค์ที่ส่งผ่าน Lazada Chat API
                จะไม่สามารถแสดงโฆษณาท้ายใบเสร็จได้
              </li>
            </ul>
          </div>
        </v-col>
        <v-col cols="12" class="pt-3">
          <chat-api-setting-table
            :items="lazada_shops"
            :value="settings.company.LAZADA_CHAT_API"
            @input="update({ LAZADA_CHAT_API: $event })"
            @click-reconnect="connectLazadaChatAPI()"
          >
            <template #footer.prepend>
              <v-btn
                small
                outlined
                color="primary"
                @click="connectLazadaChatAPI()"
              >
                เชื่อมต่อ Lazada Chat API
              </v-btn>
            </template>
          </chat-api-setting-table>
        </v-col>
        <v-col cols="12" md="6" class="px-1 pt-3">
          <v-select
            filled
            :label="$t('settings.lazada-notification')"
            :rules="[
              $rules.required_if(hasTrue(settings.company.LAZADA_CHAT_API)),
            ]"
            :items="example_chat_messages"
            :value="settings.company.LAZADA_READY_TO_SHIP_MESSAGE"
            item-value="text"
            @change="update({ LAZADA_READY_TO_SHIP_MESSAGE: $event })"
          >
            <template #selection="props">
              <div class="py-2">
                {{ props.item.title }}
              </div>
            </template>
            <template #item="props">
              <div class="py-2 message-container" style="max-width: 600px">
                <h4>{{ props.item.title }}</h4>
                <div style="white-space: pre-line" class="message-content">
                  {{ props.item.text }}
                </div>
              </div>
            </template>
          </v-select>
          <div class="message-container">
            <h4>ตัวอย่างข้อความ</h4>
            <span style="white-space: pre-line" class="message-content">
              {{ settings.company.LAZADA_READY_TO_SHIP_MESSAGE }}
            </span>
          </div>
        </v-col>
        <v-col cols="12" md="6" class="px-1 pt-2">
          <div>
            {{ $t("settings.variable-explanation") }}
            <ul>
              <li>{{ $t("settings.customer-name") }}</li>
              <li>{{ $t("settings.order-number") }}</li>
              <li>{{ $t("settings.tracking_no-tracking") }}</li>
              <li>{{ $t("settings.receipt_link-shorten-url") }}</li>
              <li>{{ $t("settings.shipping_channel") }}</li>
            </ul>
          </div>
        </v-col>
      </v-row>
    </template>
  </setting-template>
</template>

<script lang="ts">
import Vue from 'vue'
import SettingTemplate from '~/components/settings/SettingTemplate.vue'
import ConfirmExit from '~/mixins/confirm-exit'
import { openPopupWindow } from '~/utils/popup'
import { FEATURE_FLAGS, Marketplace } from '~/models'
import ChatApiSettingTable from '~/components/settings/sms/ChatApiSettingTable.vue'
import { wait } from '~/utils/timer'
export default Vue.extend({
  components: {
    SettingTemplate,
    ChatApiSettingTable
  },

  mixins: [ConfirmExit],
  middleware: ['auth'],

  data () {
    return {
      chat_marketplaces: [] as Marketplace[],
      etax_chat_messages: [
        {
          title: 'ข้อความรูปแบบที่ 1 (e-Tax)',
          text: 'ออเดอร์ {order_number} ของคุณได้ทำการแพคเสร็จแล้ว สามารถตรวจสอบคลิปการแพ็ค และ ขอใบกำกับภาษีอิเล็กทรอนิกส์ (e-Tax) ได้ที่นี่ {receipt_link} หากพบว่ามีข้อผิดพลาด สามารถแจ้งกลับมาได้ทันทีค่ะ'
        },
        {
          title: 'ข้อความรูปแบบที่ 2 (e-Tax)',
          text: 'ออเดอร์ {order_number} ของคุณแพ็คเสร็จแล้ว ตรวจสอบคลิปการแพ็คและหมายเลขติดตามสินค้ารวมถึงการ ขอใบกำกับภาษีอิเล็กทรอนิกส์ (e-Tax) ได้ที่นี่ {receipt_link} หากพบว่ามีข้อผิดพลาด สามารถแจ้งกลับมาได้ทันทีค่ะ'
        }
      ],
      chat_messages: [
        {
          title: 'ข้อความรูปแบบที่ 1',
          text: 'ออเดอร์ {order_number} ของคุณได้ทำการแพคเสร็จแล้ว\nสามารถตรวจสอบคลิปการแพคได้ที่นี่ {receipt_link} หากพบว่ามีข้อผิดพลาด สามารถแจ้งกลับมาได้ทันทีค่ะ'
        },
        {
          title: 'ข้อความรูปแบบที่ 2',
          text: 'ออเดอร์ {order_number} ของคุณแพ็กเสร็จแล้ว หมายเลขติดตามสินค้า คือ {tracking_no} และสามารถตรวจสอบคลิปการแพ็กได้ที่นี่ {receipt_link} หากพบว่ามีข้อผิดพลาด สามารถแจ้งกลับมาได้ทันทีค่ะ'
        },
        {
          title: 'ข้อความรูปแบบที่ 3',
          text: 'ออเดอร์ {order_number} ของคุณแพ็คเสร็จแล้ว ตรวจสอบคลิปการแพค และหมายเลขติดตามสินค้าได้ที่นี่ {receipt_link} หากพบว่ามีข้อผิดพลาด สามารถแจ้งกลับมาได้ทันทีค่ะ ในช่วงนี้การจัดส่งอาจจะล่าช้ากว่าปกติ 1-2 วัน ต้องขออภัยในความไม่สะดวกค่ะ'
        },
        {
          title: 'ข้อความรูปแบบที่ 4',
          text: 'ออเดอร์ {order_number} ของคุณได้ทำการแพคเสร็จแล้ว'
        },
        {
          title: 'ข้อความรูปแบบที่ 5',
          text: 'ออเดอร์ {order_number} ขอขอบพระคุณ คุณลูกค้ามากครับ สินค้าแพ็คเสร็จแล้ว ตรวจสอบคลิปการแพค และ ตัวสินค้า หมายเลขติดตามสินค้าได้ที่นี่ {receipt_link} หากพบว่ามีข้อผิดพลาด สามารถแจ้งกลับมาได้เลยนะครับ หวังว่าการบริการครั้งนี้จะถูกใจลูกค้า และหวังว่าลูกค้าจะให้ รีวิว 5 ดาว เพื่อเป็นกำลังใจให้ทีมงาน ALAIROD ด้วยนะครับ'
        },
        {
          title: 'ข้อความรูปแบบที่ 6',
          text: 'ออเดอร์ {order_number} ขอขอบพระคุณ คุณลูกค้ามากค่ะ สินค้าแพ็คเสร็จแล้ว ตรวจสอบคลิปการแพค และ ตัวสินค้า หมายเลขติดตามสินค้าได้ที่นี่ {receipt_link} หากพบว่ามีข้อผิดพลาด สามารถแจ้งกลับมาได้เลยนะคะ หวังว่าการบริการครั้งนี้จะถูกใจลูกค้า และหวังว่าลูกค้าจะให้ รีวิว 5 ดาว เพื่อเป็นกำลังใจให้ทีมงาน What the beans ด้วยนะคะ'
        },
        {
          title: 'ข้อความรูปแบบที่ 7',
          text: 'ออเดอร์ {order_number} ของคุณลูกค้าแพคเรียบร้อยแล้วค่ะ สามารถตรวจสอบคลิปการแพคและสินค้าได้ในลิงค์นี้เลยนะคะ {receipt_link} หากพบว่ามีข้อผิดพลาด สามารถแจ้งกลับมาได้เลยนะคะ หวังว่าคุณลูกค้าจะประทับใจในบริการของทางร้านนะคะและหวังเป็นอย่างยิ่งว่าจะได้บริการคุณลูกค้าอีกเรื่อยๆค่า หากไม่เป็นการรบกวน ฝากให้5ดาวเป็นกำลังใจให้ทางร้านด้วยนะคะ ขอบพระคุณมากๆค่ะ'
        },
        {
          title: 'ข้อความรูปแบบที่ 8',
          text: 'ออเดอร์ {order_number} ของคุณลูกค้าแพคเรียบร้อยแล้วค่ะ สามารถตรวจสอบคลิปการแพคและสินค้าได้ในลิงค์นี้เลยนะคะ {receipt_link} หากพบว่ามีข้อผิดพลาด สามารถแจ้งกลับมาได้เลยนะคะ หวังว่าคุณลูกค้าจะประทับใจในบริการของทางร้านนะคะและหวังเป็นอย่างยิ่งว่าจะได้บริการคุณลูกค้าอีกเรื่อยๆค่า หากไม่เป็นการรบกวน ฝากให้5ดาวเป็นกำลังใจให้ทางร้านด้วยนะคะ ญาซากิลลาฮมากๆนะคะ'
        },
        {
          title: 'ข้อความรูปแบบที่ 9',
          text: 'ออเดอร์ {order_number} หมายเลขแทรกกิ้ง {shipping_channel} {tracking_no} ของคุณได้ทำการแพคเสร็จแล้ว สามารถตรวจสอบคลิปการแพคได้ที่นี่ {receipt_link} หากพบว่ามีข้อผิดพลาด สามารถแจ้งกลับมาได้ทันทีค่ะ'
        },
        {
          title: 'ข้อความรูปแบบที่ 10',
          text: 'ออเดอร์ {order_number} ของคุณถูกแพ็คเรียบร้อยแล้วค่ะ! 📦✨\n หากพบข้อผิดพลาดหรือมีข้อสงสัย สามารถแจ้งทางร้านได้ทันที เรายินดีดูแลค่ะ 💕\n\n เราหวังเป็นอย่างยิ่งว่าคุณลูกค้าจะประทับใจกับสินค้าและบริการของเรา และจะได้มีโอกาสให้บริการอีกครั้งเร็ว ๆ นี้นะคะ 😊\n\n 📌 ฝากกดติดตาม คอมเมนต์รีวิว และให้ 5 ดาว ⭐⭐⭐⭐⭐ เป็นกำลังใจให้ทางร้านด้วยนะคะ\n 🎥 สามารถตรวจสอบวิดีโอการแพ็คสินค้าของคุณได้ที่ลิงก์นี้ 👉 {receipt_link}\n\n ขอบพระคุณจากใจ ❤️🙏\n #ขอบคุณที่อุดหนุน #บริการด้วยใจ'
        },
        {
          title: 'ข้อความรูปแบบที่ 11',
          text: 'ออเดอร์ {order_number} ของคุณลูกค้าแพคเรียบร้อยแล้วค่ะ สามารถตรวจสอบคลิปการแพคและสินค้าได้ในลิงก์นี้เลยนะคะ {receipt_link} หลังจากได้รับสินค้าแล้วกรุณาตรวจเช็คชิ้นส่วนแผงรันเนอร์โดยไม่แกะซองพลาสติกและหากพบว่ามีข้อผิดพลาด สามารถแจ้งกลับมาได้เลยนะคะ หวังว่าคุณลูกค้าจะประทับใจในบริการของทางร้านนะคะและหวังเป็นอย่างยิ่งว่าจะได้บริการคุณลูกค้าอีกเรื่อยๆค่า หากไม่เป็นการรบกวน ฝากให้ 5 ดาวเป็นกำลังใจให้ทางร้านด้วยนะคะ ขอบพระคุณมากๆค่ะ'
        },
        {
          title: 'ข้อความรูปแบบที่ 12',
          text: 'ออเดอร์ของคุณลูกค้าถูกจัดเตรียมเรียบร้อยแล้วค่ะ 🎁\n สามารถดูคลิปการแพ็คได้ที่นี่เลยนะคะ 👉 {receipt_link}\n หากต้องการขอใบกำกับภาษี สามารถกรอกข้อมูลได้ที่ลิงก์เดียวกันนี้เลยค่ะ 🥰'
        },
        {
          title: 'ข้อความรูปแบบที่ 13',
          text: 'ออเดอร์ {order_number} ของคุณได้ทำการแพ็คเสร็จแล้ว สามารถตรวจสอบคลิปการแพ็คได้ที่นี่ {receipt_link}\n\n หมายเหตุ 📦 Tavi Cold Brew จัดส่งออเดอร์ส่งด่วนหรือส่งทันที\n ด้วยถุงพลาสติกคุณภาพสูง แข็งแรง ไม่ฉีกขาดระหว่างทาง\n เพื่อให้สินค้าถึงมือคุณอย่างรวดเร็วและปลอดภัยที่สุด\n รอรับความสดชื่นได้เลย ขอบคุณที่ไว้วางใจ Tavi Cold Brew ค่ะ 💙'
        }
      ],
      saleschannel_choices: []
    }
  },

  computed: {
    shopee_shops (): Marketplace[] {
      return this.chat_marketplaces.filter(
        x => x.marketplace === 'shopee_chat'
      )
    },
    lazada_shops (): Marketplace[] {
      return this.chat_marketplaces.filter(
        x => x.marketplace === 'lazada_chat'
      )
    },
    example_chat_messages (): { title: string; text: string }[] {
      if (this.$hasCompanyFeatureFlag(FEATURE_FLAGS.ETAX)) {
        return [...this.etax_chat_messages, ...this.chat_messages]
      }

      return [...this.chat_messages]
    }
  },

  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.SMS_CHAT)) {
      this.$nuxt.error({
        statusCode: 400,
        message: this.$t('error.feature_flag')
      })
    }
  },

  mounted () {
    this.getChoices('saleschannel_choices')
    this.getChatMarketplaces()
  },

  methods: {
    async getChatMarketplaces () {
      const shops: Marketplace[] = (
        await this.$axios.get('/api/companies/marketplaces/')
      ).data
      this.chat_marketplaces = shops.filter(
        x =>
          x.marketplace === 'shopee_chat' || x.marketplace === 'lazada_chat'
      )
    },

    async connectShopeeChatAPI () {
      const url = (
        await this.$axios.get(
          '/api/companies/dobybot-connect/seller/auth/shopee-chat/'
        )
      ).data
      openPopupWindow(url, 1200, 760, async () => {
        this.$loader(true)
        await wait(1000)
        await this.getChatMarketplaces()
        this.$loader(false)
      })
    },

    async connectLazadaChatAPI () {
      const url = (
        await this.$axios.get(
          '/api/companies/dobybot-connect/seller/auth/lazada-chat/'
        )
      ).data
      openPopupWindow(url, 1200, 760, async () => {
        this.$loader(true)
        await wait(1000)
        await this.getChatMarketplaces()
        this.$loader(false)
      })
    },

    hasUnsavedChanges () {
      return (this.$refs.base as never as any).hasUnsavedChanges()
    },

    scrollTo (selector: string) {
      const el: any = document.querySelector(selector)
      if (el) {
        el.scrollIntoView({ block: 'center', inline: 'nearest' })
        el.focus()
      }
    },

    hasTrue (obj: object) {
      return Object.values(obj).some(x => x)
    },

    async getChoices (
      choice_name:
        | 'warehouse_choices'
        | 'shippingchannel_choices'
        | 'saleschannel_choices'
    ) {
      // @ts-ignore
      this[choice_name] = (
        await this.$axios.get(`/api/companies/choices/${choice_name}/`)
      ).data
    }
  }
})
</script>

<style>
.message-container {
  padding: 0px;
}

.message-content {
  display: block;
}

h4 {
  margin: 0;
  padding: 0;
}
</style>
