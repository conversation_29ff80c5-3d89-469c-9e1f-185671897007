<template>
  <setting-template ref="base" title="JamboLive API">
    <template #form="{ settings, update }">
      <h2>API Token</h2>
      <div class="my-5">
        <h5>Token</h5>
        {{ token.token }}

        <h5 class="mt-4">
          Username
        </h5>
        {{ token.username }}

        <h5 class="mt-4">
          Permissions
        </h5>
        <ul>
          <li v-for="perm in token.permissions" :key="perm">
            {{ perm }}
          </li>
        </ul>
      </div>

      <v-btn color="error" outlined small @click="axiosDeleteJamboliveToken()">
        REVOKE TOKEN
      </v-btn>
      <v-btn outlined small @click="axiosCreateJamboliveToken()">
        GENERATE TOKEN
      </v-btn>

      <v-divider class="my-5" />

      <h2>JamboLive Webhook</h2>
      <div>ระบบจะส่ง Webhook เฉพาะออเดอร์ที่ถูกสร้างด้วย Token ของ Jambolive เท่านั้น</div>
      <webhook-setting-form
        :allow-methods="['POST']"
        :example-payload="post_record_webhook.example_payload"
        :form-data="settings.company.JAMBOLIVE_WEBHOOK_CONFIG"
        @update:form-data="update({ JAMBOLIVE_WEBHOOK_CONFIG: $event })"
      />
    </template>
  </setting-template>
</template>

<script lang="ts">
import Vue from 'vue'
import SettingTemplate from '~/components/settings/SettingTemplate.vue'
import WebhookSettingForm from '~/components/apidoc/WebhookSettingForm.vue'
export default Vue.extend({
  components: {
    SettingTemplate,
    WebhookSettingForm
  },
  data () {
    return {
      token: {
        token: '',
        username: '',
        permissions: []
      },
      post_record_webhook: {
        example_payload: {
          id: 1,
          name: '6667009696',
          order_id: 1,
          order_customer: 'นาย ก สวัสดี',
          order_customerphone: '081XXXXXXX',
          order_data: {
            integrationName: 'lazada',
            integrationShop: '1100XXXXXX'
          },
          order_number: '6667009696',
          order_trackingno: 'TRACK00000XXXX',
          order_saleschannel: 'LAZADA ABC',
          video_url: 'https://drive.google.com/file/d/dighencpejfie-xxxx',
          receipt_url: 'https://cloud.dobybot.com/receipt/?t=adieldo-xxxx',
          file_size: 100000,
          duration: 60,
          resolution: '1280x720',
          record_date: '2021-10-31T19:28:43+07:00',
          upload_date: '2021-10-31T19:29:43+07:00',
          upload_by: {
            id: 1,
            full_name: 'admin',
            username: 'admin@test'
          },
          scan_logs: [
            { barcode: 'G001-DGBrownCap:1', time_ms: 5233, amount: 1 },
            { barcode: 'G001-DGBrownCap:1', time_ms: 5831, amount: 1 },
            { barcode: 'G005-DGTP', time_ms: 6264, amount: 1 },
            { barcode: 'G003-XDGStand', time_ms: 6749, amount: 1 },
            { barcode: 'G004-XDGWeight', time_ms: 7523, amount: 1 },
            { barcode: 'G004-XDGWeight', time_ms: 8081, amount: 1 },
            { barcode: 'G004-XDGWeight', time_ms: 8304, amount: 1 },
            { barcode: 'G004-XDGWeight', time_ms: 8500, amount: 1 }
          ],
          scan_logs_summary: {
            'G001-DGBrownCap:1': 2,
            'G005-DGTP': 1,
            'G003-XDGStand': 1,
            'G004-XDGWeight': 4
          }
        }
      }
    }
  },
  created () {
    if (!this.$auth.user?.is_superuser) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }
    this.axiosGetJamboliveToken()
  },
  methods: {
    async axiosGetJamboliveToken () {
      const res = await this.$axios.get('/api/users/jambolive-token/')
      this.token = res.data
    },

    async axiosCreateJamboliveToken () {
      const res = await this.$axios.post('/api/users/jambolive-token/')
      this.token = res.data
    },

    async axiosDeleteJamboliveToken () {
      await this.$axios.delete('/api/users/jambolive-token/')
      this.token = {
        token: '',
        username: '',
        permissions: []
      }
    }
  }

})
</script>
