<template>
  <setting-template ref="base" title="Dobybot Open API">
    <template #overview>
      <v-row>
        <v-col cols="10">
          <div>
            เชื่อมต่อ Dobybot กับระบบอื่น ๆ ผ่าน Dobybot Open API
          </div>
          <div>
            <ul>
              <!-- <li>
                <a href="#api-overview">API Overview</a>
              </li> -->
              <li>
                <a href="#api-doc">API Documentation</a>
              </li>
              <li>
                <a href="#webhook">Webhook</a>
              </li>
            </ul>
          </div>
        </v-col>
        <v-col cols="2" class="text-right" />
      </v-row>
    </template>

    <template #form="{ settings, update }">
      <!-- What api can do -->
      <div class="ph-no-capture mt-10">
        <!-- <div class="mt-10">
          <h2 id="api-overview">
            API Overview
          </h2>
          <div>ภาพรวมการเชื่อมต่อ API กับ Dobybot</div>
          <v-row class="py-3 px-3 pt-8">
            <v-col cols="12" md="8" style="border: 1px solid #dddddd; max-height: 80vh; overflow-y: scroll;">
              <a href="https://storage.googleapis.com/dobybot-public-bucket/apidoc/dobybot-api.drawio.png" target="_blank">
                <v-img src="https://storage.googleapis.com/dobybot-public-bucket/apidoc/dobybot-api.drawio.png" />
              </a>
            </v-col>
            <v-col cols="12" md="4">
              <p>
                ในการใช้งาน API กับ Dobybot ท่านต้องเชื่อมต่อ API อย่างน้อย 1 เส้นคือ
                <a class="font-weight-bold" href="https://documenter.getpostman.com/view/2263911/UVRGF4d6#7d52ea5d-5201-45d9-83f2-f7a0ce0a45a9" target="_blank">"Insert or Update Order" API</a> เพื่อส่งข้อมูลคำสั่งซื้อไปยัง Dobybot Server
              </p>
              <p>เมื่อพนักงานเริ่มบันทึกวิดีโอโดยการสแกนหมายเลขคำสั่งซื้อ/หมายเลขพัสดุ ระบบจะแสดงข้อมูลคำสั่งซื้อและรายการสินค้าที่ต้องแพ็ค</p>
              <p>
                เมื่อแพ็คสินค้าเสร็จแล้ว และทำการหยุดบันทึกวิดีโอ ระบบ Dobybot จะทำการอัพโหลดไฟล์วิดีโอไปยัง Google Drive
              </p>
              <p>
                หากมีการตั้งค่า <a class="font-weight-bold" href="#webhook">"Post Record Webhook"</a> ไว้ Dobybot Server จะส่งข้อมูลเกี่ยวกับการแพ็คไปยัง URL ที่ท่านตั้งค่าไว้ ข้อมูลที่ส่งไปจะประกอบด้วย
                <ul>
                  <li>หมายเลขคำสั่งซื้อ / หมายเลขพัสดุ</li>
                  <li>URL ของวิดีโอ</li>
                  <li>รายการสินค้าที่ถูกสแกนระหว่างแพ็ค</li>
                  <li>Username ของพนักงานที่ทำการแพ็ค</li>
                </ul>
                ท่านสามารถบันทึกข้อมูลเหล่านี้ไว้ในระบบของท่านเพื่อนำไปใช้งานต่อได้
              </p>
            </v-col>
          </v-row>
        </div> -->
        <div class="mt-10">
          <v-row>
            <v-col cols="12" md="8">
              <admin-container>
                <h2 class="mb-2">
                  เชื่อมต่อบริการอื่น
                </h2>

                <v-btn
                  small
                  outlined
                  to="/settings/apidoc/jambolive"
                >
                  JAMBOLIVE
                </v-btn>
                <v-btn
                  small
                  outlined
                  to="/settings/apidoc/cf-shop"
                >
                  CF-SHOPS
                </v-btn>
              </admin-container>
            </v-col>
          </v-row>
        </div>

        <div class="mt-10">
          <h2 id="api-doc">
            API Documentation
          </h2>
          <v-row class="px-3 pt-5">
            <div>
              <v-btn
                href="https://documenter.getpostman.com/view/2263911/2s9Y5YT3aq"
                target="_blank"
                small
                outlined
                color="#FE6C37"
                class="py-4 mr-2"
              >
                <v-img src="https://www.svgrepo.com/show/354202/postman-icon.svg" width="25" height="25" class="mr-2" />
                ORDER API
              </v-btn>

              <v-btn
                small
                outlined
                class="py-4 mr-2"
                color="primary"
                target="_blank"
                :href="`${backend_host}/openapi/schema/redoc/`"
              >
                <v-icon left>
                  mdi-file-document-multiple
                </v-icon>
                ETAX API
              </v-btn>

              <v-btn
                small
                outlined
                class="py-4 mr-2"
                color="primary"
                target="_blank"
                to="/docs/etax/qrcode"
              >
                <v-icon left>
                  mdi-file-document-multiple
                </v-icon>
                ETAX QRCode
              </v-btn>
            </div>
          </v-row>
          <v-row class="px-3 pt-5">
            <p>
              ในการทดสอบ API ด้วย Postman ให้ตั้งค่า
              <a href="https://learning.postman.com/docs/sending-requests/variables/#variables-quick-start" style="text-decoration: none;" target="_blank">Environment Variable</a>
              <code>host</code> และ <code>token</code> ตามข้อมูลด้านล่าง
            </p>
          </v-row>
          <v-row>
            <v-col cols="12" md="8">
              <api-token-form />
            </v-col>
          </v-row>
        </div>
      </div>

      <div class="ph-no-capture mt-12">
        <h2 id="webhook">
          Webhook
        </h2>

        <v-row>
          <v-col cols="12" md="8">
            <v-expansion-panels class="mt-2">
              <!-- Post Record Webhook Settings -->
              <v-expansion-panel>
                <v-expansion-panel-header>
                  <v-row>
                    <v-checkbox
                      label="Post Record Webhook"
                      :input-value="settings.company.POST_RECORD_ACTION_WEBHOOK_ENABLE"
                      @change="update({POST_RECORD_ACTION_WEBHOOK_ENABLE: $event})"
                      @click.prevent
                      @click.stop
                    />
                  </v-row>
                </v-expansion-panel-header>
                <v-expansion-panel-content>
                  <webhook-setting-form
                    :allow-methods="['POST']"
                    :example-payload="post_record_webhook.example_payload"
                    :form-data="settings.company.POST_RECORD_WEBHOOK_CONFIG"
                    @update:form-data="update({ POST_RECORD_WEBHOOK_CONFIG: $event })"
                  />
                </v-expansion-panel-content>
              </v-expansion-panel>

              <!-- SMS Webhook Settings -->
              <v-expansion-panel>
                <v-expansion-panel-header>
                  <v-row>
                    <v-checkbox
                      label="SMS Webhook"
                      :input-value="settings.company.SMS_WEBHOOK_ENABLE"
                      @change="update({SMS_WEBHOOK_ENABLE: $event})"
                      @click.prevent
                      @click.stop
                    />
                  </v-row>
                </v-expansion-panel-header>
                <v-expansion-panel-content>
                  <webhook-setting-form
                    :allow-methods="['POST']"
                    :form-data="settings.company.SMS_WEBHOOK_CONFIG"
                    :example-payload="sms_webhook.example"
                    @update:form-data="update({ SMS_WEBHOOK_CONFIG: $event })"
                  />
                </v-expansion-panel-content>
              </v-expansion-panel>

              <!-- ETAX Webhook Settings -->
              <v-expansion-panel>
                <v-expansion-panel-header>
                  <v-row>
                    <v-checkbox
                      label="E-Tax Webhook"
                      :input-value="settings.company.ETAX_WEBHOOK_ENABLE"
                      @change="update({ETAX_WEBHOOK_ENABLE: $event})"
                      @click.prevent
                      @click.stop
                    />
                  </v-row>
                </v-expansion-panel-header>
                <v-expansion-panel-content>
                  <div>
                    ส่ง Webhook เมื่อมีการสร้างเอกสารใบกำกับภาษี/ใบเสร็จรับเงินในระบบ
                  </div>
                  <webhook-setting-form
                    class="mt-2"
                    :allow-methods="['POST']"
                    :form-data="settings.company.ETAX_WEBHOOK_CONFIG"
                    :example-payload="etax_webhook.example_payload"
                    :docs="etax_webhook.docs"
                    @update:form-data="update({ ETAX_WEBHOOK_CONFIG: $event })"
                  />
                </v-expansion-panel-content>

                <!-- ETAX Request Webhook Settings -->
                <v-expansion-panel>
                  <v-expansion-panel-header>
                    <v-row>
                      <v-checkbox
                        label="E-Tax Customer Request Webhook"
                        :input-value="settings.company.ETAX_REQUEST_WEBHOOK_ENABLE"
                        @change="update({ETAX_REQUEST_WEBHOOK_ENABLE: $event})"
                        @click.prevent
                        @click.stop
                      />
                    </v-row>
                  </v-expansion-panel-header>
                  <v-expansion-panel-content>
                    <div>
                      ส่ง Webhook เมื่อลูกค้าทำการขอใบกำกับภาษี/ใบเสร็จรับเงิน
                    </div>
                    <webhook-setting-form
                      class="mt-2"
                      :allow-methods="['POST']"
                      :form-data="settings.company.ETAX_REQUEST_WEBHOOK_CONFIG"
                      :example-payload="etax_request_webhook.example_payload"
                      :docs="etax_request_webhook.docs"
                      @update:form-data="update({ ETAX_REQUEST_WEBHOOK_CONFIG: $event })"
                    />
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panel>
            </v-expansion-panels>
          </v-col>
        </v-row>
      </div>
    </template>
  </setting-template>
</template>

<script lang="ts">
import Vue from 'vue'
import AdminContainer from '~/components/admin/AdminContainer.vue'
import ApiTokenForm from '~/components/apidoc/ApiTokenForm.vue'
import WebhookSettingForm from '~/components/apidoc/WebhookSettingForm.vue'
import SettingTemplate from '~/components/settings/SettingTemplate.vue'
import { SettingState } from '~/store/settings'
import { getBackendHost } from '~/utils'

export default Vue.extend({
  components: {
    SettingTemplate,
    WebhookSettingForm,
    ApiTokenForm,
    AdminContainer
  },

  data () {
    return {
      sms_webhook: {
        example: {
          id: 120438,
          bulk_id: 'unspecified',
          message_id: '4EHvC7cuR0',
          sender: 'OrderNotice',
          to: '66889519856',
          text: 'test',
          remark: '',
          credit: '1.00',
          timestamp: '2023-12-01T09:23:57.706586+07:00',
          sms_type: 'paid',
          status: { code: '000', name: 'DELIVERED', description: 'Successfully sent to phone' },
          status_timestamp: '2023-12-01T09:24:10.041553+07:00',
          channel: null,
          company: 1,
          campaign: null
        }
      },
      post_record_webhook: {
        example_payload: {
          id: 1,
          name: '6667009696',
          order_id: 1,
          order_customer: 'นาย ก สวัสดี',
          order_customerphone: '081XXXXXXX',
          order_data: {
            integrationName: 'lazada',
            integrationShop: '1100XXXXXX'
          },
          order_number: '6667009696',
          order_trackingno: 'TRACK00000XXXX',
          order_saleschannel: 'LAZADA ABC',
          video_url: 'https://drive.google.com/file/d/dighencpejfie-xxxx',
          receipt_url: 'https://cloud.dobybot.com/receipt/?t=adieldo-xxxx',
          file_size: 100000,
          duration: 60,
          resolution: '1280x720',
          record_date: '2021-10-31T19:28:43+07:00',
          upload_date: '2021-10-31T19:29:43+07:00',
          upload_by: {
            id: 1,
            full_name: 'admin',
            username: 'admin@test'
          },
          scan_logs: [
            { barcode: 'G001-DGBrownCap:1', time_ms: 5233, amount: 1 },
            { barcode: 'G001-DGBrownCap:1', time_ms: 5831, amount: 1 },
            { barcode: 'G005-DGTP', time_ms: 6264, amount: 1 },
            { barcode: 'G003-XDGStand', time_ms: 6749, amount: 1 },
            { barcode: 'G004-XDGWeight', time_ms: 7523, amount: 1 },
            { barcode: 'G004-XDGWeight', time_ms: 8081, amount: 1 },
            { barcode: 'G004-XDGWeight', time_ms: 8304, amount: 1 },
            { barcode: 'G004-XDGWeight', time_ms: 8500, amount: 1 }
          ],
          scan_logs_summary: {
            'G001-DGBrownCap:1': 2,
            'G005-DGTP': 1,
            'G003-XDGStand': 1,
            'G004-XDGWeight': 4
          }
        }
      },
      etax_webhook: {
        example_payload: {
          buyer: {
            buyer_name: 'นายสมชาย ทดสอบ',
            tax_id: '110XXXXXXXXXX',
            branch_code: '',
            branch_name: '',
            address: '123/234 หมู่บ้าน กขค แขวงมีนบุรี เขตมีนบุรี กทม. 10510',
            post_code: '10510',
            email: '<EMAIL>',
            phone_number: '08********',
            tax_type: 'NIDN',
            country_id: 'TH',
            is_consent_marketing: true
          },
          seller: {
            seller_name: 'บริษัท ทดสอบขาย จำกัด',
            tax_id: '010XXXXXXXXXX',
            branch_code: '00000',
            branch_name: 'สำนักงานใหญ่',
            address: '123/234 หมู่บ้าน กขค แขวงมีนบุรี เขตมีนบุรี กทม. 10510',
            post_code: '10510',
            phone_number: '080XXXXXXX',
            tax_type: 'TXID',
            country_id: 'TH'
          },
          order: {
            list: [
              {
                sku: '-',
                name: 'ค่าอาหารและบริการ',
                number: 1,
                pricepernumber: '111.0000',
                totalprice: '118.7700',
                discount: '0.0000',
                vatamount: '7.7700',
                pretaxamount: '111.0000'
              },
              {
                sku: '-',
                name: 'Service charge',
                number: 1,
                pricepernumber: '222.0000',
                totalprice: '237.5400',
                discount: '0.0000',
                vatamount: '15.5400',
                pretaxamount: '222.0000'
              }
            ],
            number: 'EASYETAX-001',
            totaldiscount: '0.0000',
            pretaxamount: '333.0000',
            totalvatamount: '23.3100',
            grandtotal: '356.3100',
            vattype: '2'
          },
          document: {
            doc_id: 'RT-EASYETAX-001',
            doc_url: 'https://drive.google.com/file/d/1CQpxxxxxxxxxxxxxxxxxxxxxxxxxxxxx/view',
            doc_type: 'T03',
            doc_create_date: '2025-02-12T15:07:55.323463+07:00',
            doc_issue_date: '2025-02-12T00:00:00+07:00',
            doc_email_flag: 'Y',
            doc_purpose_code: '',
            doc_purpose_detail: '',
            ref_doc_id: '',
            ref_doc_issue_date: null,
            ref_doc_type: ''
          },
          event: 'NEW'
        },
        docs: [
          {
            title: 'DATA DICTIONARY',
            url: 'https://docs.google.com/spreadsheets/d/1iQsq-HoYA0GJ7a3vnJlxQoSUdX6gZ67RtnFk4AGJ9GQ/edit?gid=*********#gid=*********'
          }
        ]
      },
      etax_request_webhook: {
        example_payload: {
          buyer: {
            buyer_name: 'นายสมชาย ทดสอบ',
            tax_id: '110XXXXXXXXXX',
            branch_code: '',
            branch_name: '',
            address: '123/234 หมู่บ้าน กขค แขวงมีนบุรี เขตมีนบุรี กทม. 10510',
            post_code: '10510',
            email: '<EMAIL>',
            phone_number: '08********',
            tax_type: 'NIDN',
            country_id: 'TH',
            is_consent_marketing: true
          },
          order: {
            list: [
              {
                sku: '-',
                name: 'ค่าอาหารและบริการ',
                number: 1,
                pricepernumber: '111.0000',
                totalprice: '118.7700',
                discount: '0.0000',
                vatamount: '7.7700',
                pretaxamount: '111.0000'
              },
              {
                sku: '-',
                name: 'Service charge',
                number: 1,
                pricepernumber: '222.0000',
                totalprice: '237.5400',
                discount: '0.0000',
                vatamount: '15.5400',
                pretaxamount: '222.0000'
              }
            ],
            number: 'EASYETAX-001',
            totaldiscount: '0.0000',
            pretaxamount: '333.0000',
            totalvatamount: '23.3100',
            grandtotal: '356.3100',
            vattype: '2'
          }
        },
        docs: [
          {
            title: 'DATA DICTIONARY',
            url: 'https://docs.google.com/spreadsheets/d/1iQsq-HoYA0GJ7a3vnJlxQoSUdX6gZ67RtnFk4AGJ9GQ/edit?gid=*********#gid=*********'
          }
        ]
      },
      backend_host: ''
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },

  created () {
    if (!this.$hasPerms('change_settingvalue')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }
  },
  mounted () {
    this.backend_host = getBackendHost()
  },

  methods: {
    hasUnsavedChanges () {
      return (this.$refs.base as never as any).hasUnsavedChanges()
    }
  }
})
</script>
