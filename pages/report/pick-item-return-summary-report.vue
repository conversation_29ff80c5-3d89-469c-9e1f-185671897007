<template>
  <data-studio-report
    required-permission="view_pick_item_daily_summary_report"
    report-url="https://lookerstudio.google.com/embed/reporting/e01e9ac7-64ce-4df2-9f7c-79e896328cdf/page/1PWZD"
    datasource-path="/api/reports/v2/pick-item-return-daily-summary/"
    :params="params"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import DataStudioReport from '~/components/report/DataStudioReport.vue'

export default Vue.extend({
  components: { DataStudioReport },

  data () {
    return {
      params: {}
    }
  }
})
</script>
