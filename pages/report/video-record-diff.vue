<template>
  <data-studio-report
    required-permission="view_video_record_diff_report"
    report-url="https://lookerstudio.google.com/embed/reporting/171e59dc-8d3e-43d2-910d-695558155873/page/BfEPD"
    datasource-path="/api/reports/v2/video-record-diff/"
    :params="params"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import DataStudioReport from '~/components/report/DataStudioReport.vue'

export default Vue.extend({
  components: { DataStudioReport },

  data () {
    return {
      params: {}
    }
  },

  created () {
    this.params = this.$route.query
  }
})
</script>
