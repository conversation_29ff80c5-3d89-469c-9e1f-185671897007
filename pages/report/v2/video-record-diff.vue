<template>
  <div>
    <!-- <v-alert
      type="info"
      border="left"
      colored-border
      elevation="2"
      dismissible
    >
      ท่านกำลังดูรายงาน Version ใหม่ <a href="/report/video-record-diff">เปลี่ยนไปใช้รายงานเก่า</a>
    </v-alert> -->
    <iframe
      src="/report-v2/video-record-diff/"
      style="border: 0; width: 100%; height: calc(100vh - 100px);"
      frameborder="0"
      allowfullscreen
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  data () {
    return {
      params: {}
    }
  },

  created () {
    this.params = this.$route.query
  }
})
</script>
