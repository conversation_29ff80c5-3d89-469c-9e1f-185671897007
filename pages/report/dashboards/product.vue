<template>
  <div>
    <iframe
      src="/report-v2/dashboards/product"
      style="border: 0; width: 100%; height: calc(100vh - 100px);"
      frameborder="0"
      allowfullscreen
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { FEATURE_FLAGS } from '~/models'

export default Vue.extend({
  data () {
    return {
      params: {}
    }
  },

  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.DASHBOARD)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }

    if (!this.$hasPerms('view_best_selling_products_dashboard')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }

    this.params = this.$route.query
  }
})
</script>
