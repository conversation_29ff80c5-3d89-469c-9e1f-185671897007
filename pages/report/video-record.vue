<template>
  <data-studio-report
    required-permission="view_video_record_report"
    report-url="https://datastudio.google.com/embed/reporting/e8674898-5867-4a7f-90e7-e14312c8e023/page/ISTfC"
    datasource-path="/api/reports/v2/video-record-log/"
    :params="params"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import DataStudioReport from '~/components/report/DataStudioReport.vue'

export default Vue.extend({
  components: { DataStudioReport },

  data () {
    return {
      params: {}
    }
  },

  created () {
    this.params = this.$route.query
  }
})
</script>
