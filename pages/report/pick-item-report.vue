<template>
  <data-studio-report
    required-permission="view_pick_item_report"
    report-url="https://lookerstudio.google.com/embed/reporting/78241289-b446-42ed-8a67-af2b59ccae01/page/oLWZD"
    datasource-path="/api/reports/v2/pick-item/"
    :params="params"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import DataStudioReport from '~/components/report/DataStudioReport.vue'

export default Vue.extend({
  components: { DataStudioReport },

  data () {
    return {
      params: {}
    }
  }

})
</script>
