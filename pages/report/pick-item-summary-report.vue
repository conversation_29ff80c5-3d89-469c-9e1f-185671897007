<template>
  <data-studio-report
    required-permission="view_pick_item_daily_summary_report"
    report-url="https://lookerstudio.google.com/embed/reporting/f2f482f3-242b-442d-817b-0f0193227cac/page/1PWZD"
    datasource-path="/api/reports/v2/pick-item-daily-summary/"
    :params="params"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import DataStudioReport from '~/components/report/DataStudioReport.vue'

export default Vue.extend({
  components: { DataStudioReport },

  data () {
    return {
      params: {}
    }
  }
})
</script>
