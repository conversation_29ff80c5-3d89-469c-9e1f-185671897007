<template>
  <data-studio-report
    required-permission="view_video_record_report_no_video_link"
    report-url="https://lookerstudio.google.com/embed/reporting/22eaf55a-78ee-4a14-900e-9d2b0eafa76b/page/ISTfC"
    datasource-path="/api/reports/v2/video-record-log/"
    :params="params"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import DataStudioReport from '~/components/report/DataStudioReport.vue'

export default Vue.extend({
  components: { DataStudioReport },

  data () {
    return {
      params: {}
    }
  },

  created () {
    this.params = this.$route.query
  }
})
</script>
