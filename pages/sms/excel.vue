<template>
  <v-row>
    <v-col cols="12">
      <sms-page-header :title="$t('sms.send-sms-excel')" />
      <v-divider />
    </v-col>
    <v-col cols="12">
      <div id="instruction">
        <h4>{{ $t('sms.how-to') }}</h4>
        <ol>
          <li>{{ $t('sms.download') }} <a href="https://docs.google.com/spreadsheets/d/1ffkaHlQHCO4JBk0mBvJ2ilz2b3Errv15/export">{{ $t('sms.please-fix-error-in-excel-file-5000') }}</a></li>
          <li>{{ $t('sms.enter-phone-number') }}</li>
          <li>{{ $t('sms.analyse-credit') }}</li>
          <li>{{ $t('sms.send-sms-click') }}</li>
        </ol>
      </div>
      <v-form ref="form" v-model="valid" class="mt-5">
        <v-row>
          <v-col cols="8">
            <v-text-field
              v-model="campaign.name"
              :label="$t('sms.campaign-name')"
              outlined
              dense
              prepend-icon="mdi-send"
              hide-details="auto"
              :rules="[$rules.required]"
            />
          </v-col>
          <v-col cols="4">
            <v-checkbox
              v-model="campaign.shorten_url"
              label="Shorten URL"
              outlined
              dense
              hide-details="auto"
            />
          </v-col>
          <v-col cols="12" md="8">
            <v-file-input
              v-model="campaign.file"
              outlined
              dense
              show-size
              :label="$t('sms.sms-32mb')"
              persistent-hint
              hide-details="auto"
              :rules="[$rules.required]"
              accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
            />
          </v-col>
          <v-col cols="12" md="2">
            <v-btn
              block
              color="primary"
              type="button"
              class="my-auto"
              @click="onAnalyseBtnClick()"
            >
              <v-icon left>
                mdi-calculator
              </v-icon>
              ANALYSE
            </v-btn>
          </v-col>
          <v-col cols="12" md="2">
            <v-btn
              block
              color="primary"
              type="button"
              class="my-auto"
              :disabled="!can_send"
              @click="onSendBtnClick()"
            >
              <v-icon left>
                mdi-send
              </v-icon>
              SEND
            </v-btn>
          </v-col>
        </v-row>
      </v-form>
    </v-col>

    <v-col cols="12">
      <div v-if="estimate_quota > $auth.user.company.sms_balance" class="mb-3">
        <v-alert type="error" class="mb-0">
          {{ $t('sms.add-credit') }}
        </v-alert>
        <wallet-balance-card />
      </div>
      <sms-analysis-table v-if="hasAnalysedResult" :items="analysis_results" />
      <sms-analysis-table v-if="analysis_errors.length > 0" :items="analysis_errors" :error="true" />
      <v-alert v-if="analysis_header_errors.length" type="error">
        {{ $t('sms.error-response-missing-fields', [analysis_header_errors.join(', ')]) }}
      </v-alert>
    </v-col>

    <v-col v-if="send_success" cols="12">
      <v-alert type="success">
        {{ $t('sms.status-sms') }}
        <a href="#" class="white--text" @click.prevent="$router.push({ path: '/report/sms' })">{{ $t('sms.sms-report') }}</a>
      </v-alert>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import _ from 'lodash'
import moment from 'moment'
import SmsAnalysisTable from '~/components/sms/SmsAnalysisTable.vue'
import WalletBalanceCard from '~/components/wallets/WalletBalanceCard.vue'
import SmsPageHeader from '~/components/sms/SmsPageHeader.vue'
import { FEATURE_FLAGS } from '~/models'
export default Vue.extend({
  components: {
    SmsAnalysisTable,
    WalletBalanceCard,
    SmsPageHeader
  },
  data () {
    return {

      valid: false,
      campaign: {
        name: '',
        shorten_url: false,
        file: null as File | null
      },
      analysis_errors: [],
      analysis_results: [],
      analysis_header_errors: [],
      estimate_quota: 0,
      can_send: false,
      send_success: false
    }
  },

  computed: {
    hasAnalysedResult (): boolean {
      return this.analysis_results.length > 0
    }
  },

  watch: {
    'campaign.shorten_url': {
      handler () { this.can_send = false }
    },
    'campaign.file': {
      handler () { this.can_send = false }
    }
  },

  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.SMS_CHAT)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }

    if (!this.$hasPerms('add_smscampaign')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }
  },

  methods: {
    // Event Handler
    async onAnalyseBtnClick () {
      if (!this.campaign.name) {
        this.campaign.name = 'SMS Campaign ' + moment().format('YYYY-MM-DD')
      }

      if (!(this.$refs.form as any).validate() || !this.campaign.file) {
        return
      }

      this.analysis_errors = []
      this.analysis_header_errors = []
      this.analysis_results = []
      this.can_send = false

      try {
        const result = await this.analyseBulkSms(this.campaign.file, this.campaign.shorten_url)
        this.analysis_results = result.messages
        this.estimate_quota = result.estimate_quota

        const currentUser = this.$auth.user as any
        if (currentUser.company.sms_balance > this.estimate_quota) {
          this.can_send = true
        }
      } catch (error: any) {
        if (error.response) {
          const response = error.response
          if (response.status === 400) {
            if (response.data.missing_fields) {
              this.analysis_header_errors = response.data.missing_fields
              return
            }

            if (response.data.file) {
              this.$snackbar('error', this.$t('sms.error-invalid-file', [error.response.data.file[0]]))
              return
            }

            this.analysis_errors = error.response.data.filter((x: any) => !_.isEmpty(x))
            this.campaign.file = null
            this.$snackbar('error', this.$t('sms.error-response-status-excel', [error.response.status]))
          } else {
            this.$snackbar('error', this.$t('sms.error-response-status', [error.response.status]))
          }
        }
        console.error(error)
      }
    },

    async onSendBtnClick () {
      if (!(this.$refs.form as any).validate() || !this.campaign.file) {
        return
      }

      try {
        await this.sendBulkSms(
          this.campaign.name,
          this.campaign.shorten_url,
          this.campaign.file
        )
        this.send_success = true
        this.can_send = false
        this.analysis_errors = []
        this.analysis_results = []
      } catch (error: any) {
        if (error.response) {
          this.$snackbar('error', error.response.data)
        }
        console.error(error)
      }
    },

    // API
    async sendBulkSms (name: string, shorten_url = false, file: File) {
      const action = '/api/sms/send-bulk-sms/'
      const options = {
        headers: { 'Content-Type': 'multipart/form-data' }
      }

      const data = new FormData()
      data.append('file', file)
      data.append('shorten_url', `${shorten_url}`)
      data.append('campaign_name', name)

      const response = await this.$axios.post(action, data, options)
      return response.data
    },

    async analyseBulkSms (file: File, shorten_url = false) {
      const action = '/api/sms/analyse-bulk-sms/'
      const options = {
        headers: { 'Content-Type': 'multipart/form-data' }
      }

      const data = new FormData()
      data.append('file', file)
      data.append('shorten_url', `${shorten_url}`)

      const response = await this.$axios.post(action, data, options)
      return response.data
    }
  }
})
</script>
