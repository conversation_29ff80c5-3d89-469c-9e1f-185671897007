<template>
  <div>
    <v-row>
      <v-col cols="12">
        <!-- <sms-navigator /> -->
        <sms-page-header :title="$t('sms-api')" />
        <v-divider />
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="10">
        <div>
          {{ $t('sms-api') }}
        </div>
        <div>
          <ul>
            <li>
              <a href="#api-overview">API Overview</a>
            </li>
            <li>
              <a href="#api-doc">API Documentation</a>
            </li>
          </ul>
        </div>
      </v-col>
      <v-col cols="2" class="text-right" />
    </v-row>

    <div class="mt-10">
      <div class="mt-10">
        <h2 id="api-overview">
          API Overview
        </h2>
        {{ $t('sms-api') }}
        <v-img src="/sms/sms-flow.jpg" max-width="600" />
      </div>
    </div>

    <div class="mt-10">
      <h2 id="api-doc">
        API Documentation
      </h2>
      <v-row class="px-3 pt-5">
        <div>
          <v-btn
            href="https://documenter.getpostman.com/view/2263911/2s9Y5YT3aq#5058feb8-c79f-4edf-9f84-7b5951243765"
            target="_blank"
            small
            outlined
            color="#FE6C37"
            class="py-4 mr-2"
          >
            <v-img src="https://www.svgrepo.com/show/354202/postman-icon.svg" width="25" height="25" class="mr-2" />
            View Documentation
          </v-btn>
        </div>
      </v-row>
      <v-row class="px-3 pt-5">
        <p>
          ในการทดสอบ API ด้วย Postman ให้ตั้งค่า
          <a href="https://learning.postman.com/docs/sending-requests/variables/#variables-quick-start" style="text-decoration: none;" target="_blank">Environment Variable</a>
          <code>host</code> และ <code>token</code><br>
          ท่านสามารถดู/สร้าง <code>token</code> และตั้งค่า <b>Webhook</b> ได้ในหน้า <a href="/settings/apidoc" style="text-decoration: none;" target="_blank">Dobybot API</a>
        </p>
      </v-row>
    </div>

    <div style="height: 50px;" />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { FEATURE_FLAGS } from '~/models'
import { SettingState } from '~/store/settings'

export default Vue.extend({

  data () {
    return {
    }
  },
  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },
  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.SMS_CHAT)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }
    if (!this.$hasPerms('add_smscampaign')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }
  }

})
</script>

<style scoped>
</style>
