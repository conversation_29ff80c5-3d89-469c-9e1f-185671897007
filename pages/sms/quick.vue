<template>
  <v-form ref="form" @submit.prevent="">
    <v-row>
      <v-col cols="12">
        <!-- <sms-navigator /> -->
        <sms-page-header :title="$t('quick-sms')" />
        <v-divider />
      </v-col>

      <v-col cols="12">
        <div id="instruction">
          <h4>{{ $t('sms.how-to') }}</h4>
          <ol>
            <li>{{ $t('sms.enter-phone-number') }}</li>
            <li>{{ $t('sms.analyse-credit') }}</li>
            <li>{{ $t('sms.send-sms-click') }}</li>
          </ol>
        </div>
      </v-col>

      <v-col cols="12" md="6">
        <v-textarea
          id="phone"
          v-model="phoneNumber"
          filled
          label="หมายเลขโทรศัพท์ (1 เบอร์ต่อ 1 บรรทัด)"
          :rules="[$rules.required]"
          :error-messages="error.to"
          :messages="`${phoneNumber.split('\n').filter(x=>x!=='').length} บรรทัด`"
        />
      </v-col>

      <v-col cols="12" md="6">
        <v-textarea
          id="text"
          v-model="text"
          filled
          :rules="[$rules.required]"
          label="ข้อความ"
          counter
          :error-messages="error.text"
        />
      </v-col>

      <v-col cols="12">
        <v-row justify="end">
          <v-col cols="12" md="2">
            <v-btn
              block
              color="primary"
              type="button"
              @click="onAnalyseBtnClick()"
            >
              <v-icon left>
                mdi-calculator
              </v-icon>
              ANALYSE
            </v-btn>
          </v-col>
          <v-col cols="12" md="2">
            <v-btn
              block
              type="button"
              :disabled="!can_send"
              @click="submitForm()"
            >
              <v-icon left>
                mdi-send
              </v-icon>
              SEND
            </v-btn>
          </v-col>
        </v-row>
      </v-col>

      <v-col cols="12">
        <div v-if="estimate_quota > current_quota" class="mb-3">
          <v-alert type="error" class="mb-0">
            {{ $t('sms.add-credit') }}
          </v-alert>
          <wallet-balance-card />
        </div>
        <sms-analysis-table v-if="analysis_results.length" mode="quick" :items="analysis_results" />
        <sms-analysis-table v-if="analysis_errors.length > 0" mode="quick" :items="analysis_errors" :error="true" />
      </v-col>

      <v-col v-if="send_success" cols="12">
        <v-alert type="success">
          {{ $t('sms.status-sms') }}
          <a href="#" class="white--text" @click.prevent="$router.push({ path: '/report/sms' })">{{ $t('sms.sms-report') }}</a>
        </v-alert>
      </v-col>
    </v-row>
  </v-form>
</template>

<script lang="ts">
import _ from 'lodash'
import Vue from 'vue'
import WalletBalanceCard from '~/components/wallets/WalletBalanceCard.vue'
import { FEATURE_FLAGS, SmsPayload, VForm } from '~/models'

export default Vue.extend({
  components: {
    WalletBalanceCard
  },
  data () {
    return {
      phoneNumber: '', // Step 4: Initialize phoneNumber data property
      text: '', // Step 5: Initialize text data property
      estimate_quota: 0,
      current_quota: 0,
      analysis_errors: [],
      analysis_results: [],
      can_send: false,
      error: {},
      send_success: false
    }
  },
  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.SMS_CHAT)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }

    if (!this.$hasPerms('add_smscampaign')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }
  },
  methods: {
    submitForm () {
      const form = this.$refs.form as never as VForm
      if (form.validate()) {
        const phone_numbers = this.phoneNumber.split('\n').filter(x => x !== '')
        this.sendSMS(phone_numbers, this.text)
      }
    },

    async  sendSMS (phoneNumbers:string[], text:string) {
      try {
        const payload = [] as SmsPayload[]
        for (let i = 0; i < phoneNumbers.length; i++) {
          payload.push({
            to: phoneNumbers[i],
            text
          })
        }
        await this.$axios.post('/api/sms/send-sms/', payload)

        this.send_success = true
        this.can_send = false
        this.analysis_errors = []
        this.analysis_results = []
      } catch (e:any) {
        console.log(e.response.data)
      }
    },

    async onAnalyseBtnClick () {
      const form = this.$refs.form as never as VForm
      if (!form.validate()) {
        return
      }

      this.analysis_errors = []
      this.analysis_results = []
      this.can_send = false

      const phone_numbers = this.phoneNumber.split('\n').filter(x => x !== '')
      const payload = [] as SmsPayload[]
      for (let i = 0; i < phone_numbers.length; i++) {
        payload.push({
          to: phone_numbers[i],
          text: this.text
        })
      }
      try {
        const response = await this.$axios.post('/api/sms/analyse-sms/', payload)
        this.estimate_quota = response.data.estimate_quota
        this.current_quota = response.data.sms_balance
        this.analysis_results = response.data.messages
        if (this.current_quota >= this.estimate_quota) {
          this.can_send = true
        }
      } catch (e:any) {
        console.log(e.response.data)
        if (e.response) {
          this.analysis_errors = e.response.data.filter((x: any) => !_.isEmpty(x))
        }
      }
    }
  }
})
</script>

<style scoped>
.text-help {
  padding-left: 33px;
  font-size: 0.85rem;
  color: #555555;
}
</style>
