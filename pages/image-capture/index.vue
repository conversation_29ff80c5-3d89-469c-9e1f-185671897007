<template>
  <div class="mx-auto" style="max-width:550px; height:100%">
    <v-form ref="form" @submit.prevent="">
      <v-row>
        <v-col cols="12">
          <v-row class="pa-3">
            <h2>{{ $t('image-capture.title') }}</h2>
            <v-spacer />
            <v-btn data-test="v-btn-setting" icon>
              <v-icon @click="is_setting = true">
                mdi-cog
              </v-icon>
            </v-btn>
          </v-row>
          <v-divider class="mb-3" />
        </v-col>
      </v-row>

      <!-- QRCode input -->
      <div>
        <v-text-field
          v-model="order_number"
          class="main-control-input"
          data-test="image-capture-code-input"
          :placeholder="$t('image-capture.placeholder')"
          :hint="$t('image-capture.hint')"
          autofocus
          outlined
          :rules="[$rules.barcode]"
          :append-icon="is_scaned ? 'mdi-window-close' : 'mdi-qrcode-scan'"
          :readonly="is_scaned"
          @click:append="is_scaned ? resetPage() : is_scanning = true"
          @keypress.enter="preImageCaptureLog()"
        />

        <scanner v-if="is_scanning && !is_scaned" @click:close="is_scanning = false" @get-order_number="getOrderNumber($event)" />

        <v-btn
          v-show="order_number && !is_scaned && !is_scanning"
          color="success"
          data-test="image-capture-next-btn"
          style="max-width:550px; width:100%"
          @click="preImageCaptureLog()"
        >
          {{ $t('image-capturn-next') }}
        </v-btn>

        <v-btn
          v-show="!is_scanning || is_scaned"
          class="mt-3"
          data-test="v-btn-choose-picture"
          color="primary"
          :disabled="!is_scaned"
          outlined
          x-large
          block
          @click="openInputFiles()"
        >
          <v-icon left>
            mdi-image
          </v-icon>
          {{ $t('image-capture.choose-image') }}
        </v-btn>

        <input
          id="filesInp"
          accept="image/*"
          type="file"
          multiple
          hidden
          @input="chooseImageFiles($event)"
        >
      </div>

      <image-grid
        v-show="is_scaned && images.length"
        data-test="image-grid-component"
        class="my-3"
        :images="images"
        @click:remove="images.splice($event,1)"
      />

      <v-btn
        v-show="images.length != 0"
        color="success"
        style="max-width:550px; width:100%"
        data-test="v-btn-upload-photo"
        dark
        @click="uploadImages()"
      >
        {{ $t('image-capture-upload-image') }}
      </v-btn>

      <!-- Loading dialog   -->
      <v-dialog v-model="uploading_dialog" persistent max-width="400">
        <v-card class="pa-3">
          <v-row class="ma-0">
            <v-col cols="3">
              <v-icon size="50">
                mdi-image
              </v-icon>
            </v-col>
            <v-col cols="9">
              {{ $t('image-capture-uploading') }} {{ uploaded_images.length }} / {{ images.length }}
              <v-progress-linear
                indeterminate
                class="mt-3"
              />
            </v-col>
          </v-row>
        </v-card>
      </v-dialog>

      <!-- Success dialog -->
      <v-dialog v-model="success_dialog" max-width="400" @input="resetPage()">
        <v-card class="pa-3">
          <v-card-title>
            <v-icon size="40" color="green">
              mdi-check-circle-outline
            </v-icon>
            <span>{{ $t('image-capture.upload-succees') }}</span>
          </v-card-title>

          <v-card-text>
            <div v-for="image in uploaded_images" :key="image.id" class="d-flex">
              <ul>
                <li>
                  <span>{{ image.name }}</span>
                  <v-icon color="green">
                    mdi-check
                  </v-icon>
                </li>
              </ul>
            </div>
          </v-card-text>

          <v-card-actions>
            <v-btn
              class="mx-auto"
              color="primary"
              data-test="v-btn-success-dialog"
              @click="(success_dialog= false),resetPage()"
            >
              {{ $t('image-capture-confirm') }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <setting-image-capture-dialog :dialog="is_setting" @click:close="is_setting = $event" />
    </v-form>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import moment from 'moment'
import { mapMutations, mapGetters, mapActions, mapState } from 'vuex'
import _ from 'lodash'
import Scanner from '~/components/image-capture/Scanner.vue'
import ImageGrid from '~/components/image-capture/ImageGrid.vue'
import { Image, LoggedInUser, VForm } from '~/models'
import { createFolder, findFolderInMyDrive, uploadFile, findFolderInShareDrive, share, GoogleDriveFile } from '~/api/googledrive'
import { SettingState } from '~/store/settings'
import SettingImageCaptureDialog from '~/components/image-capture/SettingImageCaptureDialog.vue'

export default Vue.extend({
  components: {
    Scanner,
    ImageGrid,
    SettingImageCaptureDialog
  },
  data () {
    return {
      order_number: '',
      capture_dialog: false,
      is_scaned: false,
      is_scanning: false,
      is_setting: false,
      alert: {} as Object,
      images: [] as Image[],

      success_dialog: false,
      uploading_dialog: false,
      uploaded_images: [] as any[]
    }
  },

  computed: {

    settings (): SettingState {
      return this.$store.state.settings
    },

    ...mapGetters('gapi', {
      gapiToken: 'token',
      gapiUser: 'currentUser'
    }),
    ...mapState('wallet', {
      record_balance: 'record_balance',
      google_drive_usage: 'google_drive_usage',
      google_drive_limit: 'google_drive_limit',
      wallet_version: 'version'
    })
  },

  created () {
    if (!this.$hasPerms('add_imagecapturelog')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }
  },

  mounted () {
    this.$store.dispatch('gapi/autoSignIn')
    setTimeout(() => {
      this.setupFolders()
    }, 5000)
  },

  methods: {

    ...mapActions('gapi', {
      gapiSignIn: 'signIn',
      gapiSignOut: 'signOut',
      gapiAutoSignIn: 'autoSignIn'
    }),

    openInputFiles () {
      const file_input = document.getElementById('filesInp')
      if (file_input != null) {
        file_input.click()
      }
    },

    chooseImageFiles (event: Event) {
      const fileInput = event.target as HTMLInputElement
      const fileList = fileInput.files

      if (!fileList) {
        return
      }

      for (let i = 0; i < fileList.length; i++) {
        // const _value = value as File
        const file = fileList[i]
        const duplicate_file = this.images.find(image => image.file.name === file.name)

        if (!file.type.startsWith('image/')) {
          this.$snackbar('error', 'ไม่สามารถส่งไฟล์ที่นอกเหนือจากไฟล์รูปได้')
          return
        }

        if (duplicate_file) {
          this.$snackbar('warning', this.$t('image-capture-duplicate-file'))
          continue
        }

        const obj: Image = {
          id: _.uniqueId('img'),
          urlData: URL.createObjectURL(file),
          file,
          status: 'loading'
        }
        this.images.push(obj)
      }

      fileInput.value = ''
    },

    renameFile (originalFile:File, newName:string) {
      return new File([originalFile], newName, {
        type: originalFile.type,
        lastModified: originalFile.lastModified
      })
    },

    getOrderNumber (order_number:string) {
      this.order_number = order_number
      this.is_scanning = false
      this.preImageCaptureLog()
    },

    resetPage () {
      this.order_number = ''
      this.images = []
      this.is_scaned = false
      this.is_scanning = false
      this.capture_dialog = false
      this.uploaded_images = []
    },

    setupFolders () {
      if (this.settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID) {
        this.setupShareDriveFolder()
      } else {
        this.setupMyDriveFolder()
      }
    },

    async setupShareDriveFolder () {
      const share_drive_id = this.settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID

      // Setup Root Folder
      const rootFolderName = `Dobybot Image Capture (${this.gapiUser.email})`
      try {
        let rootFolder = await findFolderInShareDrive(share_drive_id, rootFolderName)
        if (!rootFolder) {
          rootFolder = await createFolder(rootFolderName, { parentFolderId: share_drive_id })
        }
        this.setLocalSetting({ googleDriveImageRootFolder: rootFolder })
      } catch (error: any) {
        this.$alert({
          title: this.$t('error.unable-to-connect-to-google-drive'),
          text: error.result,
          theme: 'error'
        })
        throw error
      }

      // Setup Today Folder
      // const todayFolderName = moment().format('YYYY-MM-DD')
      // try {
      //   const parentFolderId = this.settings.local.googleDriveImageRootFolder.id
      //   let todayFolder = await findFolderInFolder(todayFolderName, parentFolderId)
      //   if (!todayFolder && this.settings.local.googleDriveImageRootFolder) {
      //     todayFolder = await createFolder(todayFolderName, {
      //       parentFolderId: this.settings.local.googleDriveImageRootFolder.id
      //     })
      //   }
      //   this.setLocalSetting({ googleDriveImageTodayFolder: todayFolder })
      // } catch (error: any) {
      //   this.$alert({
      //     title: this.$t('error.unable-to-connect-to-google-drive'),
      //     text: error.result,
      //     theme: 'error'
      //   })
      //   throw error
      // }
    },

    async setupMyDriveFolder () {
      const rootFolderName = 'Dobybot Image Capture'

      let rootFolder = await findFolderInMyDrive(rootFolderName)
      if (!rootFolder) {
        rootFolder = await createFolder(rootFolderName)
      }
      this.setLocalSetting({ googleDriveImageRootFolder: rootFolder })

      // // @ts-ignore
      // let todayFolder = await findFolderInFolder(todayFolderName, this.settings.local.googleDriveImageRootFolder.id)
      // if (!todayFolder && this.settings.local.googleDriveImageRootFolder) {
      //   todayFolder = await createFolder(todayFolderName, {
      //     parentFolderId: this.settings.local.googleDriveImageRootFolder.id
      //   })
      // }
      // this.setLocalSetting({ googleDriveImageTodayFolder: todayFolder })
    },

    async checkSupervisorPassword (detail: string): Promise<boolean> {
      const passwd = await this.$prompt({
        text: `<p style="white-space: pre-wrap;">${detail}</p><br>
        <p style="text-align: center;">${this.$t('image-capture-superviser')}</p>`,
        preset: 'password'
      })

      if (passwd === null) { return false }
      if (passwd !== this.settings.company.SUPERVISOR_PASSWORD) {
        this.$alert({ text: this.$t('image-capture-superviser-alert') })
        return false
      }
      return true
    },

    async uploadImages () {
      if (!this.settings.local.googleDriveImageRootFolder) {
        throw new Error('This should not happened: User sign in google drive first')
      }
      this.uploading_dialog = true

      try {
        let document_no_folder: gapi.client.drive.File | null = null

        if (this.settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID) {
          document_no_folder = await findFolderInShareDrive(this.settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID, this.order_number)
        } else {
          document_no_folder = await findFolderInMyDrive(this.order_number)
        }

        if (!document_no_folder) {
          document_no_folder = await createFolder(
            this.order_number, { parentFolderId: this.settings.local.googleDriveImageRootFolder.id })
        }

        const promises = [] as any[]
        const datetime = moment().format('YYYYMMDDHHmmss')
        const user = this.$auth.user as never as LoggedInUser

        for (let i = 0; i < this.images.length; i++) {
          this.images[i].file = this.renameFile(this.images[i].file, `${datetime}_${user.username}_${this.order_number}`)
          const image_new_status = { ...this.images[i] }

          if (this.images[i].status !== 'success') {
            promises.push(
              // @ts-ignore
              uploadFile(this.images[i].file, document_no_folder.id)
                .then((gfile) => {
                  image_new_status.status = 'success'
                  this.images.splice(i, 1, image_new_status)
                  this.uploaded_images.push(gfile)

                  return gfile
                }).catch((error) => {
                  console.log(error)
                  image_new_status.status = 'failed'
                  this.images.splice(i, 1, image_new_status)
                })
            )
          }
        }
        const gfiles = await Promise.all(promises)

        if (this.settings.company.POST_IMAGE_CAPTURE_SHARE_IMAGE_LINK) {
          for (const file of gfiles) {
            share(file.id)
          }
          // @ts-ignore
          share(document_no_folder.id)
        }

        // make images to dictionary to check status failed
        const images_dic = _.keyBy(this.images, 'status')

        if (images_dic.failed) {
          this.$alert({
            title: 'พบข้อผิดพลาดระหว่างอัปโหลดรูป',
            text: 'โปรดตรวจสอบอินเตอร์เน็ต เเละ ลองอัปรูปใหม่อีกครั้ง',
            theme: 'error'
          })
          this.uploading_dialog = false
          return
        }
        // @ts-ignore
        await this.postImageCaptureLog(gfiles, document_no_folder.id)
        this.uploading_dialog = false
        this.success_dialog = true
      } catch (error : any) {
        this.uploading_dialog = false
        this.$alert({
          title: this.$t('image-capture-internet-title'),
          text: this.$t('image-capture-internet-text'),
          theme: 'error'
        })
      }
    },

    async preImageCaptureLog () {
      const form = this.$refs.form as never as VForm
      const is_valid = form.validate()
      if (!is_valid) {
        return
      }

      if (!this.settings.company.ALLOW_NEGATIVE_CREDIT && this.record_balance <= 0) {
        this.$alert({
          title: this.$t('error.no-balance'),
          text: this.$t('error.image-capture.no-balance-text'),
          theme: 'error'
        })
        return
      }

      if (!this.gapiUser) {
        this.$alert({
          text: `<ol>
            <li style="line-height: 1.5; padding: 8px 0;">${this.$t('error.click-mechanism')}</li>
            <li style="line-height: 1.5; padding: 8px 0;">${this.$t('error.not-login-step2')}</li>
            <li style="line-height: 1.5; padding: 8px 0;">${this.$t('error.un-login-step3')}</li>
            <li style="line-height: 1.5; padding: 8px 0;">${this.$t('error.not-login-step5')}</li>
            <li style="line-height: 1.5; padding: 8px 0;">${this.$t('error.un-login-step6')}</li>
          </ol>`,
          title: this.$t('error.un-login-title'),
          theme: 'error'
        })

        this.is_setting = true
        return
      }

      const payload = {
        name: this.order_number
      }

      try {
        const response = await this.$axios.post('/api/picking/pre-image-capture/', payload)

        if (response.data.code === 'ALREADY_EXIST') {
          if (this.settings.company.CONFIRM_IMAGE_CAPTURE_PASSWORD_ENABLE) {
            const pass = await this.checkSupervisorPassword(this.$t('image-capture-superviser-title'))
            if (!pass) {
              return
            }
          } else {
            this.$alert({
              title: '',
              text: 'ออเดอร์นี้เคยมีการถ่ายรูปเเล้ว',
              theme: 'warning'
            })
          }
        }

        this.is_scaned = true
      } catch (e) {
        console.error(e)
        this.$alert({
          title: this.$t('image-capture-internet-title'),
          text: this.$t('image-capture-internet-preimage-text'),
          theme: 'error'
        })
      }
    },

    async postImageCaptureLog (gfiles:any[], document_no_folder_id:string) {
      const payload = {
        name: this.order_number,
        images: gfiles,
        drive_folder_id: document_no_folder_id,
        drive_id: this.settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID,
        drive_account: this.gapiUser.email
      }

      await this.$axios.post('/api/picking/post-image-capture/', payload)
    },

    ...mapMutations('settings', {
      setLocalSetting: 'setLocalSetting'
    })
  }
})
</script>

<style scoped>
  .barcode-color{
    background-color: #e0e0e0;
  }

</style>
