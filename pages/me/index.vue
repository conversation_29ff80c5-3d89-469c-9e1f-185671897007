<template>
  <v-row justify="center">
    <v-col cols="12" md="6">
      <h2>
        {{ $t('menu.change-password') }}
      </h2>
      <v-divider class="my-5" />
      <v-form ref="form">
        <v-alert v-if="error" outlined dark color="error">
          {{ error }}
        </v-alert>

        <h4 class="mb-2">
          {{ $t('me.old-password') }}
        </h4>
        <password-input
          v-model="form.old_password"
          filled
          :label="$t('me.old-password')"
          :rules="[$rules.required]"
        />

        <h4 class="mt-5 mb-2">
          {{ $t('me.new-password') }}
        </h4>
        <password-input
          v-model="form.password"
          filled
          :label="$t('me.new-password')"
        />
        <confirm-input
          v-model="form.confirm"
          filled
          :password="form.password"
        />
      </v-form>

      <v-row justify="end" class="px-3">
        <v-btn
          width="100"
          color="success"
          align-self="end"
          :loading="loading"
          @click="submit()"
        >
          {{ $t('settings.save') }}
        </v-btn>
      </v-row>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import ConfirmInput from '~/components/settings/users/ConfirmInput.vue'
import PasswordInput from '~/components/settings/users/PasswordInput.vue'

export default Vue.extend({
  components: { PasswordInput, ConfirmInput },

  middleware: ['auth'],
  data () {
    return {
      form: {
        old_password: '',
        password: '',
        confirm: ''
      },
      error: '',
      loading: false,
      hide_password: true
    }
  },

  head () {
    return {
      title: 'Dobybot.com - Change Password'
    }
  },

  methods: {
    async submit () {
      if (!(this.$refs.form as any).validate()) {
        return
      }

      this.loading = true
      try {
        const payload = {
          old_password: this.form.old_password,
          new_password: this.form.password
        }

        await this.$axios.post('/api/users/change-password/', payload)
        this.$snackbar('success', this.$t('me.change-password-success'))

        setTimeout(() => {
          this.$router.replace({ path: '/' })
        }, 1000)
      } catch (e:any) {
        this.error = e.response.data.detail
      }
      this.loading = false
    }
  }
})
</script>

<style scoped>

</style>
