<template>
  <v-row>
    <v-col cols="12">
      <h2>
        {{ $t('me.my-device') }}
      </h2>
      <v-divider />
    </v-col>
    <v-col cols="12">
      <v-row>
        <v-col v-for="(device,key) in devices" :key="key" cols="4">
          <v-card>
            <v-card-text>
              <v-row>
                <v-col cols="4">
                  <v-img v-if="device.device == 'PC'" contain src="/pc.png" />
                  <v-img v-else contain src="/mobile.png" />
                </v-col>
                <v-col cols="8">
                  <h3 class="pb-2">
                    OS: {{ device.os }}
                    <v-chip v-if="device_id === key" class="ml-3" small color="primary">
                      {{ $t('me.current-device') }}
                    </v-chip>
                  </h3>
                  <h4>
                    Browser : {{ device.browser }}
                  </h4>
                  <h4>
                    {{ $t('me.time') }} : {{ $datetime(device.login_at*1000) }}
                  </h4>

                  <div class="caption mt-3">
                    ref: {{ key }}
                  </div>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions>
              <v-btn :disabled="device_id === key" color="error" block @click="logout(key)">
                {{ $t('me.logout') }}
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-col>
      </v-row>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import { Device } from '~/models'
import { performLogout } from '~/utils/logout'
export default Vue.extend({
  middleware: 'auth',
  data () {
    return {
      devices: {} as Device,
      device_id: localStorage.getItem('login.device_id')
    }
  },

  computed: {
    ...mapState(['auth'])
  },
  created () {
    this.getDevice()
  },
  methods: {
    async getDevice () {
      const res = await this.$axios.get('/api/users/me/')
      this.devices = res.data.devices
    },
    async logout (device_id:string) {
      try {
        await this.$axios.post('/api/users/logout/', { device_id })
        await this.getDevice()
        this.$snackbar('success', this.$t('me.logout-success'))
      } catch (err) {
        performLogout(this)
      }
    }
  }
})
</script>

<style scoped>

</style>
