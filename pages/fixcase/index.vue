<template>
  <v-row>
    <v-col cols="12">
      <v-row class="pa-3">
        <h2>Fix Case</h2>
        <v-spacer />
        <v-btn v-if="$hasPerms('add_fixcase')" color="primary" small data-test="open-create-fixcase-dialog" @click="dialog=true; filters.is_complete=false">
          {{ $t('fixcase.create-fixcase') }}
        </v-btn>
      </v-row>
      <v-divider />
    </v-col>

    <v-col cols="12">
      <fix-case-filter-form :form-data.sync="filters" />
    </v-col>
    <v-col cols="12">
      <fix-case-data-table
        data-test="fixcase-table"
        :items.sync="table.fixcases"
        :selected.sync="selected"
        :options.sync="table.options"
        :show-select="!filters.is_complete"
        :server-items-length="table.meta.total_results"
        :loading="loading"
        :footer-props="{'items-per-page-options': [5, 10, 25, 50, 100]}"
        @click:create-fix-order="onCreateFixOrderClick($event)"
        @click:edit-fix-order="onEditFixOrderClick($event)"
      />

      <div v-if="$hasPerms('change_fixcase') && !filters.is_complete" class="text-center">
        <v-btn
          color="primary"
          :loading="loading"
          large
          :disabled="selected.length === 0"
          data-test="close-fixcase"
          @click="onCloseFixCaseButtonClick()"
        >
          {{ $t('fixcase.sms') }} ({{ selected.length }})
        </v-btn>
      </div>
    </v-col>

    <!-- Dialogs -->
    <fix-case-create-dialog
      v-model="dialog"
      @create:fixcase="onFixCaseCreated($event)"
    />
    <fix-order-create-dialog
      v-model="dialog_create_fix_order.show"
      :fix-case="dialog_create_fix_order.fix_case"
      @update:fixcase="onFixCaseUpdated($event)"
    />
    <fix-order-edit-dialog
      v-model="dialog_edit_fix_order.show"
      :fix-case="dialog_edit_fix_order.fix_case"
      @update:fixcase="onFixCaseUpdated($event)"
    />
  </v-row>
</template>

<script lang="ts">
import _ from 'lodash'
import Vue from 'vue'
import FixCaseCreateDialog from '~/components/fixcase/FixCaseCreateDialog.vue'
import FixCaseDataTable from '~/components/fixcase/FixCaseDataTable.vue'
import FixCaseFilterForm from '~/components/fixcase/FixCaseFilterForm.vue'
import FixOrderCreateDialog from '~/components/fixcase/FixOrderCreateDialog.vue'
import FixOrderEditDialog from '~/components/fixcase/FixOrderEditDialog.vue'
import { FEATURE_FLAGS, FixCase, VDataTableOption } from '~/models'
import { getDynamicRESTPagination, getDynamicRESTSorting } from '~/utils/api'

interface FixCaseFilter {
  search: string
  is_complete: boolean
}

export default Vue.extend({
  components: {
    FixCaseFilterForm,
    FixCaseDataTable,
    FixCaseCreateDialog,
    FixOrderCreateDialog,
    FixOrderEditDialog
  },

  data () {
    return {
      dialog: false,
      dialog_create_fix_order: {
        show: false,
        fix_case: null as FixCase | null
      },
      dialog_edit_fix_order: {
        show: false,
        fix_case: null as FixCase | null
      },

      filters: {
        search: '',
        is_complete: false
      },

      table: {
        meta: {
          total_results: 1,
          total_pages: 1,
          page: 1,
          per_page: 25
        },
        options: {
          page: 1,
          itemsPerPage: 25,
          sortBy: ['create_date'],
          sortDesc: [true],
          groupBy: [],
          groupDesc: [],
          multiSort: false,
          mustSort: false
        } as VDataTableOption,
        fixcases: [] as FixCase[]
      },
      loading: false,
      selected: [],

      debouncedGetFixCases: null as Function | null
    }
  },

  watch: {
    'filters.search': {
      handler () {
        if (this.debouncedGetFixCases) {
          this.table.options.page = 1
          this.debouncedGetFixCases()
        }
      }
    },
    'filters.is_complete': {
      handler () {
        this.getFixCases(this.filters, this.table.options)
      }
    },
    'table.options': {
      handler () {
        this.getFixCases(this.filters, this.table.options)
      }
    }
  },

  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.FIX_CASE)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }

    if (!this.$hasPerms('view_fixcase')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
      return
    }

    if (this.$route.query.q) {
      this.filters.search = this.$route.query.q as string
    }

    this.debouncedGetFixCases = _.debounce(() => {
      this.getFixCases(this.filters, this.table.options)
    }, 500)

    // This will trigger duplicated request
    // The watcher for 'table.options' is automatically trigger the request
    // this.getFixCases(this.filters, this.table.options)
  },

  methods: {
    async getFixCases (filters: FixCaseFilter, options: VDataTableOption) {
      this.loading = true

      try {
        const config: any = {
          params: {
            include: ['open_case_sms_log.*', 'pick_order.*', 'close_case_sms_log.*'],
            // sort: ['-create_date'],
            ...getDynamicRESTPagination(options),
            ...getDynamicRESTSorting(options)
          }
        }

        if (filters.is_complete !== undefined) {
          config.params['filter{is_complete}'] = `${filters.is_complete}`
        }
        if (filters.search) {
          config.params.search = filters.search
        }
        console.log('before error')
        console.log('config', config)
        const res = await this.$axios.get('/api/fixcases/resource/fixcases/', config)
        console.log('after error')
        this.table.fixcases = res.data.fixcases
        this.table.meta = res.data.meta
      } catch (error) {
        console.error(error)
      }

      this.loading = false
    },

    onFixCaseCreated (fixcase: FixCase) {
      this.table.fixcases.unshift(fixcase)
      this.table.meta.total_results += 1
    },

    onFixCaseUpdated (fixcase: FixCase) {
      console.log('11111111111111', fixcase)
      const idx = this.table.fixcases.findIndex(x => x.id === fixcase.id)
      this.table.fixcases.splice(idx, 1, fixcase)
    },

    onCloseFixCaseButtonClick () {
      this.loading = true
      setTimeout(() => {
        this.closeFixCases(this.selected)
      }, 500)
    },

    async closeFixCases (selected: FixCase[]) {
      const selected_ids = selected.map(x => x.id)
      const fixcases = this.table.fixcases.filter(x => selected_ids.includes(x.id))
      if (fixcases.find(x => !x.tracking_code)) {
        alert(this.$t('fixcase.tracking'))
        this.loading = false
        return
      }

      if (!await this.checkFixCaseWithoutVideos(fixcases)) {
        this.loading = false
        return
      }

      const payload = fixcases.map((x: FixCase) => ({
        fixcase: x.id,
        tracking_code: x.tracking_code,
        remark: x.remark
      }))

      this.loading = true
      try {
        await this.$axios.post('/api/fixcases/fixcases/close/', payload)
        this.table.fixcases = _.differenceBy(this.table.fixcases, fixcases, 'id')
        this.selected = []
        this.$snackbar('success', this.$t('fixcase-fixcases-length', [fixcases.length]))
      } catch (error) {
        console.error(error)
      }
      this.loading = false
    },

    async checkFixCaseWithoutVideos (fixcases: FixCase[]): Promise<boolean> {
      const fixcases_without_videos = fixcases.filter(x => x.fix_order ? !x.fix_order.has_videos : false)
      if (fixcases_without_videos.length > 0) {
        return await this.$confirm({
          title: `พบ ${fixcases_without_videos.length} รายการยังไม่ได้ทำการบันทึกวิดิโอ`,
          text: `
            <ul>
              ${fixcases_without_videos.map(x => `<li>${x.fix_order.order_number}</li>`).join('')}
            </ul>
            <p class="mt-4">
              คุณต้องการทำการปิดรายการทั้งหมดหรือไม่ ?
            </p>
          `,
          theme: 'warning'
        })
      }

      return true
    },

    onCreateFixOrderClick (fixcase: FixCase) {
      this.dialog_create_fix_order.fix_case = fixcase
      this.dialog_create_fix_order.show = true
    },

    onEditFixOrderClick (fixcase: FixCase) {
      console.log('edit fix order', fixcase)
      this.dialog_edit_fix_order.fix_case = fixcase
      this.dialog_edit_fix_order.show = true
    }
  }
})
</script>
