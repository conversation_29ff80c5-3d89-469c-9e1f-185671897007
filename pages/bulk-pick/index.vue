<template>
  <div>
    <v-card
      class="no-print w-100 mx-auto"
      max-width="900px"
      outlined
      rounded="lg"
    >
      <div class="mx-8">
        <h1 class="text-center">
          {{ $t("bulkpick-title") }}
        </h1>
        <v-textarea
          v-model="reference_number"
          autofocus
          auto-grow
          clearable
          class="main-control-input"
          rows="2"
          :error-messages="error_message"
          :label="$t('bulkpick-label')"
          :readonly="loading"
          hide-details="auto"
          @keydown.enter="getMultiOrderItems(reference_number)"
        >
          <template #append>
            <v-progress-circular
              v-show="loading"
              size="24"
              color="info"
              indeterminate
            />
          </template>
        </v-textarea>
        <div class="d-flex">
          <v-spacer />
          <!-- v-show="reference_number? reference_number.split(/[\t\n]/).length > 1 : false" -->
          <v-btn
            class="my-2"
            color="primary"
            @click="getMultiOrderItems(reference_number)"
          >
            {{ $t("bulkpick-get-list") }}
          </v-btn>
        </div>
        <div class="d-flex">
          <div class="font-weight-black my-auto">
            Order: {{ selected_reference_number.length }}
          </div>
          <v-spacer />
          <v-btn
            class="my-3"
            text
            :disabled="!selected_reference_number.length"
            @click="clearOrderNumber()"
          >
            {{ $t("bulkpick-clear") }}
          </v-btn>
        </div>
        <v-chip
          v-for="ref_number in selected_reference_number"
          :key="ref_number"
          class="mb-4 me-2"
          close
          color="success"
          text-color="white"
          @click:close="deleteReferenceNumber(ref_number)"
        >
          {{ ref_number }}
        </v-chip>
      </div>
    </v-card>
    <div class="mt-4 table-container A4 print-container">
      <div class="d-flex justify-space-between">
        <h2>Bulk Picklist ({{ datetime_now }})</h2>
        <div class="no-print">
          <v-chip
            v-show="list_order_items.length"
            outlined
            color="primary"
            @click="print()"
          >
            Print
          </v-chip>
        </div>
      </div>
      <table>
        <thead>
          <tr>
            <th />
            <th>{{ $t("bulkpick-itemsku") }}</th>
            <th>{{ $t("bulkpick-itemname") }}</th>
            <th>{{ $t("bulkpick-order") }}</th>
            <th>{{ $t("bulkpick-amount") }}</th>
          </tr>
        </thead>
        <tbody>
          <template v-for="item in tableOrderItem">
            <tr :key="item.sku" style="border: none;">
              <td>
                <v-checkbox
                  class="my-auto"
                  density="compact"
                  hide-details
                  disabled
                />
              </td>
              <td>
                <span style="font-size: 18px;">
                  {{ item.sku }}
                </span>
              </td>
              <td>
                <span style="font-size: 18px;">
                  {{ item.name }}
                  <template v-if="item.location">
                    <v-divider class="mt-1 mb-2" />
                    <v-icon>mdi-map-marker</v-icon>
                    {{ item.location }}
                  </template>
                </span>
              </td>
              <td>
                <span style="font-size: 18px;">
                  {{ item.order_number }}
                </span>
              </td>
              <td>
                <span style="font-size: 18px;">
                  <strong>{{ item.number }}</strong>
                </span>
              </td>
            </tr>
          </template>
        </tbody>
        <tfoot v-if="list_order_items.length">
          <tr style="font-size: 18px;">
            <td />
            <td />
            <td />
            <td class="font-weight-black">
              {{ $t("bulkpick-total") }}
            </td>
            <td class="font-weight-black">
              {{ totalItemAmount }}
            </td>
          </tr>
        </tfoot>
      </table>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import moment from 'moment'
import { BulkPickItem } from '~/models'

export default Vue.extend({
  data () {
    return {
      error_message: '' as string,
      reference_number: '' as string,
      list_order_items: [] as BulkPickItem[],
      selected_reference_number: [] as string[],
      datetime_now: moment().format('DD/MM/YYYY HH:mm'),
      loading: false as boolean
    }
  },

  computed: {
    tableOrderItem () {
      const table_items = [] as BulkPickItem[]

      this.list_order_items.forEach((order_item: BulkPickItem) => {
        const existing_item: BulkPickItem | undefined = table_items.find(
          (item: BulkPickItem) =>
            item.sku === order_item.sku &&
            item.name === order_item.name
        )

        if (existing_item !== undefined) {
          existing_item.number += order_item.number
          existing_item.order_number += `, ${order_item.order_number}`
        } else {
          table_items.push({ ...order_item })
        }
      })

      return table_items
    },

    totalItemAmount () {
      let total_item_amount = 0
      for (const item of this.list_order_items) {
        total_item_amount += item.number
      }
      return total_item_amount
    }
  },

  methods: {
    async getMultiOrderItems (ref_numbers: string) {
      const ref_number_list = ref_numbers.split(/[\t\n ]+/)

      for (const ref_number of ref_number_list) {
        await this.getOrderItems(ref_number)
      }
    },

    async getOrderItems (ref_number: string) {
      if (this.loading) {
        return
      }

      if (ref_number === '') {
        return
      }

      this.loading = true
      const payload = {
        reference_number: ref_number
      }

      const foundeditem = this.list_order_items.find(
        (order_item: BulkPickItem) =>
          ref_number === order_item.order_number ||
          ref_number === order_item.tracking_number
      )

      if (foundeditem) {
        this.error_message = this.$t('bulkpick-error-scanned')
        this.reference_number = ''
        this.loading = false
        return
      }

      try {
        const response = await this.$axios.$post(
          '/api/picking/pick-orders/bulk-pick/',
          payload
        )

        this.list_order_items = [
          ...this.list_order_items,
          ...response.list_order_items
        ]
        this.selected_reference_number.push(ref_number)
        this.reference_number = ''
        this.error_message = ''
        this.loading = false
      } catch (error: any) {
        if (error.response.data.message === 'PickOrder does not exist') {
          this.reference_number = ''
          this.error_message = this.$t('bulkpick-error-notfound')
        }
        this.loading = false
      }
    },

    deleteReferenceNumber (reference_number: string) {
      this.selected_reference_number = this.selected_reference_number.filter(
        (ref_number: string) => ref_number !== reference_number
      )
      this.list_order_items = this.list_order_items.filter(
        (order_item: BulkPickItem) =>
          order_item.order_number !== reference_number &&
          order_item.tracking_number !== reference_number
      )
    },

    clearOrderNumber () {
      this.list_order_items = []
      this.selected_reference_number = []
    },

    print () {
      window.print()
    }
  }
})
</script>

<style scoped>
.table-container {
  width: 100%;
  max-width: 900px;
  margin: auto;
  border-radius: 8px;
  overflow: hidden;
}

table {
  width: 100%;
  border-collapse: collapse;
  text-align: left;
}

th,
td {
  padding: 8px;
  border-bottom: 1px solid #000;
}

th {
  background-color: #c5ced3;
  color: #333;
  text-transform: uppercase;
}

tbody tr:last-child td {
  border-bottom: none;
}

.number-collumn {
  text-align: right;
}
</style>
