<template>
  <div>
    {{ $t('boardcast.stop') }}
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import DobybotSocketClient from '~/utils/dobybot-socket'
export default Vue.extend({
  mounted () {
    const order_number = this.$route.query.order_number as string

    if (order_number) {
      this.boardcastStopRecord(order_number)
      this.websocketStopRecord().finally(() => {
        window.close()
      })
    }
  },

  methods: {
    boardcastStopRecord (order_number: string) {
      const channel = new BroadcastChannel('toggle-recording')
      channel.postMessage({
        command: 'stop',
        order_number
      })
    },

    websocketStopRecord () {
      return new Promise((resolve, reject) => {
        const socket = new DobybotSocketClient()
        socket.onopen = () => {
          socket.stopRecord()
          resolve(true)
        }
        socket.onerror = (error) => {
          reject(error)
        }
      })
    }
  }
})
</script>
