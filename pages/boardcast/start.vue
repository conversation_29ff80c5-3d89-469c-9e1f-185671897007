<template>
  <div>
    {{ $t('boardcast.boardcast') }}
    <v-btn @click="boardcastStartRecord()">
      {{ $t('boardcast.start') }}
    </v-btn>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import DobybotSocketClient from '~/utils/dobybot-socket'

export default Vue.extend({
  mounted () {
    const order_number = this.$route.query.order_number as string

    if (order_number) {
      this.boardcastStartRecord(order_number)
      this.websocketStartRecord(order_number).finally(() => {
        window.close()
      })
    }
  },

  methods: {
    boardcastStartRecord (order_number: string) {
      const channel = new BroadcastChannel('toggle-recording')
      channel.postMessage({
        command: 'start',
        order_number
      })
    },

    websocketStartRecord (order_number: string) {
      return new Promise((resolve, reject) => {
        const socket = new DobybotSocketClient()
        socket.onopen = () => {
          socket.stopStartRecord(order_number)
          resolve(true)
        }
        socket.onerror = (error) => {
          reject(error)
        }
      })
    }
  }
})
</script>
