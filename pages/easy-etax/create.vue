<template>
  <div style="max-width: 600px; margin: 0 auto">
    <v-card outlined>
      <v-card-title>
        <span class="headline">สร้างออเดอร์ Easy E-Tax</span>
        <v-spacer />
        <v-btn icon @click="setting_dialog=true">
          <v-icon>mdi-cog</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider />

      <v-card-text class="pt-3">
        <div>
          <etax-order-form
            ref="form"
            :order-json.sync="order_json_form"
            :pos-number.sync="pos_number"
            :running-number.sync="running_number"
            :last-order-number="last_order_number"
            :errors="form_errors.order_json || {}"
            @click:next-running-number="fillNextRunningNumber()"
          />
        </div>
        <!-- Price Section -->
        <v-row dense class="mt-5">
          <v-col cols="12" md="4">
            <!-- Add spacing for balance -->
          </v-col>
          <v-col cols="12" md="8">
            <e-tax-price-details
              :order-json="order_json_form"
              :select-vat-type="vat_type"
            />
          </v-col>
        </v-row>
      </v-card-text>
      <v-alert v-show="error_message" class="mt-5" text outlined type="error">
        <div v-html="error_message" />
      </v-alert>

      <v-divider />

      <!-- <v-card-actions> -->
      <div class="btns py-3">
        <v-btn :loading="loading" color="primary" @click="submit()">
          บันทึก + สร้าง QR Code
        </v-btn>
      </div>
    </v-card>
    <easy-etax-setting-dialog
      :dialog.sync="setting_dialog"
      :value="settings"
      :vat-type="vat_type"
      @input="updateLocalStorageSetting($event)"
      @update:vat-type="updateVatType($event)"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

import EtaxOrderForm from '~/components/order-center/EtaxOrderForm.vue'
import { VForm } from '~/models'
import EasyEtaxSettingDialog from '~/components/etax-invoice/EasyEtaxSettingDialog.vue'

const VAT_TYPE_INC = 3
const VAT_TYPE_EXC = 2

export default Vue.extend({
  components: {
    EtaxOrderForm,
    EasyEtaxSettingDialog
  },

  middleware: ['auth'],

  data () {
    return {
      setting_dialog: false,
      settings: {
        seller_branch: '00000',
        show_remark_cancel_abb: false
      },
      vat_type: 'inclusive_vat' as 'inclusive_vat' | 'exclusive_vat',
      qr_dialog: false,
      is_order_exist: false,
      loading: false,
      error_message: '',
      order_json_form: {
        number: '',
        remark: '',
        trackingno: '',
        status: 'Pending',
        customername: '',
        customerphone: '',
        shippingaddress: '',
        shippingchannel: '',
        amount: 0,
        createdatetimeString: '',
        discountamount: 0,
        isCOD: false,
        orderdateString: '',
        paymentamount: '',
        payments: [],
        paymentstatus: 'Pending',
        saleschannel: '-',
        shippingname: '',
        orderdate: null as any,
        createdatetime: null as any,
        list: [] as { totalprice: number }[],
        is_confirm_received: true,
        vattype: 3,
        vatamount: 0,
        extra: {
          branch_number: '00000', // seller branch
          ref_tax_invoice_number: ''
        } as any
      },
      form_errors: {} as any,
      pos_number: '',
      running_number: '',
      last_order_number: '',
      pick_order: {} as any
    }
  },
  computed: {
    grandTotal (): number {
      const total_price = this.order_json_form.list.reduce(
        (acc: number, item: any) => acc + item.totalprice,
        0
      )
      if (this.vat_type === 'inclusive_vat') {
        return total_price
      } else if (this.vat_type === 'exclusive_vat') {
        return total_price * 1.07
      }
      return 0
    }
  },

  created () {
    if (!this.$hasPerms('add_taxdocument')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }

    this.getLocalStorageData()
  },

  methods: {
    fillNextRunningNumber () {
      const last_running_number = localStorage.getItem(
        'easy_etax.last_running_number'
      )
      if (last_running_number) {
        if (isNaN(Number(last_running_number))) {
          alert('ไม่สามารถกำหนดหมายเลขถัดไปได้\nหมายเลขล่าสุด: ' + last_running_number)
          return
        }

        // count zero padding
        const zeroPadding =
          last_running_number.length -
          parseInt(last_running_number).toString().length

        // generate next running number
        this.running_number = (parseInt(last_running_number) + 1)
          .toString()
          .padStart(zeroPadding + 1, '0')
      }
    },

    async submit () {
      if (!(this.$refs.form as any).validate()) {
        return
      }

      await this.createEtaxOrder()
    },

    async createEtaxOrder () {
      if (this.loading) {
        return
      }

      const form = this.$refs.form as any as VForm
      if (!form.validate()) {
        return
      }

      this.loading = true

      // format date
      this.order_json_form.orderdate = this.$moment().toDate()
      this.order_json_form.createdatetime = this.$moment().toDate()
      this.order_json_form.orderdateString =
        this.$moment().format('YYYY-MM-DD')
      this.order_json_form.createdatetimeString = this.$moment().format(
        'YYYY-MM-DD HH:mm:ss'
      )

      // format order number
      this.order_json_form.number = this.pos_number + this.running_number

      // format total price (for etax calculation)
      this.order_json_form.amount = Number(this.grandTotal.toFixed(2))
      this.order_json_form.extra.branch_number = this.settings.seller_branch
      this.order_json_form.saleschannel = this.settings.seller_branch
      if (this.vat_type === 'inclusive_vat') {
        this.order_json_form.vatamount = Number((this.order_json_form.amount * (1 - 1 / 1.07)).toFixed(4))
      } else if (this.vat_type === 'exclusive_vat') {
        this.order_json_form.vatamount = Number(((this.order_json_form.amount / 1.07) * 0.07).toFixed(4))
      }

      if (this.settings.show_remark_cancel_abb) {
        this.order_json_form.extra.ref_tax_invoice_number = this.order_json_form.number
      }

      try {
        const res = await this.$axios.post('/api/picking/easy-orders/', {
          order_json: this.order_json_form,
          auto_gen_order_number: false
        })
        this.is_order_exist = false
        this.pick_order = res.data
        this.order_json_form = this.pick_order.order_json
        this.order_json_form.saleschannel = this.pick_order.order_saleschannel
        this.order_json_form.list = this.pick_order.order_json.list

        this.setlocalStorage()
        this.$snackbar('success', this.$t('order-center.easy-order-created'))
        this.last_order_number = this.order_json_form.number
        this.running_number = ''
        form.resetValidation()

        this.$router.push({
          path: `/easy-etax/${this.order_json_form.number}/qr-code`
        })
      } catch (error: any) {
        console.error(error)

        if (error.response) {
          this.loading = false
          if (error.response.status === 400) {
            if (error.response.data.status[0] === 'THIS_ORDER_ALREADY_EXISTS') {
              this.confirmOrderExistDialog()
            } else {
              this.error_message = `
              <div>พบข้อผิดพลาด ไม่สามารถสร้างออเดอร์ที่มีเลขที่คำสั่งซื้อซ้ำได้</div>
              <ul>
              <li>โปรดตรวจสอบเลขที่คำสั่งซิ้อใหม่อีกครั้ง</li>
              </ul>
            `
            }

            this.form_errors = error.response.data
            this.is_order_exist = true
          } else {
            this.$snackbar('error', this.$t('error.unknown'))
          }
        }
      }
      this.loading = false
    },

    updateVatType (value: 'inclusive_vat' | 'exclusive_vat') {
      this.vat_type = value
      this.order_json_form.vattype = value === 'inclusive_vat' ? VAT_TYPE_INC : VAT_TYPE_EXC
      localStorage.setItem('easy_etax.vat_type', value)
    },

    updateLocalStorageSetting (data: any) {
      this.settings = data
      localStorage.setItem('easy_etax.settings', JSON.stringify(data))
    },

    setlocalStorage () {
      localStorage.setItem('easy_etax.pos_number', this.pos_number)
      localStorage.setItem(
        'easy_etax.last_running_number',
        this.running_number
      )
      localStorage.setItem(
        'easy_etax.order_list',
        JSON.stringify(this.order_json_form.list)
      )
    },

    getLocalStorageData () {
      const posNumber = localStorage.getItem('easy_etax.pos_number')
      if (posNumber) {
        this.pos_number = posNumber
      }

      const lastRunningNumber = localStorage.getItem(
        'easy_etax.last_running_number'
      )
      if (posNumber && lastRunningNumber) {
        this.last_order_number = this.pos_number + lastRunningNumber
      }

      // Get order list
      const orderList = localStorage.getItem('easy_etax.order_list')
      const parsedOrderList = orderList ? JSON.parse(orderList) : []

      if (parsedOrderList.length > 0) {
        this.order_json_form.list = parsedOrderList.map((item: any) => {
          return {
            ...item,
            totalprice: ''
          }
        })
      }

      // vat type
      const vat_type = localStorage.getItem('easy_etax.vat_type')
      if (vat_type) {
        this.vat_type = vat_type as 'inclusive_vat' | 'exclusive_vat'
        this.order_json_form.vattype = this.vat_type === 'inclusive_vat' ? VAT_TYPE_INC : VAT_TYPE_EXC
      } else {
        localStorage.setItem('easy_etax.vat_type', 'inclusive_vat')
        this.vat_type = 'inclusive_vat'
        this.order_json_form.vattype = this.vat_type === 'inclusive_vat' ? VAT_TYPE_INC : VAT_TYPE_EXC
      }

      // settings
      const settings = localStorage.getItem('easy_etax.settings')
      if (settings) {
        this.settings = JSON.parse(settings)
      }
    },

    async confirmOrderExistDialog () {
      await this.$store.dispatch('alert/confirm', {
        title: 'หมายเลขคำสั่งซื้อนี้ถูกใช้ไปแล้ว',
        text: 'ไม่สามารถสร้างคำสั่งซื้อ ที่มีหมายเลขซ้ำกับออเดอร์อื่นได้',
        theme: 'error'
      })
    }
  }
})
</script>

<style scoped>
.btns {
  display: flex;
  gap: 10px;
  justify-content: center;
}
</style>
