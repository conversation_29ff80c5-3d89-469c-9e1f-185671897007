<template>
  <v-dialog :value="openDialog" persistent max-width="500px">
    <v-card class="rounded-lg">
      <!-- Enhanced Header -->
      <v-card-title class="dialog-header d-flex align-center">
        <div class="d-flex align-center">
          <v-icon left color="primary" class="mr-2">
            mdi-qrcode-scan
          </v-icon>
          <span class="text-h5 font-weight-bold">สแกนเพื่อขอใบกำกับภาษี</span>
        </div>
        <v-spacer />
        <v-btn icon small class="ml-2" @click="closeDialog">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>

      <!-- Order Information Card -->
      <v-card-text class="pa-6 print-container">
        <v-card outlined class="info-card mb-6">
          <v-list dense>
            <v-list-item>
              <v-list-item-content>
                <div class="d-flex justify-space-between">
                  <span class="font-weight-medium">เลขออเดอร์:</span>
                  <span class="text--primary">{{ order.order_number }}</span>
                </div>
              </v-list-item-content>
            </v-list-item>
            <v-divider />
            <v-list-item>
              <v-list-item-content>
                <div class="d-flex justify-space-between">
                  <span class="font-weight-medium">ราคารวม:</span>
                  <span class="text--primary">฿{{ order.order_json?.list[0].totalprice || 0 }}</span>
                </div>
              </v-list-item-content>
            </v-list-item>
          </v-list>
        </v-card>

        <!-- QR Code Section -->
        <div class="text-center print-container mb-6">
          <v-img
            :src="qrCodeUrl"
            max-width="250"
            class="mx-auto"
            :aspect-ratio="1"
            contain
          />
          <div>สแกนเพื่อขอใบกำกับภาษี</div>
        </div>

        <!-- Phone Input Section -->
        <div class="no-print">
          <v-text-field
            v-model="phoneNumber"
            label="เบอร์โทรศัพท์"
            :rules="[$rules.phoneNumberFormat]"
            outlined
            dense
            clearable
            placeholder="กรุณาใส่เบอร์โทรศัพท์"
            prepend-inner-icon="mdi-phone"

            @input="handlePhoneInput"
          />

          <v-btn
            block
            color="primary"
            :disabled="!isPhoneValid"
            class="mb-4"
            @click="$emit('update-sms')"
          >
            <v-icon left>
              mdi-content-save
            </v-icon>
            บันทึกเบอร์โทรศัพท์
          </v-btn>
        </div>
      </v-card-text>

      <!-- Action Buttons -->
      <v-card-actions class="action-buttons pa-6 pt-0">
        <v-row>
          <v-col cols="6">
            <v-btn
              block
              outlined
              color="primary"
              :loading="printLoading"
              @click="handlePrint"
            >
              <v-icon left>
                mdi-printer
              </v-icon>
              พิมพ์
            </v-btn>
          </v-col>
          <v-col cols="6">
            <v-btn
              block
              outlined
              color="secondary"
              :loading="smsLoading"
              :disabled="!isPhoneValid || !phoneNumber"
              @click="handleSMS"
            >
              <v-icon left>
                mdi-send
              </v-icon>
              ส่ง SMS
            </v-btn>
          </v-col>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'TaxInvoiceDialog',
  props: {
    order: {
      type: Object,
      required: true
    },
    openDialog: {
      type: Boolean,
      required: true
    }
  },

  data () {
    return {
      isFormValid: false,
      phoneNumber: this.order.order_customerphone || '',
      smsLoading: false,
      printLoading: false

    }
  },

  computed: {
    qrCodeUrl () {
      const company_id = this.$auth.user.company.uuid
      const qrcode = `${window.location.origin}/public/etax-invoice?cid=${company_id}%26oid=${this.order.uuid}`
      return `https://go-barcode-yuo4mnnlaa-as.a.run.app/qrcode/?data=${qrcode}`
    },
    isPhoneValid () {
      const phone = this.order.order_customerphone || ''
      const phoneRegex = /^(66\d{9}|0\d{9}|0\d{2}-\d{3}-\d{4}|02\d{7})$/
      return phoneRegex.test(phone)
    }
  },

  methods: {
    closeDialog () {
      this.$emit('update:openDialog', false)
    },

    handlePhoneInput (value) {
      this.update({
        order_customerphone: value,
        order_json: { ...this.order.order_json, customerphone: value }
      })
    },

    handlePrint () {
      this.printLoading = true
      try {
        window.print()
        this.$snackbar('success', 'พิมพ์สำเร็จ')
      } catch (error) {
        this.$snackbar('error', 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง')
      }
      this.printLoading = false
    },

    async handleSMS () {
      this.smsLoading = true
      try {
        await this.$axios.post('/api/etax/send-link/', {
          order_uuid: this.order.uuid
        })
        this.$snackbar('success', 'ส่ง SMS สำเร็จ')
      } catch (error) {
        this.$snackbar('error', 'ส่ง SMS ไม่สำเร็จ กรุณาลองใหม่อีกครั้ง')
      }
      this.smsLoading = false
    },

    update (event) {
      const updatedOrder = { ...this.order, ...event }
      this.$emit('update:order', updatedOrder)
    }
  }
}
</script>

<style scoped>
.dialog-header {
  background-color: var(--v-primary-lighten5);
  border-bottom: 1px solid var(--v-primary-lighten4);
  padding: 16px 24px;
}

.info-card {
  background-color: var(--v-background-base);
  border: 1px solid var(--v-primary-lighten4) !important;
  border-radius: 8px;
}

.action-buttons {
  background-color: var(--v-background-base);
  border-top: 1px solid var(--v-primary-lighten4);
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .v-dialog {
    margin: 0;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    max-width: none !important;
  }

  .v-card {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .v-card-text {
    flex: 1;
    overflow-y: auto;
  }
}
@media print {
  body {
    margin: 0;
    padding: 0;
  }

  .page {
    border: none;
  }

  .no-print {
    display: none;
  }
}
</style>
