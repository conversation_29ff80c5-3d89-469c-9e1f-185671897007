<template>
  <v-card style="max-width: 600px;" outlined class="rounded-lg mx-auto">
    <!-- Enhanced Header -->
    <v-card-title class="dialog-header d-flex align-center no-print">
      <div class="d-flex align-center">
        <v-icon left color="primary" class="mr-2">
          mdi-qrcode-scan
        </v-icon>
        <span class="text-h5 font-weight-bold">ขอใบกำกับภาษี E-Tax</span>
      </div>
      <v-spacer />
      <v-btn icon small class="ml-2" @click="$router.go(-1)">
        <v-icon>mdi-close</v-icon>
      </v-btn>
    </v-card-title>

    <!-- Order Information Card -->
    <v-card-text v-if="order">
      <v-card outlined class="info-card no-print">
        <v-list dense>
          <v-list-item>
            <v-list-item-content>
              <div class="d-flex justify-space-between">
                <span class="font-weight-medium">เลขออเดอร์:</span>
                <span class="text--primary">
                  {{ order.order_number }}
                </span>
              </div>
            </v-list-item-content>
          </v-list-item>
          <v-list-item>
            <v-list-item-content>
              <div class="d-flex justify-space-between">
                <span class="font-weight-medium">ราคารวม:</span>
                <span v-if="order.order_json" class="text--primary">฿{{ order.order_json.amount || 0 }}</span>
              </div>
            </v-list-item-content>
          </v-list-item>
        </v-list>
      </v-card>
      <div class="text-center no-print">
        <v-btn x-small outlined color="secondary" :to="`/easy-etax/${order.order_number}/edit`">
          <v-icon left x-small>
            mdi-pencil
          </v-icon> แก้ไขออเดอร์
        </v-btn>
      </div>
      <v-divider class="my-6 no-print" />

      <!-- QR Code Section -->
      <div class="text-center print-container mb-6">
        <div contenteditable style="color: black;">
          {{ settings.ETAX_HEADER_MESSAGE_BILL }}
        </div>
        <img
          :src="qrCodeUrl"
          style="max-width: 250px; object-fit: contain;"
          class="mx-auto"
          :aspect-ratio="1"
        >
        <div contenteditable style="color: black;">
          {{ settings.ETAX_FOOTER_MESSAGE_BILL }}
        </div>
      </div>

      <!-- Phone Input Section -->
      <div class="no-print">
        <v-text-field
          v-model="order.order_customerphone"
          label="เบอร์โทรศัพท์"
          outlined
          dense
          clearable
          placeholder="กรุณาใส่เบอร์โทรศัพท์"
          prepend-inner-icon="mdi-phone"
          hide-details="auto"
          :rules="[$rules.phoneNumberFormat]"
        />

        <v-row dense class="mt-2">
          <v-col cols="6">
            <v-btn
              block
              outlined
              color="primary"
              :loading="printLoading"
              @click="handlePrint"
            >
              <v-icon left>
                mdi-printer
              </v-icon>
              พิมพ์
            </v-btn>
          </v-col>
          <v-col cols="6">
            <v-btn
              block
              outlined
              color="secondary"
              :loading="smsLoading"
              @click="handleSMS"
            >
              <v-icon left>
                mdi-send
              </v-icon>
              ส่ง SMS
            </v-btn>
          </v-col>
        </v-row>
      </div>

      <v-divider class="my-8 no-print" />

      <div class="no-print">
        <v-btn color="primary" to="/easy-etax/create" block>
          Easy E-Tax ใหม่
        </v-btn>
      </div>
    </v-card-text>

    <!-- Action Buttons -->
    <v-card-actions class="action-buttons" />
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import { LoggedInUser, PickOrder } from '~/models'
import { SettingState } from '~/store/settings'

export default Vue.extend({
  data () {
    return {
      isFormValid: false,
      smsLoading: false,
      printLoading: false,
      order: null as PickOrder | null
    }
  },

  computed: {
    qrCodeUrl (): string {
      if (!this.order) {
        return ''
      }

      const user = this.$auth.user as any as LoggedInUser
      const company_id = user.company.uuid
      const qrcode = `${window.location.origin}/public/etax-invoice?cid=${company_id}%26oid=${this.order.uuid}`
      return `https://go-barcode-yuo4mnnlaa-as.a.run.app/qrcode/?data=${qrcode}`
    },
    settings (): SettingState {
      return this.$store.state.settings.company
    }
    // isPhoneValid () {
    //   const phone = this.order.order_customerphone || ''
    //   const phoneRegex = /^(66\d{9}|0\d{9}|0\d{2}-\d{3}-\d{4}|02\d{7})$/
    //   return phoneRegex.test(phone)
    // }
  },

  created () {
    this.getPickOrder(this.$route.params.order_number)
  },

  methods: {
    // handlePhoneInput (value) {
    //   this.update({
    //     order_customerphone: value,
    //     order_json: { ...this.order.order_json, customerphone: value }
    //   })
    // },
    async getPickOrder (order_number: string) {
      try {
        const res = await this.$axios.get(`/api/picking/easy-orders/etax/${order_number}/`)
        this.order = res.data
      } catch (error: any) {
        this.$snackbar('error', this.$t('error.unknown'))
      }
    },
    handlePrint () {
      this.printLoading = true
      try {
        window.print()
        this.$snackbar('success', 'พิมพ์สำเร็จ')
      } catch (error) {
        this.$snackbar('error', 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง')
      }
      this.printLoading = false
    },

    async handleSMS () {
      if (this.order === null) {
        return
      }

      this.smsLoading = true
      try {
        await this.$axios.post('/api/etax/send-link/', {
          order_uuid: this.order.uuid,
          phone_number: this.order.order_customerphone
        })
        this.$snackbar('success', 'ส่ง SMS สำเร็จ')
      } catch (error) {
        this.$snackbar('error', 'ส่ง SMS ไม่สำเร็จ กรุณาลองใหม่อีกครั้ง')
      }
      this.smsLoading = false
    }

    // update (event) {
    //   const updatedOrder = { ...this.order, ...event }
    //   this.$emit('update:order', updatedOrder)
    // }
  }

})
</script>

<style scoped>
.dialog-header {
  background-color: var(--v-primary-lighten5);
  border-bottom: 1px solid var(--v-primary-lighten4);
  padding: 16px 24px;
}

.info-card {
  background-color: var(--v-background-base);
  border: 1px solid var(--v-primary-lighten4) !important;
  border-radius: 8px;
}

.action-buttons {
  background-color: var(--v-background-base);
  border-top: 1px solid var(--v-primary-lighten4);
}

/* Print-specific styles */
@media print {
   @page {
    size: 80mm 80mm;
    margin: 0;
  }
  body {
    margin: 0;
    padding: 0;
  }

  .page {
    border: none;
  }

  .no-print {
    display: none !important;
    visibility: hidden !important;
  }

  /* Ensure print button visibility for preview adjustments */
  .print-button {
    display: none !important;
  }
}
</style>
