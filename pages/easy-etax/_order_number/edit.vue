<template>
  <div style="max-width: 600px; margin: 0 auto;">
    <v-card outlined>
      <v-card-title style="background-color: #FDECCA">
        <span class="headline">แก้ไข Easy E-Tax</span>
        <v-spacer />
        <v-btn v-if="!is_read_only" icon @click="vat_setting_dialog=true">
          <v-icon>mdi-cog</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider />

      <v-card-text class="pt-3">
        <div>
          <etax-order-form
            ref="form"
            :is-read-only="is_read_only"
            :order-json.sync="order_json_form"
            :pos-number.sync="pos_number"
            :running-number.sync="running_number"
            :last-order-number="last_order_number"
            :is-update="true"
            :errors="form_errors.order_json || {}"
            mode="update"
            @click:next-running-number="fillNextRunningNumber()"
          />
        </div>
        <v-row dense class="mt-5">
          <v-col cols="12" md="4">
            <!-- Add spacing for balance -->
          </v-col>
          <v-col cols="12" md="8">
            <e-tax-price-details :order-json="order_json_form" :select-vat-type="vat_type" />
          </v-col>
        </v-row>

        <div class="mt-4">
          *แก้ไขได้เฉพาะ ออเดอร์ที่ยังไม่มีการออก ใบกำกับภาษีอีเล็กทรอนิกส์ (E-Tax)
          หากมีการออกเอกสารแล้ว ต้อง "ยกเลิก" เอกสารก่อนทำการแก้ไข
        </div>
      </v-card-text>
      <v-alert v-if="is_read_only" class="mx-3" color="error" outlined>
        คำสั่งซื้อนี้ออก e-Tax ไปแล้ว ไม่สามารถแก้ไขได้
      </v-alert>

      <v-divider />

      <v-card-actions v-if="!is_read_only">
        <v-spacer />
        <v-btn
          :loading="loading"
          color="success"
          @click="updatePickOrder()"
        >
          <v-icon left>
            mdi-content-save
          </v-icon>
          บันทึกการแก้ไข
        </v-btn>
        <v-spacer />
      </v-card-actions>
    </v-card>
    <e-tax-calculation-settings
      :dialog.sync="vat_setting_dialog"
      :vat-type="vat_type"
      @update:vat-type="updateVatType($event)"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import EtaxOrderForm from '~/components/order-center/EtaxOrderForm.vue'

export default Vue.extend({
  components: {
    EtaxOrderForm
  },

  data () {
    return {
      loading: false,
      vat_setting_dialog: false,
      is_read_only: false,
      vat_type: 'inclusive_vat' as 'inclusive_vat' | 'exclusive_vat',
      order_json_form: {
        number: '',
        short_order_number: '',
        remark: '',
        trackingno: '',
        status: 'Pending',
        customername: '',
        customerphone: '',
        shippingaddress: '',
        shippingchannel: '',
        amount: 0,
        createdatetimeString: '',
        discountamount: 0,
        isCOD: false,
        orderdateString: '',
        paymentamount: 0,
        payments: [],
        paymentstatus: 'Pending',
        saleschannel: '-',
        shippingname: '',
        orderdate: null as any,
        createdatetime: null as any,
        list: [] as { totalprice: number }[]
      },
      is_auto_generate_tracking: false,
      form_errors: {} as any,
      pos_number: '',
      running_number: '',
      last_order_number: '',
      pick_order: {} as any
    }
  },
  computed: {
    grandTotal (): number {
      const total_price = this.order_json_form.list.reduce(
        (acc: number, item: any) => acc + item.totalprice,
        0
      )
      if (this.vat_type === 'inclusive_vat') {
        return total_price
      } else if (this.vat_type === 'exclusive_vat') {
        return total_price * 1.07
      }
      return 0
    }
  },

  created () {
    this.getPickOrder(this.$route.params.order_number)
    this.checkETaxHasCreated(this.$route.params.order_number)
    const vat_type = localStorage.getItem('easy_etax.vat_type')
    if (vat_type) {
      this.vat_type = vat_type as 'inclusive_vat' | 'exclusive_vat'
    } else {
      this.vat_type = 'inclusive_vat'
    }
  },

  methods: {
    async getPickOrder (order_number: string) {
      try {
        const res = await this.$axios.get(`/api/picking/easy-orders/etax/${order_number}/`)
        this.pick_order = res.data

        this.order_json_form = this.pick_order.order_json
      } catch (error: any) {
        this.$snackbar('error', this.$t('error.unknown'))
        // const err = error.response.data
        // if (err.non_field_errors) {
        //   await this.confirmETaxHasCreated(this.$t(err.non_field_errors[0].code))
        //   this.$router.push('/easy-etax/create')
        // }
      }
    },
    async checkETaxHasCreated (order_number: string) {
      try {
        await this.$axios.get(`/api/etax/easy-orders/check-etax/${order_number}/`)
      } catch (error:any) {
        const err = error.response.data
        if (err.non_field_errors) {
          this.is_read_only = true
        }
      }
    },

    async updatePickOrder () {
      this.loading = true

      this.order_json_form.amount = Number(this.grandTotal.toFixed(2))
      this.order_json_form.paymentamount = Number(this.grandTotal.toFixed(2))

      try {
        await this.$axios.put(`/api/picking/easy-orders/etax/${this.order_json_form.number}/`, {
          order_json: this.order_json_form
        })
        this.$snackbar('success', this.$t('etax.easy-order-updated'))
        this.$router.go(-1)
      } catch (error: any) {
        this.$snackbar('error', this.$t('error.unknown'))
      }

      this.loading = false
    },

    updateVatType (vat_type: 'inclusive_vat' | 'exclusive_vat') {
      this.vat_type = vat_type
      localStorage.setItem('easy_etax.vat_type', vat_type)
    }
  }
})
</script>

<style scoped>
.btns {
  display: flex;
  gap: 10px;
  justify-content: center;
}
</style>
