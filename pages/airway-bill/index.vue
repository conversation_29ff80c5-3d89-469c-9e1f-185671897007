<template>
  <v-row wrap>
    <v-col cols="12">
      <h2>Airway Bill</h2>
      <v-divider />
    </v-col>

    <v-col cols="12">
      <h4>{{ $t('airway-bill.scan') }}</h4>
      <v-form ref="printform" @submit.prevent="print(printer_id)">
        <v-row dense>
          <v-col cols="12" md="4">
            <printnode-printer-select
              v-model="printer_id"
              filled
              label="PrintNode Printer"
              hide-details
              :rules="[$rules.required]"
            />
          </v-col>
          <v-col cols="12" md="6">
            <v-text-field
              v-model="print_order_number"
              filled
              :label="$t('airway-bill.order-number')"
              :rules="[v => !!v || $t('airway-bill.fill')]"
              :hint="$t('airway-bill.enter-to-print')"
              persistent-hint
            />
          </v-col>
          <v-col cols="12" md="2">
            <v-btn block style="height: 54px;" type="submit" color="success" :loading="loading">
              {{ $t('airway-bill.print') }}
            </v-btn>
          </v-col>
        </v-row>
      </v-form>
      <!-- <v-divider class="mt-5 pt-5" /> -->
    </v-col>

    <v-col v-if="$hasPerms('add_airwaybill')" cols="12">
      <h4>{{ $t('airway-bill.upload') }} (Airway Bill)</h4>
      <v-form @submit.prevent="">
        <v-row wrap>
          <v-col cols="12" md="4">
            <v-file-input
              v-model="file"
              label="เลือกไฟล์ AirwayBill"
              truncate-length="15"
              :hint="$t('airway-bill.file-upload')"
              persistent-hint
              accept="application/pdf"
            />
          </v-col>
          <v-col cols="12" md="4">
            <v-select
              v-model="upload_mode"
              label="ประเภทไฟล์"
              :items="[
                { text: 'Shopee, Lazada (1 ออเดอร์ = 1 หน้า)', value: 'normal'},
                { text: 'Lazada (1 ออเดอร์ = 2 หน้า)', value: 'lazada'},
                { text: 'TikTok (1 ออเดอร์ = 1 หน้า)', value: 'tiktok'}
              ]"
              :rules="[$rules.required]"
            />
          </v-col>
          <v-col cols="12" md="2">
            <v-btn
              block
              :loading="loading"
              :disabled="!file"
              class="mt-3"
              color="success"
              @click="onFileUploadSubmit(upload_mode)"
            >
              Upload
            </v-btn>
          </v-col>
        </v-row>
        <br>
      </v-form>
    </v-col>

    <v-col cols="12">
      <h4>{{ $t('airway-bill.cover-sheet') }} (Airway Bill)</h4>
      <v-text-field v-model="search" prepend-icon="mdi-magnify" class="mt-2" dense :placeholder=" $t('airway-bill.search')" />
    </v-col>
    <v-col cols="12">
      <v-data-iterator :items="airway_bills" :search="search" :options.sync="pagination" :server-items-length="total_items">
        <template #default="props">
          <v-row>
            <v-col v-for="item in props.items" :key="item.id" cols="2">
              <a :href="item.file" target="_blank">
                {{ item.order_number }}
              </a>
            </v-col>
          </v-row>
        </template>
      </v-data-iterator>
    </v-col>
  </v-row>
</template>
<script lang="ts">
import Vue from 'vue'
import PrintnodePrinterSelect from '~/components/printers/PrintnodePrinterSelect.vue'
import { FEATURE_FLAGS } from '~/models'
export default Vue.extend({
  components: {
    PrintnodePrinterSelect
  },

  middleware: 'auth',

  data () {
    return {
      airway_bills: [] as any[],
      printers: [] as any[],

      printer_id: '',
      print_order_number: '',

      file: null,
      loading: false,

      search: '',
      pagination: {
        page: 1,
        itemsPerPage: 30
      },
      total_items: -1,
      upload_mode: 'normal'
    }
  },

  head () {
    return {
      title: 'Dobybot.com - Airway Bill'
    }
  },

  watch: {
    pagination: {
      async handler (val) {
        const { airway_bills, meta } = await this.getAirwayBills(this.search, val)
        this.airway_bills = airway_bills
        this.total_items = meta.total_results
      },
      deep: true,
      immediate: true
    },
    search: {
      async handler (val) {
        this.pagination.page = 1
        const { airway_bills, meta } = await this.getAirwayBills(val, this.pagination)
        this.airway_bills = airway_bills
        this.total_items = meta.total_results
      }
    },
    printer_id: {
      handler (val) {
        localStorage.setItem('airway-bill.printer_id', val)
      }
    }
  },

  created () {
    if (!this.$hasCompanyFeatureFlag(FEATURE_FLAGS.PRINTING)) {
      this.$nuxt.error({ statusCode: 400, message: this.$t('error.feature_flag') })
    }

    if (!this.$hasPerms('view_airwaybill')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }
  },

  mounted () {
    (async () => {
      this.printers = await this.$store.dispatch('printnode/getPrinters')
      this.printers = this.printers.filter(x => x.state === 'online')
      this.printer_id = localStorage.getItem('airway-bill.printer_id') || ''
    })()
  },

  methods: {
    async onFileUploadSubmit (mode: 'normal'|'lazada'|'tiktok') {
      if (!this.file) {
        return
      }

      this.$loader(true)
      await this.uploadAirwayBill(this.file, mode)
      this.$loader(false)

      this.pagination.page = 1
      const { airway_bills, meta } = await this.getAirwayBills(this.search, this.pagination)
      this.airway_bills = airway_bills
      this.total_items = meta.total_results
    },

    async getAirwayBills (search: string, pagination: { page: number, itemsPerPage: number }) {
      const action = '/api/picking/resource/airway-bills/'
      const options = {
        params: {
          page: pagination.page,
          per_page: pagination.itemsPerPage,
          'filter{order_number.icontains}': search
        }
      }
      const response = await this.$axios.get(action, options)
      return response.data
    },

    async uploadAirwayBill (file: File | null, mode: 'normal'|'lazada'|'tiktok') {
      try {
        const action = this.getUploadUrl(mode)
        const data = new FormData()
        if (file) {
          data.append('file', file)
        }

        const options = { headers: { 'Content-Type': 'multipart/form-data' } }
        await this.$axios.post(action, data, options)
      } catch (error: any) {
        console.error(error)
        if (error.response) {
          alert(
            'Something went wrong\n' +
              `Status: ${error.response.status}\n` +
              `Error: ${JSON.stringify(error.response.data)}`
          )
        } else {
          alert('Something went wrong\n' + error)
        }
      }
    },

    getUploadUrl (mode: 'normal'|'lazada'|'tiktok') {
      if (mode === 'normal') {
        return '/api/picking/airway-bills/split/'
      } else if (mode === 'lazada') {
        return '/api/picking/airway-bills/split/lazada/'
      } else if (mode === 'tiktok') {
        return '/api/picking/airway-bills/split/tiktok/'
      }
    },

    async print (printer?: string) {
      this.print_order_number = this.print_order_number.trim()

      if (!(this as any).$refs.printform.validate()) {
        return
      }

      this.loading = true
      try {
        await this.$axios.post(`/api/picking/airway-bills/${this.print_order_number}/print/`, { printer })
        this.$snackbar(this.$t('airway-bill.success'), this.$t('airway-bill.send-finish'))
        this.print_order_number = ''
      } catch (error: any) {
        if (error.response) {
          if (error.response.status === 400) {
            alert(error.response.data)
            this.print_order_number = ''
          } else {
            alert(
              'Something went wrong\n' +
                `Status: ${error.response.status}\n` +
                `Error: ${JSON.stringify(error.response.data)}`
            )
            console.error(error)
          }
        } else {
          alert('Something went wrong\n' + error)
          console.error(error)
        }
      }
      this.loading = false
    }
  }
})
</script>
