<template>
  <div>
    <v-container fluid>
      <v-row>
        <v-col cols="12">
          <h1 class="text-h4 mb-6">
            Admin Tools
          </h1>

          <!-- Form Section -->
          <v-card class="mb-6" outlined>
            <v-card-title>
              <v-icon left>
                mdi-cog
              </v-icon>
              Pick Orders Tasks
            </v-card-title>
            <v-card-text>
              <v-form ref="form" @submit.prevent="submitForm">
                <v-row>
                  <!-- Order Numbers Input -->
                  <v-col cols="12" md="6">
                    <v-select
                      v-model="form.taskType"
                      label="Task Type (ประเภท Task)"
                      :items="taskTypeOptions"
                      item-text="label"
                      item-value="value"
                      filled
                      :rules="[$rules.required]"
                      :error-messages="formErrors.task_type"
                      data-test="task-type-select"
                    />
                    <v-textarea
                      v-model="form.orderNumbers"
                      label="Order Numbers"
                      placeholder="1 แถว 1 เลขออเดอร์"
                      rows="6"
                      filled
                      :rules="[$rules.required]"
                      :error-messages="formErrors.order_numbers"
                      data-test="order-numbers-textarea"
                    />
                  </v-col>

                  <v-col cols="12" md="6">
                    <ul style="list-style-type: none;">
                      <li v-for="log in logs" :key="log.name">
                        <v-btn x-small icon @click="copyText(log.href)">
                          <v-icon small>
                            mdi-content-copy
                          </v-icon>
                        </v-btn>
                        <a target="_blank" :href="log.href">{{ log.name }}</a>
                      </li>
                    </ul>
                  </v-col>
                </v-row>

                <!-- Submit Button -->
                <v-row>
                  <v-col cols="12">
                    <v-btn
                      type="submit"
                      color="primary"
                      :loading="loading"
                      :disabled="loading"
                      data-test="submit-button"
                    >
                      <v-icon left>
                        mdi-play
                      </v-icon>
                      Execute Tasks
                    </v-btn>
                  </v-col>
                </v-row>
              </v-form>
            </v-card-text>
          </v-card>

          <!-- Results Table -->
          <v-card v-if="results.length > 0" outlined>
            <!-- Error Alert -->
            <v-alert
              v-if="errorMessage"
              type="error"
              dismissible
              class="mb-6"
              @input="errorMessage = ''"
            >
              {{ errorMessage }}
            </v-alert>

            <!-- Success Alert -->
            <v-alert
              v-if="successMessage"
              type="success"
              dismissible
              class="mb-6"
              @input="successMessage = ''"
            >
              {{ successMessage }}
            </v-alert>

            <v-card-title>
              <v-icon left>
                mdi-table
              </v-icon>
              Results
            </v-card-title>
            <v-card-text>
              <v-data-table
                :headers="resultsHeaders"
                :items="results"
                :loading="loading"
                class="elevation-1"
                data-test="results-table"
              />
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { LoggedInUser, VForm } from '~/models'

interface FormData {
  orderNumbers: string
  taskType: string
}

interface TaskTypeOption {
  label: string
  value: string
}

export default Vue.extend({
  middleware: ['auth'],

  data () {
    return {
      form: {
        orderNumbers: '',
        taskType: ''
      } as FormData,

      formErrors: {
        order_numbers: [],
        task_type: []
      },

      loading: false,
      errorMessage: '',
      successMessage: '',
      results: [] as any[],

      taskTypeOptions: [
        {
          label: 'Create Zort Update Order Status Task',
          value: 'create_zort_update_order_status_task'
        },
        {
          label: 'Create Zort Ready to Ship Task',
          value: 'create_zort_ready_to_ship_task'
        },
        {
          label: 'Create VRich Checkout Task',
          value: 'create_vrich_checkout_task'
        },
        {
          label: 'Create Lazada Ready to Ship Task',
          value: 'create_lazada_ready_to_ship_task'
        },
        {
          label: 'Create NocNoc Ready to Ship Task',
          value: 'create_nocnoc_ready_to_ship_task'
        },
        {
          label: 'Create Ready to Ship Messaging Task',
          value: 'create_ready_to_ship_messaging_task'
        }
      ] as TaskTypeOption[],

      resultsHeaders: [
        {
          text: '#',
          value: 'index',
          sortable: false,
          width: '80px'
        },
        {
          text: 'Task Name',
          value: 'task_name',
          sortable: false
        }
      ],

      logs: [
        {
          name: 'Log: zort-update-order-status',
          href: 'https://console.cloud.google.com/logs/query;query=logName%3D%22projects%2Fdobybot%2Flogs%2Fzort-update-order-status%22;duration=P1D?project=dobybot'
        },
        {
          name: 'Log: zort-ready-to-ship',
          href: 'https://console.cloud.google.com/logs/query;query=logName%3D%22projects%2Fdobybot%2Flogs%2Fzort-ready-to-ship%22;duration=P1D?project=dobybot'
        },
        {
          name: 'Log: vrich',
          href: 'https://console.cloud.google.com/logs/query;query=logName%3D%22projects%2Fdobybot%2Flogs%2Fvrich%22;duration=P1D?project=dobybot'
        }

      ]
    }
  },

  created () {
    // @ts-ignore
    const user = this.$auth.user as never as LoggedInUser
    console.log(user)
    if (!user.is_superuser) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }
  },

  methods: {
    async submitForm () {
      const form = this.$refs.form as never as VForm
      if (!form.validate()) {
        return
      }

      this.loading = true
      this.errorMessage = ''
      this.successMessage = ''
      this.formErrors = { order_numbers: [], task_type: [] }
      this.results = []

      try {
        // Parse order numbers from textarea (one per line)
        const orderNumbers = this.form.orderNumbers
          .split('\n')
          .map(line => line.trim())
          .filter(line => line.length > 0)

        if (orderNumbers.length === 0) {
          this.errorMessage = 'Please enter at least one order number'
          return
        }

        const payload = {
          order_numbers: orderNumbers,
          task_type: this.form.taskType
        }

        const response = await this.$axios.post('/api/picking/pick-orders/tasks/', payload)

        if (response.data && response.data.tasks) {
          this.results = response.data.tasks.map((x, i) => ({ index: i + 1, task_name: x }))
          this.successMessage = `สร้าง Task สำเร็จ (${this.results.length} task(s)) กรุณาตรวจสอบ Log`
        } else {
          this.errorMessage = 'Unexpected response format from server'
        }
      } catch (error: any) {
        console.error('API Error:', error)

        if (error.response && error.response.data) {
          // Handle validation errors
          if (error.response.data.order_numbers) {
            this.formErrors.order_numbers = error.response.data.order_numbers
          }
          if (error.response.data.task_type) {
            this.formErrors.task_type = error.response.data.task_type
          }

          // Handle general error message
          if (error.response.data.detail) {
            this.errorMessage = error.response.data.detail
          } else if (error.response.data.non_field_errors) {
            this.errorMessage = error.response.data.non_field_errors.join(', ')
          } else {
            this.errorMessage = 'An error occurred while processing your request'
          }
        } else {
          this.errorMessage = 'Network error. Please check your connection and try again.'
        }
      } finally {
        this.loading = false
      }
    },

    copyText (text: string) {
      navigator.clipboard.writeText(text)
      this.$snackbar('info', 'Copied to clipboard')
    }
  }
})
</script>

<style scoped>
.text-caption {
  font-family: 'Roboto Mono', monospace;
}
</style>
