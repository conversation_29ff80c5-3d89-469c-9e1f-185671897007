<template>
  <div>
    <v-row justify="center">
      <v-col cols="10" sm="6" md="4">
        <v-form @submit.prevent="login()">
          <v-img data-test="dobybot-logo" :src="logo" height="300" contain />
          <v-row class="px-3 mb-5">
            <div class="blue-grey--text text--lighten-3">
              <!-- {{ form.device_id }} -->
            </div>
            <v-spacer />
            <div
              data-test="dbb-version"
              class="blue-grey--text text--lighten-3 text-right"
            >
              {{ $version() }}
            </div>
          </v-row>

          <v-alert v-if="error.detail" data-test="error-alert" outlined dark color="error">
            {{ error.detail }}
          </v-alert>

          <v-text-field
            v-model="form.username"
            data-test="username-field"
            :error-messages="error.username"
            :label="$t('username')"
          />

          <v-text-field
            v-model="form.password"
            data-test="password-field"
            :error-messages="error.password"
            :label="$t('password')"
            type="password"
            autocomplete="current-password"
          />
          <v-row class="px-3" justify="space-between">
            <div class="mt-5">
              <v-btn
                v-for="locale in availableLocales"
                :key="locale.code"
                data-test="change-language"
                text
                small
                @click="$i18n.setLocale(locale.code)"
              >
                <v-icon left>
                  mdi-translate
                </v-icon>
                {{ locale.name }}
              </v-btn>
            </div>
            <div>
              <v-checkbox
                v-model="remember_username"
                data-test="remember-me"
                :label="$t('remember-me')"
                @change="onRememberUsernameChange($event)"
              />
            </div>
          </v-row>
          <v-btn
            id="v-btn-lobin"
            data-test="login-btn"
            class="mt-3"
            block
            color="primary"
            type="submit"
          >
            {{ $t('login') }}
          </v-btn>
        </v-form>
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import type { LocaleObject } from '@nuxtjs/i18n'

import { v4 as uuidv4 } from 'uuid'
import Vue from 'vue'
import { LoggedInUser } from '~/models'
import { SettingState } from '~/store/settings'

export default Vue.extend({
  data () {
    return {
      form: {
        username: '',
        password: '',
        device_id: ''
      },
      error: {
        detail: '',
        username: '',
        password: ''
      },
      logo: '/logo-with-text.png',
      show_contact: true,
      remember_username: false
    }
  },
  computed: {
    session_id (): string {
      return this.$store.state.session_id
    },
    availableLocales (): LocaleObject[] {
      const locales = this.$i18n.locales as LocaleObject[]
      return locales.filter(x => x.code !== this.$i18n.locale)
    },
    settings (): SettingState {
      return this.$store.state.settings
    }
  },
  created () {
    this.form.device_id = this.getDeviceID()
  },
  mounted () {
    const hostname = window.location.hostname
    if (hostname === 'pim.dobybot.com') {
      this.logo = '/pim-x-dobybot-logo.png'
      this.show_contact = false
    }

    const username = localStorage.getItem('login.username')
    if (username) {
      this.form.username = username
      this.remember_username = true
    }
  },
  methods: {
    async login () {
      this.$loader(true, this.$t('logging_in'))
      try {
        await this.$auth.loginWith('local', { data: this.form })

        if (this.remember_username) {
          localStorage.setItem('login.username', this.form.username)
        }

        const user = this.$auth.user as never as LoggedInUser
        if (!user.is_superuser) {
          console.log('[PostHog] identify', user.username)
          this.$posthog.identify(user.username, {
            username: user.username,
            company: user.company.name,
            company_id: user.company.id,
            package: user.company.package
          })
        }

        if (this.session_id) {
          window.location.href = `/$/record/?session_id=${this.session_id}`
        } else {
          window.location.href = '/$/record/'
        }
      } catch (e:any) {
        console.error(e)
        console.log('----------------------------')
        this.error = e.response.data
        this.$auth.reset()
      }
      this.$loader(false)
    },
    getDeviceID () {
      let device_id = localStorage.getItem('login.device_id')

      if (!device_id) {
        device_id = uuidv4().slice(0, 6)
        localStorage.setItem('login.device_id', device_id)
      }

      return device_id
    },

    onRememberUsernameChange (value:boolean) {
      if (!value) {
        localStorage.removeItem('login.username')
      }
    }
  }

})
</script>
