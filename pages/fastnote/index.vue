<template>
  <v-row>
    <v-col cols="12">
      <h2>Fast Note</h2>
      <v-divider />
    </v-col>

    <v-col cols="12">
      <v-form ref="form" @submit.prevent="addFastNote()">
        <v-textarea
          v-model.trim="form.text"
          data-test="fastnote-textarea"
          prepend-icon="mdi-note"
          filled
          :label="$t('fastnote.text')"
          rows="4"
          :rules="[$rules.required]"
        />
        <v-row class="px-2">
          <v-spacer />
          <v-btn data-test="fastnote-clear" text type="button" @click="form.text = ''; $refs.form.reset()">
            CLEAR
          </v-btn>
          <v-btn data-test="fastnote-copy-main" text type="button" color="info" @click="cp(form.text)">
            <v-icon left>
              mdi-content-copy
            </v-icon>
            COPY
          </v-btn>
          <v-btn data-test="fastnote-save" type="submit" color="success">
            <v-icon left>
              mdi-content-save
            </v-icon>
            SAVE
          </v-btn>
        </v-row>
      </v-form>
    </v-col>

    <v-col cols="12" class="mt-5">
      <v-text-field v-model="search" data-test="fastnote-search" dense prepend-icon="mdi-magnify" :placeholder="$t('fixcase.search')" />
      <v-data-table :headers="table.headers" :items="table.items" :search="search" data-test="fastnote-table" :items-per-page="-1">
        <template #body="props">
          <draggable
            v-model="fastnotes"
            tag="tbody"
            handle=".handle"
          >
            <tr
              v-for="(item) in props.items"
              :key="item.index"
              @click="copyToTextarea(item.text)"
            >
              <td class="text-center px-0" style="white-space: nowrap; width: 1%;">
                <v-icon
                  small
                  style="cursor: grabbing;"
                  class="handle"
                >
                  mdi-drag
                </v-icon>
                <v-btn text x-small @click.stop="changeIndex(item.index)">
                  <v-icon small>
                    mdi-swap-vertical
                  </v-icon>
                </v-btn>
              </td>
              <td class="py-1" style="white-space: pre-line;" v-text="item.text" />
              <td class="px-0" style="white-space: nowrap; width: 1%;">
                <v-btn data-test="fastnote-copy" text color="info">
                  <v-icon left>
                    mdi-content-copy
                  </v-icon>
                  COPY
                </v-btn>
                <v-btn data-test="fastnote-delete" icon color="error" @click.stop="deleteFastNote(item.index)">
                  <v-icon>mdi-delete</v-icon>
                </v-btn>
              </td>
            </tr>
          <!-- the row will go here -->
          </draggable>
        </template>
      </v-data-table>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import draggable from 'vuedraggable'
import { copyToClipboard } from '~/api/utils'
import { FEATURE_FLAGS } from '~/models'

export default Vue.extend({
  components: {
    draggable
  },

  data () {
    return {
      fastnotes: [] as string[],
      form: {
        text: ''
      },
      search: ''
    }
  },

  computed: {
    table (): any {
      const headers = [
        { text: this.$t('fastnote.no'), value: 'index', align: 'center', class: 'w-100', sortable: false },
        { text: this.$t('fastnote.text'), value: 'text', sortable: false },
        { text: '', value: 'action', sortable: false }
      ]

      const items = this.fastnotes.map((note, index) => ({
        index,
        text: note
      }))

      return { headers, items }
    }
  },

  created () {
    if (!this.$hasPerms('view_fastnote')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
      return
    }

    ;(async () => {
      try {
        this.fastnotes = await this.getFastNote()
      } catch (error) {
        this.fastnotes = await this.createFastNote()
      }
    })()
  },

  methods: {
    async getFastNote (): Promise<string[]> {
      const company_id = (this.$auth.user as any).company.id
      const res = await this.$axios.get(`/api/fastnotes/fastnote/${company_id}/`)
      return res.data.fastnote.notes
    },

    async createFastNote () {
      const company_id = (this.$auth.user as any).company.id
      const res = await this.$axios.post('/api/fastnotes/fastnote/', { company_id, notes: null })
      return res.data.fastnote.notes
    },

    async updateFastNote () {
      const company_id = (this.$auth.user as any).company.id
      try {
        await this.$axios.put(`/api/fastnotes/fastnote/${company_id}/`, { notes: this.fastnotes })
      } catch (error) {
        console.error(error)
        this.$snackbar('error', this.$t('fastnote.error'))
      }
    },

    changeIndex (oldIndex: number) {
      const promptInput = prompt(this.$t('fastnote.lenght', [this.fastnotes.length]))
      if (promptInput === null) {
        return
      }

      const newIndex = Number(promptInput)
      if (isNaN(newIndex)) {
        return
      }
      const text = this.fastnotes[oldIndex]
      if (newIndex < oldIndex) {
        this.fastnotes.splice(newIndex - 1, 0, text)
        this.fastnotes.splice(oldIndex + 1, 1)
      } else {
        this.fastnotes.splice(newIndex, 0, text)
        this.fastnotes.splice(oldIndex, 1)
      }

      this.updateFastNote()
    },

    addFastNote () {
      const form: any = this.$refs.form
      if (!form.validate() || !this.form.text) {
        return
      }
      this.fastnotes.unshift(this.form.text)

      this.form.text = ''
      form.reset()
      this.updateFastNote()
    },

    deleteFastNote (index: number) {
      if (confirm(this.$t('fastnote.delete'))) {
        this.fastnotes.splice(index, 1)
      }
      this.updateFastNote()
    },

    copyToTextarea (value: string) {
      this.form.text = (this.form.text || '') + ' ' + value
      copyToClipboard(value)
      this.$snackbar('info', `"${value.substring(0, 40)}..." ${this.$t('fastnote.copy')}`)
    },

    cp (value: string) {
      copyToClipboard(value)
      this.$snackbar('info', `"${value.substring(0, 40)}..." ${this.$t('fastnote.copy')}`)
    }
  }
})
</script>

<style scoped>
.w-100 {
  width: 100px;
}
</style>
