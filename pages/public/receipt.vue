<template>
  <v-main class="app">
    <snackbar rounded top />
    <v-container v-if="!loading" style="max-width: 650px">
      <!-- Brand Logo -->
      <v-row class="no-print" wrap>
        <v-col cols="12" class="text-center">
          <div class="text-center">
            <v-img :src="logo_url" max-height="150px" contain />
            <a v-if="!custom_brand" href="https://dobybot.com">
              <v-img
                src="https://storage.googleapis.com/dobybot-public-bucket/images/dobybot/recorded-by-dobybot.png"
                max-height="18"
                contain
                class="mt-3"
              />
            </a>
          </div>
          <div v-if="!custom_brand" style="font-size: 0.63rem">
            {{ $t("public.interest-system") }}
            <a href="https://dobybot.com" class="teal--text">www.dobybot.com</a>
          </div>
        </v-col>
      </v-row>

      <!-- Video Player -->
      <v-row v-if="video" class="no-print px-0">
        <v-col cols="12" class="px-0">
          <div class="text-center mx-auto">
            <div
              class="text-center black white--text"
              style="font-size: 18px; cursor: pointer"
              @click="copy(order.number)"
            >
              {{ $t("public.order-number", [order.number]) }}
            </div>

            <!-- ================================================================= -->
            <!-- Video Player -->
            <!-- ================================================================= -->

            <!-- FAKE VIDEO PLAYER -->
            <div v-if="video_player === 'fake-video-player'">
              <a :href="video.url" target="_blank">
                <v-img
                  src="https://storage.googleapis.com/dobybot-bucket-1/video-player.jpg"
                />
              </a>
            </div>

            <!-- HTML VIDEO PLAYER -->
            <div
              v-if="video_player === 'html-video-player'"
              style="backgroud: black"
            >
              <video
                id="video"
                controls
                :autoplay="!video.audio"
                :muted="!video.audio"
                playsinline
                preload="metadata"
                style="width: 100%"
              >
                <source
                  v-if="video.filetype === 'mp4'"
                  type="video/mp4"
                  :src="source_mp4"
                  @error="onVideoSourceError($event)"
                >
                <source
                  v-if="video.filetype === 'webm'"
                  type="video/webm"
                  :src="source_webm"
                  @error="onVideoSourceError($event)"
                >
                Sorry, your browser doesn't support embedded videos.
              </video>
            </div>

            <!-- GOOGLE DRIVE VIDEO PLAYER -->
            <div v-if="video_player === 'google-drive-player'">
              <div class="iframe-container">
                <iframe
                  :src="video.google_drive_preview_url"
                  width="100%"
                  height="660"
                  allow="autoplay"
                />
              </div>
              <!-- <div class="text-center caption">
                หากไม่สามารถดูวิดีโอได้กรุณา <a :href="video.google_drive_url">เปิดใน Google Drive</a>
              </div> -->
            </div>

            <!-- NO VIDEO -->
            <div v-if="video_player === 'no_video'">
              <div
                class="py-5"
                style="
                  border-left: 1px solid black;
                  border-right: 1px solid black;
                  background: black;
                "
              >
                <div class="text-center">
                  <v-icon size="200" color="white">
                    mdi-video-off
                  </v-icon>
                </div>
                <div class="text-center caption white--text">
                  {{ $t("no-video-recorded") }}
                </div>
              </div>
            </div>

            <!-- LOADING -->
            <div v-if="video_loading">
              <div
                class="py-5"
                style="
                  border-left: 1px solid black;
                  border-right: 1px solid black;
                  background: black;
                "
              >
                <div
                  class="text-center"
                  style="margin-top: 100px; margin-bottom: 100px"
                >
                  <!-- <v-icon size="200" color="white">
                    mdi-video-off
                  </v-icon> -->
                  <v-progress-circular
                    :size="70"
                    :width="7"
                    color="white"
                    indeterminate
                  />
                </div>
                <div class="text-center caption white--text">
                  ... {{ $t("video-loading") }} ...
                </div>
              </div>
            </div>
            <!-- ================================================================= -->

            <div
              v-if="video.scan_logs && video.scan_logs.length > 0"
              class="text-left"
              style="
                overflow-y: scroll;
                border: 1px solid black;
                position: relative;
              "
              :style="{ 'max-height': max_height + 'px' }"
            >
              <record-scan-log-data-table
                :scan-logs="video.scan_logs"
                :columns="['index', 'time_ms', 'barcode', 'name', 'amount']"
                :order-by="{ column: 'time_ms', direction: 'asc' }"
                @click:row="seekTo($event.time_ms)"
              />
            </div>
            <v-row
              v-if="video.scan_logs && video.scan_logs.length > 0"
              justify="end"
              class="mb-2"
            >
              <v-col xs="12" class="text-right py-0 mt-3">
                <v-btn
                  x-small
                  outlined
                  style="width: 80px"
                  @click="max_height > 0 ? (max_height -= 150) : 0"
                >
                  <v-icon left>
                    mdi-menu-up
                  </v-icon>
                  ย่อ
                </v-btn>
                <v-btn
                  x-small
                  outlined
                  style="width: 80px"
                  @click="max_height += 150"
                >
                  <v-icon left>
                    mdi-menu-down
                  </v-icon>
                  ขยาย
                </v-btn>
              </v-col>
            </v-row>
          </div>

          <div v-if="video" class="text-center text-caption">
            บันทึกวิดีโอเมื่อ
            {{ $moment(video.upload_date).format("DD/MM/YYYY HH:mm:ss") }}
          </div>

          <div v-if="video && video.url" class="text-center mt-2">
            <v-btn
              small
              outlined
              color="primary"
              rounded
              :href="`https://drive.google.com/uc?export=download&id=${file_id}&confirm=t`"
            >
              <v-icon left>
                mdi-download
              </v-icon>
              {{ $t("public.download-video") }}
            </v-btn>
          </div>

          <h3
            v-if="company.tel || (company.line_name && company.line_url)"
            class="text-center mt-4"
          >
            {{ $t("public.found-package-not-correct") }}
          </h3>
          <v-row justify="space-around" class="pt-1">
            <v-col v-if="company.tel" cols="6" class="text-center">
              {{ $t("public.click-here-to-call-to-shop") }}
              <v-btn :href="`tel:${company.tel}`" x-large block>
                <v-icon left>
                  mdi-phone
                </v-icon>
                {{ company.tel }}
              </v-btn>
            </v-col>

            <v-col
              v-if="company.line_name && company.line_url"
              cols="6"
              class="text-center"
            >
              {{ $t("line-click-here-to-add-friend") }}
              <v-btn
                :href="company.line_url"
                target="_blank"
                x-large
                block
                color="#40B566"
                class="white--text"
              >
                <!-- <v-img
                  src="/logo/LINE_Brand_icon.png"
                  width="24"
                  height="24"
                  class="mr-2"
                  style="border: 1px solid #dddddd; border-radius: 5px;"
                /> -->
                {{ company.line_name }}
              </v-btn>
            </v-col>
          </v-row>
        </v-col>
      </v-row>

      <!-- Receipt -->
      <v-row v-if="show_receipt_body" wrap class="">
        <v-col cols="12" class="px-0">
          <div
            class="text-center teal darken-1 white--text title"
            style="padding-top: 8px; height: 40px"
          >
            {{ $t("public.receipt") }}
          </div>
        </v-col>
        <v-col cols="12" class="pt-0">
          <h2>{{ company.name }}</h2>
          <div>{{ company.address }}</div>
          <div style="height: 10px" />

          <v-row
            v-if="order.receipt_invoice && order.receipt_invoice.ref_number"
            class="my-1 px-2"
            justify="space-between"
          >
            <span class="my-auto">หมายเลขใบเสร็จ</span>
            <span style="font-size: 18px">{{
              order.receipt_invoice.ref_number
            }}</span>
          </v-row>
          <v-row class="my-1 px-2" justify="space-between">
            <span class="my-auto">{{ $t("date") }}</span>
            <span style="font-size: 18px">{{
              video ? $moment(video.upload_date).format("DD/MM/YYYY") : "-"
            }}</span>
          </v-row>
          <v-row class="my-1 px-2" justify="space-between">
            <span class="my-auto">{{ $t("public.order-num") }}</span>
            <span style="font-size: 18px">
              {{ order.number }}
              <a
                class="no-print copy teal--text"
                href="#"
                @click.prevent="copy(order.number)"
              >{{ $t("public.copy") }}</a>
            </span>
          </v-row>
          <v-row class="my-1 px-2" justify="space-between">
            <span class="my-auto">{{ $t("public.delivery") }}</span>
            <span style="font-size: 18px">{{ displayShippingChannel(order.shippingchannel) }}</span>
          </v-row>
          <v-row class="my-1 px-2" justify="space-between">
            <span class="my-auto">{{ $t("public.parcel") }}</span>
            <span style="font-size: 18px">
              {{ order.trackingno || "-" }}
              <a
                v-if="order.trackingno"
                class="no-print copy teal--text"
                href="#"
                @click.prevent="copy(order.trackingno)"
              >{{ $t("public.copy") }}</a>
            </span>
          </v-row>

          <template v-if="order.receipt_invoice">
            <div style="height: 10px" />

            <v-row
              v-if="order.receipt_invoice.name"
              class="my-1 px-2"
              justify="space-between"
            >
              <span class="my-auto">ชื่อผู้ซื้อ</span>
              <span style="font-size: 18px">{{
                order.receipt_invoice.name
              }}</span>
            </v-row>
            <v-row
              v-if="order.receipt_invoice.tax_id"
              class="my-1 px-2"
              justify="space-between"
            >
              <span class="my-auto">เลขที่ผู้เสียภาษี</span>
              <span style="font-size: 18px">{{
                order.receipt_invoice.tax_id
              }}</span>
            </v-row>
            <v-row
              v-if="order.receipt_invoice.address"
              class="my-1 px-2"
              justify="space-between"
            >
              <span class="my-auto">ที่อยู่ผู้ซื้อ</span>
              <span
                style="font-size: 18px; max-width: 400px; text-align: end"
              >{{ order.receipt_invoice.address }}</span>
            </v-row>
          </template>
        </v-col>

        <v-col cols="12" class="pt-3 px-0">
          <v-simple-table v-if="receipt" class="table">
            <thead class="teal lighten-4">
              <th class="text-left pl-2">
                {{ $t("public.list") }}
              </th>
              <th>{{ $t("public.amount") }}</th>
              <th class="text-right pr-2">
                {{ $t("public.price") }}
              </th>
            </thead>
            <tbody>
              <tr v-for="(item, i) in receipt.items" :key="i">
                <td class="pl-2">
                  {{ item.name }}
                </td>
                <td class="pr-2 text-right">
                  {{ item.number }}
                </td>
                <td class="pr-2 text-right">
                  {{ item.totalprice.toFixed(2) }}
                </td>
              </tr>
            </tbody>
            <tfoot>
              <tr v-if="isNumber(receipt.shipping) && receipt.shipping">
                <th class="text-right">
                  {{ $t("public.freight") }}
                </th>
                <td />
                <td class="pr-2 text-right">
                  {{ receipt.shipping.toFixed(2) }}
                </td>
              </tr>
              <tr v-if="receipt.discount && Number(receipt.discount)">
                <th class="text-right">
                  {{ $t("public.discount") }}
                </th>
                <td />
                <td
                  class="pr-2 text-right red lighten-5 red--text text-ligthen-3"
                >
                  ({{ Number(receipt.discount).toFixed(2) }})
                </td>
              </tr>
              <tr
                v-if="isNumber(receipt.voucheramount) && receipt.voucheramount"
              >
                <th class="text-right">
                  <!-- {{ $t('public.discount') }} -->
                  รายได้จาก Platform
                </th>
                <td />
                <td class="pr-2 text-right lighten-5 text-ligthen-3">
                  {{ receipt.voucheramount.toFixed(2) }}
                </td>
              </tr>
              <template v-if="isNumber(receipt.vatamount) && receipt.vatamount">
                <tr
                  v-if="isNumber(receipt.amount_pretax) && receipt.amount_pretax"
                >
                  <th class="text-right">
                    <!-- {{ $t('public.discount') }} -->
                    รวมก่อนภาษี
                  </th>
                  <td />
                  <td class="pr-2 text-right lighten-5 text-ligthen-3">
                    {{ receipt.amount_pretax.toFixed(2) }}
                  </td>
                </tr>
                <tr>
                  <th class="text-right">
                    <!-- {{ $t('public.discount') }} -->
                    ภาษีมูลค่าเพิ่ม
                  </th>
                  <td />
                  <td class="pr-2 text-right lighten-5 text-ligthen-3">
                    {{ receipt.vatamount.toFixed(2) }}
                  </td>
                </tr>
              </template>
              <tr>
                <td class="text-right font-weight-bold">
                  {{ $t("public.total-payment") }}
                </td>
                <td class="pr-2 text-right">
                  <!-- {{ order_total_number }} -->
                </td>
                <td class="pr-2 text-right font-weight-bold">
                  {{ receipt.net.toFixed(2) }}
                </td>
              </tr>
            </tfoot>
          </v-simple-table>
        </v-col>

        <v-col v-if="order.etax_link" cols="12" class="no-print text-center">
          <div>
            <b>ขอ E-TAX กดที่นี่</b>
          </div>
          <a
            v-ripple
            :href="order.etax_link"
            style="
              border: 2px solid black;
              display: inline-block;
              padding: 10px;
              border-radius: 10px;
            "
          >
            <img src="/logo/etax-button2.png" style="width: 250px">
          </a>
        </v-col>
      </v-row>

      <!-- Warning Cancel/Return Order -->
      <div
        v-if="order.status == 'Voided' || order.status == 'Partial Voided'"
        class="w-100 warning-voided-order mt-5"
        v-html="$t('receipt.void-return-order-message', [order.number])"
      />

      <v-divider class="no-print my-5" />

      <!-- Tracking No. -->
      <v-row v-if="order" class="no-print">
        <v-col cols="12" class="px-0">
          <div
            class="teal darken-1 white--text text-center"
            style="font-size: 18px"
            @click="copy(order.trackingno)"
          >
            {{ $t("public.shipping-number", [displayShippingChannel(order.shippingchannel)]) }}

            <div>
              <span style="font-size: 22px">
                {{ order.trackingno || "-" }}
              </span>
              &nbsp;
              <v-btn x-small style="position: relative; top: -2px">
                {{ $t("public.copy") }}
              </v-btn>
            </div>
          </div>
        </v-col>
      </v-row>

      <!-- Ads -->
      <v-row v-if="company && !noads" class="no-print">
        <v-col cols="12" class="pa-0">
          <div
            v-if="order.receipt_invoice && order.receipt_invoice.footer_html"
            v-html="order.receipt_invoice.footer_html"
          />
          <div v-else v-html="company.ads" />
        </v-col>
      </v-row>
    </v-container>

    <!-- Error -->
    <v-row v-if="error">
      <v-col cols="12">
        <v-alert class="text-center" color="error" dark>
          {{ error }}
        </v-alert>
      </v-col>
    </v-row>
  </v-main>
</template>

<script lang="ts">
import axios from 'axios'
import Vue from 'vue'
import moment from 'moment'
import { copyToClipboard } from '~/utils/clipboard'
import { Order, Company, ReceiptTable } from '~/models/receipt'
import { isIos } from '~/utils/devices'
import RecordScanLogDataTable from '~/components/home/<USER>'
import { TaxInvoiceFinance } from '~/models/tax_document'

interface VideoData {
  url: string;
  filetype: string;
  scan_logs: any[];
  audio: boolean;
  file_size: number;
  download_url: string;
  google_drive_url: string;
  google_drive_preview_url: string;
  upload_date: string;
}

export default Vue.extend({
  components: { RecordScanLogDataTable },
  layout: 'blank',

  auth: 'guest',

  data () {
    return {
      logo_url: 'https://cloud.dobybot.com/logo-with-text.png',
      receipt: null as ReceiptTable | null,
      video: null as VideoData | null,
      company: null as Company | null,
      order: null as Order | null,
      tax_invoice: null as TaxInvoiceFinance | any,

      error: '',
      video_source_error: false,
      loading: true,
      video_loading: true,
      noads: false,
      max_height: 150,

      source_mp4: '',
      source_webm: '',
      custom_brand: false
    }
  },

  computed: {
    file_id (): string | null {
      if (!this.video) {
        return null
      }
      if (!(this.video as any).url) {
        return null
      }

      const video = this.video as any
      const parts = video.url.split('/')
      const index = parts.findIndex((x: string) => x === 'd')
      return parts[index + 1]
    },

    video_player (): string {
      const video = this.video
      if (!video || !video.url) {
        return 'no_video'
      }

      if (this.video_loading) {
        return 'loading'
      }

      if (this.video_source_error) {
        return 'fake-video-player'
      }

      // iOS
      if (isIos()) {
        if (video.filetype === 'mp4') {
          return 'html-video-player'
        } else {
          return 'fake-video-player'
        }
      }

      // Android / Windows / Other OS
      if (video.file_size > 100e6) {
        const days_since_upload = moment
          .duration(moment().diff(video.upload_date))
          .asDays()
        if (days_since_upload < 7) {
          return 'html-video-player'
        } else {
          return 'google-drive-player'
        }
      } else {
        return 'html-video-player'
      }
    },

    // can_play_video (): boolean {
    //   if (!this.video) {
    //     return false
    //   }

    //   if (isIos()) {
    //     return this.video.filetype === 'mp4'
    //   }

    //   return true
    // },

    show_receipt_body (): boolean {
      if (!this.order || !this.company) {
        return false
      }

      if (this.company.hide_receipt) {
        return false
      }

      if (
        this.order.status === 'Voided' ||
        this.order.status === 'Partial Voided'
      ) {
        return false
      }

      return this.order.list.length > 0
    }
  },

  created () {
    const token = this.$route.query.t as string
    const noads = this.$route.query.noads as string
    this.getReceiptData(token)
    this.noads = noads === '1'
  },

  mounted () {
    window.addEventListener('keydown', this.onKeyPress)
  },

  methods: {
    async getReceiptData (token: string) {
      const base_url = this.$axios.defaults.baseURL
      this.loading = true
      try {
        const response = await axios.get(
          `${base_url}/api/printing/data/receipt/`,
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        )
        this.getPlayableVideo(response.data.video, token)

        this.company = response.data.company
        this.video = response.data.video
        this.order = response.data.order
        this.tax_invoice = response.data.tax_invoice

        if (this.order && response.data.order_trackingno) {
          this.order.trackingno = response.data.order_trackingno
        }

        if (this.company && this.company.receipt_logo) {
          this.logo_url = this.company.receipt_logo
        }
        if (this.company && this.company.custom_brand) {
          this.custom_brand = true
        }

        if (this.order) {
          this.createReceipt(this.order, this.tax_invoice)
        }
      } catch (error: any) {
        console.error(error)
        if (error.response.status === 401) {
          this.error = 'Unauthorized'
        } else if (error.response.status === 404) {
          this.error = this.$t('public.order-not-found')
        } else {
          this.error = `${error}`
        }
      }
      this.loading = false
    },

    createReceipt (order: Order, tax_inv: TaxInvoiceFinance) {
      if (
        order.version === 2 &&
        ['dobybot-connect', 'dobybot-connect-zort'].includes(order.orderManagementSystem.toLowerCase())
      ) {
        return this.createDobysyncReceipt(tax_inv)
      } else if (['fixcase', 'easy-order', 'openapi'].includes(order.orderManagementSystem.toLowerCase())) {
        return this.createDobysyncReceipt(tax_inv)
      } else if (order.saleschannel.toLowerCase() === 'vrich') {
        return this.createVrichReceipt(order)
      } else if (order.receipt_invoice) {
        return this.createCustomReceipt(order)
      } else {
        return this.createZortReceipt(order)
      }
    },

    createVrichReceipt (order: Order) {
      this.receipt = {
        shipping: order.shippingamount,
        discount: order.discountamount,
        net: order.amount,
        items: order.list.map(x => ({
          sku: x.sku,
          name: x.name || x.sku,
          number: x.number,
          totalprice: x.totalprice
        }))
      }
    },

    createZortReceipt (order: Order) {
      this.receipt = {
        shipping: order.shippingamount,
        discount: order.discountamount + order.voucheramount,
        net: order.paymentamount - order.voucheramount,
        items: order.list.map(x => ({
          sku: x.sku,
          name: x.name || x.sku,
          number: x.number,
          totalprice: x.totalprice + x.discountamount
        }))
      }
    },

    createCustomReceipt (order: Order) {
      this.receipt = {
        shipping: order.shippingamount,
        discount: order.discount,
        voucheramount: order.voucheramount,
        amount_pretax: order.amount_pretax,
        vatamount: order.vatamount,
        net: order.amount,
        items: order.list.map(x => ({
          sku: x.sku,
          name: x.name || x.sku,
          number: x.number,
          totalprice: x.totalprice
        })),
        receipt_invoice: order.receipt_invoice
      }
    },

    createDobysyncReceipt (tax_inv: TaxInvoiceFinance) {
      // const shipping_row = tax_inv.tax_inv_rows.find(
      //   (x) => x.sku === "shipping-fee"
      // );
      // let shipping_fee = 0;
      // if (shipping_row) {
      //   shipping_fee = shipping_row.totalprice;
      // }

      this.receipt = {
        shipping: 0,
        discount: tax_inv.discountamount,
        voucheramount: 0,
        amount_pretax: tax_inv.pretaxamount,
        vatamount: tax_inv.totalvatamount,
        net: tax_inv.grandtotal,
        items: tax_inv.rows.map(x => ({
          sku: x.sku,
          name: x.name || x.sku,
          number: x.number,
          totalprice: x.totalprice
        })),
        receipt_invoice: null
      }
    },

    copy (text: string) {
      copyToClipboard(text)
      this.$snackbar('info', `คัดลอก "${text}" แล้ว`)
    },

    print () {
      window.print()
    },

    onKeyPress (event: KeyboardEvent) {
      if (event.ctrlKey && event.key === 'D') {
        this.printDebugData()
      }
    },

    printDebugData () {
      // eslint-disable-next-line no-console
      console.log('receipt', this.receipt)
      // eslint-disable-next-line no-console
      console.log('video', this.video)
      // eslint-disable-next-line no-console
      console.log('company', this.company)
      // eslint-disable-next-line no-console
      console.log('order', this.order)
    },

    seekTo (ms: number) {
      const el = document.getElementById('video') as HTMLVideoElement
      el.currentTime = ms / 1000
      el.pause()
    },

    isNumber (value: string) {
      if (typeof value === 'number') {
        return true
      }
      return !isNaN(value as any) && !isNaN(parseFloat(value))
    },

    onVideoSourceError (error: any) {
      console.log('onVideoSourceError')
      console.error(error)
      this.video_source_error = true
    },

    getFileIdFromUrl (url: string) {
      const parts = url.split('/')
      const index = parts.findIndex((x: string) => x === 'd')
      return parts[index + 1]
    },

    async getPlayableVideo (video_data: any, token: string) {
      if (!video_data || !video_data.url) {
        this.video_source_error = true
        this.video_loading = false
        return
      }

      this.video_loading = true
      const file_id = this.getFileIdFromUrl(video_data.url)
      const url = `https://dobybot-video-yuo4mnnlaa-as.a.run.app/playable-video/${file_id}`

      setTimeout(() => {
        if (this.video_loading) {
          this.video_source_error = true
          this.video_loading = false
        }
      }, 10000)

      try {
        const response = await axios.get(url, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        })

        if (video_data.filetype === 'mp4') {
          this.source_mp4 = response.data.url
        } else {
          this.source_webm = response.data.url
        }
        this.video_source_error = false
        this.video_loading = false
      } catch (error) {
        console.error(error)
      }
    },

    displayShippingChannel (shipping_channel: string) {
      if (shipping_channel === 'DBB_FLASH') {
        return 'Flash Express'
      } else if (shipping_channel === 'DBB_KERRY') {
        return 'Kerry Express'
      } else if (shipping_channel === 'DBB_THAIPOST_REG') {
        return 'ไปรษณีย์ไทย (ลงทะเบียน)'
      } else if (shipping_channel === 'DBB_THAIPOST_EMS') {
        return 'ไปรษณีย์ไทย (EMS)'
      } else {
        return shipping_channel
      }
    }
  }
})
</script>

<style scoped>
@import url("https://fonts.googleapis.com/css?family=Kanit");
.app {
  font-family: kanit;
}

a.copy {
  text-decoration: none;
}

@media print {
  .no-print,
  .no-print * {
    display: none !important;
  }

  /* .title {
    color: black !important;
  } */
}

::-webkit-scrollbar {
  -webkit-appearance: none;
  width: 7px;
}

::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
}

.iframe-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding-top: 56.25%;
}

.iframe-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  border: none;
}

.warning-voided-order {
  border: 1px red solid;
  text-align: center;
  padding: 10px;
  font-size: 20px;
}
</style>
