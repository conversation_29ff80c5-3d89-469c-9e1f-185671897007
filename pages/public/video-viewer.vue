<template>
  <v-container>
    <v-row wrap>
      <v-col cols="12" class="text-center">
        <v-img src="/logo-with-text.png" height="100" contain />
      </v-col>
      <v-col v-if="file_name" cols="12">
        <h1>Record Name: {{ file_name }}</h1>
        <!-- <div>
          <div>File type: {{ file_type }}</div>
        </div> -->
      </v-col>
      <v-col v-if="!loading" cols="12">
        <!-- LOADING -->
        <div v-if="player == 'loading'">
          <div
            class="py-5"
            style="border-left: 1px solid black; border-right: 1px solid black; background: black;"
          >
            <div class="text-center" style="margin-top: 200px; margin-bottom: 200px">
              <!-- <v-icon size="200" color="white">
                    mdi-video-off
                  </v-icon> -->
              <v-progress-circular
                :size="70"
                :width="7"
                color="white"
                indeterminate
              />
            </div>
            <div class="text-center caption white--text">
              ... {{ $t('video-loading') }} ...
            </div>
          </div>
        </div>

        <!-- HTML VIDEO PLAYER -->
        <video
          v-if="player == 'html5-video'"
          id="video"
          controls
          autoplay
          muted
          playsinline
          preload="metadata"
          style="width: 100%; border: 1px solid black;"
        >
          <source
            v-if="file_type === 'mp4' || file_type === ''"
            type="video/mp4"
            :src="source_mp4"
            @error="onVideoSourceError($event)"
          >
          <source
            v-if="file_type === 'webm' || file_type === ''"
            type="video/webm"
            :src="source_webm"
            @error="onVideoSourceError($event)"
          >
          Sorry, your browser doesn't support embedded videos.
        </video>

        <!-- GOOGLE_DRIVE_PLAYER -->
        <div v-if="player === 'google-drive'">
          <div class="iframe-container">
            <iframe :src="google_drive_preview" width="100%" height="660" allow="autoplay" />
          </div>
          <!-- <div class="text-center caption">
            หากไม่สามารถดูวิดีโอได้กรุณา <a :href="google_drive_link">เปิดใน Google Drive</a> หรือ <a :href="video_src">ดาวโหลดน์ไฟล์</a>
          </div> -->
        </div>

        <!-- FAKE VIDEO PLAYER -->
        <div v-if="player === 'fake'">
          <a :href="google_drive_link" target="_blank">
            <img
              src="https://storage.googleapis.com/dobybot-bucket-1/video-player.jpg"
              style="width: 100%;"
            >
          </a>
        </div>

        <!-- NO VIDEO -->
        <div v-if="player === 'no_video'" class="py-5" style="border: 1px solid black; background: black; border-bottom: none;">
          <div class="text-center">
            <v-icon size="200" color="white">
              mdi-video-off
            </v-icon>
          </div>
          <div class="text-center caption white--text">
            {{ $t('no-video-recorded') }}
          </div>
        </div>

        <div
          v-if="scan_logs && scan_logs.length > 0"
          class="text-left"
          style="overflow-y: scroll; border: 1px solid black; position: relative; top: -7px;"
          :style="{'max-height': max_height + 'px'}"
        >
          <record-scan-log-data-table
            :scan-logs="scan_logs"
            :columns="['index', 'time_ms', 'barcode', 'name', 'amount']"
            :order-by="{ column: 'time_ms', direction: 'asc' }"
            @click:row="seekTo($event.time_ms)"
          />
        </div>
        <v-row v-if="scan_logs && scan_logs.length > 0" justify="end">
          <v-col xs="12" class="text-right py-0 mt-1">
            <v-btn x-small outlined style="width: 80px;" @click="max_height > 0 ? max_height -= 150 : 0">
              <v-icon left>
                mdi-menu-up
              </v-icon>
              ย่อ
            </v-btn>
            <v-btn x-small outlined style="width: 80px;" @click="max_height += 150">
              <v-icon left>
                mdi-menu-down
              </v-icon>
              ขยาย
            </v-btn>
          </v-col>
        </v-row>
        <div class="text-center mt-3">
          <v-btn :href="google_drive_link" target="_blank">
            <v-img
              src="https://ssl.gstatic.com/images/branding/product/2x/hh_drive_96dp.png"
              width="25"
              height="25"
              class="mr-3"
            />
            {{ $t("public.open-in-google-drive") }}
          </v-btn>
          <v-btn :href="video_src">
            <v-icon left>
              mdi-download
            </v-icon>
            {{ $t("public.normal-download") }}
          </v-btn>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import axios from 'axios'
import Vue from 'vue'
import { isIos, isSafari } from '~/utils/devices'
import RecordScanLogDataTable from '~/components/home/<USER>'

export default Vue.extend({
  components: { RecordScanLogDataTable },

  layout: 'blank',

  auth: 'guest',

  data () {
    return {
      loading: true,

      video_src: '',
      source_mp4: '',
      source_webm: '',
      google_drive_link: '',
      google_drive_preview: '',
      file_name: '',
      file_type: '',
      scan_logs: [],

      player: 'loading',
      max_height: 150,
      video_source_error: false,
      video_loading: true
    }
  },

  mounted () {
    const uuid = this.$route.query.uuid as string
    if (uuid) {
      this.setupWithVideoUUID(uuid)
      return
    }

    const file_id = this.$route.query.file_id as string
    if (file_id) {
      const file_name = (this.$route.query.name as string) || ''
      const file_type = (this.$route.query.type as string) || ''
      this.setupWithGoogleDriveFileId(file_id, file_name, file_type)
    }
  },

  methods: {
    setupWithGoogleDriveFileId (
      file_id: string,
      file_name: string,
      file_type: string
    ) {
      this.loading = true

      this.video_src = `https://drive.google.com/uc?export=download&id=${file_id}&confirm=t`
      this.google_drive_link = `https://drive.google.com/file/d/${file_id}`
      this.file_name = file_name
      this.file_type = file_type

      if (file_type === 'webm' && isIos() && isSafari()) {
        this.player = 'fake'
      }

      this.loading = false
    },

    async setupWithVideoUUID (video_uuid: string) {
      this.loading = true

      try {
        const base_url = this.$axios.defaults.baseURL
        const res = await axios.get(`${base_url}/api/printing/data/video-record-log/${video_uuid}/`)
        const video_data = res.data

        this.video_src = video_data.download_url
        this.google_drive_link = `https://drive.google.com/file/d/${video_data.drive_file_id}`
        this.google_drive_preview = `https://drive.google.com/file/d/${video_data.drive_file_id}/preview`

        this.file_name = video_data.name
        this.file_type = video_data.filetype
        this.scan_logs = video_data.scan_logs
        this.getPlayableVideo(video_data, video_data.token)

        // For file larger than 100MB GoogleDrive will show warning page for file too large to scan for virus
        // if (video_data.file_size > 100e6) {
        //   this.player = 'google-drive'
        // }
        // if (isIos() && video_data.file_type === 'webm') {
        //   this.player = 'fake'
        // }
        // if (!video_data.video_url) {
        //   this.player = 'no_video'
        // }
      } catch (error) {
        console.error(error)
      }

      this.loading = false
    },

    seekTo (ms: number) {
      const el = document.getElementById('video') as HTMLVideoElement
      el.currentTime = ms / 1000
      el.pause()
    },

    onVideoSourceError (error: any) {
      console.log('onVideoSourceError')
      console.error(error)
      this.video_source_error = true
      this.player = 'fake'
    },

    async getPlayableVideo (video_data: any, token: string) {
      if (!token) {
        this.loading = false
        this.video_source_error = true
        this.video_loading = false
        this.player = 'no_video'
        return
      }

      this.video_loading = true
      this.player = 'loading'
      const file_id = video_data.drive_file_id
      const url = `https://dobybot-video-yuo4mnnlaa-as.a.run.app/playable-video/${file_id}`

      setTimeout(() => {
        if (this.video_loading) {
          this.video_source_error = true
          this.video_loading = false
        }
      }, 10000)

      try {
        const response = await axios.get(url, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        })

        if (video_data.filetype === 'mp4') {
          this.source_mp4 = response.data.url
        } else {
          this.source_webm = response.data.url
        }
        this.video_source_error = false
        this.video_loading = false
        this.player = 'html5-video'
      } catch (error) {
        console.error(error)

        if (video_data.file_size > 100e6) {
          this.player = 'google-drive'
        }
        if (isIos() && video_data.file_type === 'webm') {
          this.player = 'fake'
        }
        if (!video_data.video_url) {
          this.player = 'no_video'
        }
      }
    }
  }
})
</script>

<style scoped>
.iframe-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
}

.iframe-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  border: none;
}
</style>
