<template>
  <v-container>
    <div class="mx-auto" style="max-width:600px; ">
      <v-stepper v-if="!error && !is_security_error" v-model="step" height="100%">
        <v-row class="mt-5">
          <v-col cols="1">
            <v-btn v-if="step==2 && !order_uuid" fab icon class="ml-5" @click="step = 1">
              <v-icon>mdi-arrow-left</v-icon>
            </v-btn>
          </v-col>
          <v-col cols="10">
            <v-img :src="logo" height="50" contain />
          </v-col>
          <v-col cols="1" />
        </v-row>

        <v-stepper-items v-if="!is_security_error">
          <!-- Step 1: Order Number -->
          <v-stepper-content step="1">
            <e-tax-invoice-step-1
              v-if="step == 1"
              :company-uuid="company_uuid"
              :order-number.sync="order_number"
              :is-staff="is_staff"
              :is-update="is_update"
              :tax-document-uuid="tax_document_uuid"
              :order-uuid="order_uuid"
              :settings="company_settings"
              @next-step="step = 2"
              @customer-data="tax_data = $event"
              @update:error="step1Error()"
            />
          </v-stepper-content>

          <!-- Step 2: Main Form -->
          <v-stepper-content step="2">
            <e-tax-order-overview-card
              v-if="platform_logo"
              :order-overview="tax_data"
              :logo="platform_logo"
            />

            <e-tax-invoice-step-2
              v-if="step == 2"
              :tax-document-uuid="tax_document_uuid"
              :company-uuid="company_uuid"
              :order-uuid="order_uuid"
              :is-staff="is_staff"
              :user-uuid="user_uuid"
              :is-update="is_update"
              :settings="company_settings"
              :order-number="order_number"
              @error="step = 1"
            />
          </v-stepper-content>
        </v-stepper-items>
      </v-stepper>

      <!-- error section -->
      <div v-if="error" class="mt-5">
        {{ error }}
      </div>

      <div v-if="is_security_error">
        <v-alert
          type="error"
          outlined
          text
          dense
        >
          คุณไม่มีสิทธิการแก้ไขใบกำกับภาษีอิเล็กทรอนิกส์ กรุณาติดต่อเจ้าหน้าที่
        </v-alert>
        <a href="/login">เป็นเจ้าหน้าที่ ?</a>
      </div>
    </div>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'

import ETaxInvoiceStep1 from '~/components/etax-invoice/ETaxInvoiceStep1.vue'
import ETaxInvoiceStep2 from '~/components/etax-invoice/ETaxInvoiceStep2.vue'
import { CustomerTaxData } from '~/models/tax_document'

export default Vue.extend({
  components: {
    ETaxInvoiceStep1,
    ETaxInvoiceStep2
  },
  layout: 'blank',
  data () {
    return {
      company_uuid: '',
      error: '',
      tax_document_uuid: null as string|null,
      order_number: '',
      order_uuid: '',
      user_uuid: '',
      is_security_error: false,
      is_staff: false,
      is_update: false,
      step: 1,
      platform_logo: '',
      tax_data: {
        customer_address: '',
        customer_branch_name: '',
        customer_branch_no: '',
        post_code: '',
        customer_email: '',
        customer_id_number: '',
        customer_name: '',
        customer_phone: '',
        order_number: '',
        payment_amount: '',
        tax_document_uuid: '',
        platform_name: ''
      } as CustomerTaxData,
      company_settings: {
        ETAX_ALLOW_ON_ORDER_RECEIVED: false,
        ETAX_SHOW_DOBYBOT_CONTACT: false,
        ETAX_SHOW_CONSENT_CHECKBOX: false,
        ETAX_WELCOME_MESSAGE: '',
        ETAX_LOGO: '',
        ETAX_ORDER_OPEN_DAYS: 10
      }
    }
  },
  computed: {
    logo (): string {
      if (this.company_settings.ETAX_LOGO) {
        return this.company_settings.ETAX_LOGO
      }

      return '/logo-with-text.png'
    }
  },
  watch: {
    tax_data: {
      handler () {
        this.getPlatformLogo()
      }
    }
  },
  created () {
    this.order_uuid = this.$route.query.oid as string
    this.user_uuid = this.$route.query.uid as string
    this.company_uuid = String(this.$route.query.cid)
  },
  mounted () {
    this.company_uuid = String(this.$route.query.cid)
    this.getCompanySettings()
    if (!this.$route.query.cid) {
      this.error = '404 - Invalid URL'
      return
    }

    if (this.$route.query.update) {
      this.tax_document_uuid = this.$route.query.tid as string
      this.order_uuid = this.$route.query.oid as string
      this.user_uuid = this.$route.query.uid as string
      this.is_update = true
    } else if (this.$route.query.oid && !this.$route.query.update) {
      this.order_uuid = this.$route.query.oid as string
      this.user_uuid = this.$route.query.uid as string
      this.is_update = false
    }

    if (this.$route.query.u === 'staff') {
      if (!localStorage.getItem('auth._token.local')) {
        alert('You do not have permission to update')
        this.is_security_error = true
        return
      }
      this.is_staff = true
      this.step = 2
    }
  },
  methods: {
    getPlatformLogo () {
      const platform_name = this.tax_data.platform_name
      if (platform_name === 'lazada') {
        this.platform_logo = platform_name
      }
      if (platform_name === 'shopee') {
        this.platform_logo = platform_name
      }
      if (platform_name === 'tiktok shop') {
        this.platform_logo = 'tiktok-shop'
      }
      if (platform_name === 'nocnoc') {
        this.platform_logo = 'nocnoc'
      }
    },
    async getCompanySettings () {
      const response = await this.$axios.get(`/api/etax/get-etax-company-setting/${this.company_uuid}/`)
      this.company_settings = response.data
    },
    step1Error () {
      this.step = 1
    }
  }

})
</script>

<style >
.center-card {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  width: 100%;
  overflow: auto;
}
</style>
