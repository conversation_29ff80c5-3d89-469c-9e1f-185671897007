<template>
  <v-container>
    <v-row wrap>
      <v-col cols="12" class="text-center">
        <v-img src="/logo-with-text.png" height="100" contain />
      </v-col>
      <v-col cols="12">
        <h1>Image Capture Name: {{ image_capture_name }}</h1>
      </v-col>

      <v-col v-for="src in images" :key="src" class="d-flex child-flex" cols="4">
        <v-img
          style="border-radius:15px"
          :src="src"
          :aspect-ratio="1"
        />
        <template #placeholder>
          <v-row
            class="fill-height ma-0"
            align="center"
            justify="center"
          >
            <v-progress-circular
              indeterminate
              color="grey lighten-5"
            />
          </v-row>
        </template>
      </v-col>

      <v-col cols="12">
        <div class="text-start">
          <h3>วันที่ {{ $datetime(create_date) }}</h3>
          <h3>รูปภาพโดย {{ create_by }}</h3>
        </div>
      </v-col>

      <v-col cols="12">
        <div class="text-center mt-3">
          <v-btn :href="google_drive_link" target="_blank">
            <v-img
              src="https://ssl.gstatic.com/images/branding/product/2x/hh_drive_96dp.png"
              width="25"
              height="25"
              class="mr-3"
            />
            {{ $t("public.open-in-google-drive") }}
          </v-btn>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import axios from 'axios'
export default Vue.extend({
  data () {
    return {
      image_capture_name: '',
      images: [] as string[],
      image_detail: '',
      show_detail: false,
      create_by: '',
      create_date: '',
      google_drive_link: ''
    }
  },

  mounted () {
    const uuid = this.$route.query.uuid as string
    if (uuid) {
      this.setupWithImageUUID(uuid)
    }
  },

  methods: {

    async setupWithImageUUID (image_uuid: string) {
      try {
        const base_url = this.$axios.defaults.baseURL
        const res = await axios.get(`${base_url}/api/printing/data/image-record-log/${image_uuid}/`)
        const image_data = res.data

        this.create_by = image_data.create_by_name
        this.create_date = image_data.create_date
        this.image_capture_name = image_data.name
        this.google_drive_link = `https://drive.google.com/drive/folders/${image_data.drive_folder_id}`

        for (const img_link of image_data.images) {
          const image_src = `https://drive.google.com/thumbnail?id=${img_link.id}`
          this.images.push(image_src)
        }
      } catch (error) {
        console.error(error)
      }
    }
  }
})
</script>

<style scoped>

</style>
