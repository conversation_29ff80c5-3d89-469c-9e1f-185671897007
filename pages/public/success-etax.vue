<template>
  <v-container class="success-container fill-height pa-4">
    <v-row justify="center" align="center" class="fill-height">
      <v-col cols="12" sm="8" md="8" lg="6">
        <v-card
          class="success-card"
          :class="$vuetify.breakpoint.xsOnly ? 'mx-2' : ''"
          elevation="6"
        >
          <!-- Animated Success Icon -->
          <div class="success-icon-wrapper">
            <v-img
              src="/check.png"
              alt="success"
              contain
              :height="$vuetify.breakpoint.xsOnly ? '120' : '140'"
              class="success-icon"
            />
            <div class="success-ring" />
          </div>

          <!-- Success Header with Gradient -->
          <h1
            class="success-header text-center"
            :class="$vuetify.breakpoint.xsOnly ? 'text-h5' : 'text-h4'"
          >
            {{ routeType==='update'? 'อัปเดตสำเร็จ':'การดำเนินการเสร็จสิ้น ' }}
          </h1>

          <!-- Message Content with Enhanced Typography -->
          <v-card-text class="message-content">
            <p
              class="main-message text-center"
              :class="$vuetify.breakpoint.xsOnly ? 'body-2' : 'text-body-1'"
            >
              ระบบกำลังดำเนินการจัดส่งใบกำกับภาษีอิเล็กทรอนิกส์ (e-Tax Invoice) ไปยังอีเมลที่ท่านได้ระบุไว้
            </p>
            <p
              class="sub-message text-center"
              :class="$vuetify.breakpoint.xsOnly ? 'caption' : 'body-2'"
            >
              กรุณาตรวจสอบอีเมลของท่าน  <br> การจัดส่งอาจใช้เวลา 1-2 วันทำการ
            </p>

            <div v-if="pdf_ready">
              <v-divider class="my-8" />
              <p class="sub-message text-center mb-2">
                เอกสารของท่านพร้อมแล้ว คลิกที่ปุ่มด้านล่างเพื่อดาวน์โหลด
              </p>
              <div v-for="file in files" :key="file.doc_id" class="text-center mb-2">
                <v-btn color="success" @click="downloadPDF(file)">
                  <v-icon left>
                    mdi-download
                  </v-icon>
                  {{ file.doc_id }}.pdf
                </v-btn>
              </div>
            </div>
          </v-card-text>

          <!-- Enhanced Action Button -->
          <!-- <div class="text-center button-wrapper">
            <v-btn
              color="primary"
              class="edit-button"
              elevation="2"
              rounded
              @click="navigateToEditPage"
            >
              <v-icon left small>
                mdi-pencil
              </v-icon>
              แก้ไขข้อมูล
            </v-btn>
          </div> -->
        </v-card>
        <div v-if="show_contact" class="text-center justify-center text-body-1 pt-5">
          <p class="mb-1">
            ระบบออกใบกำกับภาษีอัตโนมัติสำหรับร้านค้าออนไลน์โดย บริษัท ดูบายบอท จำกัด
          </p>
          <p>
            สนใจติดต่อ | <a href="https://www.dobybot.com" target="_blank">www.dobybot.com</a> | <a href="tel:02-662-8500" target="_blank">02-662-8500</a>
          </p>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import { saveAs } from 'file-saver'
import Vue from 'vue'

interface TaxDocForDownload {
  uuid: string
  doc_id: string
}

export default Vue.extend({
  name: 'SuccessPage',
  layout: 'blank',

  data () {
    return {
      show_contact: false,
      pdf_ready: false,
      files: [] as TaxDocForDownload[]
    }
  },

  computed: {
    routeType () {
      return this.$route.query.type ?? null
    }
  },

  mounted () {
    if (this.$route.query.ct === 'true') {
      this.show_contact = true
    }

    this.checkPDFReady()
  },

  methods: {
    navigateToEditPage () {
      const queryRoute = this.$route.query
      this.$router.push({
        path: '/public/etax-invoice',
        query: {
          tid: queryRoute.tid,
          cid: queryRoute.cid,
          oid: queryRoute.oid,
          update: 'true'
        }
      })
    },

    /**
     * Check if PDF is ready to download
     */
    async checkPDFReady () {
      const payload = {
        company: this.$route.query.cid,
        order: this.$route.query.oid
      }
      const data = await this.$axios.$post('/api/etax/download/check/', payload)

      if (data && data.length > 0) {
        this.pdf_ready = true
        this.files = data
      }
    },

    async downloadPDF (file: TaxDocForDownload) {
      this.$loader(true)
      const payload = {
        company: this.$route.query.cid,
        tax_document: file.uuid
      }
      try {
        const data = await this.$axios.$post('/api/etax/download/', payload, { responseType: 'blob' })
        saveAs(data, `${file.doc_id}.pdf`)
      } catch (error) {
        console.error(error)
      }
      this.$loader(false)
    }
  }
})
</script>

<style scoped>
.success-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
}

.success-card {
  border-radius: 24px;
  max-width: 100%;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  position: relative;
}

.success-icon-wrapper {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1.5rem;
}

.success-icon {
  position: relative;
  z-index: 2;
  animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.success-header {
  background: linear-gradient(45deg, #2E7D32, #4CAF50);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.message-content {
  padding: 0 1rem;
}

.main-message {
  color: #2c3e50;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.sub-message {
  color: #7f8c8d;
  line-height: 1.4;
}

.button-wrapper {
  margin-top: 2rem;
  padding-bottom: 1rem;
}

.edit-button {
  min-width: 160px;
  text-transform: none;
  letter-spacing: 0.5px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.edit-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.2);
}

@keyframes bounceIn {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  60% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

/* Mobile Optimizations */
@media (max-width: 600px) {
  .success-card {
    padding: 1.5rem;
    border-radius: 20px;
  }

  .success-ring {
    width: 140px;
    height: 140px;
  }

  .button-wrapper {
    margin-top: 1.5rem;
  }
}

/* Safe Area Support */
@supports (padding: max(0px)) {
  .success-container {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}
</style>
