<template>
  <v-container>
    <v-row justify="center" align="center" class="fill-height">
      <v-col cols="12" md="8">
        <v-card v-if="loading" class="pa-5">
          <v-row justify="center">
            <v-col cols="12" class="text-center">
              <v-progress-circular indeterminate color="primary" />
            </v-col>
          </v-row>
        </v-card>

        <!-- error mode -->
        <div v-if="error" class="text-center">
          <v-icon large color="error">
            mdi-alert-circle-outline
          </v-icon>
          <h2 class="headline my-4">
            Invalid URL
          </h2>
          <p class="grey--text">
            {{ error_code }}
          </p>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script lang="ts">
import axios from 'axios'
import Vue from 'vue'

// http://localhost:3000/request-etax/?t=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjpbIjAwMSIsOTAsMTAsIklOVjAwMDAxIiwxMDAsNi45MywiMDAwMDAiLCJBQkNERUYiXX0.hvX-BeD1Rf0_Cck08aSaj9K_VX2CkqLWfPJNSXfxUcA
// http://localhost:3000/request-etax/?t=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjpbIjAwMSIsOTAsMTAsIklOVjAwMDAyIiwxMDAsNi45MywiMDAwMDQiLCJBQkNERUYiXX0.Jh1Jq4guVZg-1rEt8guI8LtsA9scCgAsQiBE1QkakYQ
export default Vue.extend({
  name: 'RequestEtax',
  layout: 'blank',
  auth: 'guest',

  data () {
    return {
      loading: true,
      error: false,
      error_code: ''
    }
  },

  created () {
    this.getDataFromToken()
  },

  methods: {
    async getDataFromToken () {
      const token = this.$route.query.t
      try {
        const base_url = this.$axios.defaults.baseURL
        const response = await axios.post(`${base_url}/api/etax/easy-order-from-token/`, { token })
        this.$router.push({ path: '/public/etax-invoice', query: { cid: response.data.company_uuid, oid: response.data.order_uuid } })
      } catch (error: any) {
        this.error = true
        console.log(error.response)
        if (error.response) {
          this.error_code = error.response.data.non_field_errors[0].code
        }
      // Handle error appropriately
      } finally {
        this.loading = false
      }
    }
  }

})
</script>

<style scoped>
.fill-height {
  height: 100vh;
}
</style>
