<template>
  <div class="w-full h-full bg ma-0">
    <v-row class="justify-center d-flex align-md-center align-lg-center py-0 ma-0">
      <v-col
        cols="12"
        md="5"
        offset="0"
        offset-md="7"
        style="height: 100vh"
        class="pa-0 my-0"
      >
        <v-card
          class="pa-10 rounded-lg align-md-center align-lg-center justify-center d-flex"
          height="100%"
        >
          <v-form ref="companyForm" style="max-width:350px">
            <v-img
              class="mt-2 mb-6"
              src="/logo-with-text.png"
              contain
              height="50px"
            />
            <h3 class="text-center text-h5 w-100 mb-3" style="margin: auto;">
              {{ $t('dobybot-packing-solution') }}
            </h3>
            <h4 class="mt-6" style="margin: auto;">
              {{ $t('rename-company') }}
            </h4>

            <v-text-field
              v-model="company.name"
              :rules="[$rules.required]"
              :label="$t('settings.company')"
              :error-messages="error.name ? $t('name-already-used') : ''"
            />

            <v-text-field
              v-model="company.suffix"
              :label="$t('suffix')"
              prefix="@"
              :rules="[$rules.required, $rules.englishAndNumberOnly]"
              :error-messages="error.suffix ? $t('suffix-is-already-used') : ''"
            />
            <p class="mt-3 grey--text text--darken-2 ">
              ** {{ $t('suffix-explain') }} user@{{ company.suffix || 'abc' }}
            </p>
            <v-btn
              class="mt-6"
              block
              color="primary"
              @click="registerCompany"
            >
              {{ $t('next') }}
            </v-btn>
          </v-form>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  layout: 'blank',
  middleware: 'auth',
  data () {
    return {
      company: {
        name: '',
        suffix: ''
      },
      error: {}
    }
  },
  methods: {
    async registerCompany () {
      const form = this.$refs.companyForm
      if (!form.validate()) {
        return
      }

      try {
        await this.$axios.post('/api/companies/company/rename/', {
          name: this.company.name,
          suffix: '@' + this.company.suffix
        })
        await this.$auth.fetchUser()
        this.$router.push('/setup/create-admin')
      } catch (e) {
        this.error = e.response.data
        console.log(e)
      }
    }
  }
}
</script>

<style scoped>
.bg{
  background: url('/register-background.webp') no-repeat center center fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 100%;
}
</style>
