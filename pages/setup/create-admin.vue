<template>
  <div class="w-full h-full blue-grey bg lighten-5 ma-0">
    <v-row class="justify-center d-flex align-md-center align-lg-center py-0 ma-0">
      <v-col
        cols="12"
        md="5"
        offset="0"
        offset-md="7"

        style="height: 100vh"
        class="pa-0 my-0"
      >
        <v-card height="100%" class="pa-10 rounded-lg align-md-center align-lg-center justify-center d-flex">
          <v-form ref="adminForm" style="max-width:350px">
            <v-img class="mt-2 mb-6" src="/logo-with-text.png" contain height="50px" />
            <h3 class="text-center text-h5 w-100 mb-5" style="margin: auto;">
              {{ $t('register-user') }}
            </h3>
            <user-data-form :form-data.sync="form" :username-suffix="companySuffix" :error="error" />
            <v-btn class="mt-3" block color="primary" @click="registerAdmin">
              {{ $t('start-using-system') }}
            </v-btn>
          </v-form>
        </v-card>
      </v-col>
    </v-row>
    <app-alert />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { User, LoggedInUser } from '~/models'
import UserDataForm from '~/components/settings/users/UserDataForm.vue'
import { performLogout } from '~/utils/logout'

export default Vue.extend({
  components: {
    UserDataForm
  },
  layout: 'blank',
  middleware: ['auth', 'is-setup'],
  data () {
    return {
      form: {
        id: 0,
        username: '',
        password: '',
        confirm: '',
        first_name: '',
        last_name: ''
      } as User,
      error: {
        detail: '',
        username: '',
        password: ''
      },
      hidePassword: false
    }
  },
  computed: {
    companySuffix ():string {
      const company = (this.$auth.user as never as LoggedInUser).company
      if (!this.$auth.user || !company) {
        return ''
      }
      return company.account_suffix
    }
  },
  methods: {
    async registerAdmin (): Promise<void> {
      const form = this.$refs.adminForm as any
      if (!form.validate()) {
        return
      }

      try {
        const admin_payload = {
          username: this.form.username,
          password: this.form.password,
          first_name: this.form.first_name,
          last_name: this.form.last_name
        }
        await this.$axios.post('/api/companies/company/change-admin/', admin_payload)
        localStorage.setItem('login.username', this.form.username + this.companySuffix)
      } catch (e: any) {
        this.error = e.response.data
        console.error(e)
      }

      this.$alert({
        title: this.$t('register.success'),
        text: `<div class='text-center'>
      ${this.$t('please-resign-in')}
      <br></br>
      <code>${this.form.username + this.companySuffix}</code>
      <br></br>
      ${this.$t('password-for-register')}</div>`,
        theme: 'success',
        buttons: [
          {
            text: this.$t('confirm-only'),
            color: 'success',
            onclick: () => {
              performLogout(this)
            }
          }
        ]
      })
    }
  }
})
</script>

<style scoped>
.bg{
  background: url('/register-background.webp') no-repeat center center fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  height: 100%;
}
</style>
