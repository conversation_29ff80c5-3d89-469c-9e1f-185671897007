<template>
  <div>
    <v-btn @click="testOrderCapture()">
      Capture
    </v-btn>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { PRODlog } from '~/utils/logger'
export default Vue.extend({
  data () {
    return {
      message: 'This is a test page for PostHog experiments.'
    }
  },
  methods: {
    testOrderCapture () {
      PRODlog('Order captured successfully')
    }
  }
})
</script>
