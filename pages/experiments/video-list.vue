<template>
  <div>
    <v-simple-table>
      <template #default>
        <thead>
          <tr>
            <th class="text-left">
              Name
            </th>
            <th class="text-left">
              Blob
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="video in videos" :key="video.id">
            <td>{{ video.name }}</td>
            <td>
              type: {{ video.blob.type }} |
              size: {{ (video.blob.size / 1024 / 1024).toFixed(2) }} MB
            </td>
            <td>
              <v-btn icon color="info" @click="upload(video)">
                <v-icon>mdi-upload</v-icon>
              </v-btn>
              <v-btn icon color="info" @click="download(video)">
                <v-icon>mdi-download</v-icon>
              </v-btn>
              <v-btn icon color="success" @click="play(video)">
                <v-icon>mdi-play</v-icon>
              </v-btn>
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>

    <v-divider class="my-5" />

    <video ref="video" controls autoplay playsinline />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import RecordRTC from 'recordrtc'
import { mapGetters } from 'vuex'
import { IVideo } from '@/plugins/db'

export default Vue.extend({
  data () {
    return {
      videos: [] as IVideo[]
    }
  },
  computed: {
    ...mapGetters('gapi', {
      gapiToken: 'token'
    })
  },

  async created () {
    this.videos = await this.getVideos()
  },

  methods: {
    getVideos (): Promise<IVideo[]> {
      return this.$db.table('videos').where('id').above(0).toArray()
    },

    play (video: IVideo) {
      const el = this.$refs.video as HTMLVideoElement
      el.src = ''
      el.srcObject = null
      el.muted = false
      el.volume = 1
      el.src = URL.createObjectURL(video.blob)
    },

    download (video: IVideo) {
      RecordRTC.invokeSaveAsDialog(video.blob, `${video.name}.webm`)
    },

    upload (video: IVideo) {
      const file = video.blob
      const metadata = {
        name: video.name + '.webm',
        mimeType: 'video/webm'
      }

      const form = new FormData()
      form.append(
        'metadata',
        new Blob([JSON.stringify(metadata)], { type: 'application/json' })
      )
      form.append('file', file)

      fetch(
        'https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart',
        {
          method: 'POST',
          headers: new Headers({ Authorization: `Bearer ${this.gapiToken}` }),
          body: form
        }
      )
        .then((res) => {
          return res.json()
        })
        .then((response) => {
          console.log(response)
          const gdFile = response
          this.createPermission(gdFile.id)
        })
    },

    createPermission (fileId: string) {
      fetch(`https://www.googleapis.com/drive/v3/files/${fileId}/permissions`, {
        method: 'POST',
        headers: new Headers({
          Authorization: `Bearer ${this.gapiToken}`,
          'Content-Type': 'application/json'
        }),
        body: JSON.stringify({
          role: 'reader',
          type: 'anyone'
        })
      })
        .then((res) => {
          return res.json()
        })
        .then((response) => {
          console.log(response)
          const link = `https://drive.google.com/file/d/${fileId}/view?usp=sharing`
          console.log(link)
          alert(link)
        })
      // https://drive.google.com/file/d/1pCBFKEW83nPvWhUfBp-k4f7po9zmm-V6/view?usp=sharing
    }
  }
})
</script>
