<template>
  <div>
    <h1>Google Drive API Quickstart</h1>
    <p>Please sign in first</p>
    <div>
      <code>user: {{ gapiUser }}</code>
    </div>
    <v-btn @click="fastSignIn()">
      fast sign in
    </v-btn>
    <v-btn v-if="!gapiUser" @click="signIn()">
      Sign In
    </v-btn>
    <v-btn v-if="!!gapiUser" @click="signOut()">
      Sign Out
    </v-btn>

    <v-btn @click="createFolderInShareDrive()">
      Create folder in shared drive
    </v-btn>

    <v-divider class="my-5" />
    <h1>List Files</h1>
    <v-row>
      <v-col>
        <v-text-field v-model="search" label="Search Files ..." />
      </v-col>
      <v-col shrink>
        <v-btn v-if="isAuth" @click="listFiles(search)">
          List Files
        </v-btn>
        <v-btn v-if="isAuth" @click="listDrives()">
          List Drives
        </v-btn>
      </v-col>
    </v-row>

    <v-data-table
      :headers="[
        { text: 'id', value: 'id' },
        { text: 'name', value: 'name' },
        { text: 'type', value: 'mimeType' },
      ]"
      :items="files"
      :items-per-page="5"
    />

    <v-divider class="my-5" />
    <h1>Upload File Simple</h1>
    <v-text-field v-model="upload.destination" label="destination (folder id)" />
    <v-file-input v-model="upload.file" />
    <v-btn color="success" @click="uploadFileSimple()">
      UPLOAD
    </v-btn>
    <v-btn color="success" @click="uploadSimpleTextFile()">
      UPLOAD HELLO.TXT
    </v-btn>
    <v-btn @click="createFolder()">
      create folder
    </v-btn>
    <v-btn @click="createSubFolder()">
      create sub folder
    </v-btn>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState, mapGetters } from 'vuex'
import moment from 'moment'
import jwt_decode from 'jwt-decode'
import { createFolder } from '~/api/googledrive'

export default Vue.extend({
  data () {
    return {
      gapi: null as any,
      files: [] as any[],
      file_upload: null as File | null,
      folders: [] as gapi.client.drive.File[],
      gapiUser: null as any | null,
      search: '',

      upload: {
        destination: '',
        file: null as File | null
      }
    }
  },
  computed: {
    isAuth (): boolean {
      return !!this.gapiUser
    }
  },
  methods: {
    signIn () {
      const oauth2CodeClient = window.google.accounts.oauth2.initCodeClient({
        client_id: '*************-2v0obh7hh7ej1kir4g4e7e8bgpqp7043.apps.googleusercontent.com',
        scope: 'https://www.googleapis.com/auth/drive.file email profile',
        redirect_uri: `${window.location.origin}`,
        ux_mode: 'popup',
        callback: async (res: any) => {
          if (res.error_description) {
            alert(
              'Fail to authorize google service: ' + res.error_description + '\n' +
            'Operation failed with error GA0x0001'
            )
          }

          try {
            const data = await this.$axios.$post('/api/users/google/oauth2/auth-code/', {
              code: res.code,
              redirect_uri: `${window.location.origin}`
            })
            localStorage.setItem('experiments.google-drive.signin', JSON.stringify(data))
            const decodedToken = jwt_decode(data.id_token) as any
            this.gapiUser = decodedToken
            window.gapi.client.setToken({ access_token: data.access_token })
          } catch (error) {
            alert(
              'Fail to authorize google service: ' + error + '\n' +
              'Operation failed with error GA0x0002'
            )
          }
        }
      })

      oauth2CodeClient.requestCode()
    },

    fastSignIn () {
      const strData = localStorage.getItem('experiments.google-drive.signin')
      const data = JSON.parse(strData || '{}')
      const decodedToken = jwt_decode(data.id_token) as any
      this.gapiUser = decodedToken
      window.gapi.client.setToken({ access_token: data.access_token })
    },

    signOut () {
      const cred = window.gapi.client.getToken()
      if (cred !== null) {
        window.gapi.client.setToken({ access_token: '' })
      }
      this.$axios.post('/api/users/google/oauth2/logout/')
    },

    listDrives () {
      window.gapi.client.drive.drives.list().then((response) => {
        console.log('response' + response)
      })
    },

    listFiles (filename: string) {
      const config = {
        pageSize: 10,
        fields: 'nextPageToken, files(id, name, mimeType)',
        spaces: '',
        corpora: 'allDrives',
        includeItemsFromAllDrives: true,
        supportsAllDrives: true,
        q: undefined as string | undefined
      }

      if (filename) {
        config.q = `name contains '${filename}'`
      }

      window.gapi.client.drive.files
        .list(config)
        .then((response: any) => {
          const files = response.result.files
          if (files && files.length > 0) {
            this.files = files
            console.log('files' + files)
          } else {
            console.log('no files fould')
            this.files = []
          }
        })
    },

    uploadSimpleTextFile () {
      const file = new Blob(['This file is uploaded to shared drive from dobybot'], { type: 'text/plain' })
      const metadata = {
        name: 'test-upload.txt',
        mimeType: 'text/plain',
        parents: [this.upload.destination]
      }

      const form = new FormData()
      form.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }))
      form.append('file', file)

      fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart&supportsAllDrives=true', {
        method: 'POST',
        headers: new Headers({ Authorization: `Bearer ${window.gapi.client.getToken().access_token}` }),
        body: form
      }).then((res) => {
        return res.json()
      }).then((response) => {
        console.log(response)
      })
    },

    uploadFileSimple () {
      if (!this.upload.file) { return }

      const file = this.upload.file
      const metadata = {
        name: file.name,
        mimeType: file.type,
        parents: [this.upload.destination]
      }

      const form = new FormData()
      form.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }))
      form.append('file', file)

      fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart&supportsAllDrives=true', {
        method: 'POST',
        headers: new Headers({ Authorization: `Bearer ${window.gapi.client.getToken().access_token}` }),
        body: form
      }).then((res) => {
        return res.json()
      }).then((response) => {
        console.log(response)
      })
    },

    async createFolder () {
      let name = prompt('what is folder name?')
      if (!name) { name = 'New Folder' }
      this.folders = [(await createFolder(name))]
    },

    createFolderInShareDrive () {
      const name = 'New Folder'
      const metadata = {
        name,
        mimeType: 'application/vnd.google-apps.folder',
        parents: ['0AIsm-QqgNTFWUk9PVA']
      } as any

      const form = new FormData()
      form.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }))

      fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart&supportsAllDrives=true', {
        method: 'POST',
        headers: new Headers({ Authorization: `Bearer ${window.gapi.client.getToken().access_token}` }),
        body: form
      }).then((res) => {
        return res.json()
      }).then((response) => {
        console.log(response)
      })
    },

    async createSubFolder () {
      const name = prompt('what is folder name?')
      const parent = this.folders[0]
      if (!name) { return }
      if (!parent) { return }
      await createFolder(name, { parentFolderId: parent.id || '' })
    }
  }
})
</script>
