<template>
  <div style="position: relative">
    <video
      id="video"
      ref="videoElement"
      class="w-100 elevation-1 bg-black"
      autoplay="true"
      @click="Click()"
    />

    <v-col
      v-if="currentStream"
      style="position: absolute; bottom: 12vh"
      class="text-center"
    >
      <v-btn
        size="xxx-large"
        class="text-h3 pa-2 elevation-8"
        @click="Click()"
      >
        <v-icon>
          mdi-camera
        </v-icon>
      </v-btn>
    </v-col>
    <canvas id="myCanvas" class="hidden" />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapActions } from 'vuex'
import { uploadFile } from '~/api/googledrive'
export default Vue.extend({
  data () {
    return {
      UrlDatas: [],
      videoElement: null,
      currentStream: null as MediaStream | null,
      isUsingEnvironmentCamera: true as boolean,
      canvas: null as HTMLCanvasElement | null,
      video: null as HTMLVideoElement | null
    }
  },

  mounted () {
    this.startCamera()
    this.gapiAutoSignIn()
  },

  methods: {
    ...mapActions('gapi', {
      gapiSignIn: 'signIn',
      gapiSignOut: 'signOut',
      gapiAutoSignIn: 'autoSignIn'
    }),

    async startCamera () {
      console.log('run function startCamera')
      const videoElement = this.$refs.videoElement as HTMLVideoElement
      if (!videoElement) {
        return
      }

      if (this.currentStream) {
        this.currentStream.getTracks().forEach(track => track.stop())
      }

      try {
        const constraints = {
          video: {
            facingMode: this.isUsingEnvironmentCamera ? 'environment' : 'user'
          }
        }

        const stream = await navigator.mediaDevices.getUserMedia(constraints)

        videoElement.srcObject = stream
        this.currentStream = stream
      } catch (error) {
        console.error('Error accessing camera:', error)
      }
    },
    Click () {
      this.video = document.getElementById('video') as HTMLVideoElement
      this.canvas = document.getElementById('myCanvas') as HTMLCanvasElement
      if (!this.canvas) {
        return
      }
      const context = this.canvas.getContext('2d')
      if (!context) {
        return
      }

      const width = this.video.videoWidth
      const height = this.video.videoHeight
      this.canvas.width = width
      this.canvas.height = height
      context.drawImage(this.video, 0, 0, width, height)
      const imgURL = this.canvas.toDataURL('image/png')
      this.convertImageUrltoFile(imgURL)
    },

    async convertImageUrltoFile (imgUrl) {
      console.log(this.dataURLtoBlob(imgUrl))

      const response = await uploadFile(this.dataURLtoBlob(imgUrl), '1GjQ6YmwxwhZ2XufFpySbxpmY14COByPT')
      console.log(response)
    },

    dataURLtoBlob (dataURL) {
      const parts = dataURL.split(';base64,')
      const contentType = parts[0].split(':')[1]
      const raw = window.atob(parts[1])
      const rawLength = raw.length
      const uInt8Array = new Uint8Array(rawLength)

      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i)
      }

      const blob = new Blob([uInt8Array], { type: contentType })
      const fileName = 'image.png' // Specify the desired filename
      const file = new File([blob], fileName, { type: blob.type })

      return file
    }
  }
})
</script>

<style scoped>

</style>
