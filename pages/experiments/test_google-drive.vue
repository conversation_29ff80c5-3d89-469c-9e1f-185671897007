<template>
  <div>
    user: {{ gapiUser }}<br>
    token: {{ gapiToken }}<br>

    <v-btn @click="gapiSignIn()">
      Sign in google drive
    </v-btn>

    <v-btn color="success" @click="gapiAutoSignIn()">
      Auto Login Google Drive
    </v-btn>

    <div class="mt-5">
      <h1>Create Folder</h1>
      <v-btn color="success" @click="clickCreateFolder()">
        Run
      </v-btn>
      folder_link: <a :href="folder_link">{{ folder_link }}</a>
    </div>

    <div class="mt-5">
      <h1>Create Sub Folder</h1>
      <v-btn color="success" @click="clickCreateSubFolder()">
        Run
      </v-btn>
      parent_folder_link: <a :href="parent_folder_link">{{ parent_folder_link }}</a><br>
      sub_folder_link: <a :href="sub_folder_link">{{ sub_folder_link }}</a>
    </div>

    <div class="mt-5">
      <h1>Find Folder In My Drive</h1>
      <v-btn color="success" @click="clickFindFolderInMyDrive()">
        Run
      </v-btn>
      check console
    </div>
    <div class="mt-5">
      <h1>Upload text file</h1>
      <input type="file" @change="text_file = $event.target.files[0]">
      <v-btn color="success" @click="clickUploadTextFile()">
        Upload
      </v-btn>
    </div>
    <div class="mt-5">
      <h1>SignIn for automate testing (Cypress)</h1>
      <v-btn data-test="test_google-drive-sign-in-google" color="success" @click="gapiCypressSignIn()">
        <v-icon size="medium" class="mr-2">
          mdi-google
        </v-icon>
        Sign In
      </v-btn>
      <div v-if="gapiUser" data-test="test_google-drive-gapi-user">
        user: {{ gapiUser }}
      </div>
      <div v-if="gapiToken" data-test="test_google-drive-gapi-token">
        token: {{ gapiToken }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapGetters, mapActions } from 'vuex'
import { createFolder, findFolderInMyDrive, share, uploadFile } from '~/api/googledrive'

export default Vue.extend({
  data () {
    return {
      message: 'world',
      folder_link: '',
      parent_folder_link: '',
      sub_folder_link: '',
      text_file: null as File | null
    }
  },

  computed: {
    ...mapGetters('gapi', {
      gapiToken: 'token',
      gapiUser: 'currentUser'
    })
  },
  mounted () {
    // this.$store.dispatch('gapi/autoSignIn')
  },

  methods: {
    ...mapActions('gapi', {
      gapiSignIn: 'signIn',
      gapiSignOut: 'signOut',
      gapiAutoSignIn: 'autoSignIn',
      gapiCypressSignIn: 'cypressSignIn'
    }),

    async clickCreateFolder () {
      const folder = await createFolder('My Test Folder (sine)')
      console.log('clickCreateFolder', folder)
      this.folder_link = `https://drive.google.com/drive/folders/${folder.id}`
    },

    async clickCreateSubFolder () {
      const parent_folder = await createFolder('Parent')
      const sub_folder = await createFolder('Child', { parentFolderId: parent_folder.id })
      this.parent_folder_link = `https://drive.google.com/drive/folders/${parent_folder.id}`
      this.sub_folder_link = `https://drive.google.com/drive/folders/${sub_folder.id}`
    },

    async clickFindFolderInMyDrive () {
      const folder = await findFolderInMyDrive('Parent')
      console.log("findFolderInMyDrive('Parent')", folder)
    },
    async clickUploadTextFile () {
      if (!this.text_file) {
        return
      }
      console.log(this.text_file)
      const g_file = await uploadFile(this.text_file, '1GjQ6YmwxwhZ2XufFpySbxpmY14COByPT')
      console.log(g_file)
      if (g_file && g_file.id) {
        await share(g_file.id)
      }
    }
  }
})
</script>

<style scoped>

</style>
