<template>
  <div>
    <!-- <vue-qr-reader
      @code-scanned="codeArrived"
    /> -->
    <qrcode-stream :formats="['qr_code', 'code_128']" @decode="onDecode" />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
// import VueQrReader from 'vue-qr-reader/dist/lib/vue-qr-reader.umd.js'
import { QrcodeStream } from 'vue-qrcode-reader'
export default Vue.extend({
  components: {
    // VueQrReader,
    QrcodeStream
  },
  data () {
    return {
      is_scanning: true as boolean
    }
  },
  methods: {
    codeArrived (event) {
      console.log('Is in a function')

      console.log(event)
    },
    onDecode (event) {
      console.log(event)
    }
  }
})
</script>

<style scoped>

</style>
