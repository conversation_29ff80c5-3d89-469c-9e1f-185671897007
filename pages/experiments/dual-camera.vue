<template>
  <div>
    <video
      controls
      autoplay
      playsinline
    />
    <div>
      <v-btn @click="getDevicesList()">
        List Camera
      </v-btn>
      <v-btn @click="start()">
        Start
      </v-btn>
      <v-btn @click="stop()">
        stop
      </v-btn>
    </div>
    <div>
      Full HD Webcam: 5631dc4aff028c1041e0b9320d9d66c29a9644fafdb78071fb6ec582fb707bd7<br>
      FaceTime HD Camera: df068ead4f13d7efc7c505f0de69beba48da60da023cc45592bbc1847fec1262
    </div>
    <div>
      {{ devices }}
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import RecordRTC from 'recordrtc'
export default Vue.extend({
  data () {
    return {
      recorder: null as any,
      devices: [] as any[],
      blobs: []
    }
  },
  methods: {
    async start () {
      const stream1 = await this.captureCamera('5631dc4aff028c1041e0b9320d9d66c29a9644fafdb78071fb6ec582fb707bd7', 1920, 1080)
      // @ts-ignore
      stream1.onRender = function (context, x, y, width: number) {
        const textToDisplay = 'TEST'
        context.font = '50px Georgia'
        const measuredTextWidth = parseInt(context.measureText(textToDisplay).width)
        x = x + width - measuredTextWidth - 40
        y = y + 80
        context.strokeStyle = 'rgb(0, 0, 0)'
        context.fillText(textToDisplay, x, y)
      }
      const stream2 = await this.captureCamera('df068ead4f13d7efc7c505f0de69beba48da60da023cc45592bbc1847fec1262', 1920, 1080)
      this.recorder = new RecordRTC.MultiStreamRecorder([stream1, stream2], {
        // type: 'video',
        // mimeType: 'video/webm;codecs=h264',
        // frameInterval: 1
      })
      // this.recorder.canvas = {
      //   width: 3840,
      //   heigth: 1920
      // }
      this.recorder.record()

      const video = document.querySelector('video') as HTMLVideoElement
      this.recorder.getMixer().width = 1280
      this.recorder.getMixer().height = 720
      video.srcObject = this.recorder.getMixer().getMixedStream()
      this.recorder.ondataavailable = function (blobs) {
        console.log('ondataavailable')
        this.blobs.push(blobs)
        console.log({ blobs: this.blobs })
      }

      const recorder = this.recorder
      console.log({ recorder })
      debugger
    },

    stop () {
      this.recorder.stop(() => {
        // const blob = this.recorder.blob
        // concatenateBlobs(this.blobs, 'video/webm;codecs=h264', (blob) => {
        //   RecordRTC.invokeSaveAsDialog(blob, 'test.webm')
        // })
      })
    },

    getDevicesList (): void {
      try {
        navigator.mediaDevices.enumerateDevices().then(
          (deviceInfos: MediaDeviceInfo[]) => {
            for (const device of deviceInfos) {
              if (device.kind === 'videoinput' && device.deviceId !== '') {
                this.devices.push(device)
              }
            }
          }
        )
      } catch (error) {
        console.error('The navigator.mediaDevices is undefined in insecure context.\nOperation failed with error RTC0x0001')
      }
    },

    captureCamera (device_id: string, width: number, height: number): Promise<MediaStream> {
      console.log('captureCamera', { device_id })
      const constraints = {
        audio: false,
        video: {
          width: { min: 720, ideal: width, max: 1920 },
          height: { min: 480, ideal: height, max: 1080 },
          frameRate: { min: 12, ideal: 24, max: 24 },
          deviceId: {
            exact: device_id
          }
        }
      }

      return navigator.mediaDevices
        .getUserMedia(constraints)
        // .then((camera: MediaStream) => {
        // })
        // .catch((error) => {
        //   alert('Unable to capture your camera. Please check console logs.')
        //   console.error(error)
        // })
    }
  }
})
</script>
