<template>
  <v-container>
    <h1>Record MP4</h1>
    <div>
      <v-select
        v-model="file_format"
        label="speed"
        :items="[{ text: 'webm', value: 'webm'}, {text: 'mp4', value: 'mp4'}]"
      />

      <v-select
        v-model="speed_factor"
        label="speed"
        :items="[{ text: '1x', value: 1}, {text: '2x', value: 0.5}, {text: '4x', value: 0.25}]"
      />

      <v-text-field
        v-model.number="autostop"
        label="autostop (seconds)"
      />

      <v-text-field
        v-model.number="fps"
        label="FPS"
      />
    </div>
    <div>
      <video controls autoplay playsinline style="width: 100%;" />
    </div>
    <div>
      <v-btn @click="startRecording()">
        start
      </v-btn>
    </div>
  </v-container>
</template>

<script lang="ts">
import { createFFmpeg, fetchFile, FFmpeg } from '@ffmpeg/ffmpeg'
import RecordRTCPromisesHandler, { invokeSaveAsDialog } from 'recordrtc'

import Vue from 'vue'
export default Vue.extend({
  data () {
    return {
      speed_factor: 1,
      autostop: 5,
      ffmpeg: null as FFmpeg | null,
      file_format: 'webm',
      fps: 30
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    async init () {
      this.ffmpeg = createFFmpeg({
        log: true,
        corePath: '/static/js/@ffmpeg/core@0.11.0/ffmpeg-core.js'
      })
      await this.ffmpeg.load()
    },
    async convertVideo (videoBlob: Blob) {
      if (!this.ffmpeg) {
        return
      }
      this.ffmpeg.FS('writeFile', 'input.webm', await fetchFile(videoBlob))

      // Convert to mp4
      await this.ffmpeg.run('-i', 'input.webm', '-movflags', 'faststart', '-c:v', 'copy', '-an', 'output1.mp4')

      // Speed up
      const speed_factor = this.speed_factor
      if (speed_factor === 1) {
        const data = this.ffmpeg.FS('readFile', 'output1.mp4')
        return new Blob([data.buffer], { type: 'video/mp4' })
      } else {
        await this.ffmpeg.run('-itsscale', `${this.speed_factor}`, '-i', 'output1.mp4', '-c', 'copy', '-an', 'output2.mp4')
        const data = this.ffmpeg.FS('readFile', 'output2.mp4')
        return new Blob([data.buffer], { type: 'video/mp4' })
      }
    },

    async startRecording () {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { width: { ideal: 1280 }, height: { ideal: 720 } },
        audio: false
      })

      const recorder = new RecordRTCPromisesHandler(stream, {
        type: 'video'
        // mimeType: 'video/webm;codecs=h264',
        // videoBitsPerSecond: 128000 * 10
      })

      const video = document.querySelector('video')
      if (!video) { return }
      video.muted = true
      video.volume = 0
      video.srcObject = stream

      recorder.startRecording()
      const sleep = (m: number) => new Promise((resolve, reject) => setTimeout(resolve, m))
      const autostop = this.autostop * 1000
      await sleep(autostop)
      recorder.stopRecording(async () => {
        video.srcObject = null

        const webmBlob = new Blob([recorder.getBlob()], { type: 'video/webm' })
        if (this.file_format === 'mp4') {
          const mp4Blob = await this.convertVideo(webmBlob)
          if (mp4Blob) {
            invokeSaveAsDialog(mp4Blob)
          }
        } else {
          invokeSaveAsDialog(webmBlob)
          // getSeekableBlob(webmBlob, (seekableBlob) => {
          //   invokeSaveAsDialog(seekableBlob)
          // })
        }
      })
    }
  }
})
</script>
