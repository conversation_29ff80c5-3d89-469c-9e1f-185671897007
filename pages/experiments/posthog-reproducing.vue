<template>
  <div>
    <v-btn @click="gapiCypressSignIn()">
      Login Google
    </v-btn>
    <v-text-field v-model="access_token" label="Access Token" />
    <v-file-input v-model="upload.file" label="File Upload" />
    <v-text-field v-model="upload.destination" label="Folder ID" />
    <v-btn @click="uploadFile()">
      Upload
    </v-btn>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapActions } from 'vuex'

export default Vue.extend({

  data () {
    return {
      file_upload: null,
      access_token: '*********************************************************************************************************************************************************************************************************************************',
      upload: {
        destination: '1CI3v9l1_8LMO1e4nXZaUywGVGDtBYeY3',
        file: null as File | null
      }
    }
  },
  mounted () {

  },
  methods: {
    ...mapActions('gapi', {
      gapiCypressSignIn: 'cypressSignIn'
    }),

    uploadFile () {
      if (!this.upload.file) { return }
      const file = this.upload.file
      const metadata = {
        name: file.name,
        mimeType: file.type,
        parents: [this.upload.destination]
      }
      const form = new FormData()
      form.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }))
      form.append('file', file)
      fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart&supportsAllDrives=true', {
        method: 'POST',
        headers: new Headers({ Authorization: `Bearer ${this.access_token}` }),
        body: form
      }).then((res) => {
        return res.json()
      }).then((response) => {
        console.log(response)
      })
    }
  }

})
</script>

<style lang="scss" scoped>

</style>
