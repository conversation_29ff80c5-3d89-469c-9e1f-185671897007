<template>
  <div>
    <h1>Simple Video Recording using RecordRTC</h1>
    <v-select
      v-model="selected_device"
      label="Select Device"
      item-text="label"
      return-object
      :items="devices"
    />
    <v-select
      v-model="selected_resolution"
      label="Select Resolution"
      :items="resolutions"
    />
    <v-btn @click="start()">
      Start Recording
    </v-btn>
    <v-btn @click="stop()">
      Stop Recording
    </v-btn>
    <v-divider class="my-5" />
    <video
      ref="video"
      controls
      autoplay
      playsinline
    />
  </div>
</template>

<script lang="ts">
import RecordRTC from 'recordrtc'
import Vue from 'vue'
export default Vue.extend({
  data () {
    return {
      msg: 'Hello world',
      camera: null as MediaStream | null,
      recorder: null as RecordRTC | null,

      devices: [] as MediaDeviceInfo[],
      selected_device: null as MediaDeviceInfo | null,
      resolutions: [
        '720x480',
        '1280x720',
        '1920x1080'
      ],
      selected_resolution: '720x480'
    }
  },

  mounted () {
    this.listCameras()
  },

  methods: {
    start (): void {
      if (!this.selected_device) {
        return alert('Please select a camera')
      }
      if (!this.selected_resolution) {
        return alert('Please select a resolution')
      }

      this.captureCamera(this.selected_device, this.selected_resolution, (camera: MediaStream) => {
        const video = this.$refs.video as HTMLVideoElement
        video.muted = true
        video.volume = 0
        video.srcObject = camera

        this.camera = camera
        this.recorder = new RecordRTC(camera, { type: 'video', mimeType: 'video/webm;codecs=h264', timeSlice: 15 })
        this.recorder.startRecording()
      })

      setTimeout(this.stop, 60 * 1000)
    },

    stop (): void {
      if (!this.recorder) { return }

      this.recorder.stopRecording(() => {
        if (!this.recorder) { return }
        if (!this.camera) { return }

        const video = this.$refs.video as HTMLVideoElement
        const blob = this.recorder.getBlob()
        video.src = ''
        video.srcObject = null
        video.muted = false
        video.volume = 1
        video.src = URL.createObjectURL(blob)

        this.recorder.destroy()
        this.recorder = null

        this.camera.getTracks().forEach((track) => {
          track.stop()
        })

        const filename = prompt('Video Name')
        if (filename) {
          this.$db.table('videos').put({ name: filename, blob })
          alert('Your video is saved to IndexedDB')
        }
      })
    },

    captureCamera (device: MediaDeviceInfo, resolution: string, callback: Function): void {
      const [width, height] = resolution.split('x')
      const constraints = {
        video: {
          width: { min: 720, ideal: Number(width), max: 1920 },
          height: { min: 480, ideal: Number(height), max: 1080 },
          deviceId: device.deviceId
        }
      }

      navigator.mediaDevices
        .getUserMedia(constraints)
        .then((camera: MediaStream) => {
          callback(camera)
        })
        .catch((error) => {
          alert('Unable to capture your camera. Please check console logs.')
          console.error(error)
        })
    },

    listCameras (): void {
      navigator.mediaDevices.enumerateDevices()
        .then((deviceInfos: MediaDeviceInfo[]) => {
          for (const device of deviceInfos) {
            if (device.kind === 'videoinput') {
              this.devices.push(device)
            }
          }

          this.selected_device = this.devices[0]
        })
    }
  }
})
</script>

<style scoped>
/* video {
  object-fit: contain;
} */
</style>
