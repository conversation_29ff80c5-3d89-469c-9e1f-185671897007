<template>
  <div>
    <h1>Hello world</h1>
    <!-- directive -->
    <div v-viewer class="images">
      <img v-for="src in images" :key="src" :src="src">
    </div>
    <!-- component -->
    <!-- <vue-viewer :images="images">
      <img v-for="src in images" :key="src" :src="src">
    </vue-viewer> -->
    <!-- api -->
    <button type="button" @click="show">
      Click to show
    </button>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
// import 'viewerjs/dist/viewer.css'
// import VueViewer from 'v-viewer'
export default Vue.extend({

  // components: {
  //   VueViewer
  // },

  data () {
    return {
      images: [
        'https://picsum.photos/200/200',
        'https://picsum.photos/300/200',
        'https://picsum.photos/250/200'
      ]
    }
  },

  methods: {
    show () {
      // this.$viewerApi({
      //   images: this.images
      // })
    }
  }
})
</script>

<style scoped>

</style>
