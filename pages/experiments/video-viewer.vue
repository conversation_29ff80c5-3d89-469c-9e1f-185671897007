<template>
  <div>
    <video
      controls
      autoplay
      muted
      playsinline
      preload="metadata"
      style="width: 100%; border: 1px solid #eeeeee;"
    >
      <source
        v-if="file_type === 'mp4' || file_type === ''"
        :src="video_src"
        type="video/mp4"
      >
      <source
        v-if="file_type === 'webm' || file_type === ''"
        :src="video_src"
        type="video/webm"
      >
      Sorry, your browser doesn't support embedded videos.
    </video>
    {{ video_src }}
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  data () {
    return {
      file_type: 'mp4',
      video_src: 'https://drive.google.com/uc?export=download&id=1iLAjh_Ex828HngF0EYOzb6OBWfUAD7xh&confirm=t'
      // video_src: 'https://dobybot-drive-reverse-proxy-service-yuo4mnnlaa-as.a.run.app/uc?export=download&id=1iLAjh_Ex828HngF0EYOzb6OBWfUAD7xh&confirm=t'

    }
  }
})
</script>
