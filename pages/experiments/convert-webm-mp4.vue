<template>
  <v-container>
    <h1>webm to mp4</h1>
    <p>time: {{ time.toFixed(2) }}</p>
    <v-select
      v-model="speed_factor"
      label="speed"
      :items="[
        {text: '1x', value: 1},
        {text: '2x', value: 0.5},
        {text: '3x', value: 1/3},
        {text: '4x', value: 0.25}
      ]"
    />
    <input id="file-input" type="file">
    <v-btn small @click="convertWebmToMp4()">
      Convert webm to mp4
    </v-btn>
    <v-btn small @click="convertMp4ToWebm()">
      Convert mp4 to webm
    </v-btn>
    <v-btn small @click="speedUpMp4()">
      Speedup Mp4
    </v-btn>
  </v-container>
</template>

<script lang="ts">
import Vue from 'vue'
import { createFFmpeg, fetchFile, FFmpeg } from '@ffmpeg/ffmpeg'
import { invokeSaveAsDialog } from 'recordrtc'
export default Vue.extend({
  data () {
    return {
      ffmpeg: null as FFmpeg | null,
      speed_factor: 1,
      time: 0
    }
  },
  created () {
    this.init()

    setInterval(() => {
      this.time += 0.2
    }, 200)
  },
  methods: {
    async init () {
      this.ffmpeg = createFFmpeg({
        log: true,
        corePath: '/static/js/@ffmpeg/core@0.11.0/ffmpeg-core.js'
      })
      await this.ffmpeg.load()
    },
    async convertWebmToMp4 () {
      const target = (document.getElementById('file-input') as HTMLInputElement)
      if (!target || !target.files || !this.ffmpeg) {
        return
      }

      const file = target.files[0]
      this.ffmpeg.FS('writeFile', 'input.webm', await fetchFile(file))

      await this.ffmpeg.run('-i', 'input.webm', '-movflags', 'faststart', '-c:v', 'copy', '-an', 'output1.mp4')

      const data = this.ffmpeg.FS('readFile', 'output1.mp4')
      const mp4Blob = new Blob([data.buffer], { type: 'video/mp4' })
      invokeSaveAsDialog(mp4Blob)
    },
    async convertMp4ToWebm () {
      const target = (document.getElementById('file-input') as HTMLInputElement)
      if (!target || !target.files || !this.ffmpeg) {
        return
      }

      const file = target.files[0]
      this.ffmpeg.FS('writeFile', 'input.mp4', await fetchFile(file))

      await this.ffmpeg.run('-i', 'input.mp4', '-c:v', 'copy', '-an', 'output.webm')

      const data = this.ffmpeg.FS('readFile', 'output.webm')
      const webmBlob = new Blob([data.buffer], { type: 'video/webm' })
      invokeSaveAsDialog(webmBlob)
    },
    async speedUpMp4 () {
      const target = (document.getElementById('file-input') as HTMLInputElement)
      if (!target || !target.files || !this.ffmpeg) {
        return
      }

      const file = target.files[0]
      this.ffmpeg.FS('writeFile', 'input.mp4', await fetchFile(file))
      const t1 = performance.now()
      // Speed up with raw bitstream
      // await this.ffmpeg.run('-i', 'input.mp4', '-map', '0:v', '-c:v', 'copy', '-bsf:v', 'h264_mp4toannexb', 'raw.h264')
      // await this.ffmpeg.run('-fflags', '+genpts', '-r', '120', '-i', 'raw.h264', '-c:v', 'copy', 'output.mp4')

      // itsscale
      await this.ffmpeg.run('-itsscale', '0.5', '-i', 'input.mp4', '-c', 'copy', '-an', 'output.mp4')

      // Speed up with setpts
      // await this.ffmpeg.run('-i', 'input.mp4', '-filter:v', `setpts=${this.speed_factor}*PTS`, 'output.mp4')

      const t2 = performance.now()
      console.log('------------------------------------------------------------------------------')
      console.log('finish in ' + ((t2 - t1) / 1000) + 'seconds')
      console.log('------------------------------------------------------------------------------')
      const data = this.ffmpeg.FS('readFile', 'output.mp4')
      const blob = new Blob([data.buffer], { type: 'video/mp4' })
      invokeSaveAsDialog(blob)
    }
  }
})
</script>
