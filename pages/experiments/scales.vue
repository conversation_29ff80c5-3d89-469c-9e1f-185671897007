<template>
  <div>
    <div>
      <v-select
        v-model="computer_id"
        :items="computers"
        item-text="name"
        item-value="id"
        label="Computer"
        filled
      >
        <template #item="{ item, on }">
          <v-list-item v-on="on">
            <v-icon :color="item.state == 'connected' ? 'success' : 'error'">
              mdi-circle-medium
            </v-icon>
            {{ item.id }} - {{ item.name }}
          </v-list-item>
        </template>
      </v-select>
      <v-btn @click="connectScale()">
        Connect Scales
      </v-btn>
    </div>
    <hr class="my-5">
    <div v-if="scale">
      <div>
        Device Name: {{ scale.device_name }}<br>
        Device Number: {{ scale.device_num }}<br>
      </div>
      <div style="font-size: 2rem;">
        Scale Reading: {{ weight.toFixed(1) }} g
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// https://github.com/PrintNode/PrintNode-JS

import Vue from 'vue'
export default Vue.extend({
  data () {
    return {
      weight: 0,
      computer_id: '487418',
      scale: null as any | null,
      ws: null as any | null,
      computers: [] as any[]
    }
  },

  mounted () {
    this.getComputers()
    this.ws = new window.printnode.WebSocket({ apiKey: 'CSQNGBkzrIUEb_FQbSHE8t7EA_GJLIzkSY2XvqaoEJ8' })

    this.ws.subscribe('authenticate', (auth: any) => {
      console.log('authenticated', { auth })
      if (auth.error) {
        console.error(auth.error)
      }

      // console.log('getScales')

      // ws.getScales({ computerId: 490031 }, (data) => {
      //   console.log({ data })
      //   this.weight = data.measurement.g / 10e8
      // })
    })

    this.ws.subscribe('error', (...args) => {
      console.error(...args)
    })
  },

  methods: {
    async getComputers () {
      this.computers = await this.$axios.$get('/api/picking/computers/')
    },
    async connectScale () {
      console.log('connectScale')
      try {
        const result = await this.$axios.$get(`/api/picking/computers/${this.computer_id}/scales/`)
        this.scale = result[0]
      } catch (error) {
        alert('Can fetch scales')
        return
      }

      if (!this.scale) {
        alert('there are no scale connected to this computer')
        return
      }

      this.ws.getScales({ computerId: Number(this.scale.computer_id), deviceNum: this.scale.device_num }, (data: any) => {
        console.log({ data })
        this.weight = data.measurement.g / 10e8
      })
    }
  }
})
</script>
