<template>
  <div>
    <ul>
      <li v-for="link in links" :key="link">
        <nuxt-link :to="link">
          {{ getName(link) }}
        </nuxt-link>
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import { last } from 'lodash'
import Vue from 'vue'
export default Vue.extend({
  data () {
    return {
      links: [
        '/experiments/video-record',
        '/experiments/video-list',
        '/experiments/google-drive'
      ]
    }
  },

  methods: {
    getName (link: string): string {
      let name = last(link.split('/'))
      if (!name) { return '' }

      name = name.split('-').join(' ')
      return name
    }
  }
})
</script>

<style scoped>
li {
  text-transform: capitalize;
}
</style>
