<template>
  <div>
    <h1 style="color:red">
      Please check the console for production logs.
    </h1>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { PRODlog } from '~/utils/logger'
export default Vue.extend({
  data () {
    return {}
  },
  mounted () {
    // This will log to the console in production mode only
    PRODlog('This will log to the console in production mode') // console.log will show only in development mode
    console.log('Development mode:', process.env.NODE_ENV === 'development')
  }

})
</script>

<style lang="scss" scoped>

</style>
