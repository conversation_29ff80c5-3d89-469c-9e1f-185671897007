<template>
  <div class="print-container">
    <div class="d-flex justify-center no-print mt-5">
      <v-select
        v-model="selectedPageSize"
        :items="pageSizes"
        label="Select Page Size"
        outlined
        dense
      />
    </div>
    <div class="page" :class="selectedPageSize">
      <e-tax-qr-slip />
    </div>
    <div class="d-flex justify-center text-center no-print pt-5">
      <v-btn
        width="100"
        class="print-button"
        color="primary"
        @click="printPage()"
      >
        <v-icon left>
          mdi-printer
        </v-icon>
        พิมพ์
      </v-btn>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import ETaxQrSlip from '~/components/etax-invoice/ETaxQrSlip.vue'

export default Vue.extend({
  components: { ETaxQrSlip },
  layout: 'blank',
  data () {
    return {
      selectedPageSize: 'size-80mm', // Default page size
      pageSizes: [
        { text: '80mm Slip', value: 'size-80mm' }
        // { text: 'A4', value: 'size-a4' }
      ]
    }
  },
  methods: {
    printPage () {
      window.print() // Opens the print dialog
    }
  }
})
</script>

<style scoped>
/* Define default page styles */
.print-container {
  width: 76mm;
  height: 70mm;
  padding: 0;
  margin: 0 auto;
}

.page {
  border: 1px solid black;
}

/* Print-specific styles */
@media print {
   @page {
    size: 80mm 80mm;
    margin: 0;
  }
  body {
    margin: 0;
    padding: 0;
  }

  .page {
    border: none;
  }

  .no-print {
    display: none !important;
    visibility: hidden !important;
  }

  /* Ensure print button visibility for preview adjustments */
  .print-button {
    display: none !important;
  }
}
</style>
