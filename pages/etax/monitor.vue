<template>
  <div>
    <div class="d-flex justify-space-between">
      <h2>
        Monitor E-Tax
        <span class="text--secondary ms-2">
          {{ order_json ? `#${order_json.number }` : "" }}
        </span>
      </h2>

      <span>
        <v-btn icon @click="openNewTab()">
          <v-icon>mdi-open-in-new</v-icon>
        </v-btn>
        <!-- <v-btn @click="getMonitorETax()">
          click
        </v-btn>

        <v-btn @click="logData(order_json)">
          log
        </v-btn> -->
      </span>
    </div>
    <v-divider class="my-1" />

    <v-row v-if="current_doc">
      <v-col cols="12" class="ms-3 pb-0">
        <v-select
          v-if="documents"
          v-model="current_doc"
          :items="documents"
          return-object
          item-text="doc_id"
          label="เลขที่เอกสาร"
          class="my-3"
          style="width: fit-content;"
          hide-details="auto"
        >
          <template #item="{item}">
            <p>
              {{ ` ( ${item.doc_type.toUpperCase()} ) ${item.doc_id}` }}
            </p>
          </template>
        </v-select>
      </v-col>

      <!-- Left -->
      <v-col cols="12" sm="5" class="pt-0">
        <!-- Order Number -->
        <v-col>
          <label class="text--secondary me-1">หมายเลขคำสั่งซื้อ: </label>
          <span>{{ current_doc.order_number }}</span>
        </v-col>

        <!-- Create Date -->
        <v-col class="d-flex">
          <label class="text--secondary me-1">วันที่สร้าง: </label>
          <span>{{ $datetime(current_doc.create_date) }}</span>
        </v-col>

        <v-col>
          <label class="text--secondary me-1">มูลค่าสุทธิ: </label>
          <span>
            {{ $fmtCurrency(current_doc.doc_info?.MONEY_GRAND_TOTALAMT) }} บาท
          </span>
        </v-col>

        <v-col>
          <label class="text--secondary me-1">สถานะ: </label>
          <v-chip :color="getStatusColor(current_doc.status)" class="text-uppercase">
            {{ current_doc.status }}
          </v-chip>
        </v-col>

        <!-- Order Number -->
        <v-col>
          <label class="text--secondary">ประเภทเอกสาร: </label>
          <v-chip label>
            <span class="text-uppercase me-1">{{ current_doc.doc_type }}</span>
            {{ `(V.${ current_doc.doc_version })` }}
          </v-chip>
        </v-col>

        <v-col>
          <h4>ข้อมูลผู้ซื้อ:</h4>
          <v-divider />
        </v-col>
        <v-col v-for="(value, key) in current_doc?.buyer" :key="key" class="py-1">
          <label class="text--secondary me-1">{{ key }}:</label>
          <span>{{ value }}</span>
        </v-col>

        <v-col>
          <br>
          <a href="https://docs.google.com/spreadsheets/d/1wmlVb-7S5-GAJoyU61sJJLOhCAhJpyEvBckqWQ1G394/edit?usp=sharing">Google Sheet: Data Dict</a>
        </v-col>
      </v-col>

      <!-- Right -->
      <v-col cols="12" sm="7" class="px-15 py-0">
        <iframe
          class="mx-auto"
          style="max-width: 650px;width: 100%; aspect-ratio: 1/1.414; border: 1px solid #e0e0e0"
          :src="current_doc?.doc_url?.replace(/view(?=[^view]*$)/, 'preview')"
          frameborder="0"
        />
      </v-col>
    </v-row>

    <v-col v-if="current_doc?.log" cols="12" class="px-0">
      <h3>Document Log</h3>
      <v-data-table
        class="mt-4"
        dense
        :headers="log_headers"
        :items="current_doc?.log"
      >
        <template #item.action="{ item }">
          <v-btn icon @click="openLog(item)">
            <v-icon>mdi-file-eye-outline</v-icon>
          </v-btn>
        </template>
        <template #item.timestamp="{ item }">
          {{ $datetime(item.timestamp) }}
        </template>
      </v-data-table>
    </v-col>

    <v-row class="ma-0">
      <v-col cols="12" class="px-0">
        <h3>E-Tax Document</h3>
        <div v-if="taxDoc" class="editor">
          <JsonEditorVue v-model="taxDoc" :read-only="true" class="jse-theme-dark full-width" />
        </div>
      </v-col>

      <v-col cols="12" class="px-0">
        <h3>Validation Error</h3>
        <v-row v-if="current_doc && current_doc.status_reason" class="editor mb-5">
          <v-col cols="6">
            <h3>Order Json Error</h3>
            <JsonEditorVue v-model="current_doc.status_reason.order_json" :read-only="true" class="jse-theme-dark full-width" />
          </v-col>
          <v-col cols="6">
            <h3>D1A Json Error</h3>
            <JsonEditorVue v-model="current_doc.status_reason.d1a_json" :read-only="true" class="jse-theme-dark full-width" />
          </v-col>
        </v-row>
        <div v-else>
          No Validation Error
        </div>
      </v-col>

      <v-col cols="6" class=" ps-0 pe-1">
        <h3>Order Json</h3>
        <div v-if="order_json" class="editor">
          <JsonEditorVue v-model="order_json" :read-only="true" class="jse-theme-dark full-width" />
        </div>
      </v-col>
      <v-col cols="6" class="ps-1 pe-0">
        <h3>D1A Json</h3>
        <div v-if="current_doc" class="editor">
          <JsonEditorVue v-model="current_doc.doc_info" :read-only="true" class="jse-theme-dark full-width" />
        </div>
      </v-col>

      <v-col cols="12" class="px-0">
        <h3>XML</h3>
        <div v-if="current_doc" class="editor">
          <xml-viewer v-model="current_doc.doc_xml" />
        </div>
      </v-col>
    </v-row>

    <v-dialog v-if="log_info" v-model="dialog">
      <v-card>
        <div style="width:100%;" class="text-end">
          <v-btn color="error" class="ms-auto" @click="dialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div>
        <div style="max-height: 80vh; display: flex;">
          <JsonEditorVue v-model="log_info" :read-only="true" class="jse-theme-dark full-width" />
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import XmlViewer from '../../components/editor/xml-viewer.vue'
import { TaxMonitorDocument, OrderJson } from '~/models'
import 'vanilla-jsoneditor/themes/jse-theme-dark.css'

function JsonEditorVue () {
  return process.client
    ? import('json-editor-vue')
    : Promise.resolve({ render: h => h('div') })
}

export default Vue.extend({
  components: { JsonEditorVue, XmlViewer },
  data () {
    return {
      company_uuid: '',
      order_uuid: '',
      documents: [] as TaxMonitorDocument[],
      order_json: {} as OrderJson,
      current_doc: null as TaxMonitorDocument | null,
      checker: [] as any[],

      // Log
      dialog: false,
      log_info: null as any,
      log_headers: [
        { text: '', value: 'action', align: 'center', width: '15px', sortable: false },
        { text: 'วัน-เวลา', value: 'timestamp' },
        { text: 'เหตุการณ์', value: 'extra.request.data.DOC_STATUS' },
        { text: 'โดย', value: 'create_by' },
        { text: 'สถานะ', value: 'severity' },
        { text: 'หมายเหตุ', value: 'message' }
      ]

    }
  },
  computed: {
    taxDoc (): any {
      if (!this.current_doc) {
        return null
      }
      const clone = { ...this.current_doc }
      delete clone.doc_xml

      return clone
    }
  },
  mounted () {
    this.company_uuid = this.$route.query.cid ? String(this.$route.query.cid) : ''
    this.order_uuid = this.$route.query.oid ? String(this.$route.query.oid) : ''

    // console.log(this.company_uuid)
    // console.log(this.order_uuid)
    this.getMonitorETax()
  },
  methods: {
    openLog (log: any) {
      this.log_info = log
      this.dialog = true
    },
    logData (data: any) {
      console.log(data)
    },
    async getMonitorETax () {
      try {
        const res = await this.$axios.post('/api/etax/monitor/', {
          company_uuid: this.company_uuid,
          order_uuid: this.order_uuid
        }) as {data: { order_json: OrderJson, documents: TaxMonitorDocument[], checker: any[] }}

        // console.log(res)
        this.documents = res.data.documents
        this.order_json = res.data.order_json
        this.current_doc = res.data.documents[0]
        this.checker = res.data.checker
      } catch (error: any) {
        console.error(error)
      }
    },
    getStatusColor (status: string) {
      switch (status) {
        case 'new':
          return ''
        case 'success':
          return 'success'
        case 'failed':
          return 'error'
        case 'cancel':
          return 'error'
        case 'on_hold':
          return 'warning'
        case 'invalid':
          return 'error'
        default:
          return 'primary'
      }
    },
    openNewTab () {
      window.open(`${window.location.origin}/etax/monitor?cid=${this.company_uuid}&oid=${this.order_uuid}`)
    }
  }
})
</script>

<style scoped>
.editor {
  max-height: 90vh;
  display: flex;
}
.full-width {
  width: 100%;
}
</style>
