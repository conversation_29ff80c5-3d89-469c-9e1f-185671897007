<template>
  <div>
    <v-row>
      <v-col cols="4" />
    </v-row>
    <v-card class="my-4">
      <v-card-title>
        <v-row>
          <v-col cols="12" class="d-flex">
            <v-select
              v-model="tax_doc"
              label="Choose Document"
              :items="tax_docs"
              return-object
              item-text="doc_id"
              item-value="id"
              hide-details=""
              filled
              style="max-width: 400px"
            />
            <!-- <v-btn icon large class="my-auto ms-2">
              <v-icon>mdi-file-edit-outline</v-icon>
            </v-btn> -->

            <!-- <v-dialog max-width="550">
              <template #activator="{ on }">
                <v-btn icon large class="my-auto ms-2" v-on="on">
                  <v-icon>mdi-file-edit-outline</v-icon>
                </v-btn>
              </template>
              <v-card class="pa-5">
                <e-tax-invoice-step-2
                  v-if="tax_doc"
                  :tax-document-uuid="tax_doc.uuid"
                  :company-uuid="company_uuid"
                  :order-uuid="order_uuid"
                  :user-uuid="user.uuid"
                  :is-staff="true"
                  :is-update="true"
                  :settings="settings.company"
                  :order-number="tax_doc.order_number"
                />
              </v-card>
            </v-dialog> -->
          </v-col>
          <!-- <v-col cols="8" class="d-flex justify-end align-center" /> -->
        </v-row>
      </v-card-title>
      <v-divider />
      <v-card-text>
        <v-row>
          <v-col cols="4">
            <label-value-table
              v-if="tax_doc"
              :data="{...tax_doc, ...pick_order}"
              :keys="['order_number', 'order_oms', 'company', 'company_name', 'order_saleschannel', 'doc_id', 'doc_type', 'status', 'create_date', 'doc_version']"
            />
          </v-col>
          <v-col cols="4">
            <label-value-table
              v-if="tax_doc && tax_doc.buyer"
              :data="tax_doc.buyer"
              :keys="['buyer_name', 'email', 'phone_number', 'tax_id', 'branch_code', 'branch_name']"
            />
          </v-col>
          <v-col cols="4">
            <label-value-table
              v-if="tax_doc && tax_doc.buyer"
              :data="tax_doc.buyer"
              :keys="['address', 'post_code']"
            />
          </v-col>
        </v-row>

        <v-row>
          <v-col cols="12" class="ma-0 pa-0">
            <v-divider />
          </v-col>
          <v-col cols="3" class="d-flex flex-column justify-end">
            <div>ยกเลิกเอกสาร ใช้ในกรณีไม่ต้องการเอกสารนี้แล้ว</div>
            <ul>
              <li>ไม่สามารถยกเลิกเอกสารที่ส่งเข้ากรมไปแล้ว</li>
            </ul>
            <v-spacer />
            <v-checkbox v-model="form.send_email" label="Send Email" />
            <v-btn color="error" width="100" @click="cancelEtax()">
              CANCEL
            </v-btn>
          </v-col>
          <v-divider vertical />
          <v-col cols="3" class="d-flex flex-column justify-end">
            <div>
              <div>คำนวณข้อมูลใบกำกับภาษีใหม่ และ ส่ง API หา D1A ใช้กรณีเอกสารผิด</div>
              <ul>
                <li>ใช้เลขเอกสารเดิม</li>
                <li>ตัวเลขที่คำนวณใหม่อาจไม่เท่ากับของเก่า</li>
              </ul>
            </div>
            <v-spacer />
            <v-checkbox v-model="form.send_email" label="Send Email" />
            <v-btn
              class="mr-2"
              color="warning"
              dark
              :loading="loading"
              width="100"
              @click="retryEtax()"
            >
              RETRY
            </v-btn>
          </v-col>
          <v-divider vertical />
          <v-col cols="3" class="d-flex flex-column justify-end">
            <div>ส่ง API หา D1A อีกครั้ง ใช้กรณี D1A Server ล่ม</div>
            <v-spacer />
            <v-checkbox v-model="form.send_email" label="Send Email" />
            <v-btn
              color="primary"
              dark
              :loading="loading"
              width="100"
              @click="updateEtax()"
            >
              UPDATE
            </v-btn>
          </v-col>
          <v-divider vertical />
          <v-col cols="3" class="d-flex flex-column justify-end">
            <div>
              ใช้ในกรณีที่ จำนวนเงินในเอกสารมีการเปลี่ยนแปลง
              <ul>
                <li>จะส่ง email เสมอ</li>
                <li>เลขเอกสารใหม่จะเพิ่ม -1, -2, -3 ตามหลัง</li>
              </ul>
            </div>
            <v-spacer />
            <v-checkbox label="Send Email" :input-value="true" disabled />
            <v-btn
              color="error"
              dark
              outlined
              :loading="loading"
              @click="cancelAndRenewEtax()"
            >
              ยกเลิก และ ออกเอกสารใหม่
            </v-btn>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <v-row>
      <v-col v-if="tax_doc" cols="12" md="6">
        <tax-doc-viewer
          :tab="tab_left"
          :order-json="order_json"
          :pick-order="pick_order"
          :d1a-json="tax_doc.doc_info"
          :pdf="tax_doc.doc_url"
          :pdf-custom="getCustomPdfUrl(tax_doc)"
          :xml.sync="tax_doc.doc_xml"
          :error="tax_doc.status_reason"
          :d1a-json2="tax_doc.doc_info2"
          :error2="tax_doc.status_reason2"
          @update:tab="saveLeftTabToLocalStorage($event)"
        />
      </v-col>
      <v-col v-if="tax_doc" cols="12" md="6">
        <tax-doc-viewer
          :tab.sync="tab_right"
          :pick-order="pick_order"
          :order-json="order_json"
          :d1a-json="tax_doc.doc_info"
          :pdf="tax_doc.doc_url"
          :pdf-custom="getCustomPdfUrl(tax_doc)"
          :xml.sync="tax_doc.doc_xml"
          :error="tax_doc.status_reason"
          :d1a-json2="tax_doc.doc_info2"
          :error2="tax_doc.status_reason2"
          @update:tab="saveRightTabToLocalStorage($event)"
        />
      </v-col>
    </v-row>

    <v-row>
      <v-col v-if="tax_doc" cols="12">
        <h3>Logs</h3>
        <tax-doc-log-table :logs="tax_doc.log" />
      </v-col>
    </v-row>

    <v-row>
      <v-col v-if="tax_doc" cols="12">
        <h3>History</h3>
        <tax-doc-history-table :items="history" />
      </v-col>
    </v-row>

    <code>
      <!-- {{ order_json }} -->
      <!-- {{ pick_order }} -->
    </code>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { TaxMonitorDocument, OrderJson, PickOrder, LoggedInUser } from '~/models'
import 'vanilla-jsoneditor/themes/jse-theme-dark.css'
import TaxDocViewer from '~/components/etax-debugger/TaxDocViewer.vue'
import TaxDocLogTable from '~/components/etax-debugger/TaxDocLogTable.vue'
import LabelValueTable from '~/components/etax-debugger/LabelValueTable.vue'
import TaxDocHistoryTable from '~/components/etax-debugger/TaxDocHistoryTable.vue'
import { SettingState } from '~/store/settings'
import { getBackendHost } from '~/utils'

interface TaxDebuggingResponse {
   order_json: OrderJson,
   pick_order: PickOrder,
   documents: TaxMonitorDocument[],
   history: any[]
   company: any
}

export default Vue.extend({
  components: {
    TaxDocViewer,
    TaxDocLogTable,
    LabelValueTable,
    TaxDocHistoryTable
  },
  data () {
    return {
      company_uuid: '',
      order_uuid: '',
      user: null as never as LoggedInUser,
      tax_docs: [] as TaxMonitorDocument[],
      tax_doc: null as TaxMonitorDocument | null,
      order_json: {} as OrderJson,
      pick_order: {} as PickOrder,
      history: [] as any[],

      // Tab
      tab_left: 'ORDER JSON',
      tab_right: 'D1A JSON',

      // Form
      loading: false,
      form: {
        send_email: false
      }
    }
  },
  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },
  created () {
    const user = this.$auth.user as never as LoggedInUser
    if (!user.is_superuser) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }
  },
  mounted () {
    this.company_uuid = this.$route.query.cid ? String(this.$route.query.cid) : ''
    this.order_uuid = this.$route.query.oid ? String(this.$route.query.oid) : ''
    this.user = this.$auth.user as never as LoggedInUser
    this.tab_left = localStorage.getItem('etax.debugger.left_tab') || this.tab_left
    this.tab_right = localStorage.getItem('etax.debugger.right_tab') || this.tab_right
    this.getEtaxDebuggingData()
  },
  methods: {
    saveLeftTabToLocalStorage (new_left_tab: string) {
      this.tab_left = new_left_tab
      localStorage.setItem('etax.debugger.left_tab', new_left_tab)
    },

    saveRightTabToLocalStorage (new_right_tab: string) {
      this.tab_right = new_right_tab
      localStorage.setItem('etax.debugger.right_tab', new_right_tab)
    },

    getCustomPdfUrl (tax_doc: TaxMonitorDocument) {
      const backend_host = getBackendHost()
      const doc_type = tax_doc.doc_info.DOC_TYPE
      if (!this.settings.company.ETAX_TEMPLATE_NAME) {
        return ''
      }

      const template = this.settings.company.ETAX_TEMPLATE_NAME[doc_type]
      if (!template) {
        return ''
      }
      return `${backend_host}/api/etax/pdf/preview/?doc_uuid=${tax_doc.uuid}&template=${template}`
    },

    async getEtaxDebuggingData () {
      const res = await this.$axios.post('/api/etax/monitor/', {
        company_uuid: this.company_uuid,
        order_uuid: this.order_uuid
      }) as {data: TaxDebuggingResponse}

      // console.log(res)
      this.pick_order = res.data.pick_order
      this.tax_docs = res.data.documents
      this.order_json = res.data.order_json
      this.tax_doc = res.data.documents[0]
      this.history = res.data.history
    },

    async retryEtax () {
      if (!this.tax_doc) { return }

      this.$loader(true)
      try {
        const { data } = await this.$axios.post(
          '/api/etax/debugger/retry/',
          { tax_document: this.tax_doc.id, send_email: this.form.send_email }
        )

        if (data.status === 'success') {
          this.$snackbar('success', 'Retry E-Tax: success')
        } else {
          this.$snackbar('error', 'Retry E-Tax: ' + data.status)
        }

        this.getEtaxDebuggingData()
      } catch (error: any) {
        console.error(error)
      }
      this.$loader(false)
    },

    async updateEtax () {
      if (!this.tax_doc) { return }

      this.$loader(true)
      try {
        const { data } = await this.$axios.post(
          '/api/etax/debugger/update/',
          { tax_document: this.tax_doc.id, send_email: this.form.send_email }
        )

        if (data.status === 'success') {
          this.$snackbar('success', 'Update E-Tax: success')
        } else {
          this.$snackbar('error', 'Update E-Tax: ' + data.status)
        }

        this.getEtaxDebuggingData()
      } catch (error: any) {
        console.error(error)
      }
      this.$loader(false)
    },

    async cancelEtax () {
      if (!this.tax_doc) { return }

      if (!confirm('ยืนยันการยกเลิกเอกสารนี้?')) {
        return
      }

      this.$loader(true)
      try {
        await this.$axios.post(
          '/api/etax/debugger/cancel/',
          { tax_document: this.tax_doc.id, send_email: this.form.send_email }
        )
        this.$snackbar('info', 'ยกเลิกเอกสารแล้ว')
        this.getEtaxDebuggingData()
      } catch (error: any) {
        console.error(error)
      }

      this.$loader(false)
    },

    async cancelAndRenewEtax () {
      if (!this.tax_doc) { return }

      if (!confirm('ยืนยันการยกเลิกเอกสารนี้ และ สร้างเอกสารใหม่?')) {
        return
      }

      this.$loader(true)
      try {
        const { data } = await this.$axios.post(
          '/api/etax/debugger/cancel-and-renew/',
          { tax_document: this.tax_doc.id }
        )

        if (data.status === 'success') {
          this.$snackbar('success', 'Update E-Tax: success')
        } else {
          this.$snackbar('error', 'Update E-Tax: ' + data.status)
        }

        this.getEtaxDebuggingData()
      } catch (error: any) {
        console.error(error)
      }
      this.$loader(false)
    }
  }
})
</script>
