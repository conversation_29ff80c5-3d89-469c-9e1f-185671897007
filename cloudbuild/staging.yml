steps:
  - id: "build & push image"
    name: 'gcr.io/kaniko-project/executor:latest'
    env: ['ENV=https://<EMAIL>/6190791']
    args:
    - --destination=gcr.io/${PROJECT_ID}/${_SERVICE_NAME}
    - --cache=true
    - --cache-ttl=48h
    - --build-arg=ENV
  
  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args: [
      'run', 'deploy', '${_SERVICE_NAME}', 
      '--platform', 'managed',
      '--region', '${_REGION}',
      '--image', 'gcr.io/${PROJECT_ID}/${_SERVICE_NAME}', 
      '--allow-unauthenticated'
    ]

substitutions:
  _REGION: asia-southeast1
  _SERVICE_NAME: staging-dobybot-ui-service
