export interface ReceiptTable {
  shipping: number // ค่าขนส่ง
  discount: number // ส่วนลดรวมทั้งบิล
  net: number // ราคาสุทธิ (หักส่วนลด, รวมค่าขนส่งแล้ว)
  voucheramount?: number // ส่วนลดจาก Voucher
  amount_pretax?: number // ราคาก่อนภาษี
  vatamount?: number // ภาษีมูลค่าเพิ่ม
  items: ReceiptItem[] // รายการสินค้า
  receipt_invoice?: ReceiptInvoice | null
}

export interface ReceiptItem {
  sku: string
  name: string
  number: number // จำนวนสั่งซื้อ
  totalprice: number // ราคาสินค้า (รวมทั้งแถว, คูณจำนวนสินค้าแล้ว, ไม่หักส่วนลด)
}

export interface ReceiptInvoice {
  ref_number: string
  name: string
  address: string
  footer_html: string
}

export interface Order {
  receipt_invoice: ReceiptInvoice | null;
  number: string;

  amount: number
  discount:number;
  discountamount: number;
  voucheramount: number;
  shippingamount: number;
  paymentamount: number;
  amount_pretax: number;
  vatamount: number;

  trackingno: string;
  shippingaddress: string;
  shippingchannel: string;
  saleschannel: string;
  status: string;

  // dobysync field
  version:number;
  orderManagementSystem: string;
  discount_amount: number;
  seller_discount: number;
  platform_discount: number;
  shipping_amount: number;
  original_shipping_amount: number;
  seller_shipping_discount: number;
  platform_shipping_discount: number;

  list: OrderList[];
}

export interface OrderList {
  id: null;
  sku: string;
  name: string;
  number: number;
  skutype: null;
  discount: string;
  unittext: string;
  productid: null;
  totalprice: number;
  producttype: number;
  serialnolist: any[];
  discountamount: number;
  pricepernumber: number;
}

export interface Company {
  name: string;
  address: string;
  tel: string;
  ads: string;
  hide_receipt: boolean;
  receipt_logo: string;
  custom_brand: string | null;
}
