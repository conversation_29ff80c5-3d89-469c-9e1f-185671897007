export interface TrackingInfo{
  dstPostalCode: string
  dstStoreName: string
  earlyFlightEnabled: boolean
  lineCode: string
  mchId: string
  notice: null
  outTradeNo: string
  packEnabled: boolean
  pno: string
  sortCode: string
  sortingLineCode: string
  srcPostalCode: string
  subMchId: string
  upcountryCharge: boolean
}

export interface PickOrderTrackingNo {
  id : number
  uuid: string
  tracking_no: string
  pick_order: number
  is_active: boolean
  create_date: Date
  update_date: Date
  shipping_message: string
  shipping_status: string
  shipping_provider: 'DBB-FLASH' | 'DBB-KERRY' | 'DBB-THAIPOST'
  status: 'ERROR' | 'IN_DELIVERY' | 'SUCCESS' | 'CANCELLED'
  tracking_info: TrackingInfo
  cod_amount : number | null
  // Todo webhook_logs type script
  webhook_logs: any
  is_cod_refunded: boolean
  cod_refunded_pno: string
}
