export interface Product {
  id?: number;
  company?: number;
  name: string;
  sku: string;
  barcode: string;
  product_json?: any;
  product_oms: string;
  images: string[],
  main_image: string,
  uploaded_images: string[],
  price: number,
  vat_percent: number,
  color: string,
  marketplace: string | null,
  dobysync_products: DobysyncProduct[],
  main_shop_id: string | null,
  weight: number | null,
  location: string | null,
  width: number | null,
  length: number | null,
  height: number | null,
  packing_score: number | null
  show_qrcode_on_record_page: boolean,
}

export interface ProductSetItem {
  sku: string;
  name: string;
  amount: number;
}

export interface ProductSet {
  id?: number,
  company?: number;
  name: string;
  sku: string;
  products: ProductSetItem[]
  is_deleted: boolean
  change_logs: any[]
}

interface DobysyncSKU {
  name: string;
  price: string;
  sku_id: string;
  seller_sku: string;
  stock_infos: any[];
}

export interface DobysyncProduct {
    name: string;
    skus: DobysyncSKU[];
    extra: any;
    images: string[];
    shop_id: string;
    product_id: string;
    marketplace: string;
}

export interface SerialNo {
  id?: number;
  company?: number;
  serial_no: string;
  sku: string;
  product: number;
  product_name: string;
  in_stock: boolean;
  cutoff_date: string|null;
  create_date: string;
}
