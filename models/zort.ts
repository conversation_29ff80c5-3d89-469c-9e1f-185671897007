export interface ZortOrderItem {
      id?: any;
      productid: number;
      sku: string;
      name: string;
      number: number;
      unittext: string;
      pricepernumber: number;
      discount: string;
      discountamount: number;
      totalprice: number;
      producttype: number;
      serialnolist: any[];
      skutype?: any;
  }

export interface ZortPayment {
      id: number;
      name: string;
      amount: number;
      paymentdatetime: Date;
      paymentdatetimeString: string;
      paymentmethodid: number;
      link?: any;
  }

export interface ZortOrder {
      id: number;
      ordertype: number;
      number: string;
      customerid: number;
      customername: string;
      customercode: string;
      customeridnumber: string;
      customeremail: string;
      customerphone: string;
      customeraddress: string;
      customerbranchname: string;
      customerbranchno: string;
      facebookname?: any;
      facebookid: string;
      line?: any;
      lineid: string;
      reference: string;
      warehousecode: string;
      status: string;
      paymentstatus: string;
      marketplacename?: any;
      marketplaceshippingstatus?: any;
      marketplacepayment?: any;
      amount: number;
      vatamount: number;
      shippingvat: number;
      shippingchannel: string;
      shippingamount: number;
      shippingdate?: any;
      shippingdateString?: any;
      shippingname: string;
      shippingaddress: string;
      shippingphone: string;
      shippingemail: string;
      shippingpostcode: string;
      shippingprovince?: any;
      shippingdistrict?: any;
      shippingsubdistrict?: any;
      trackingno: string;
      orderdate: Date;
      orderdateString: string;
      paymentmethod?: any;
      paymentamount: number;
      paymentdate?: any;
      description: string;
      discount: string;
      platformdiscount: number;
      sellerdiscount: number;
      discountamount: number;
      voucheramount: number;
      vattype: number;
      saleschannel: string;
      list: ZortOrderItem[];
      vatpercent: number;
      payments: ZortPayment[];
      createdby?: any;
      createusername?: any;
      createuserid: number;
      sharelink?: any;
      isCOD: boolean;
      tag?: any;
      createdatetime: Date;
      createdatetimeString: string;
      updatedatetime: Date;
      updatedatetimeString: string;
  }
