export interface SimpleUser {
  id: number;
  full_name: string;
}

export interface LoggedInUser {
  id: number;
  uuid: string;
  username: string;
  first_name: string;
  last_name: string;
  is_staff: boolean;
  is_superuser: boolean;
  groups: any[];
  user_permissions: string[];
  group_permissions: string[];
  company: Company;
  accepted_eula: number | null;
}

export const PACKAGE_FULL_INTEGRATION = '001'
export const PACKAGE_RECORD_ONLY = '002'
export interface Company {
  id: number;
  uuid: string;
  name: string;
  account_suffix: string;
  sms_balance: number;
  record_balance: number;
  google_drive_usage: number;
  google_drive_limit: number;
  wallet_last_update_datetime: string;
  expire_date: string | null;
  is_active: boolean;
  is_active_remark: string;
  package: '001' | '002';
  web_logo: string | null;
  receipt_logo: string | null;
}

export interface User {
  id: number;
  username: string;
  first_name: string;
  last_name: string;
  password?:string
  confirm?:string
  devices?: { [key: string]: Device };
  user_permissions?: string[];
  group_permissions? : string[];
}

export interface Device {
  os: string;
  device: string;
  browser: string;
  login_at: number;
}
