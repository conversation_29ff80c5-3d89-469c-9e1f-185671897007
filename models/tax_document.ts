export interface TaxBuyer {
  email: string;
  tax_id: string;
  address: string;
  post_code: string;
  buyer_name: string;
  phone_number: string;
  is_consent_marketing?: boolean;
}

export interface TaxDocument {
  id: number;
  uuid: string;
  order_number: string;
  create_date: string; // ISO 8601 date-time string
  status: string;
  doc_url: string;
  doc_type: string;
  edit_count: number;
  credit_count: number;
  buyer: TaxBuyer;
  doc_info: any;
}

export interface CompanyBranch {
  taxId: string;
  addressLocal:string;
  name: string;
  branch: string;
  branchCode: string;
  zipCode: string;
  contactGroup: number;
  branchDisplay?: string;
}

export interface PersonalForm {
  name: string;
  tax_id: string;
  address: string;
}

export interface CompanyForm {
  name: string;
  tax_id: string;
  branch: string;
  branch_code: string;
  address: string;
  post_code:string

}

export interface CoFields {
  email: string;
  phone: string;
}

export interface SavedAddress {
  id: string
  form_type: 'personal_form' | 'company_form';
  personal_form: PersonalForm;
  company_form: CompanyForm;
  co_fields: CoFields;
  branchList?: CompanyBranch[];
}

export interface EtaxDetail {
  uuid: string;
  order_number: string;
  create_date: string;
  status: string;
  doc_info: any;
  doc_url: string;
  doc_type: string;
  edit_count: number;
  buyer: any;
}

export const TaxDocumentStatus = [
  {
    value: 'new',
    text: 'ไม่มีเอกสาร',
    color: ''

  }, {
    value: 'success',
    text: 'สำเร็จ',
    color: 'success'
  },
  {
    value: 'on_hold',
    text: 'ไม่สำเร็จ*',
    color: 'warning'
  },
  {
    value: 'failed',
    text: 'ไม่สำเร็จ',
    color: 'warning'
  },
  {
    value: 'cancel',
    text: 'ยกเลิก',
    color: 'error'
  }
]

export interface CustomerTaxData {
  customer_address: string;
  customer_branch_name: string | null;
  customer_branch_no: string | null;
  customer_email: string | null;
  customer_id_number: string | null;
  customer_name: string;
  customer_phone: string;
  order_number: string;
  payment_amount: number|string;
  tax_document_uuid: string | null;
  platform_name:string
  post_code?:string
}

export interface TaxInvoiceFianceRow {
  sku:string;
  name:string;
  number: number;
  pricepernumber: number;
  discount: number;
  totalprice: number;
}

export interface TaxInvoiceFinance {
  pretaxamount: number
  totaldiscount: number
  totalvatamount: number
  grandtotal: number
  discountamount: number
  rows: TaxInvoiceFianceRow[]
}

export interface TaxMonitorDocument {
  id: number;
  uuid: string;
  edit_count: number;
  credit_count: number;
  order_number: string;
  create_date: string; // Consider using Date if you parse it
  buyer: TaxBuyer;
  doc_info: any; // Adjust the type if doc_info has a defined structure
  doc_url: string;
  doc_id: string;
  doc_type: string;
  doc_xml?: string;
  status: string;
  log: any[];
  is_consent_marketing: boolean;
}
