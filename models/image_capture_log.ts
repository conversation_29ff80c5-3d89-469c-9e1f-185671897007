export interface Image{
  id: string
  urlData: string
  file: File
  status: 'success' | 'loading' | 'failed'
}

export interface ImageCaptureLog {
  id: number;
  company: number;
  create_by: string;
  create_date: Date;
  name: string;
  images: Image[] ;
  pick_order: number;
  drive_folder_id: string;
  drive_id : string;
  drive_account : string;
  drive_file_deleted : Boolean | null;
  drive_file_deleted_timestamp : string | null;
}
