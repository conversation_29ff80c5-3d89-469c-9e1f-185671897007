export interface Printer {
  id: string;
  name: string;
  state: string;
  computer_name: string;
}

export interface PrintnodeComputer {
  id: number
  name: string
  state: 'disconnected'|'connected'
}

export interface PrintnodeScale {
  product: string
  port: string
  device_name: string
  device_num: number
  computer_id: number
}

export interface PrintnodeWebsocket {
  closeSocket (): void
  getComputerConnections (options: any, callback: any, ctx: any): void
  getScales(options: any, callback: any, ctx?: any): void
  getState (): void
  isConnected (): void
  publish (topic: string, payload: string, publishErrorCallback: any): void
  removeSesrverSubscription (...args: any[]) :void
  subscribe (topic: string, fn: Function, options?: any): void
  unsubscribe (fnOrTopic: any): void
}
