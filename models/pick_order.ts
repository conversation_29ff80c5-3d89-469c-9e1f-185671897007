
export interface PickItem {
  id?: any;
  key?: any;
  sku: string;
  name: string;
  image: string;
  number: number;
  skutype?: any;
  discount: string;
  unittext: string;
  productid?: number;
  totalprice: number;
  producttype: number;
  serialnolist: any[];
  discountamount: number;
  pricepernumber: number;
  vatpercent?: number;
  eso_vatpercent?: number;
  barcode?: string;
  seller_discount: number;
  show_image_qr_code?: boolean;
}

export interface Payment {
  id: number;
  link?: any;
  name: string;
  amount: number;
  paymentdatetime: Date;
  paymentmethodid: number;
  paymentdatetimeString: string;
}

export interface OrderJson {
  id: number;
  tag?: any;
  line?: any;
  list: PickItem[];
  isCOD: boolean;
  amount: number;
  lineid: string;
  number: string;
  status: string;
  vattype: number;
  discount: string;
  payments: Payment[];
  createdby?: any;
  orderdate: Date;
  ordertype: number;
  reference: string;
  sharelink?: any;
  vatamount: number;
  customerid: number;
  facebookid: string;
  trackingno: string;
  vatpercent: number;
  description: string;
  paymentdate?: any;
  shippingvat: number;
  createuserid: number;
  customercode: string;
  customername: string;
  facebookname?: any;
  saleschannel: string;
  shippingdate?: any;
  shippingname: string;
  customeremail: string;
  customerphone: string;
  paymentamount: number;
  paymentmethod?: any;
  paymentstatus: string;
  shippingemail: string;
  shippingphone: string;
  voucheramount: number;
  warehousecode: string;
  createdatetime: Date;
  createusername?: any;
  discountamount: number;
  sellerdiscount: number;
  shippingamount: number;
  updatedatetime: Date;
  customeraddress: string;
  marketplacename?: any;
  orderdateString: string;
  shippingaddress: string;
  shippingchannel: string;
  customerbranchno: string;
  customeridnumber: string;
  platformdiscount: number;
  shippingdistrict: string;
  shippingpostcode: string;
  shippingprovince: string;
  customerbranchname: string;
  marketplacepayment?: any;
  shippingdateString?: any;
  shippingsubdistrict: string;
  createdatetimeString: string;
  updatedatetimeString: string;
  marketplaceshippingstatus?: any;
  remark?: string;
  extra?: any;
}

export interface PickOrder {
  id: number;
  uuid: string
  company: number;
  order_number: string;
  order_trackingno: string;
  order_saleschannel: string;
  order_shippingchannel: string;
  order_customer: string;
  order_customerphone: string;
  order_date: string;
  order_json: OrderJson;
  order_oms: string
  number: number;
  print_count: number;
  print_timestamp: Date;
  remark: string;
  ready_to_ship: boolean;
  ready_to_ship_timestamp?: any;
  google_drive_video_url?: any;
  create_date: Date;
  videorecordlog_set?: number[] ;
  imagecapturelog_set?: number[];
  pickordertrackingno_set?: number[]
  ready_to_ship_by: number | null;
  signed_receipt_url: string;
  has_videos: boolean;
  taxdocument_set: number[]
}
