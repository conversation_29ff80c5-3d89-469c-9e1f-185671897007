export interface OrderItem {
  sku: string
  name: string
  serialnolist?: string[] | null
  number: number
  image?: string
  serial_no?: string
  key?: string
}

export interface DiffLog {
  sku: string
  name: string
  diff: number
  total: number
  packed: number
}

export interface ScanLog {
  time_ms: number
  sku: string
  barcode: string
  amount: number
  item: OrderItem
}

export interface RecordTag {
  label: string
  barcodes: string[]
}
