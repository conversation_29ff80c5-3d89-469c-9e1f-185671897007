// middleware/setSameOriginHeader.ts

export default function (req: any, res: any, next: any) {
  if (req.url.includes('$')) {
    res.setHeader('Cross-Origin-Embedder-Policy', 'credentialless')
    res.setHeader('Cross-Origin-Opener-Policy', 'same-origin')
  } else {
    res.setHeader('Cross-Origin-Embedder-Policy', 'unsafe-none')
    res.setHeader('Cross-Origin-Opener-Policy', 'unsafe-none')
  }
  next()
}
