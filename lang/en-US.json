{"COMPANY_DOES_NOT_EXISTS": "Company does not exist", "Choose": "choose", "EDITING_ETAX_NOT_ALLOWED_DUE_TO_OVERDUE_DATE": "Editing e-Tax is not allowed due to the expiration date.", "OK": "OK", "ORDER_DOES_NOT_EXISTS": "Order does not exist", "ORDER_IS_NOT_RECEIVED": "Please confirm receive order on the platform first.", "ORDER_IS_NO_LONGER_ELIGIBLE_FOR_ETAX_REQUEST": "This order is no longer eligible for e-Tax request.", "ORDER_IS_VOIDED": "This order has been canceled.", "ORDER_NOT_FOUND": "Incorrect order number", "TAX_DOCUMENT_ALREADY_CREATED": "e-Tax has already been created on this order", "TAX_DOCUMENT_ALREADY_EXISTS": "e-Tax document already exists", "TAX_DOCUMENT_NOT_FOUND": "e-Tax document not found", "TAX_DOCUMENT_NO_LONGER_EDITABLE": "This document is no longer editable", "TAX_DOCUMENT_TOO_MANY_EDIT_ATTEMTPS": "The order has been invoiced. Please check your email. If you did not receive it or need to make changes, please contact the admin.", "TAX_ID_NOT_FOUND": "Tax ID not found", "add": "Add", "add-barcode": "Add barcode", "address": "address", "airway-bill.cover-sheet": "List of cover sheet", "airway-bill.enter": "Press 'Enter' to print", "airway-bill.enter-to-print": "Press enter to print", "airway-bill.file-upload": "The maximum file size is 32 MB.", "airway-bill.fill": "Require to fill", "airway-bill.order-number": "order number.", "airway-bill.print": "PRINT", "airway-bill.scan": "<PERSON>an the order number to print a cover sheet.", "airway-bill.search": "Search", "airway-bill.send-finish": "Print command sent", "airway-bill.success": "Success", "airway-bill.upload": "Upload cover sheet", "alert.can_not_update_order": "Unable to edit order", "and": "and", "announcement.close": "Close", "announcement.do-not-show-again": "Don't show again", "app-bar.credit-video": "Video credit balance", "app-bar.google-drive-space": "Google drive space", "app-bar.last-update": "Latest update", "app-bar.used-all": "(Used / All)", "barcode": "Barcode", "barcode-type": "Barcode Type", "boardcast.boardcast": "Boardcast", "boardcast.start": " Start record in another tab", "boardcast.stop": "Stoping the record", "bulkpick-amount": "QTY", "bulkpick-clear": "clear", "bulkpick-error-notfound": "Referenced order not found", "bulkpick-error-scanned": "The reference number has been scanned.", "bulkpick-get-list": "Get Bulk Pick List", "bulkpick-itemname": "Name", "bulkpick-itemsku": "SKU", "bulkpick-label": "Scan pick slip here", "bulkpick-order": "ORDER", "bulkpick-title": "<PERSON><PERSON> the Pick Slip to combine.", "bulkpick-total": "Total", "by": "by", "cancel": "Cancel", "cant-upload-video-contact-admin": "Please contact administrator to resolve this issue", "change-print-status.change-print-status-of-n-orders": "Change print status of {0} orders to", "change-print-status.has-video": "Has video?", "change-print-status.is_print": "Is printed?", "change-print-status.not-printed": "Not printed", "change-print-status.printed": "Printed", "change-print-status.ready-to-ship": "Is ready to ship?", "change-setting-here": "Additional settings can be founded here.", "chat-with-us": "Chat with us now. \n<PERSON><PERSON> responds quickly to every message.", "check-if-barcode-scanner-working": "Verify that the barcode scanner is compatible. \nScan the barcode below.", "check-if-camera-is-working": "Check if the camera works.", "click-here-to-start": "Click here to start", "click-here-to-start-scanning-barcode": "Click here to start scanning barcodes.", "confirm-only": "Confirm", "confirm-revoke-token": "Confirm revoking token?", "confirm-using-1080p": "Confirm using 1080p resolution", "confirm.confirm-cancel-upload": "Confirm cancel video uploading ?", "confirm.confirm-delete": "Confirm deleting item", "confirm.confirm-upload-raw-video": "Do you want to upload an raw file?", "connect-barcode-scanner": "Connecting barcode scanner", "connect-google-drive": "Connect Google Drive", "connect-google-drive-to-store-video-files": "Connect Google Drive to store video data.", "connect-usb-scanner": "Plug the barcode scanner's USB cable into your computer.", "connect-usb-webcam": "Connect the USB Webcam to the computer.", "connected": "Connected", "connection": "connection", "contact-dobybot-support": "Please contact Dobybot Support at 02-665-8500 press 2.", "contact_us": "Contact Us", "continue": "continue", "copied": "<PERSON>pied", "create-product-set": "Add Product Set", "create_by": "created by", "created_at": "created at", "date": "Date", "deposit.contact": "Please top-up credit, contact", "ding": "<PERSON>g", "disconnect-google-drive": "disconnect", "dobybot-packing-solution": "Dobybot the best packing solution.", "edit": "Edit", "enabled": "Enabled", "error.can-not-upload-transcoding-fail": "[{0}] Cannot be uploaded because File conversion failed", "error.can-not-upload-transcoding-fail-g-drive": "{[0]} Unable to upload because Google Drive is not connected", "error.cancel-by-user": "[{0}] canceled by user", "error.cant-use-this-account": "Cannot use this Google Drive account to store videos.", "error.click-mechanism": "Click on the gear icon", "error.fail-to-connect-database": "[{0}] Error connecting database - Please contact dobybot team \n Operation failed with error REC0x0001", "error.fail-to-connect-database-status": "[{0}] Error connecting database - Please contact dobybot support \\nOperation failed with error REC0x0001", "error.feature_flag": "This service is not supported for the package you are using.", "error.full-storage-g-drive": "[{0}] Unable to upload due to insufficient Google Drive space", "error.g-drive-account-mismatch": "[{0}] Unable to upload because google account is invalid", "error.image-capture.no-balance-text": "Can not proceed due to insufficient credit. Please contact Dobybot support to topup credit.", "error.incorrect-destination-folder": "Failed to upload due to invalid destination folder, system will retry uploading again in 5 seconds.", "error.invalid_password": "Incorrect password", "error.no-balance": "Insufficient Credit", "error.no-balance-text": "Can not record video due to insufficient credit. Please contact Dobybot support to topup credit.", "error.not-login-step2": "Click on \"Connect to Google Drive\" to use video file saving.", "error.not-login-step4": "Enter the password information for the email to connect with Google Drive.", "error.not-login-step5": "Select \"View, edit, create, and delete only the Google Drive files that you use with this app\" to allow doby<PERSON> to send videos to Google Drive >> then click \"Continue\".", "error.please-select-camera": "Please select camera", "error.please_fix_form_error": "Please fix all error in the form", "error.response": "Error [{0}] {1}", "error.un-login-step3": "Enter the username to connect with Google Drive.", "error.un-login-step6": "Once the connection to Google Drive is successful, the displayed information will appear. Then, close the settings window to use video recording.", "error.un-login-title": "Google Drive is not connected yet.", "error.unable-to-capture-your-camera-noperation-failed-with-error-rec0x0002": "Unable to capture your camera.\\nOperation failed with error REC0x0002", "error.unable-to-capture-your-camera-noperation-failed-with-error-rtc0x0002": "Unable to capture your camera.\\nOperation failed with error RTC0x0002", "error.unable-to-connect-to-google-drive": "Can't connect to Google Drive", "error.unknown": "There is an error, please try again later.", "error.use-these-account-only": "Please change to an authorized account only.", "esan1": "Esan 1", "esan2": "Esan 2", "esan3": "Esan 3", "esan4": "Esan 4", "etax.action.order-amount-change": "Action when the order amount changes.", "etax.action.allow_received": "Allow e-Tax document generation on order received", "etax.action.auto_cancel": "Automatically cancel e-Tax when order is cancelled or returned", "etax.action.receipt_show_etax_button": "Show a button to request an e-Tax invoice on the receipt page.", "etax.action.show_qr": "Show QR Code on pick slip for e-Tax form", "etax.action.title": "Settings", "etax.action.zort_auto_tag": "Add a tag in automatic order when creating ETAX or canceled.", "etax.auto-e-tax-description": "Auto create e-Tax when platform request", "etax.auto-e-tax-platform-label": "Auto create e-Tax when platform request", "etax.bill-preview": "<PERSON>", "etax.easy-order-updated": "saved successfully", "etax.footer-message": "Footer Message", "etax.header-message": "Header Message", "etax.order-center-qr-menu": "Print e-Tax QR Code", "etax.seller_info.address": "Address", "etax.seller_info.branch_code": "Branch Code", "etax.seller_info.branch_name": "Branch Name", "etax.seller_info.company_name": "Company/Store Name", "etax.seller_info.phone": "Phone Number", "etax.seller_info.post_code": "Postal Code", "etax.seller_info.tax_id": "Tax ID", "etax.seller_info.title": "Seller Information", "etax.setup-qr-code": "Set up QR Code", "etax.sms_link": "{sms_etax_link} e-Tax Link", "etax.sms_message_header": "Set up message and e-Tax link to send via SMS", "etax.sms_message_input": "SMS message", "etax.technical.title": "Technical Information", "etax.title": "ETax Settings", "example": "Example", "export-excel": "Download as Microsoft Excel", "fastnote.copy": "<PERSON>pied", "fastnote.delete": "Confirm deletion", "fastnote.error": "an error occurred can't save", "fastnote.lenght": "Change order: Please enter a new order (1-{0})", "fastnote.no": "No.", "fastnote.text": "Text", "female1": "Female 1", "female2": "Female 2", "female3": "Female 3", "female4": "Female 4", "finish-setting-up": "Settings are complete.", "fixcase-fixcases-length": "Success: Close FixCase for {0} cases", "fixcase.address": "Delivery address", "fixcase.close-case": "Show closed cases", "fixcase.cost": "Cost(optional)", "fixcase.cost-require": "Cost", "fixcase.create": "Create fixcase", "fixcase.create-fixcase": "CREATE FIX CASE", "fixcase.create-success": "FixCase was created successfully", "fixcase.customer": "Customer", "fixcase.date-opening": "FixCase opening date", "fixcase.details": "Details", "fixcase.issues": "Issues details", "fixcase.note": "Remark(internal use)", "fixcase.order-product": "Ordered products", "fixcase.ordered": "Order", "fixcase.phone": "Phone number", "fixcase.save": "Save", "fixcase.search": "Search", "fixcase.setting": "Settings page", "fixcase.shop": "Shop", "fixcase.sms": "Close the selected case and send SMS notification to the customer.", "fixcase.sms-message": "SMS message", "fixcase.sms-setting": "*When recording The system will send sms to inform customers automatically. You can set the sms message in", "fixcase.tracking": "Please complete the Tracking URL / Tracking Code.", "found-more-than-1-order": "Found more than 1 orders", "found-not-uploaded-video-warning": "{0} videos were not uploaded", "from": "From", "global.close": "Close", "global.close-details": "Close details", "global.details": "See details", "global.shift-esc": "For administrators, press shift + esc to close", "global.sms": "Your SMS credit is low", "global.storage-google-drive": "Google Drive space is almost full", "global.this-date-expire_date": "Your package will expire on {0}", "global.update-refresh-f5": "There is an update! Please Refresh (F5) to update", "global.vdo": "Your VDO credit is low", "global.vdo-sms": "Your VDO and SMS credits are low", "google-drive-space-available": "Available", "hardware-setup-complete": "The hardware is now ready to use. \nLet's set up the application.", "hd-for-normal-usage": "HD suitable for general use", "home.airway-bill-printer": "Please set up the printer for printing the Airway Bill", "home.cancel": "Cancel", "home.date-time": "Date-Time", "home.do-you-want-to-continue-recording": "Do you want to continue recording videos ?", "home.duration": "Duration", "home.file-size": "File size", "home.file-type": "File type", "home.google-account": "The google account that can be used is", "home.history": "View history", "home.invalid-control-code": "Invalid control code", "home.not-found-airway-bill": "Can't find the AirWay bill, want to continue recording the video?", "home.not-upload": "not uploaded yet", "home.order": "Order", "home.press-upload": "Upload", "home.proccess-file": "Processing file", "home.record": "Do you want to continue recording videos ?", "home.remark": "Remark", "home.resolution": "Resolution", "home.retry-upload-strategy": "Retry upload every 60 minutes.", "home.scan-password-for-record-video": "Scan control code to {0} record video", "home.send-print-command": "Send print orders for orders. {0} already", "home.start": "Start", "home.start-test": "Start testing", "home.start-test-duration": "Test duration (seconds per video)", "home.status": "Status", "home.stop": "Stop", "home.stop-record": "stop / save", "home.testing-tool": "Test name", "home.tool": "Test tool", "home.transcoding": "Transcoding", "home.upload-success": "Upload successfully", "home.upload-success-gmail": "Upload success ({0})", "home.upload-video-success": "{0} uploaded successfully", "home.uploading": "Uploading", "home.uploading-n-videos": "Uploading {0} video", "home.uploading-videos": "Uploading is in progress", "i-am-not-recording-video": "Not using the video recording system", "if-you-have-any-question": "If you have any questions or need help", "image-capture-already-exists": "This barcode has previously been saved.", "image-capture-close": "close", "image-capture-confirm": " confirm", "image-capture-duplicate-file": "Do not send duplicate files.", "image-capture-internet-preimage-text": "Please check the internet and try entering the product code again.", "image-capture-internet-text": "Please check the internet and try uploading the picture again.", "image-capture-internet-title": "Can't find internet signal", "image-capture-order-center.title": "Record image capture", "image-capture-share-image-setting": "Set up access to Google Drive.", "image-capture-share-image-setting-description": "After uploading the picture The system will automatically share the link to the recorded image. Makes it possible to open and view immediately.", "image-capture-superviser": "Please enter your password to save product images.", "image-capture-superviser-action": "The password is incorrect.", "image-capture-superviser-alert": "The password is incorrect.", "image-capture-superviser-setting": "Set up the image capture process.", "image-capture-superviser-setting-description": "Before starting to take image capture The system will notify if duplicate entries are found.", "image-capture-superviser-setting-lable": "The \"Supervisor Password\" must be entered in order to save images with warnings.", "image-capture-superviser-title": "This product has been captured. Is it allowed to take repeated photos?", "image-capture-upload-image": "Upload photo", "image-capture-uploading": "Uploading", "image-capture.choose-image": "Select picture", "image-capture.hint": "Enter the order number / document number and press Enter.", "image-capture.placeholder": " Enter the document number or press scan QR code.", "image-capture.title": "Image Capture", "image-capture.upload-succees": "Successfully uploaded", "image-capturn-next": " Next", "intro-scan-barcode-stop-recording": "<PERSON>an the barcode again to stop recording.", "intro-start-by-scan-order-number": "Scan order number / tracking number barcode to start recording", "intro-the-system-will-start-record-automatically": "The system will start recording video automatically.", "intro-youre-ready": "Setup is successful.", "ios-not-supported": "iOS is not supported.", "ios-not-supported-text": "Please use a web browser on another device.", "kid1": "Kid 1", "kid2": "Kid 2", "kid3": "Kid 3", "kid4": "Kid 4", "line-click-here-to-add-friend": "Contact us via LINE@", "logging_in": "Logging In...", "login": "<PERSON><PERSON>", "male1": "Male 1", "male2": "Male 2", "male3": "Male 3", "male4": "Male 4", "marketplace-connection-error": "A connection error occurred.", "marketplace-status-error": "connection error", "marketplace-status-expired": "expired", "marketplace-status-expired_soon": "will expired soon", "marketplace.marketplace": "Martketplace", "marketplace.platform": "Platform", "marketplace.shop_name": "Shop", "marketplace.token_expire": "expire", "me.change-password-success": "Password change successfully", "me.current-device": "Current devices", "me.logout": "Logout", "me.logout-success": "Logout successfully ", "me.my-device": "My devices", "me.new-password": "New password", "me.old-password": "Old password", "me.time": "Login time", "menu.change-password": "Change Password", "menu.logout": "Sign Out", "menu.my-devices": "My Devices", "menu.open-get-start": "Open Quickstart", "minute": "minute", "mp4-can-be-viewed-from-every-device": "Can be viewed conveniently from every device", "mp4-recommendation": "Recommended only you want to send SMS, so that it can be easily viewed from any device.", "mp4-short-desc": "MP4 is a common video recording standard.", "mp4-use-more-system-resources": "MP4 consumes more resources. \nThe file is larger.", "must_be_greater_than_or_equal": "Must greater than or equal to {0}", "must_be_less_than_or_equal": "Must less than or equal to {0}", "name": "name", "name-already-used": "This name is already taken.", "next": "next", "no-sender-address": "Sender address has not been set", "no-video-recorded": "No video recorded / No permissions", "not-login-flash": "Not connected with Flash", "not-recommend-wifi": "WiFi is not recommended", "or": "or", "order-center.all": "All", "order-center.barcode": "Barcode", "order-center.barcodecode": "Scan Barcode VRICXXXXXXXXXXXX-X-X", "order-center.cannot-ready-to-ship": "Could not change status to \"ready to ship\"", "order-center.change-print-status": "Change Print Status", "order-center.click-view-ready-to-ship-log": "Click to view ready to ship history", "order-center.copy-click": "Click to copy Order No.", "order-center.count_new_orders": "Found {0} new orders", "order-center.create-order": "Create Order", "order-center.customer-name": "Customer name", "order-center.date-time": "Date - Time", "order-center.filter_order_status": "Order Status", "order-center.filter_printing_airway_bill_status": "Printing Airway Bill status", "order-center.filter_printing_pick_slip_status": "Printing Pick Slip status", "order-center.filter_trackingno": "Tracking Number", "order-center.fix-case": "Show only items with Fix Case", "order-center.from": "From", "order-center.have-remark": "Show only items with Notes", "order-center.incorrect-barcode": "Barcode is invalid, it must be VRICXXXXXXXXX-X-X- code only", "order-center.left-right-click": "Left click to open receipt | Right click to open receipt", "order-center.log-label": "Logs", "order-center.n-fixcases-are-opened": "There is {0} Fix Case in progress", "order-center.no_order_trackingno": "No Tracking number", "order-center.not-found": "The order number {0} could not be found", "order-center.not-printed": "Not yet printed", "order-center.not-ready": "Not yet ready to ship", "order-center.not-record": "Haven't taken a video yet", "order-center.note": "Note", "order-center.on-sync-order": "The system is syncing data.", "order-center.order-no-tracking-no": "Search Order No. / Tracking No. / Customer name", "order-center.order-no-tracking-no-enter": "Enter Order No./Tracking No. and press Enter", "order-center.order-status": "Platform Status", "order-center.order_trackingno": "Have Tracking Number", "order-center.paid": "Paid", "order-center.payment-status": "Payment Status", "order-center.plase-contact": "Please contact", "order-center.print": "Print", "order-center.print-detail-airway-bill": "Airway bill First print {0} typed {1} times", "order-center.print-detail-pick-slip": "Pick slip First print {0} typed {1} times", "order-center.print-pick-orders": "Print pick slip", "order-center.printed": "Printed", "order-center.ready": "Ready to ship", "order-center.ready-status": "Ready to ship status", "order-center.ready-to": "Ready to ship", "order-center.ready-to-ship-history": "History of Ready To Ship", "order-center.record&image": "Record video and Image", "order-center.record-from": "Record video from", "order-center.record-to": "Record video to", "order-center.rts-from": "Ready to ship from", "order-center.rts-to": "Ready to ship to", "order-center.save-successful": "Save successfully", "order-center.scan-ready-to-ship": "Scanned , ready to ship", "order-center.scan-ready-to-ship-by": "Scanned by", "order-center.scan-ready-to-ship-kerry": "Scan and send (<PERSON>)", "order-center.status": "Status", "order-center.success": "Success", "order-center.sync": "Please select the date you want to sync data", "order-center.sync-fail": "An error occurred while syncing data.", "order-center.sync-success": "Successfully synced data.", "order-center.sync-task-created-at": "Creation date time", "order-center.syncing_order_in_progress": "The system is syncing order information. It may take 1-2 minutes. You can close this window and use the program as usual.", "order-center.system-automate-sync-message": "You can close this window. The system will automatically sync the data.", "order-center.tax-document-exist": "This order has already been invoiced. This order cannot be edited.", "order-center.to": "To", "order-center.unpaid": "Unpaid", "order-center.update": "Update information from Marketplace", "order-center.video": "Video", "order-center.video-save": "Video recorded", "order-import.being-import-data": "Data is being imported", "order-import.by": "By", "order-import.date-time": "Date - Time", "order-import.error": "Please correct any errors in the file", "order-import.file": "File", "order-import.has-error-contact-admin": "[500] An error has occurred. Please contact the administrator.", "order-import.history": "Data import history", "order-import.import-data": "Import data", "order-import.order": "Import order data", "order-import.select-file": "Select file", "order-import.status": "Status", "order-import.type": "Type", "order-number-or-tracking-number-for-video-name": "Order number / Tracking number", "order.order-number": "Order number", "packing-status.instead": "instead", "packing-status.switch": "Please switch to", "paid_at": "paid at", "password": "Password", "password-for-register": "and password used to apply for register", "permission.add_airwaybill": "Upload Airway Bill", "permission.add_fixcase": "Create Fix Case", "permission.add_imagecapturelog": "Access Page: Image Capture", "permission.add_orderimportrequest": "Import Data", "permission.add_pickorder": "Create Easy Order", "permission.add_smscampaign": "Access Page: SMS", "permission.add_taxdocument": "Create e-Tax", "permission.add_user": "Add new user", "permission.add_videorecordlog": "Access Page: Record", "permission.add_videorecordlog_return": "Access Page: Record (Return)", "permission.add_videorecordlog_transfer": "Access Page: Record (Transfer)", "permission.change_fixcase": "Change and close FixCase", "permission.change_pickorder": "Edit Pickorder", "permission.change_settingvalue": "Access Page: Settings", "permission.change_taxdocument": "Edit e-Tax", "permission.change_user": "Edit User", "permission.delete_taxdocument": "Cancel e-Tax", "permission.delete_user": "Delete User", "permission.export_pickorder": "Export orders as .xlsx", "permission.view_airwaybill": "Access Page: Airway Bill", "permission.view_billing_report": "View Billing Report", "permission.view_fastnote": "Access Page: Fast Note", "permission.view_fixcase": "Access Page: <PERSON><PERSON>", "permission.view_fixcase_report": "View Fix Case Report", "permission.view_image_capture_log_report": "View Image Capture Log Report", "permission.view_orderimportrequest": "Access Page: Order Import", "permission.view_performance_report": "View Performance Report and Performance Analysis Report", "permission.view_pick_item_daily_summary_report": "View Pick Item Summary Report", "permission.view_pick_item_report": "View Pick Item By Order Report", "permission.view_pickorder": "Access Page: Order Center", "permission.view_product": "Access Page: Product SKU/Barcode", "permission.view_productset": "Access Page: Product Set", "permission.view_productserialno": "Access Page: Product Serial No.", "permission.view_sms_report": "View SMS Report", "permission.view_taxdocument": "View e-Tax", "permission.view_user": "Access Page: User Management", "permission.view_video_record_diff_report": "View Diff Report", "permission.view_video_record_report": "View Video Record Report", "permission.view_video_record_report_no_video_link": "View Video Record Report (No Video Link)", "permission.export_pickorder_detail": "Export orders as Microsoft Excel (Detail)", "permission.view_order_dashboard": "Access Page: Order Dashboard", "permission.view_best_selling_products_dashboard": "Access Page: Product Dashboard", "permission.view_performance_dashboard": "Access Page: Performance Dashboard", "permission.view_fix_case_dashboard": "Access Page: Fix Case Dashboard", "picking.order-number": "Order number", "pingpong": "Ping pong", "please": "Please", "please-resign-in": "Please login again with this username", "please-select-camera": "Please select the camera connected via USB port according to the camera name.", "please-select-order-you-wish-to-record": "Please select an order you want or a scanning of the tracking number instead.", "please-select-receipt-printer-id": "Please select a printer for printing receipts / tax invoices.", "print-receipt-order-notfound": "Could not find order for printing receipt.", "printer.find-printer-name": "How to find computer name", "printers.fail-to-update-printer-list": "Unable to update printer list. Please check the Dobybot Cloud Print settings.", "printers.load": "Load a list of printers from PrintNode.", "printers.printer": "Printer", "printers.success": "Success | The printer list has been updated.", "printnode.cant-update-computer-list": "The computer list could not be updated.", "printnode.choose-computer-for-scale": "Select the computer to which the weigh scale is connected.", "printnode.computer-list-updated": "The list of computers has been updated.", "product-barcode.add-image": "Add Image", "product-barcode.click-to-select-main-image": "* Press the small picture to change the main picture.", "product-barcode.create-product-barcode": "Create Product Barcode", "product-barcode.delete-image": "clear", "product-barcode.edit-product": "Edit Product Barcode", "product-barcode.general-product-infomation": "General Product Infomation", "product-barcode.platform-images": "Platform Images", "product-barcode.price": "Price", "product-barcode.product-from-marketplace": "Product From Marketplace", "product-barcode.product-images": "Product Images", "product-barcode.select": "Select", "product-barcode.selected": "Selected", "product-barcode.sku-barcode-record": "Match SKU codes with product barcodes for use in scanning and checking items on the Record page.", "product-barcode.uploaded-images": "Uploaded Images", "product-sync-menu": "An error occurred while connecting the data.", "product.barcode-vat_percent": "This price includes VAT.", "public.amount": "Amount", "public.click-here-to-call-to-shop": "Call the store", "public.copy": "Copy", "public.delivery": "delivered by", "public.discount": "Discount", "public.download-video": "Download video", "public.found-package-not-correct": "If found that the package list is not correct", "public.freight": "Freight", "public.interest-system": "Interested in the system, contact", "public.list": "Product list", "public.normal-download": "Download", "public.open-in-google-drive": "Open in Google Drive", "public.order-not-found": "Order not found", "public.order-num": "Order number", "public.order-number": "Order number {0}", "public.parcel": "parcel tracking number", "public.price": "Price", "public.print": "Print", "public.receipt": "Receipt", "public.shipping-number": "Parcel tracking number {0}", "public.total-payment": "Total payment", "queue-for-upload": "In queued", "quick-sms": "Send Quick SMS", "receipt.show-packing-video": "Show video of product packing", "receipt.show-receipt": "Show receipt", "receipt.void-return-order-message": "Order ({0}) is being canceled/returned.", "recipient-full-name": "Full name", "recipient-information": "Recipient information", "recipient-phone": "Phone number", "recommend-lan-connection": "Recommend LAN cable", "recommended": "recommend", "reconnect": "Reconnect", "record-form.cant-print-airway-bill": "Something went wrong. Can not print airway bill (Error 500)", "record-form.cant-print-receipt": "An error occurred. \nUnable to print receipt (Error 500)", "record-form.continue-record-video": "Continue recording?", "record-history": "Record history", "record.control-code": "Video name (order no. / tracking no.)", "record.downloading-mp4-engine": "Downloading MP4 Engine", "record.history": "Record History", "record.normal_mode": "Normal Mode", "record.not-connect-drive": "Please connect to Google Drive.", "record.recoding": "Recording", "record.record-video": "Recording", "record.return-mode": "Return Mode", "record.start-recording": "Start", "record.testing-transcode-mp4": "Converting files to MP4.", "record_browser_not_supported": "Video recording via web browser is not supported.", "register-user": "Register to start using the system.", "register.success": "Register Success", "remember-me": "Remember Me", "rename-company": "Name your company", "request-etax-invoice": "Request a tax invoice (e-Tax)", "resolution-setting-recommendation": "We recommend 720p as the best setting. \nYou can come back and edit it later in the settings menu.", "revoke-token-success": "The token has been cancelled.", "rules.barcode": "Must be in English letters or numbers or symbols -,_,/,#,$,%,&,@,:", "rules.barcode_thai": "Must be in Thai/English letters or numbers or symbols -,_,/,#,$,%,&,@,:", "rules.between": "value must be between {0} and {1}", "rules.confirm-password": "Passwords are not the same", "rules.email": "The email format is incorrect.", "rules.english_and_number_only": "Must be in lowercase English letter or numbers only.", "rules.exact_length": "his field must be exactly {0} characters long", "rules.includes_value": "The message must include \"{0}\".", "rules.integer_only": "Must be an integer", "rules.invalid_thai_zipcode": "Please enter a valid postal code.", "rules.maxlength": "Must not exceed {0} characters.", "rules.no_zero_width_space": "Input must not contain zero-width spaces. Try to type the text manually.", "rules.number_gt": "Must be greater than {0}", "rules.number_only": "Must be a number", "rules.password-min-length": "Password must be at least 8 characters long.", "rules.password-must-contain-digit": "Password must contain at least 1 digit.", "rules.password-must-contain-lowercase-letter": "Password must contain at least one lowercase character.", "rules.password-must-contain-special-character": "Password must contain at least 1 special character.", "rules.password-must-contain-uppercase-letter": "Password must contain at least one uppercase character.", "rules.phone_number": "The phone number format is incorrect.", "rules.required": "This field is required", "rules.username_will_include_at_sign": "The system will automatically add {0} after the username. \nPlease do not use the '@' in your username.", "save": "Save", "save-storage-space": "Save storage space", "search": "Search", "send-print-receipt-command-success": "Sending print command for {0}", "setiing-shipping-flash-connect-button": "Connect", "setting": "Setting", "setting-shipping-flash-auto-tracking-help": "The system will fire an API to generate a tracking number from the shipping service provider.", "setting-shipping-flash-auto-tracking-label": "When an order is placed, the system will automatically generate a tracking number.", "setting-shipping-flash-connected": "Connected", "setting-shipping-flash-connecting": "Connecting ...", "setting-shipping-flash-not_connected": "Not connected yet Please connect to transport.", "setting-shipping-flash-select-shipping-channel": "Select the shop for which the tracking number will be generated.", "setting-shipping-flash-select-shipping-channel-help": "When there is an order from the selected shop, the system will automatically generate a tracking number.", "setting-shipping-flash-select-shipping-help": "The system will create a tracking number and airway bill for the selected courier.", "setting-shipping-flash-select-shipping_service": "Select shipping service provider", "setting-shipping-flash-shipping-address": "Shipping address", "setting-shipping-flash-test-button": "Test", "setting-shipping-sender-address": "Sender address", "setting-shipping-sender-phone": "Shipping phone number", "setting-shipping-sender_name": "Sender name", "setting-shipping-title": "Transport connections", "setting-up-the-camera": "Set up the camera for recording video.", "setting.automatically-delete-uploaded-video": "Automatically delete uploaded videos", "setting.automatically-retry-upload-video": "Automatically upload not-uploaded videos", "setting.camera": "camera", "setting.connected-to-google-drive": "Connected to Google Drive", "setting.delete-only-video-on-this-device-not-on-google-drive": "Delete only videos on this device (will not delete from Google Drive).", "setting.download-for-testing": "Download to try it out", "setting.filetype": "File type", "setting.google-drive": "Google Drive Setting", "setting.play-sound-when-start-or-stop-recording": "Play a sound when starting/stopping video recording.", "setting.please-allow-access-to-camera": "Please allow access to the camera.", "setting.print-airway-bill-when-start-recording": "Print airway bill automatically when start video recording", "setting.print-receipt-when-start-recording": "When you start recording the video, print the Receipt/Tax Invoice automatically.", "setting.print-weight-slip-after-recording": "Print weight label after recording", "setting.record": "Record Setting", "setting.record_audio": "Record audio", "setting.resolution": "Resolution", "setting.retry-upload": "Retry Setting", "setting.speed": "Speed", "setting.storage": "Storage Setting", "setting.storage-period": "Storage period (days)", "setting.using-share-drive": "Using Google Drive Enterprise", "settings.admin-line-name": "Line Name/ID", "settings.admin-line-qrcode-url": "Line add friend URL", "settings.admin-phone-number": "Admin phone number", "settings.change-status": "Set up SMS sending when the order is changed to Ready To Ship", "settings.check-update": "Fix the check mark in the order center page, update, ready to send to market (only lazada)", "settings.company": "Company name", "settings.company-address": "Company address", "settings.confirm": "Confirm", "settings.confirm-record-password": "password", "settings.confirm-record-password-enable": "You must enter the \"Supervisor Password\" in order to record a video with notifications.", "settings.connection-printnode": "Test printnode connection", "settings.correct-data": "Please fill out the correct information", "settings.customer-name": "{customer} customer name-surname", "settings.delete-users": "Show deleted users", "settings.edit-password": "Edit password", "settings.etax_link": "{etax_link} e-Tax Link", "settings.facebook-messenger": "Send receipt link via Facebook Messenger when order status is changed to Ready To Ship", "settings.fill-0": "Enter 0 if you don't want a notification", "settings.first-name": "First name", "settings.fix-case": "Set up Fix Cases", "settings.fixcase-close-message": "{fixcase_remark} Close fixcase message", "settings.general-setting": "General settings", "settings.html-customer-name": "{{customer}} Customer name-surname", "settings.html-order-number": "{{order_number}} Order number", "settings.html-tracking_no-tracking": "{{tracking_no}} tracking number", "settings.last-name": "Last name", "settings.lazada-notification": "Ready to ship notification for <PERSON><PERSON><PERSON>", "settings.lon-remark-message": "Remark Message", "settings.manual": "Manual (coming soon)", "settings.name-sender": "SMS sender name", "settings.or-printnode": "Success | Updated printer list from PrintNode", "settings.order": "Automatically print packing slip (The system will automatically send a print order when a new order arrives)", "settings.order-number": "{order_number} Order number", "settings.order-status": "Change order status to Ready To Ship", "settings.order_number": "Order number", "settings.paper": "Paper type", "settings.permission": "Access permission", "settings.permissions": "Permissions", "settings.personal": "Personal information", "settings.pick-item-qr-code-enable": "Show the QR Code on the product image in the PickItem page.", "settings.ready-to-ship": "Messages sent to customers when all boxes are ready to ship in the order", "settings.receipt_link-shorten-url": "{receipt_link} Receipt link (automatically shorten the url)", "settings.record-piece-scan-mode-enable": "Show product list while recording video \n(Must have an order in Order Center to use)", "settings.record-setting": "Set up the video recording process", "settings.save": "Save", "settings.save-successful": "Save successfully", "settings.search": "Search", "settings.select-one": "Please select at least one position", "settings.send-link-status": "Send receipt link via SMS when order status is changed to Ready To Ship", "settings.send-lon-close-fix-case": "Send Line Official Notification (LON) to inform customers when closing a Fix Case.", "settings.send-lon-open-fix-case": "Send Line Official Notification (LON) to inform customers when opening a Fix Case.", "settings.send-ready-to-ship-lon-message": "Send Line Officail Notification (LON)", "settings.setting": "Setting", "settings.setting-API": "Set up PrintNode", "settings.setting-footer": "Set up at the end of the receipt (HTML)", "settings.setting-market": "Set up Market Place API", "settings.setting-market-v2": "ตั้งค่าการเชื่อมต่อ Marketplace V2", "settings.setting-packing-slip": "Set up printing packing slip (PrintNode API KEY must be set first)", "settings.setting-post-recording": "Set up Post Recording Action -- Events after video recording.", "settings.setting-pre-recording": "Set up to prevent video recording. Order has FixCase / Notes / Repeat video recording.", "settings.setting-print-airway-bill": "Set up Airway Bill printing", "settings.setting-printing": "Print settings", "settings.setting-receipt": "Receipt Settings", "settings.setting-sms": "setting SMS", "settings.setting-supervisor-password": "Supervisor Password", "settings.setting-top": "Set up receipt headers", "settings.settings-notification": "Set up notifications", "settings.share-google-drive": "Share Google Drive video link", "settings.shipping-setting": "Shipping Setting", "settings.shipping_channel": "{shipping_channel} Shipping channel", "settings.shopee-notification": "Ready to ship notification for <PERSON>ee", "settings.sms-1": "Set up SMS to alert the number of boxes (in case of more than 1 box per order)", "settings.sms-fix-case": "Send SMS to notify customers when opening Fix Case", "settings.sms-fixcase-customer": "SMS messages to customers when FixCase is open", "settings.sms-notification": "SMS message notification Ready To Ship", "settings.sms-ready-to-ship": "Send SMS when all boxes in the order are scanned ready to ship", "settings.sms-shop-name": "{shop_name} Shop name", "settings.sms-sms-notification": "Notify when SMS credit is less than", "settings.sms-support": "If you want to change the SMS sender name, please contact support.", "settings.stop-uncomplete-record-require-supervisor-password": "You must enter the \"Supervisor Password\" in order to stop recording videos that do not scan the required number of individual products.", "settings.store-company": "{company} Store/Company name It will use the same name as the receipt header", "settings.test-connection": "Test zort connection", "settings.total-packages": "{total_packages} Total number of boxes", "settings.tracking_no-tracking": "{tracking_no} Tracking number", "settings.user-password": "Username and Password", "settings.username": "Username", "settings.variable-explanation": "Variables", "settings.variables-sms": "The following variables can be used in SMS messages", "settings.vdo": "Alert that the VDO credit is less", "settings.weight-scale": "Set up weighing", "shipping-form": "Shipping information", "shipping-form-district": "district", "shipping-form-postcode": "postcode", "shipping-form-province": "province", "shipping-form-subdistrict": "subdistrict", "shipping-setting-flash-auto-tracking": "Automatically generate tracking number", "sms-api": "Send SMS via API", "sms-fix-case-closed": "Send SMS to notify customers when the Fix Case is closed", "sms-fixcase-sms-to-customer": "SMS messages to customers when FixCase is closed", "sms.add-credit": "You don't have enough credit please add credit", "sms.amount": "Amount of sms credit required", "sms.amount-credit": "Amount of credit", "sms.analyse-credit": "Click ANALYSE to estimate the amount of credit required to send (Evaluation may vary slightly)", "sms.campaign-name": "Campaign name", "sms.credit": "Credit", "sms.credit-remain": "Remaining SMS credit", "sms.download": "Download", "sms.enter-phone-number": "Enter the phone number and the message you want to send.", "sms.error-invalid-file": "Invalid file type: {0}", "sms.error-response-missing-fields": "Missing column \"{0}\" from Excel file", "sms.error-response-status": "There was an error[{0}] , please try again later", "sms.error-response-status-excel": "There is an error [{0}], please correct the Excel file", "sms.how-to": "How to use", "sms.messege": "Messege", "sms.messege-text": "Messege(text)", "sms.note": "Note (remark)", "sms.number": "Number", "sms.number-messege": "Number of messages sent", "sms.phone-number": "Phone number (to)", "sms.please-fix-error-in-excel-file": "Please correct any errors in the Excel file", "sms.please-fix-error-in-excel-file-5000": "Sample Excel file (up to 5000 rows per file)", "sms.please-fix-error-in-quick-form": "Please correct the error.", "sms.recipients": "Number of recipients", "sms.row": "Row", "sms.send-sms-click": "Click SEND to send SMS", "sms.send-sms-excel": "ส่ง SMS", "sms.sms-32mb": "Upload files for sending SMS (up to 32MB)", "sms.sms-report": "SMS report", "sms.status-sms": "We are in the process of sending SMS to you. You can check the SMS sending status at", "sms.top-up-credit": "Top up credit", "snackbar.save_success": "Success", "start-recording": "start recording", "start-recording-video": "Start recording video", "start-recording-your-first-video": "Start recording your first Video now.", "start-setting-dobybot": "Setting up <PERSON><PERSON><PERSON>", "start-using-system": "Let get start", "stop-recording": "Stop recording", "store_video_to_local_database": "Your store is configured to store video files in its own database.", "success": "Success", "suffix": "suffix", "suffix-explain": "Suffix that will be used to log in to the employee system, e.g.", "suffix-is-already-used": "This suffix is already taken.", "switch_to_desktop_for_video_recording": "To record video, please switch to the", "system-will-create-google-drive-folder": "A folder will be automatically created in Google Drive to store recorded video files.", "tel": "tel", "to": "To", "to-allow-camera-access": "to grant permission to access the camera", "total-google-drive-storage": "Total storage", "total-google-drive-usage": "Used", "updated_at": "updated at", "username": "Username", "users.add-user": "Add user", "users.confirm-to-delete-user": "Confirm User Deletion", "users.delete-user": "Delete user", "users.edit": "Edit user", "users.manage-users": "Manage users", "users.save": "Save", "users.share-google-drive": "Share Google Drive video link", "users.share-image-google-drive": "Share Google Drive image capture link", "using-1080p-required-lan-cable-connection": "Requires high speed internet and using LAN cable is recommended.", "using-1080p-will-take-longer-to-upload": "Uploading each file takes twice as long as 720p.", "using-1080p-will-use-more-storage": "Using 1080p will take up more space on Google Drive.", "video-loading": "Loading video", "video.mp4": "Convert webm files (seekable)", "video.mp4-2x": "Convert mp4 files (2x speed up)", "video.webm-seekable": "Convert webm files (seekable)", "view-record-history-here": "View recording history here.", "webm-load-faster-smaller-filesize": "Loads faster \nand has a smaller file size (compare to MP4 at the same resolution)", "webm-recommendation": "If using Record Only and not sending SMS, it is recommended to use Webm.", "webm-short-desc": "Webm is the modern standard for viewing video in browsers.", "webm-use-less-storage": "Uses less storage space", "weight": "weight", "weight-scale-unit": "Weighing unit", "wifi-n-station": "Can use WiFi (< {0} station)", "xlsx-only-max-32-MB": ".xlsx, .xls only, maximum file size 32 MB", "please-confirm-supervisor-password-before-manually-add-item-by-clicking": "Please enter the password to increase the amount of packaging manually.", "browser.warning": "Browser Warning", "browser.dismiss": "<PERSON><PERSON><PERSON>", "browser.more_info": "More Info", "browser.less_info": "Less Info", "browser.compatibility_info": "Your browser may not be fully compatible with this application.", "browser.minimum_versions": "Minimum supported browser versions", "browser.supported": "Your browser ({name} {version}) is supported.", "browser.not_supported": "Your browser ({name} {version}) may not be fully compatible with this application. For the best experience, please upgrade to {name} {minVersion} or higher.", "settings.add-item-barcode-require-supervisor-password": "\"Supervisor Password\" must be entered when adjusting manual pack quantity.", "export-etax-excel": "Download as Microsoft Excel (Detail)", "desktop-app": "Desktop App", "error.easy-order-created": "Check orders and verify order information", "add-product": "Add Product", "settings.record-voided-order-require-supervisor-password": "You must enter the \"Supervisor Password\" when recording a video from an order with a Voided status."}