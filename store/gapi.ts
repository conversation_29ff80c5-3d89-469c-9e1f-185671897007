/// <reference path="../types/index.d.ts" />

import { ActionContext } from 'vuex'
import jwt_decode from 'jwt-decode'
import moment from 'moment'
import { $axios } from '~/utils/api'
import { waitUntil } from '~/utils/timer'

export const state = () => {
  return {
    isAuth: false,
    name: '',
    email: '',
    given_name: '',
    family_name: '',
    picture: '',
    timer_id: undefined
  }
}

interface gapiState {
  isAuth: boolean
  name: string
  email: string
  given_name: string
  family_name: string
  picture: string
  timer_id?: number
}

export const getters = {
  token (state: gapiState): string {
    if (state.isAuth) {
      return window.gapi.client.getToken().access_token
    }

    return ''
  },
  currentUser (state: gapiState): any {
    if (state.isAuth) {
      return {
        name: state.name,
        email: state.email,
        getId: () => '',
        getName: () => state.name,
        getGivenName: () => state.given_name,
        getFamilyName: () => state.family_name,
        getImageUrl: () => state.picture,
        getEmail: () => state.email
      }
    }
    return null
  }
}

export const mutations = {
  set (state: gapiState, data: gapiState) {
    for (const k in data) {
      const key = k as keyof gapiState
      state[key] = data[key] as never
    }
  }
}

type Context = ActionContext<gapiState, gapiState>
export const actions = {
  signIn (context: Context) {
    if (window.location.pathname.includes('/image-capture')) {
      window.location.href = '/record/'
      localStorage.setItem('redirect_to', '/image-capture')
      return
    }
    if (window.location.pathname.includes('/$/record')) {
      localStorage.setItem('open-setting', '1')
      localStorage.setItem('open-google-signin', '1')
      window.location.href = '/record/'
      return
    }

    const oauth2CodeClient = window.google.accounts.oauth2.initCodeClient({
      client_id: '*************-2v0obh7hh7ej1kir4g4e7e8bgpqp7043.apps.googleusercontent.com',
      scope: 'https://www.googleapis.com/auth/drive.file email profile',
      redirect_uri: `${window.location.origin}`,
      ux_mode: 'popup',
      callback: async (res: any) => {
        if (res.error_description) {
          alert(
            'Fail to authorize google service: ' + res.error_description + '\n' +
            'Operation failed with error GA0x0001'
          )
        }

        try {
          const data = await $axios.$post('/api/users/google/oauth2/auth-code/', {
            code: res.code,
            redirect_uri: `${window.location.origin}`
          })

          context.dispatch('setAuthState', data)
          const redirect_to = localStorage.getItem('redirect_to')
          if (redirect_to) {
            localStorage.removeItem('redirect_to')
            window.location.href = redirect_to
          } else {
            localStorage.setItem('open-setting', '1')
            window.location.href = '/$/record/'
          }
        } catch (error) {
          alert(
            'Fail to authorize google service: ' + error + '\n' +
              'Operation failed with error GA0x0002'
          )
        }
      }
    })
    oauth2CodeClient.requestCode()
  },

  signOut (context: Context) {
    const cred = window.gapi.client.getToken()
    if (cred !== null) {
      window.gapi.client.setToken({ access_token: '' })
    }

    context.commit('set', { isAuth: false, email: '', name: '', given_name: '', picture: '' })
    localStorage.removeItem('auth.google.id_token')
    localStorage.removeItem('auth.google.access_token')
    localStorage.removeItem('auth.google.access_token_exp')
    $axios.post('/api/users/google/oauth2/logout/')
    clearInterval(context.state.timer_id)
  },

  async autoSignIn (context: Context) {
    await waitUntil('gapi.client loaded', () => window.gapi && window.gapi.client !== undefined)

    console.log('[gapi] autoSignIn')
    const id_token = localStorage.getItem('auth.google.id_token')
    const access_token = localStorage.getItem('auth.google.access_token')
    const exp = localStorage.getItem('auth.google.access_token_exp')

    if (!id_token || !access_token || !exp) {
      console.log('[gapi] autoSignIn - not signed in')
      return
    }

    try {
      await context.dispatch('refresh')
    } catch (error) {
      return context.dispatch('signOut')
    }
  },

  async refresh (context: Context) {
    console.log('[gapi] refresh token...')
    try {
      const data = await $axios.$post('/api/users/google/oauth2/refresh/')
      context.dispatch('setAuthState', data)
      console.log('[gapi] refresh token... OK')
    } catch (error) {
      console.log(
        '[gapi] Fail to authorize google service: ' + error + '\n' +
              'Operation failed with error GA0x0002'
      )
    }
  },
  async cypressSignIn (context:Context) {
    // const refreshToken = 'TODO: use env variable here'
    const refreshToken = '1//0geuAGgaDVt4ZCgYIARAAGBASNwF-L9Ir4M1EH4NrGcuM7fj2yrdXeUcpXF_ChXoDQFb07pqeX7gFRWS-3BkAYS4uSWZrbirlcI0'

    try {
      const data = await $axios.$post('/api/users/cypress-google/oauth2/refresh/', {
        refresh_token: refreshToken
      })
      context.dispatch('setAuthState', data)
    } catch (error) {
      console.log(
        'Fail to authorize google service: ' + error + '\n' +
              'Operation failed with error GA0x0002'
      )
    }
  },

  setAuthState (context: Context, data: any) {
    window.gapi.client.setToken({ access_token: data.access_token })

    const decodedToken = jwt_decode(data.id_token) as any

    context.commit('set', {
      email: decodedToken.email,
      name: decodedToken.name,
      given_name: decodedToken.given_name,
      family_name: decodedToken.family_name,
      picture: decodedToken.picture,
      isAuth: true
    })

    if (data.localStorage !== false) {
      localStorage.setItem('auth.google.id_token', data.id_token)
      localStorage.setItem('auth.google.access_token', data.access_token)
      localStorage.setItem('auth.google.access_token_exp', data.exp)
      localStorage.setItem('use_new_google_oauth', '1')
    }

    clearTimeout(context.state.timer_id)
    const expire_in = moment(data.exp * 1000).diff(moment(), 'ms')
    const timer_id = setTimeout(() => { context.dispatch('refresh') }, expire_in - (60_000))
    context.commit('set', { timer_id })
  }
}
