import { ActionContext } from 'vuex'
import { $axios } from '~/utils/api'

export interface UserState {
}

export const state = (): UserState => ({})

export const mutations = {
  set (state: UserState, data: UserState) {
    for (const k in data) {
      const key = k as keyof UserState
      state[key] = data[key] as never
    }
  }
}

type Context = ActionContext<UserState, UserState>
export const actions = {
  async get (_: Context, params: { id: number }) {
    const res = await $axios.get(`/api/users/resource/users/${params.id}/`)
    return res.data.user
  },

  async list () {
    const res = await $axios.get('/api/users/resource/users/')
    return res.data.users
  },

  async create (_: Context, data: any) {
    const res = await $axios.post('/api/users/resource/users/', data)
    return res.data
  },

  async update (_: Context, data: any) {
    const { id } = data

    if (!id) {
      console.error('Could not perform action `user/update` without `id`')
      return
    }

    const res = await $axios.put(`/api/users/resource/users/${id}/`, data)
    return res.data
  },

  async delete (_: Context, userId: number) {
    await $axios.delete(`/api/users/resource/users/${userId}/`)
  }
}
