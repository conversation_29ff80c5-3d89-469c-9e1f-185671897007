import { ActionContext } from 'vuex'
import { $axios } from '~/utils/api'

export interface PrintNodeState {
}

export const state = (): PrintNodeState => ({})

export const mutations = {
  set (state: PrintNodeState, data: PrintNodeState) {
    for (const k in data) {
      const key = k as keyof PrintNodeState
      state[key] = data[key] as never
    }
  }
}

type Context = ActionContext<PrintNodeState, PrintNodeState>
export const actions = {
  async getPrinters () {
    const res = await $axios.get('/api/picking/printers/')
    return res.data
  },

  /**
   * return status 204 if nothing wrong, 404 if order_number not founded
   */
  printAirwayBill (_: Context, params: { order_number: string, printer_id: string }) {
    return $axios.post(
      `/api/picking/airway-bills/${params.order_number}/print/`,
      { printer: params.printer_id }
    )
  }
}
