interface WalletState {
  version: number | null
  record_balance: number | null
  google_drive_usage: number | null
  google_drive_limit: number | null
}

const state = () => ({
  version: null,
  record_balance: null,
  google_drive_limit: null,
  google_drive_usage: null
})

const mutations = {
  set (state: WalletState, data: any) {
    for (const k in data) {
      const key = k
      state[key] = data[key]
    }
  }
}

export {
  state,
  mutations
}
