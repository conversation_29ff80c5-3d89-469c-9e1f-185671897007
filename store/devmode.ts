export interface DevModeState {
  isEnabled: boolean
  adminName: string | null
}

export const state = (): DevModeState => ({
  isEnabled: false,
  adminName: null
})

export const getters = {
  isDevModeEnabled: (state: DevModeState): boolean => state.isEnabled,
  adminName: (state: DevModeState): string | null => state.adminName
}

export const mutations = {
  setDevMode (state: DevModeState, { isEnabled, adminName }: { isEnabled: boolean, adminName: string | null }) {
    state.isEnabled = isEnabled
    state.adminName = adminName
  },

  disableDevMode (state: DevModeState) {
    state.isEnabled = false
    state.adminName = null
  }
}

export const actions = {
  activateDevMode (context: any, { password }: { password: string }) {
    // Password to admin name mapping
    const adminCredentials: { [key: string]: string } = {
      'HID@#&937': 'AdminHide@test'
    }

    const adminName = adminCredentials[password]

    if (adminName) {
      context.commit('setDevMode', { isEnabled: true, adminName })
      return { success: true, adminName }
    } else {
      return { success: false, adminName: null }
    }
  },

  deactivateDevMode (context: any) {
    context.commit('disableDevMode')
  }
}
