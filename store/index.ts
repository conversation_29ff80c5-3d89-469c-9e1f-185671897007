import { ActionContext } from 'vuex'
import { $axios } from '~/utils/api'
// import router from '~/router'

export interface StoreState {
  frontend: {
    dobybot_ui_version: string,
  }
  backend: {
    dobybot_version: string
  }
  session_id: string
  get_start_status: boolean
  is_intro_running: boolean
}

export const state = (): StoreState => ({
  frontend: {
    dobybot_ui_version: '25.5'
  },
  backend: {
    dobybot_version: ''
  },
  session_id: '',
  get_start_status: false,
  is_intro_running: false
})

export const getters = {
  isAuthenticated (state: any): boolean {
    return state.auth.loggedIn
  },

  loggedInUser (state: any) {
    return state.auth.user
  },

  getStartStatus (state: StoreState) {
    return state.get_start_status
  }
}

export const mutations = {
  set (s: StoreState, data: StoreState) {
    for (const k in data) {
      const key = k as keyof any
      s[key] = data[key] as any
    }
  }
}

type Context = ActionContext<StoreState, StoreState>
export const actions = {
  async getVersions (context: Context) {
    const res = await $axios.get('/versions/')
    context.commit('set', { backend: res.data })
  },
  openGetStart (context: Context) {
    localStorage.removeItem('get-start-setting')
    context.commit('set', { get_start_status: true })
  },
  closeGetStart (context: Context) {
    localStorage.setItem('get-start-setting', 'true')
    context.commit('set', { get_start_status: false })
  }
}
