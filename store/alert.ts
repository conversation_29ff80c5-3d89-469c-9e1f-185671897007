import { ActionContext } from 'vuex'
import { LoggedInUser } from '~/models'
import { performLogout } from '~/utils/logout'

export interface AlertButton {
  text: string
  color: string
  onclick: Function
  align?: 'left'|'right'|'center'
}

export interface FormInput {
  tag: 'v-text-field'|'v-otp-input'
  attrs: any,
  value: string,
  onchange(value: string): void
}

export interface AlertState {
  title: string
  text: string
  theme: string
  visible: boolean
  inputs: FormInput[]
  buttons: AlertButton[]
  width: number
}

export interface AlertOptions {
  text: string
  title?: string
  theme?: string
  buttons?: AlertButton[]
}

export interface ConfirmOptions {
  text: string
  title?: string
  theme?: string
}

export interface PromptOptions {
  title?: string
  text: string
  preset: 'text'|'password'|'password-text'
}

export const state = (): AlertState => ({
  title: '',
  text: '',
  theme: 'primary',
  visible: false,
  width: 600,
  buttons: [],
  inputs: []
})

export const mutations = {
  set (state: AlertState, data: AlertState) {
    for (const k in data) {
      const key = k as keyof AlertState
      state[key] = data[key] as never
    }
  }
}

type Context = ActionContext<AlertState, any>
export const actions = {
  show (context: Context, options: AlertOptions) {
    const default_buttons: AlertButton[] = [
      { text: 'ok ', color: 'primary', onclick: () => { context.dispatch('hide') }, align: 'center' }
    ]
    const text = options.text
    const title = options.title || ''
    const theme = options.theme || 'primary'
    const buttons = options.buttons || default_buttons
    context.commit('set', { title, text, theme, buttons, visible: true })
  },

  hide (context: Context) {
    context.commit('set', {
      title: '',
      text: '',
      theme: 'primary',
      visible: false,
      width: 600,
      buttons: [],
      inputs: []
    })
  },

  showInActiveCompanyAlert (context: Context) {
    if (!window.$nuxt.$auth.loggedIn) {
      return
    }

    const user: LoggedInUser = window.$nuxt.$auth.user as never
    const company = user.company
    const default_message = 'กรุณาติดต่อ Dobybot Support'

    const opt = {
      title: 'บัญชีของท่านถูกระงับการใช้งาน',
      text: company.is_active_remark || default_message,
      theme: 'error',
      buttons: [
        {
          text: 'ออกจากระบบ',
          color: 'primary',
          onclick: () => {
            // NOT SURE YET
            // window.$nuxt.$auth.logout()
            performLogout(context)
            context.dispatch('hide')
          }
        }
      ]
    }

    context.dispatch('show', opt)
  },

  prompt (context: Context, opt: PromptOptions) {
    return new Promise((resolve) => {
      let value = ''

      // Form
      const inputs = [] as FormInput[]
      if (opt.preset === 'password') {
        inputs.push({
          tag: 'v-otp-input',
          attrs: { type: 'password', length: context.rootState.settings.company.SUPERVISOR_PASSWORD.length },
          value,
          onchange: (v: string) => { value = v }
        })
      }
      if (opt.preset === 'password-text') {
        inputs.push({
          tag: 'v-text-field',
          attrs: { type: 'password' },
          value,
          onchange: (v: string) => { value = v }
        })
      }
      if (opt.preset === 'text') {
        inputs.push({
          tag: 'v-text-field',
          attrs: { autofocus: true },
          value,
          onchange: (v: string) => { value = v }
        })
      }

      // Buttons
      const buttons: AlertButton[] = [
        {
          text: 'CANCEL',
          color: '',
          align: 'right',
          onclick: () => {
            context.dispatch('hide')
            resolve(null)
          }
        },
        {
          text: 'OK',
          color: 'primary',
          align: 'right',
          onclick: () => {
            context.dispatch('hide')
            resolve(value)
          }
        }

      ]

      context.commit('set', {
        visible: true,
        theme: 'error',
        title: opt.title,
        text: opt.text,
        inputs,
        buttons
      })
    })
  },

  confirm (context: Context, opt: ConfirmOptions): Promise<boolean> {
    const i18n = window.$nuxt.$options.i18n
    return new Promise((resolve) => {
      // Buttons
      const buttons: AlertButton[] = [
        {
          text: i18n.t('cancel'),
          color: '',
          align: 'right',
          onclick: () => {
            context.dispatch('hide')
            resolve(false)
          }
        },
        {
          text: i18n.t('OK'),
          color: 'primary',
          align: 'right',
          onclick: () => {
            context.dispatch('hide')
            resolve(true)
          }
        }
      ]

      context.commit('set', {
        visible: true,
        text: opt.text,
        title: opt.title ?? 'แจ้งเตือน',
        theme: 'warning',
        buttons
      })
    })
  },

  error (context: Context, error: any) {
    const { title, text, ...err } = error
    const opt = {
      title: title || 'เกิดข้อผิดพลาด',
      text: text || err,
      theme: 'error'
    }
    context.dispatch('show', opt)
  }
}
