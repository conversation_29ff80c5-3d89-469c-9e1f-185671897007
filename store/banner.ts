import { ActionContext } from 'vuex'

export interface BannerButton {
  text: string
  color: string
  onclick: Function
  align?: 'left'|'right'|'center'
}

export interface BannerState {
  text: string
  theme: string
  visible: boolean
  buttons: BannerButton[]
}

export interface BannerOptions {
  text: string
  theme?: string
  buttons?: BannerButton[]
}

export const state = (): BannerState => ({
  text: '',
  theme: 'info',
  visible: false,
  buttons: []
})

export const mutations = {
  set (state: BannerState, data: BannerState) {
    for (const k in data) {
      const key = k as keyof BannerState
      state[key] = data[key] as never
    }
  }
}

type Context = ActionContext<BannerState, BannerState>
export const actions = {
  show (context: Context, options: BannerOptions) {
    const default_buttons: BannerButton[] = [
      { text: 'ok ', color: 'success', onclick: () => { context.dispatch('hide') }, align: 'center' }
    ]

    const text = options.text
    const theme = options.theme || 'info'
    const buttons = options.buttons || default_buttons
    context.commit('set', { text, theme, buttons, visible: true })
  },

  hide (context: Context) {
    context.commit('set', { text: '', visible: false })
  }
}
