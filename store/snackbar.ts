import { ActionContext } from 'vuex'

export interface ISnackbarState {
  show: boolean,
  text: string,
  color: string
}

export function state (): ISnackbarState {
  return {
    show: false,
    text: '',
    color: 'success'
  }
}

export const mutations = {
  set (s: ISnackbarState, data: ISnackbarState) {
    for (const k in data) {
      const key = k as keyof ISnackbarState
      s[key] = data[key] as never
    }
  }
}

type Context = ActionContext<ISnackbarState, ISnackbarState>

export const actions = {
  show (context: Context, { color, text }: {color:string, text:string}) {
    context.commit('set', { color, text, show: true })
  },

  hide (context: Context) {
    context.commit('set', { show: false })
  }
}
