import { ActionContext } from 'vuex'

export interface LoaderState {
  text: string,
  visible: boolean
}

export const state = (): LoaderState => ({
  text: '',
  visible: false
})

export const mutations = {
  set (state: LoaderState, data: LoaderState) {
    for (const k in data) {
      const key = k as keyof LoaderState
      state[key] = data[key] as never
    }
  }
}

type Context = ActionContext<LoaderState, LoaderState>
export const actions = {
  show (context: Context, text: string) {
    if (!text) { text = 'Loading ...' }
    context.commit('set', { text, visible: true })
  },

  hide (context: Context) {
    context.commit('set', { text: '', visible: false })
  }
}
