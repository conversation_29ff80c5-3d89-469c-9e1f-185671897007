import { ActionContext, ActionTree, MutationTree } from 'vuex'
import { GoogleDriveFolder } from '~/api/googledrive'
import { RecordTag } from '~/models'
interface ShippingSender {
  province: string
  district: string
  subdistrict: string
  province_en: string
  district_en: string
  subdistrict_en: string
  postcode: string
}

export interface LocalSetting {
  deviceId: string | null
  resolution: string
  googleDriveRootFolder: GoogleDriveFolder | null
  googleDriveTodayFolder: GoogleDriveFolder | null
  printAirwayBillBeforeRecord: boolean
  printWeightSlipAfterRecord: boolean
  printReceiptBeforeRecord: boolean
  airwayBillPrinterId: string
  weightSlipPrinterId: string
  receiptPrinterId: string
  enableSound: boolean
  filetype: 'mp4'|'webm'|'webm-raw'
  speed: '1x'|'2x'
  automaticallyRemoveOldVideo: boolean
  automaticallyRetryUploadVideo: boolean
  storageDuration: number
  recordAudio: boolean
  weightScaleComputerId: number | null
  enableOverlay: boolean
  overlayShowDatetime: boolean
  overlayShowWeight: boolean
  overlayShowScannedSKU: boolean
  googleDriveImageRootFolder: GoogleDriveFolder | null
  googleDriveImageTodayFolder: GoogleDriveFolder | null
  enableCheerupSound: boolean
  selectedCheerupSounds: string[]
  pickItemImageMode: boolean
  pickItemShowQRCode: boolean
}

export interface CompanySetting {
  AUTO_SYNC_ORDER_ENABLE: boolean
  ZORT_STORENAME: string
  ZORT_API_KEY: string
  ZORT_API_SECRET: string

  RECORD_SHOW_START_BUTTON: boolean
  RECORD_GMAIL_ALLOWED_LIST: string[]
  RECORD_PIECE_SCAN_MODE_ENABLE: boolean
  RECORD_CHECK_BUTTON_DISABLE: boolean
  CONTROL_CODE_FORCE_UPPERCASE_LETTERS: boolean

  PRINTNODE_API_KEY: string
  PRINTNODE_PICKORDER_PRINTER_ID: string
  PRINTNODE_PICKORDER_PRINTER_TYPE: string
  PICKORDER_AUTO_PRINT_ENABLE: boolean
  PICKORDER_STORE_BLOCKED_LIST: string[]
  PICK_ORDER_FOOTER_HTML: string

  COMPANY_NAME: string
  COMPANY_ADDRESS: string
  COMPANY_ADMIN_TEL: string
  RECEIPT_FOOTER: string
  HIDE_RECEIPT: boolean

  CONFIRM_RECORD_PASSWORD_ENABLE: boolean
  SUPERVISOR_PASSWORD: string
  STOP_RECORD_REQUIRE_COMPLETED_SCAN_LOG: boolean
  ADD_ITEM_BARCODE_REQUIRE_SUPERVISOR_PASSWORD: boolean
  RECORD_VOIDED_ORDER_REQUIRE_SUPERVISOR_PASSWORD: boolean

  POST_RECORD_ACTION_SHARE_VIDEO_LINK: boolean
  POST_RECORD_ACTION_READY_TO_SHIP: boolean
  POST_RECORD_ACTION_SEND_SMS: boolean
  POST_RECORD_ACTION_SEND_LON: boolean
  POST_RECORD_ACTION_CREATE_ORDER: boolean
  POST_RECORD_ACTION_CREATE_ORDER_PATTERN: string
  POST_RECORD_ACTION_WEBHOOK_ENABLE: boolean
  POST_RECORD_WEBHOOK_CONFIG: object | null
  JAMBOLIVE_WEBHOOK_CONFIG: object | null
  CF_SHOPS_WEBHOOK_CONFIG: object | null
  POST_RECORD_ACTION_SEND_WEBHOOK_BEFORE_VIDEO_UPLOAD: boolean

  FIXCASE_SEND_OPEN_MESSAGE: boolean
  FIXCASE_SEND_CLOSE_MESSAGE: boolean
  FIXCASE_LON_SEND_OPEN_MESSAGE: boolean
  FIXCASE_LON_SEND_CLOSE_MESSAGE: boolean

  READY_TO_SHIP_ORDER_NUMBER_REGEX: string

  // WEBHOOK_URL: string
  // WEBHOOK_SECRET: string
  VRICH_HOST: string
  VRICH_READY_TO_SHIP_SEND_FACEBOOK_MESSAGE: boolean

  SMS_SENDER: string
  SMS_READY_TO_SHIP_MESSAGE: string
  LON_READY_TO_SHIP_REMARK_MESSAGE: string
  LON_MORE_DETAIL_LINK: string
  SMS_FIXCASE_OPEN_MESSAGE: string
  SMS_FIXCASE_CLOSE_MESSGE: string
  LON_FIXCASE_OPEN_MESSAGE: string
  LON_FIXCASE_CLOSE_MESSAGE: string
  SALES_CHANNEL_SMS_BLOCKED_LIST: string[]

  LINE_NOTIFY_TOKEN: string

  SMS_MULTI_PACKAGE_MESSAGE: string
  MULTI_PACKAGE_READY_TO_SHIP_SEND_SMS: boolean
  GOOGLE_DRIVE_SHARE_DRIVE_ID: string

  LOW_RECORD_CREDIT_WARNING_AMOUNT: number
  LOW_SMS_CREDIT_WARNING_AMOUNT: number

  USE_MQTT: boolean

  DOBYBOT_AIRWAYBILL_SHOP_LIST: string[]
  DOBYBOT_AIRWAYBILL_SENDER_NAME: string
  DOBYBOT_AIRWAYBILL_SENDER_PHONE: string
  DOBYBOT_AIRWAYBILL_SENDER_ADDRESS: string
  DOBYBOT_AIRWAYBILL_FOOTER: string

  SHOPEE_API: object
  SHOPEE_CHAT_API: object
  SHOPEE_READY_TO_SHIP_MESSAGE: string

  LAZADA_API: object
  LAZADA_CHAT_API: object
  LAZADA_READY_TO_SHIP_MESSAGE: string

  TIKTOK_SHOP_API: object
  TIKTOK_SHOP_V2_API: object
  WOO_COMMERCE_API: object

  ZORT_API_V2: object
  ZORT_API_V2_WEBHOOKS: object
  ZORT_API_V2_READY_TO_SHIP_HOOK: string
  ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG: object
  ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK: string
  ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG: object

  CONFIRM_RECORD_PASSWORD_CONFIG: string[]
  WEIGHT_SCALE_PREFER_UNIT: 'g' | 'kg' | 'auto'
  WEIGHT_ONLY_MODE_ENABLE: boolean
  WEIGHT_ONLY_MODE_PRINT_METHOD: 'printnode' | 'webprint'

  HOMEPAGE_URL: string
  ENABLE_NO_RECORD_MODE: boolean
  DISABLED_PRE_RECORD_CHECK: boolean
  ALLOW_NEGATIVE_CREDIT: boolean

  // ALLOW_THAI_CHARACTER_IN_BARCODE: boolean

  SMS_WEBHOOK_ENABLE: boolean
  SMS_WEBHOOK_CONFIG: object | null
  ETAX_WEBHOOK_ENABLE: boolean
  ETAX_WEBHOOK_CONFIG: object | null
  ETAX_REQUEST_WEBHOOK_ENABLE: boolean
  ETAX_REQUEST_WEBHOOK_CONFIG: object | null
  RECORD_FAILED_TRANSCODE_SHOW_ERROR: boolean
  PICKORDER_PRINT_START_DATE: string
  CONFIRM_IMAGE_CAPTURE_PASSWORD_ENABLE: boolean
  PRINT_RECEIPT_TAX_INVOICE_2_COPY: boolean
  POST_IMAGE_CAPTURE_SHARE_IMAGE_LINK: boolean

  AUTO_GENERATE_TRACKINGNO_ENABLE: boolean
  AUTO_GENERATE_TRACKINGNO_PROVIDER: 'DBB_FLASH' | 'DBB_KERRY' | 'DBB_THAIPOST_EMS' | 'DBB_THAIPOST_REG' | ''
  AUTO_GENERATE_TRACKINGNO_SHOPS: string[]
  DOBYBOT_CONNECT_VERSION: number
  NOCNOC_API: object

  SHIPPING_SENDER: ShippingSender | null
  // LOG_ROCKET_ENABLE: boolean
  POSTHOG_ENABLED: true
  RECORD_TAGS: RecordTag[] | null

  // ETax
  ETAX_SELLER:ETaxSeller
  ETAX_DOCUMENT_FOLDER_ID:string
  ETAX_ENABLE_PICKSLIP_QR_CODE:boolean

  ETAX_ALLOW_ON_ORDER_RECEIVED: boolean
  ETAX_CANCEL_ON_ORDER_CANCELLED_OR_RETURNED: boolean
  ETAX_SMS_MESSAGE:string
  ETAX_SEND_LINK_TRIGGER:string
  ETAX_ALLOW_SEND_TO_PLATFORM_CHAT:boolean
  ETAX_CUSTOM_MESSAGE_CHAT:string
  ETAX_DOC_CREATE_DATE:string
  ETAX_HEADER_MESSAGE_BILL:string
  ETAX_FOOTER_MESSAGE_BILL:string
  ETAX_BRANCHES: EtaxBranch[]
  ETAX_AUTO_CREATE_WHEN_PLATFORM_REQUEST: boolean
  ETAX_AUTO_START_DATE: string
  ETAX_AUTO_CANCEL_MODE: string
  ETAX_AUTO_CANCEL_MODE_AFTER_CUTOFF: string
  ETAX_AUTO_ZORT_TAG: boolean
  ETAX_CUSTOMER_EDIT_MESSAGE: string
  ETAX_WELCOME_MESSAGE: string
  ETAX_ALERT_OVERDUE_MESSAGE: string

  // Desktop App
  WINDOW_NETWORK_FOLDER_PATH: string

  CUSTOM_BRAND: string

  // Serial No.
  RECORD_SCAN_SERIAL_NO_MODE: 'NONE' | 'SERIAL_NO_ONLY' | 'SERIAL_NO_AND_SKU'
  ETAX_TEMPLATE_NAME: object
}
export interface SellerInfo{
  name: string;
  address: string;
  post_code: string;
  phone_number: string;
  tax_id: string;
  seller_branch: string;
  seller_branch_name: string;
}

export interface DoaInfo{
  api_host: string;
  api_token: string;
  branch_id: string;
  type_tax: string;
  tenant_code: string;
  tenant_id: string;
}

export interface SettingState {
  local: LocalSetting,
  company: CompanySetting
}
export interface ETaxSeller extends SellerInfo, DoaInfo {

}

export interface EtaxBranch {
  number: string;
  name: string;
  address: string
}

export function state (): SettingState {
  return {
    local: {
      deviceId: null,
      resolution: '1280x720',
      googleDriveRootFolder: null,
      googleDriveTodayFolder: null,
      printAirwayBillBeforeRecord: false,
      printWeightSlipAfterRecord: false,
      printReceiptBeforeRecord: false,
      airwayBillPrinterId: '',
      weightSlipPrinterId: '',
      receiptPrinterId: '',
      enableSound: true,
      filetype: 'webm',
      speed: '1x',
      automaticallyRemoveOldVideo: true,
      storageDuration: 30,
      automaticallyRetryUploadVideo: false,
      recordAudio: false,
      weightScaleComputerId: null,
      enableOverlay: false,
      overlayShowDatetime: false,
      overlayShowWeight: false,
      overlayShowScannedSKU: false,
      googleDriveImageRootFolder: null,
      googleDriveImageTodayFolder: null,
      enableCheerupSound: false,
      selectedCheerupSounds: [],
      pickItemImageMode: false,
      pickItemShowQRCode: true
    },

    company: {
      AUTO_SYNC_ORDER_ENABLE: false,
      ZORT_STORENAME: '',
      ZORT_API_KEY: '',
      ZORT_API_SECRET: '',

      RECORD_SHOW_START_BUTTON: true,
      RECORD_GMAIL_ALLOWED_LIST: [],
      RECORD_PIECE_SCAN_MODE_ENABLE: false,
      RECORD_CHECK_BUTTON_DISABLE: false,
      CONTROL_CODE_FORCE_UPPERCASE_LETTERS: false,
      POST_RECORD_ACTION_WEBHOOK_ENABLE: false,
      POST_RECORD_WEBHOOK_CONFIG: null,
      JAMBOLIVE_WEBHOOK_CONFIG: null,
      CF_SHOPS_WEBHOOK_CONFIG: null,

      PRINTNODE_API_KEY: '',
      PRINTNODE_PICKORDER_PRINTER_ID: '',
      PRINTNODE_PICKORDER_PRINTER_TYPE: '',
      PICKORDER_AUTO_PRINT_ENABLE: false,
      PICKORDER_STORE_BLOCKED_LIST: [],
      PICK_ORDER_FOOTER_HTML: '',

      COMPANY_NAME: '',
      COMPANY_ADDRESS: '',
      COMPANY_ADMIN_TEL: '',
      RECEIPT_FOOTER: '',
      HIDE_RECEIPT: false,

      CONFIRM_RECORD_PASSWORD_ENABLE: false,
      SUPERVISOR_PASSWORD: '',
      STOP_RECORD_REQUIRE_COMPLETED_SCAN_LOG: false,
      ADD_ITEM_BARCODE_REQUIRE_SUPERVISOR_PASSWORD: false,
      RECORD_VOIDED_ORDER_REQUIRE_SUPERVISOR_PASSWORD: false,

      POST_RECORD_ACTION_SHARE_VIDEO_LINK: false,
      POST_RECORD_ACTION_READY_TO_SHIP: false,
      POST_RECORD_ACTION_SEND_SMS: false,
      POST_RECORD_ACTION_SEND_LON: false,
      POST_RECORD_ACTION_CREATE_ORDER: false,
      POST_RECORD_ACTION_CREATE_ORDER_PATTERN: '',
      POST_RECORD_ACTION_SEND_WEBHOOK_BEFORE_VIDEO_UPLOAD: false,

      FIXCASE_SEND_OPEN_MESSAGE: true,
      FIXCASE_SEND_CLOSE_MESSAGE: true,
      FIXCASE_LON_SEND_OPEN_MESSAGE: false,
      FIXCASE_LON_SEND_CLOSE_MESSAGE: false,

      READY_TO_SHIP_ORDER_NUMBER_REGEX: '',

      VRICH_HOST: '',
      VRICH_READY_TO_SHIP_SEND_FACEBOOK_MESSAGE: false,

      SMS_SENDER: '',
      SMS_READY_TO_SHIP_MESSAGE: '',
      LON_READY_TO_SHIP_REMARK_MESSAGE: '',
      LON_MORE_DETAIL_LINK: '',
      SMS_FIXCASE_OPEN_MESSAGE: '',
      SMS_FIXCASE_CLOSE_MESSGE: '',
      LON_FIXCASE_OPEN_MESSAGE: '',
      LON_FIXCASE_CLOSE_MESSAGE: '',
      SALES_CHANNEL_SMS_BLOCKED_LIST: [],

      LINE_NOTIFY_TOKEN: '',

      MULTI_PACKAGE_READY_TO_SHIP_SEND_SMS: false,
      SMS_MULTI_PACKAGE_MESSAGE: '',

      GOOGLE_DRIVE_SHARE_DRIVE_ID: '',
      LOW_RECORD_CREDIT_WARNING_AMOUNT: 0,
      LOW_SMS_CREDIT_WARNING_AMOUNT: 0,
      USE_MQTT: false,

      DOBYBOT_AIRWAYBILL_SHOP_LIST: [],
      DOBYBOT_AIRWAYBILL_SENDER_NAME: '',
      DOBYBOT_AIRWAYBILL_SENDER_PHONE: '',
      DOBYBOT_AIRWAYBILL_SENDER_ADDRESS: '',
      DOBYBOT_AIRWAYBILL_FOOTER: '',

      SHOPEE_API: {},
      SHOPEE_CHAT_API: {},
      SHOPEE_READY_TO_SHIP_MESSAGE: '',

      LAZADA_API: {},
      LAZADA_CHAT_API: {},
      LAZADA_READY_TO_SHIP_MESSAGE: '',

      TIKTOK_SHOP_API: {},
      TIKTOK_SHOP_V2_API: {},
      WOO_COMMERCE_API: {},

      ZORT_API_V2: {},
      ZORT_API_V2_WEBHOOKS: {},
      ZORT_API_V2_READY_TO_SHIP_HOOK: '',
      ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG: {},
      ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK: '',
      ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG: {},

      CONFIRM_RECORD_PASSWORD_CONFIG: ['FIXCASE', 'DUPLICATE_RECORD', 'REMARK'],
      WEIGHT_SCALE_PREFER_UNIT: 'g',
      WEIGHT_ONLY_MODE_ENABLE: false,
      WEIGHT_ONLY_MODE_PRINT_METHOD: 'webprint',

      HOMEPAGE_URL: '',
      ENABLE_NO_RECORD_MODE: false,
      DISABLED_PRE_RECORD_CHECK: false,
      ALLOW_NEGATIVE_CREDIT: true,
      // ALLOW_THAI_CHARACTER_IN_BARCODE: false

      SMS_WEBHOOK_ENABLE: false,
      SMS_WEBHOOK_CONFIG: null,

      ETAX_WEBHOOK_ENABLE: false,
      ETAX_WEBHOOK_CONFIG: null,

      ETAX_REQUEST_WEBHOOK_ENABLE: false,
      ETAX_REQUEST_WEBHOOK_CONFIG: {},

      RECORD_FAILED_TRANSCODE_SHOW_ERROR: false,
      PICKORDER_PRINT_START_DATE: '',
      CONFIRM_IMAGE_CAPTURE_PASSWORD_ENABLE: false,
      PRINT_RECEIPT_TAX_INVOICE_2_COPY: false,
      POST_IMAGE_CAPTURE_SHARE_IMAGE_LINK: false,

      AUTO_GENERATE_TRACKINGNO_ENABLE: false,
      AUTO_GENERATE_TRACKINGNO_PROVIDER: '',
      AUTO_GENERATE_TRACKINGNO_SHOPS: [],

      DOBYBOT_CONNECT_VERSION: 1,
      NOCNOC_API: {},
      SHIPPING_SENDER: {
        province: '',
        district: '',
        subdistrict: '',
        province_en: '',
        district_en: '',
        subdistrict_en: '',
        postcode: ''
      },

      // LOG_ROCKET_ENABLE: false,
      POSTHOG_ENABLED: true,

      RECORD_TAGS: [],

      // etax
      ETAX_SELLER: {
        name: '',
        tax_id: '',

        address: '',

        seller_branch: '',
        seller_branch_name: '',
        post_code: '',
        phone_number: '',

        // DoaInfo properties
        api_host: '',
        api_token: '',
        branch_id: '',
        type_tax: '',
        tenant_code: '',
        tenant_id: ''
      },
      ETAX_ENABLE_PICKSLIP_QR_CODE: false,
      ETAX_DOCUMENT_FOLDER_ID: '',
      ETAX_ALLOW_ON_ORDER_RECEIVED: false,
      ETAX_CANCEL_ON_ORDER_CANCELLED_OR_RETURNED: false,
      ETAX_SEND_LINK_TRIGGER: '',
      ETAX_ALLOW_SEND_TO_PLATFORM_CHAT: true,
      ETAX_CUSTOM_MESSAGE_CHAT: '',
      ETAX_SMS_MESSAGE: '',
      ETAX_BRANCHES: [],
      ETAX_AUTO_CANCEL_MODE_AFTER_CUTOFF: '',
      ETAX_AUTO_CANCEL_MODE: '',

      ETAX_AUTO_CREATE_WHEN_PLATFORM_REQUEST: false,
      ETAX_AUTO_START_DATE: '',

      // Desktop App
      WINDOW_NETWORK_FOLDER_PATH: '',
      ETAX_DOC_CREATE_DATE: '',
      ETAX_HEADER_MESSAGE_BILL: '',
      ETAX_FOOTER_MESSAGE_BILL: '',
      ETAX_CUSTOMER_EDIT_MESSAGE: '',
      ETAX_ALERT_OVERDUE_MESSAGE: '',
      ETAX_WELCOME_MESSAGE: '',

      ETAX_AUTO_ZORT_TAG: false,

      CUSTOM_BRAND: '',

      // Serial No.
      RECORD_SCAN_SERIAL_NO_MODE: 'NONE',
      ETAX_TEMPLATE_NAME: {}
    }
  }
}

export const mutations: MutationTree<SettingState> = {
  setLocalSetting (state: SettingState, data: LocalSetting) {
    const local = { ...state.local }
    for (const k in data) {
      const key = k as keyof LocalSetting
      local[key] = data[key] as never
      localStorage.setItem(key, JSON.stringify(local[key]))
    }
    state.local = local
  },

  setCompanySetting (state: SettingState, data: CompanySetting) {
    const company = state.company
    for (const k in data) {
      const key = k as keyof CompanySetting
      company[key] = data[key] as never
    }
    state.company = company
  }
}

export const actions: ActionTree<SettingState, SettingState> = {
  loadFromLocalStorage (context: ActionContext<SettingState, SettingState>) {
    for (const k in context.state.local) {
      const key = k as keyof LocalSetting
      const value = localStorage.getItem(key) as any

      if (value) {
        context.commit('setLocalSetting', { [key]: JSON.parse(value) })
      }
    }
  },

  async loadFromServer (context: ActionContext<SettingState, SettingState>) {
    const res = await this.$axios.get('/api/companies/settings/')
    const companySetting: CompanySetting = res.data
    context.commit('setCompanySetting', companySetting)
  },

  async saveSettings (context: ActionContext<SettingState, SettingState>, settings_keys?: string[]) {
    let payload = {
      ...context.state.company
    }

    if (settings_keys) {
      payload = {} as any
      for (const key of settings_keys) {
        payload[key] = context.state.company[key]
      }
    }

    await this.$axios.post('/api/companies/settings/', payload)
  }
}
