# packing-recorder

Node version 18

## Build Setup

```bash
# install dependencies
$ yarn install

# serve with hot reload at localhost:3000
$ yarn dev

# build for production and launch server
$ yarn build
$ yarn start

# generate static project
$ yarn generate
```

For detailed explanation on how things work, check out [Nuxt.js docs](https://nuxtjs.org).


# Deploy Cloud Run

Use `Cloud Build` to build the image

```
gcloud builds submit --config cloudbuild.yaml
```
