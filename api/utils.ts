import moment from 'moment'
import { padStart } from 'lodash'

export function copyToClipboard (text: string) {
  const el = document.createElement('textarea')
  el.value = text
  document.body.appendChild(el)
  el.select()
  document.execCommand('copy')
  document.body.removeChild(el)
}

export function formatDuration (seconds: number): string {
  const duration = moment.duration(seconds, 'seconds')
  const hh = padStart(duration.hours().toString(), 2, '0')
  const mm = padStart(duration.minutes().toString(), 2, '0')
  const ss = padStart(duration.seconds().toString(), 2, '0')
  return `${hh}:${mm}:${ss}`
}
