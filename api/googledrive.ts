import { IVideo } from '~/plugins/db'

export interface IStorageQuota {
  limit: string
  available: string
  usage: string
  usageInDrive: string
  usageInDriveTrash: string
  limitGB: string
  usageGB: string
  availableGB: string
}

export interface GoogleDriveFolder {
  id: string
  name: string,
  mimeType: string
}

export interface GoogleDriveFile {
  id: string,
  name: string,
  mimeType: string
}

// move to Utils
export function byteToGB (byte: string | number | undefined): string {
  if (byte === undefined) { return '0' }
  return (Number(byte) / 1e9).toFixed(2) + ' GB'
}

export function byteToMB (byte: string | number | undefined): string {
  if (byte === undefined) { return '0' }
  return (Number(byte) / 1e6).toFixed(2) + ' MB'
}

const GoogleDriveClient = {
  get token () {
    return window.gapi.client.getToken().access_token
  },

  get (url: string): Promise<Response> {
    return fetch(url, {
      method: 'GET',
      headers: new Headers({
        Authorization: `Bearer ${this.token}`
      })
    })
  },

  post (url: string, body: any, options?: { headers: any, params?: any}): Promise<Response> {
    if (!options) {
      options = { headers: {} }
    }

    if (options.params) {
      url += '?' + new URLSearchParams(options.params)
    }

    return fetch(url, {
      method: 'POST',
      headers: new Headers({
        Authorization: `Bearer ${this.token}`,
        ...options.headers
      }),
      body
    })
  }
}

export async function getStorageQuota (): Promise<IStorageQuota | null> {
  try {
    const response = await window.gapi.client.drive.about.get({ fields: 'storageQuota' })
    const quota = response.result.storageQuota
    if (!quota) { return null }

    const storageQuota: IStorageQuota = {
      limit: quota.limit || '0',
      usage: quota.usage || '0',
      usageInDrive: quota.usageInDrive || '0',
      usageInDriveTrash: quota.usageInDriveTrash || '0',
      available: (Number(quota.limit) - Number(quota.usage)).toFixed(2),
      limitGB: byteToGB(quota.limit),
      usageGB: byteToGB(quota.usage),
      availableGB: byteToGB(Number(quota.limit) - Number(quota.usage))
    }
    return storageQuota
  } catch (error) {}
  return null
}

/**
 * https://developers.google.com/drive/api/v3/search-files
 * @param params
 *
 *
 * @example
 * listFiles({
 *  q: "mimeType = 'application/vnd.google-apps.folder' and name contains = 'dobybot image'"
 * })
 */
export async function listFiles (params?: { pageSize?: number, fields?: string, q?: string }): Promise<gapi.client.drive.File[]> {
  if (!params) { params = {} }
  if (!params.pageSize) { params.pageSize = 100 }
  if (!params.fields) {
    params.fields = 'nextPageToken, files(id, name, mimeType, createdTime, modifiedTime, webViewLink)'
  }

  const response = await window.gapi.client.drive.files.list({ ...params, spaces: '' })
  // console.log(response)
  return response.result.files || []
}

export function listFolders ():Promise<gapi.client.drive.File[]> {
  return listFiles({ q: "mimeType='application/vnd.google-apps.folder'" })
}

export async function listFoldersInMyDrive (): Promise<gapi.client.drive.File[]> {
  const response = await window.gapi.client.drive.files.list({
    pageSize: 100,
    fields: 'nextPageToken, files(id, name, mimeType, createdTime, modifiedTime, webViewLink)',
    q: 'mimeType=\'application/vnd.google-apps.folder\'',
    spaces: '',
    corpora: 'user'
  })
  return response.result.files || []
}

export function listFileInFolder (folderId: string):Promise<gapi.client.drive.File[]> {
  return listFiles({ q: `'${folderId}' in parents` })
}

export async function createFolder (name: string, options?: { parentFolderId: string }): Promise<GoogleDriveFolder> {
  const metadata = {
    name,
    mimeType: 'application/vnd.google-apps.folder'
  } as any
  if (options?.parentFolderId) { metadata.parents = [options.parentFolderId] }

  const form = new FormData()
  form.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }))

  const res = await GoogleDriveClient.post(
    'https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart&supportsAllDrives=true',
    form
  )
  const resData = await res.json()
  return { id: resData.id, name: resData.name, mimeType: resData.mimeType }
}

export async function uploadMp4 (video: IVideo, parentFolderId: string) {
  const metadata = {
    name: video.name + '.mp4',
    mimeType: 'video/mp4',
    parents: [parentFolderId]
  }

  const form = new FormData()
  form.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }))
  form.append('file', video.blob)

  const res = await GoogleDriveClient.post(
    'https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart&supportsAllDrives=true',
    form
  )
  return res.json()
}

export async function uploadWebm (video: IVideo, parentFolderId: string) {
  const metadata = {
    name: video.name + '.webm',
    mimeType: 'video/webm',
    parents: [parentFolderId]
  }

  const form = new FormData()
  form.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }))
  form.append('file', video.blob)

  const res = await GoogleDriveClient.post(
    'https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart&supportsAllDrives=true',
    form
  )
  return res.json()
}

export function uploadVideo (video: IVideo, parentFolderId: string): Promise<GoogleDriveFile> {
  if (video.filetype === 'webm') {
    return uploadWebm(video, parentFolderId)
  }

  if (video.filetype === 'mp4') {
    return uploadMp4(video, parentFolderId)
  }

  throw new Error(`unsupported file type ${video.filetype}`)
}

export async function share (fileId: string): Promise<string> {
  await window.gapi.client.drive.permissions.create({
    fileId,
    supportsAllDrives: true,
    resource: {
      role: 'reader',
      type: 'anyone'
    }
  })
  return `https://drive.google.com/file/d/${fileId}`
}
export async function listFoldersInShareDrive (shareDriveId: string): Promise<gapi.client.drive.File[]> {
  // if (!params) { params = {} }

  // if (!params.pageSize) { params.pageSize = 100 }
  // if (!params.fields) {
  //   params.fields = 'nextPageToken, files(id, name, mimeType, createdTime, modifiedTime, webViewLink)'
  // }

  const response = await window.gapi.client.drive.files.list({
    pageSize: 100,
    corpora: 'drive',
    fields: 'nextPageToken, files(id, name, mimeType, createdTime, modifiedTime, webViewLink)',
    driveId: shareDriveId,
    includeItemsFromAllDrives: true,
    supportsAllDrives: true,
    q: "mimeType='application/vnd.google-apps.folder'",
    spaces: ''
  })
  return response.result.files || []
}

export async function findFolderInShareDrive (shareDriveId: string, name: string): Promise<gapi.client.drive.File | null> {
  const response = await window.gapi.client.drive.files.list({
    pageSize: 100,
    corpora: 'drive',
    fields: 'nextPageToken, files(id, name, mimeType, createdTime, modifiedTime, webViewLink)',
    driveId: shareDriveId,
    includeItemsFromAllDrives: true,
    supportsAllDrives: true,
    q: `name = '${name}' and mimeType='application/vnd.google-apps.folder'`,
    spaces: ''
  })

  if (!response.result.files) {
    return null
  }

  return response.result.files[0]
}

export async function findFolderInMyDrive (name: string): Promise<gapi.client.drive.File | null> {
  const response = await window.gapi.client.drive.files.list({
    pageSize: 100,
    fields: 'nextPageToken, files(id, name, mimeType, createdTime, modifiedTime, webViewLink)',
    includeItemsFromAllDrives: false,
    supportsAllDrives: true,
    q: `name = '${name}' and mimeType='application/vnd.google-apps.folder'`,
    spaces: ''
  })

  if (!response.result.files) {
    return null
  }

  return response.result.files[0]
}

export async function findFolderInFolder (name: string, parentFolderId: string): Promise<gapi.client.drive.File | null> {
  const response = await window.gapi.client.drive.files.list({
    pageSize: 100,
    fields: 'nextPageToken, files(id, name, mimeType, createdTime, modifiedTime, webViewLink)',
    includeItemsFromAllDrives: true,
    supportsAllDrives: true,
    q: `name = '${name}' and mimeType='application/vnd.google-apps.folder' and '${parentFolderId}' in parents `,
    spaces: ''
  })

  if (!response.result.files) {
    return null
  }

  return response.result.files[0]
}

export async function listPermissions (fileId: string) {
  const url = `https://www.googleapis.com/drive/v3/files/${fileId}/permissions`
  const res = await GoogleDriveClient.get(url)
  console.log(res.json())
}

export async function uploadFile (file: File, parentFolderId: string):Promise<gapi.client.drive.File | null> {
  const metadata = {
    name: file.name,
    mimeType: file.type,
    parents: [parentFolderId]
  }

  const form = new FormData()
  form.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }))
  form.append('file', file)
  const res = await GoogleDriveClient.post(
    'https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart&supportsAllDrives=true',
    form
  )
  return res.json()
}
