<svg id="mermaidChart0" width="100%" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="flowchart mermaid-svg" style="max-width: 506.8125px; color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" viewBox="-8 -8 506.8125 976.0625" role="graphics-document document" aria-roledescription="flowchart-v2"><style style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">#mermaidChart0{font-family:sans-serif;font-size:16px;fill:#333;}#mermaidChart0 .error-icon{fill:#552222;}#mermaidChart0 .error-text{fill:#552222;stroke:#552222;}#mermaidChart0 .edge-thickness-normal{stroke-width:1px;}#mermaidChart0 .edge-thickness-thick{stroke-width:3.5px;}#mermaidChart0 .edge-pattern-solid{stroke-dasharray:0;}#mermaidChart0 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaidChart0 .edge-pattern-dashed{stroke-dasharray:3;}#mermaidChart0 .edge-pattern-dotted{stroke-dasharray:2;}#mermaidChart0 .marker{fill:#333333;stroke:#333333;}#mermaidChart0 .marker.cross{stroke:#333333;}#mermaidChart0 svg{font-family:sans-serif;font-size:16px;}#mermaidChart0 p{margin:0;}#mermaidChart0 .label{font-family:sans-serif;color:#333;}#mermaidChart0 .cluster-label text{fill:#333;}#mermaidChart0 .cluster-label span{color:#333;}#mermaidChart0 .cluster-label span p{background-color:transparent;}#mermaidChart0 .label text,#mermaidChart0 span{fill:#333;color:#333;}#mermaidChart0 .node rect,#mermaidChart0 .node circle,#mermaidChart0 .node ellipse,#mermaidChart0 .node polygon,#mermaidChart0 .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#mermaidChart0 .rough-node .label text,#mermaidChart0 .node .label text,#mermaidChart0 .image-shape .label,#mermaidChart0 .icon-shape .label{text-anchor:middle;}#mermaidChart0 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaidChart0 .rough-node .label,#mermaidChart0 .node .label,#mermaidChart0 .image-shape .label,#mermaidChart0 .icon-shape .label{text-align:center;}#mermaidChart0 .node.clickable{cursor:pointer;}#mermaidChart0 .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#mermaidChart0 .arrowheadPath{fill:#333333;}#mermaidChart0 .edgePath .path{stroke:#333333;stroke-width:2.0px;}#mermaidChart0 .flowchart-link{stroke:#333333;fill:none;}#mermaidChart0 .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaidChart0 .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#mermaidChart0 .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaidChart0 .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#mermaidChart0 .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#mermaidChart0 .cluster text{fill:#333;}#mermaidChart0 .cluster span{color:#333;}#mermaidChart0 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#mermaidChart0 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#mermaidChart0 rect.text{fill:none;stroke-width:0;}#mermaidChart0 .icon-shape,#mermaidChart0 .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaidChart0 .icon-shape p,#mermaidChart0 .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#mermaidChart0 .icon-shape rect,#mermaidChart0 .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaidChart0 :root{--mermaid-alt-font-family:sans-serif;}</style><g style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><marker id="mermaidChart0_flowchart-v2-pointEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath" style="stroke-width: 1px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></path></marker><marker id="mermaidChart0_flowchart-v2-pointStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath" style="stroke-width: 1px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></path></marker><marker id="mermaidChart0_flowchart-v2-circleEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></circle></marker><marker id="mermaidChart0_flowchart-v2-circleStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></circle></marker><marker id="mermaidChart0_flowchart-v2-crossEnd" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></path></marker><marker id="mermaidChart0_flowchart-v2-crossStart" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></path></marker><g class="root" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="clusters" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></g><g class="edgePaths" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M227.609,86L227.609,90.167C227.609,94.333,227.609,102.667,227.609,110.333C227.609,118,227.609,125,227.609,128.5L227.609,132" id="L_A_D_0" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart0_flowchart-v2-pointEnd)"></path><path d="M227.609,190L227.609,194.167C227.609,198.333,227.609,206.667,227.609,214.333C227.609,222,227.609,229,227.609,232.5L227.609,236" id="L_D_E_1" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart0_flowchart-v2-pointEnd)"></path><path d="M227.609,294L227.609,298.167C227.609,302.333,227.609,310.667,227.68,318.417C227.75,326.167,227.89,333.334,227.961,336.917L228.031,340.501" id="L_E_G_2" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart0_flowchart-v2-pointEnd)"></path><path d="M174.113,522.566L160.828,537.649C147.544,552.731,120.975,582.897,107.691,605.48C94.406,628.063,94.406,643.063,94.406,650.563L94.406,658.063" id="L_G_I_3" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart0_flowchart-v2-pointEnd)"></path><path d="M282.106,522.566L295.224,537.649C308.342,552.731,334.577,582.897,347.695,603.48C360.813,624.063,360.813,635.063,360.813,640.563L360.813,646.063" id="L_G_J_4" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart0_flowchart-v2-pointEnd)"></path><path d="M360.813,728.063L360.813,732.229C360.813,736.396,360.813,744.729,360.813,752.396C360.813,760.063,360.813,767.063,360.813,770.563L360.813,774.063" id="L_J_L_5" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart0_flowchart-v2-pointEnd)"></path><path d="M360.813,832.063L360.813,836.229C360.813,840.396,360.813,848.729,360.813,856.396C360.813,864.063,360.813,871.063,360.813,874.563L360.813,878.063" id="L_L_M_6" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart0_flowchart-v2-pointEnd)"></path></g><g class="edgeLabels" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(94.40625, 613.0625)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-22.1875, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="44.375" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">ไม่ผ่าน</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(360.8125, 613.0625)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-13.765625, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="27.53125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">ผ่าน</p></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g></g><g class="nodes" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="node default  " id="flowchart-A-0" transform="translate(227.609375, 47)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-130" y="-39" width="260" height="78"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-100, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="200" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">POS พิมพ์ใบเสร็จพร้อม QRCode สำหรับขอ E-Tax</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-D-1" transform="translate(227.609375, 163)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-103.3671875" y="-27" width="206.734375" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-73.3671875, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="146.734375" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">ลูกค้าสแกน QR Code</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-E-3" transform="translate(227.609375, 267)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-129.625" y="-27" width="259.25" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-99.625, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="199.25" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">เปิดหน้าเว็บพร้อม JWT Token</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-G-5" transform="translate(227.609375, 460.03125)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><polygon points="116.03125,0 232.0625,-116.03125 116.03125,-232.0625 0,-116.03125" class="label-container" transform="translate(-116.03125,116.03125)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></polygon><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-89.03125, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="178.0625" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">ตรวจสอบ Token และ SKU</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-I-7" transform="translate(94.40625, 689.0625)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-86.40625" y="-27" width="172.8125" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-56.40625, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="112.8125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">แสดงข้อผิดพลาด</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-J-9" transform="translate(360.8125, 689.0625)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-130" y="-39" width="260" height="78"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-100, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="200" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">เปลี่ยนเส้นทางไปหน้าขอใบกำกับภาษี</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-L-11" transform="translate(360.8125, 805.0625)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-85.59375" y="-27" width="171.1875" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-55.59375, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="111.1875" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">ลูกค้ากรอกข้อมูล</p></span></div></foreignObject></g></g><g class="node default  " id="flowchart-M-13" transform="translate(360.8125, 921.0625)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-130" y="-39" width="260" height="78"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-100, -24)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="200" height="48" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">สร้างใบกำกับภาษีและส่ง Email</p></span></div></foreignObject></g></g></g></g></g></svg>