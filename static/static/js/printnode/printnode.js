const PrintNode = (function () {
  'use strict'

  const VERSION = '0.4.1'

  function noop () {}

  // Check we've got JSON.parse, JSON.stringigy
  if (!window.JSON) {
    throw "Missing JSON support. You'll need to polyfil this"
  }

  // util fn's
  const console = window.console || { log: noop, error: noop }
  const hasOwnProperty = Object.prototype.hasOwnProperty
  const nativeForEach = Array.prototype.forEach

  const _each = function (o, fn, ctx) {
    if (o === null) {
      return
    }
    if (nativeForEach && o.forEach === nativeForEach) {
      o.forEach(fn, ctx)
    } else if (o.length === +o.length) {
      for (let i = 0, l = o.length; i < l; i++) {
        if (i in o && undefined === fn.call(ctx, o[i], i, o)) {
          return
        }
      }
    } else {
      for (const key in o) {
        if (hasOwnProperty.call(o, key)) {
          if (undefined === fn.call(ctx, o[key], key, o)) {
            return
          }
        }
      }
    }
  }

  // shallow extend a object
  const _extend = function (o) {
    _each(Array.prototype.slice.call(arguments, 1), function (a) {
      for (const p in a) {
        if (undefined !== a[p]) {
          o[p] = a[p]
        }
      }
    })
    return o
  }

  const _isArray = Array.isArray || function (obj) {
    return Object.prototype.toString.call(obj) === '[object Array]'
  }

  const _isFunction = function (obj) {
    return typeof obj === 'function' || false
  }

  const _isString = function (obj) {
    return typeof obj === 'string' || false
  }

  const _isBool = function (obj) {
    // intentionally not counting new Boolean() as a 'bool', it's suicidal to use
    return obj === true || obj === false
  }

  const _isInt = function (obj) {
    return parseInt(obj, 10) === obj
  }

  const _isObject = function (obj) {
    return typeof obj === 'object'
  }

  // return true if fn(obj) returns true for every element in obj
  const _all = function (obj, fn) {
    for (let i = 0, l = obj.length; i < l; i++) {
      if (!fn(obj[i])) {
        return false
      }
    }
    return true
  }

  const _filter = function (obj, predicate, context) {
    const results = []
    _each(obj, function (value, index, list) {
      if (predicate.call(context, value, index, list)) {
        results.push(value)
      }
    })
    return results
  }

  // object-has-keys check, keys must be a array
  // A heads up; {something: undefined} == {}. This is intentional
  const _hasKeys = function (obj, keys) {
    for (let i = 0, l = keys.length; i < l; i++) {
      if (undefined === obj[keys[i]]) {
        return false
      }
    }
    return true
  }

  // a ok-ish v4 uuid generator
  // clearly not cryptographically secure
  // function uuid_v4 () {
  //     var dte = new Date().getTime();
  //     var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
  //         var r = (dte + Math.random()*16)%16 | 0;
  //         dte = Math.floor(dte/16);
  //         return (c=='x' ? r : (r&0x3|0x8)).toString(16);
  //     });
  //     return uuid;
  // }

  const removeElementFromArray = function (arr, element) {
    // IE8+ supported only (but this doesn't matter because... WebSocket)
    if (!arr.includes(element)) {
      return arr
    }
    // don't use Array.slice here, element could appear multiple times
    // remove all occurances
    const output = []; let i = 0; const l = arr.length
    for (; i < l; i++) {
      if (element !== arr[i]) {
        output.push(arr[i])
      }
    }
    return output
  }

  // a micro hierarchical pubsub implementation
  function publify (obj, errorCallback) {
    if (obj.publish || obj.subscribe) {
      throw "publify can't operate on this object there's a collision with existing obj properties"
    }
    // subscriber list
    const subscribers = {}
    // publish
    obj.publish = function publish (topic, payload, publishErrorCallback) {
      publishErrorCallback = publishErrorCallback || errorCallback
      const hierarchy = publify.topicExplodeHierarchy(topic)
      const subscriptions = publify.getSubscribersFromTopicHierarchy(hierarchy, subscribers)
      // call each subscription
      _each(subscriptions, function (sub) {
        const detail = {
          subscription: topic,
          originalSubscription: sub[0],
          payload,
          data: sub[1].data
        }

        try {
          sub[1].fn.call(sub[1].context, payload, detail)
        } catch (e) {
          // add in the detail of the error
          detail.exception = e
          detail.fn = sub[1].fn
          detail.context = sub[1].context
          // trip error callback
          publishErrorCallback(
            new PN_Error('RunTime', 'Exception thrown in subscription callback - ' + e.toString()),
            detail
          )
        }
      })
    }
    // subscribe
    obj.subscribe = function subscribe (topic, fn, options) {
      // allowed to subscribe on a array of strings
      if (_isArray(topic)) {
        if (!_all(topic, _isString)) {
          throw 'subscription topic is a array but not a array of strings'
        }
        // IE9+ only
        topic = topic.map(publify.escapeTopicFragment).join('.')
        // string check
      } else if (!_isString(topic)) {
        throw 'subscription topic must either be a string or a array of strings'
      }

      if (!_isFunction(fn)) {
        throw 'subscription call backs must be functions'
      }
      options = options || {}
      const sub = {
        fn,
        data: options.data || null,
        context: options.context || obj
      }
      if (undefined === subscribers[topic]) {
        subscribers[topic] = [sub]
      } else {
        subscribers[topic].push(sub)
      }
      return this
    }
    // unsubscribe
    obj.unsubscribe = function unsubscribe (fnOrTopic) {
      let ret = 0
      if (_isString(fnOrTopic)) {
        if (undefined !== subscribers[fnOrTopic]) {
          ret = subscribers[fnOrTopic].length
          delete subscribers[fnOrTopic]
        }
      } else if (_isFunction(fnOrTopic)) {
        // Iterate subscriptions and compare subscription funcs. This is
        // a bit of a mess as removing from arrays being iterated is fiddly.
        let topic; let current; const topicsToRemove = []; let numRemoved
        for (topic in subscribers) {
          current = removeElementFromArray(subscribers[topic])
          numRemoved = subscribers[topic].length - current.length
          ret += numRemoved
          if (current.length === 0) {
            topicsToRemove.push(topic)
          } else if (numRemoved) {
            subscribers[topic] = current
          }
        }
        // remove empty subscription
        _each(topicsToRemove, function (topic) {
          delete subscribers[topic]
        })
      } else {
        throw 'you can only unsubscribe strings or functions'
      }
      return ret
    }
    return obj.publish
  }
  // expode a topic into all it's hierarchical components
  publify.topicExplodeHierarchy = function (topic) {
    let output, topicComponents
    if (_isArray(topic)) {
      if (!_all(topic, _isString)) {
        throw 'subscription topic is a array but not a array of strings'
      }
      topicComponents = topic
    } else if (_isString(topic)) {
      topicComponents = publify.topicExplodeFragments(topic)
    } else {
      throw 'you can only publish string or array topics'
    }
    output = [
      publify.escapeTopicFragment(topicComponents[0])
    ]
    for (let i = 1, l = topicComponents.length; i < l; i++) {
      output[i] = output[i - 1] + '.' + publify.escapeTopicFragment(topicComponents[i])
    }
    return output
  }
  // get all subscribers from a topic list
  publify.getSubscribersFromTopicHierarchy = function (hierarchy, subscribers) {
    const output = []
    _each(hierarchy, function (topic) {
      const subscriptions = subscribers[topic]
      if (!subscriptions) {
        return
      }
      for (let i = 0, l = subscriptions.length; i < l; i++) {
        output.push([topic, subscriptions[i]])
      }
    })
    return output
  }
  // escape a topic fragment
  publify.escapeTopicFragment = function (input) {
    return input.replace(/\\/g, '\\\\').replace(/\./g, '\\.')
  }
  // split a topic into fragments (escaping aware)
  publify.topicExplodeFragments = function (input) {
    const output = ['']; let outputIndex = 0; let i = 0; const l = input.length; let escaped = false; let current
    // iterate string and parse
    for (; i < l; i++) {
      current = input.charAt(i)
      // previous char was escaped
      if (escaped) {
        output[outputIndex] += current
        escaped = false
        // hit escaped char
      } else if (current === '\\') {
        escaped = true
        // separator
      } else if (current === '.') {
        output[++outputIndex] = ''
        // vanilla char
      } else {
        output[outputIndex] += current
      }
    }
    // drop trailing escape chars
    return output
  }

  // PrintNode WS API fn's
  function PN_WebSocketMessage (data) {
    try {
      data = JSON.parse(data)
    } catch (e) {
      throw new PN_Error('Server', 'server->client message not valid javascript')
    }
    // is array, length 3
    if (!_isArray(data) || data.length !== 3) {
      throw new PN_Error('Server', 'server->client message framing error')
    }
    this.typ = data[0]
    this.message = data[1]
    this.payload = data[2]
  }

  function PN_Error (code, message) {
    if (!PN_Error.CODES[code]) {
      throw "unknown error code '" + code + "'"
    }
    this.code = code
    this.message = message || PN_Error.CODES[code]
  }
  PN_Error.prototype.toString = function () {
    return 'PrintNode ' + this.code + ' exception: ' + this.message
  }
  PN_Error.CODES = {
    // quote keys to prevent the minifiers rewriting objects
    NotSupported: "This feature isn't supported",
    BadArguments: 'Bad arguments passed',
    Server: 'Server error',
    Socket: 'Socket error',
    RateLimit: 'Rate limit error',
    Internal: 'Internal error',
    RunTime: 'RunTime'
  }

  // Generate send function which (optionally) can handle reciept
  // acknowledgements and timeouts. Any errors trip errCallback.
  // This won't trigger exceptions unless thrown by errCallback
  function getWSSendFn (soc, defaultAck, ackTimeout, errCallback, logSend) {
    // ack implementation
    let ackHistory = {}
    let msgCount = 0

    // cleanup all the remaining ack timers
    send.shutdown = function () {
      for (const key in ackHistory) {
        clearTimeout(ackHistory[key][0])
      }
      ackHistory = {}
    }

    function send (message, payload, ack) {
      let ackKey; const ackSource = (undefined === ack ? defaultAck : ack)
      const when = Date.now()
      msgCount++
      // handle message timeouts, specifically detect slow servers
      // so app layer can choose what to do
      if (ackSource) {
        ack = msgCount
        ackKey = msgCount.toString()
        ackHistory[ackKey] = [
          setTimeout(
            function () {
              const timeoutDuration = Date.now() - when
              errCallback(
                new PN_Error('Server', 'No ack recieved'),
                {
                  timeout: true,
                  timeoutDuration,
                  message,
                  payload
                }
              )
            },
            ackTimeout
          ),
          when
        ]
      } else {
        ack = null
      }

      let data
      // you'd be doing something pretty exotic to trip this
      try {
        data = JSON.stringify([ack, message, payload])
      } catch (err) {
        errCallback(
          new PN_Error('BadArguments', 'Not possible to JSON encode all message arguments'),
          {
            timeout: false,
            message,
            payload
          }
        )
        return false
      }
      // try the send, likely failure mode for this is the socket isn't open
      // https://developer.mozilla.org/en-US/docs/Web/API/WebSocket
      try {
        soc.send(data)
      } catch (err) {
        errCallback(
          new PN_Error('Socket', "Send failed. It is likely the socket isn't open"),
          {
            timeout: false,
            message,
            payload
          }
        )
        return false
      }
      logSend(data)
      return true
    }

    // benchmark info, because...
    const ackDurations = [] // rotating buffer of the last n timings
    const ackDurationsMaxSize = 50

    // clear the timeout for a message
    send.ack = function (ackKey) {
      ackKey = ackKey.toString()
      const ack = ackHistory[ackKey]
      delete ackHistory[ackKey]

      if (undefined === ack) {
        errCallback(
          new PN_Error('Server', "Unexpected ack recieved; either this message didn't request a ack or we've already recieved it."),
          { timeout: false }
        )
        return
      }
      clearTimeout(ack[0])

      // benchmarking
      ackDurations.push(Date.now() - ack[1])
      if (ackDurations.length > ackDurationsMaxSize) {
        ackDurations.shift()
      }
    }

    send.getNumMessages = function () {
      return msgCount
    }

    send.getDebugInfo = function () {
      let totalDuration = 0; let i = 0; const l = ackDurations.length
      for (; i < l; i++) {
        totalDuration += ackDurations[i]
      }
      return {
        numMessages: msgCount,
        meanAck: totalDuration > 0 ? parseInt(totalDuration / l, 10) : 0,
        accDurations: ackDurations
      }
    }

    return send
  }

  // Subscription management
  function SocketSubscriptions (maxSubscriptions) {
    // Attempting to increase this here won't have any effect.
    // Authorative check is done on the server.
    // In the event this is bypassed the server will respond with
    // disconnection and will tag this account/client IP address as abusive.
    this.maxSubscriptions = maxSubscriptions

    this.subscriptions = {}
    // perhaps swap out with Object.keys(this.subscriptions).length if don't
    // care about IE
    this.subscriptionCount = 0

    // get a new subscriptionId
    this.getSubscriptionId = (function () {
      let id = 0
      return function () {
        return id++
      }
    })()
  }
  SocketSubscriptions.prototype.add = function (path, callback, ctx, handler, additionalTopics) {
    if (!_isFunction(callback)) {
      throw new PN_Error('BadArguments', 'Subscription callback must be a function if is defined.')
    }
    if (!_isFunction(handler)) {
      throw new PN_Error('Internal', 'Subscription handler must be a function.')
    }
    if (!_isArray(additionalTopics)) {
      throw new PN_Error('Internal', 'Additional topics must be a array.')
    }
    if (this.subscriptionCount === this.maxSubscriptions) {
      throw new PN_Error('RunTime', 'Max number of subscriptions reached; unable to add more.')
    }
    // check we've not already got one of these subscriptions
    _each(this.subscriptions, function (existingSub) {
      if (path === existingSub) {
        throw new PN_Error('BadArguments', "Subscription '" + path + "' already added.")
      }
    })
    // lets add it
    const newSubId = this.getSubscriptionId()
    this.subscriptionCount++
    this.subscriptions[newSubId] = [newSubId, path, callback, ctx, handler, additionalTopics]
    return newSubId
  }
  // remove a subcription by string, id or callback
  SocketSubscriptions.prototype.remove = function (arg) {
    const toDelete = []; let check; let key
    // how are we looking up the delete
    if (_isInt(arg)) {
      check = function (sub) { return sub[0] === arg }
    } else if (_isString(arg)) {
      check = function (sub) { return sub[1] === arg }
    } else if (_isFunction(arg)) {
      check = function (sub) { return sub[2] === arg }
    } else if (undefined === arg) {
      check = function () { return true }
    } else {
      throw new PN_Error('BadArguments', 'Can only remove subscription by id, path or callback.')
    }
    // what are we going to delete
    for (key in this.subscriptions) {
      if (check(this.subscriptions[key])) {
        toDelete.push(this.subscriptions[key][0])
      }
    }
    // do the delete
    _each(toDelete, function (sub) {
      delete this.subscriptions[sub]
      this.subscriptionCount--
    }, this)
    // return what we've deleted
    return toDelete
  }
  // trigger a subscription
  SocketSubscriptions.prototype.trigger = function (id, payload, errorCallback) {
    const sub = this.subscriptions[id]
    if (undefined === sub) {
      // You might consider this a error but there's a nasty race condition
      // where you unsubscribe (which is asyncronous) but before this is processed
      // another event comes in from the server.
      //
      // I don't feel this is fixed by prosponing deletes until server has
      // confirmed deletion because
      //     1. Volates 'principle of least surprise'. Callbacks will still fire after unsubscribing.
      //     2. This is only a problem if the PN server is bugged; which it isn't :)
      return false
    }
    const path = sub[1]; const fn = sub[2]; const ctx = (sub[3] || path); const handler = sub[4]; const additionalTopics = sub[5]
    const detail = { id, path }

    let handledPayload
    try {
      handledPayload = handler(payload)
    } catch (e) {
      handledPayload = payload
    }

    try {
      fn.call(ctx, handledPayload, detail)
    } catch (e) {
      // add in the detail of the error
      detail.exception = e
      detail.fn = fn
      detail.context = ctx
      // trip error callback
      errorCallback(
        new PN_Error('RunTime', 'Exception thrown in subscription callback - ' + e.toString()),
        detail
      )
    }
    // return
    return {
      payload: handledPayload,
      additionalTopics
    }
  }

  // A wrapper for the returned scale measurement object
  // Allows calling code to do instanceof tests and add additional methods to prototype
  function ScalesMeasurement (data) {
    for (const key in data) {
      this[key] = data[key]
    }
  }
  ScalesMeasurement.prototype.getLatency = function () {
    const client = new Date(this.clientReportedCreateTimestamp); const now = new Date()
    return now.getTime() - client.getTime()
  }
  ScalesMeasurement.factory = function (data) {
    return new ScalesMeasurement(data)
  }

  // Computer Connections
  function Connection (data) {
    for (const key in data) {
      this[key] = data[key]
    }
  }

  // Wrapper for ComputerConnections
  function ComputerConnections (accountId, computerId) {
    this.computerId = computerId
    this.accountId = accountId
  }
  ComputerConnections.prototype = new Array()

  ComputerConnections.prototype.add = function (cc) {
    if (!(cc instanceof Connection)) {
      throw new PN_Error('RunTime', 'You can only add a Connection object to a ComputerConnections array')
    }

    /*
        no need to be testing cc.computerId, it's not there because the server doesn't send it (why would it?)

        var computerId = cc.computerId;
        if (null == this.computerId || (this.computerId === 0 && computerId > 0)) {
            this.computerId = computerId;
        } else if (this.computerId !== computerId) {
            throw new PN_Error("RunTime", "Attempting to add Connection object to a ComputerConnections array with different computerId");
        }
        */

    cc.computerId = this.computerId
    this.push(cc)
  }
  ComputerConnections.prototype.isConnected = function () {
    return this.length > 0
  }

  ComputerConnections.factory = function (data) {
    const accountId = data.accountId
    const computerId = data.computerId
    const connections = data.connections
    if (!_isArray(connections)) {
      throw new PN_Error('Server', "Server payload for ComputerConnections, 'connections' property isn't a array")
    }
    if (!_isInt(accountId)) {
      throw new PN_Error('Server', "Server payload for ComputerConnections, 'accountId' should be a int")
    }
    if (!_isInt(computerId)) {
      throw new PN_Error('Server', "Server payload for ComputerConnections, 'computerId' should be a int")
    }
    const computerConnections = new ComputerConnections(accountId, computerId)
    _each(connections, function (connection) {
      const cc = new Connection(connection)
      computerConnections.add(cc)
    })
    return computerConnections
  }

  // used by both WS and HTTP
  function generateScalesUrlFromOptions (options, allowNoOptions) {
    // shallow copy original object and escape values
    const encodedOptions = {}; const keys = []; let key; let path
    for (key in options) {
      encodedOptions[key] = encodeURIComponent(options[key])
      keys.push(key)
    }
    // build up the path obj
    if (keys.length === 0 && allowNoOptions) {
      path = ['scales']
    } else if (_hasKeys(encodedOptions, ['computerId', 'deviceName', 'deviceNum'])) {
      path = ['computer', encodedOptions.computerId, 'scale', encodedOptions.deviceName, encodedOptions.deviceNum]
    } else if (_hasKeys(encodedOptions, ['computerId', 'deviceName'])) {
      path = ['computer', encodedOptions.computerId, 'scales', encodedOptions.deviceName]
    } else if (_hasKeys(encodedOptions, ['computerId'])) {
      path = ['computer', encodedOptions.computerId, 'scales']
      // unknown options combination
    } else {
      // determine the options combination
      throw new PN_Error('RunTime', 'Options key combination (' + keys.join(', ') + ') for getting scales data; please refer to documentation.')
    }
    return path.join('/')
  }

  // used by both WS and HTTP
  function generateComputerConnectionsUrlFromOptions (options, allowNoOptions) {
    // shallow copy original object and escape values
    const encodedOptions = {}; const keys = []; let key; let path
    for (key in options) {
      encodedOptions[key] = encodeURIComponent(options[key])
      keys.push(key)
    }
    // build up the path obj
    if (keys.length === 0 && allowNoOptions) {
      path = ['computers', 'connections']
    } else if (_hasKeys(encodedOptions, ['computerId'])) {
      path = ['computer', encodedOptions.computerId, 'connections']
      // unknown options combination
    } else {
      // determine the options combination
      throw new PN_Error('RunTime', 'Options key combination (' + keys.join(', ') + ') for getting computer connections is invalid; please refer to documentation.')
    }
    return path.join('/')
  }

  // have decided to go with a pubsub implementation
  function PN_WebSocket (options, authCallback, errorCallback) {
    if (!PN_WebSocket.isSupported()) {
      throw new PN_Error('NotSupported', 'Native WebSocker support is missing')
    }
    if (authCallback && !_isFunction(authCallback)) {
      throw new PN_Error('BadArguments', 'If set the authCallback argument should be a function')
    }
    if (errorCallback && !_isFunction(errorCallback)) {
      throw new PN_Error('BadArguments', 'If set the errorCallback argument should be a function')
    }
    if (!options || !options.apiKey) {
      throw new PN_Error('BadArguments', "Options argument expected to be a object with property 'apiKey'")
    }
    if (undefined !== options.ack && !_isBool(options.ack)) {
      throw new PN_Error('BadArguments', 'options.ack should be literal boolean')
    }
    if (undefined !== options.ackTimeout && !_isInt(options.ackTimeout)) {
      throw new PN_Error('BadArguments', 'options.ackTimeout should be a int')
    }
    if (undefined !== options.authTimeout && !_isInt(options.authTimeout)) {
      throw new PN_Error('BadArguments', 'options.authTimeout should be a int')
    }

    options = _extend(
      {
        centralOrigin: 'central.printnode.com',
        version: VERSION, // default to latest
        ack: false,
        ackTimeout: 10000,
        authTimeout: 5000
      },
      options
    )

    // scope accessor - terser than Function.prototype.bind and it's
    // only used in a couple of place if we decide to replace...
    const that = this
    // variable declarations
    let subscriptions, soc, send
    const close = function () {}
    // make this object pub/sub capable
    const publish = publify(this, failHandler)
    // oother declarations
    let state = 'NOT_STARTED'
    let isConnected = false

    function setState (newState) {
      state = newState
      publish('system.state.' + state, state)
    }
    this.getState = function () {
      return state
    }
    this.isConnected = function () {
      return isConnected
    }

    // finalHandler
    function failHandler () {
      // try cleanup the socket, we're outa here
      close()
      // authenticationTimout is no longer needed
      if (authenticateTimeout.timeout) {
        clearTimeout(authenticateTimeout.timeout)
      }
      // call the error handler
      let args = Array.prototype.slice.call(arguments)
      if (errorCallback) {
        errorCallback.apply(that, args)
      }
      // unpack args
      if (args.length === 1) {
        args = args[0]
      }
      publish(['error'], args, function () {
        console.error('error in error callback', args)
      })
    }

    // Decision/Decree. A failed authenticate shouldn't trip the error handler.
    // This fires in the event of network, server or logic errors
    function authenticateResponse (payload) {
      clearTimeout(authenticateTimeout.timeout)
      // not authed
      if (payload.error) {
        setState('UNAUTHENTICATED')
        publish('authenticate.error', payload)
        close()
        // authed
      } else {
        setState('AUTHENTICATED')
        subscriptions = new SocketSubscriptions(payload.maxSubscriptions)
        publish('authenticate.ok', payload)
      }
      // callback?
      if (authCallback) {
        try {
          authCallback.call(that, payload)
        } catch (e) {
          failHandler('Exception thrown in authentication callback:', e)
        }
      }
    }

    function authenticateTimeout () {
      setState('UNAUTHENTICATED')
      const payload = { timeout: true, error: 'Server timed out' }
      publish('authenticate.error', payload)
      if (authCallback) {
        try {
          authCallback.call(that, payload)
        } catch (e) {
          failHandler('Exception thrown in authentication callback:', e)
        }
      }
      // wrap this up
      failHandler(
        new PN_Error('Server', 'Server failed to authenticate in time'),
        payload
      )
    }

    // internal protocol level messages
    function onopen (evt) {
      isConnected = true
      authenticateTimeout.timeout = setTimeout(
        authenticateTimeout,
        options.authTimeout
      )
      setState('AUTHENTICATING')
      publish('system.socket.open', evt)
      send('authenticate', { apiKey: options.apiKey }, true)
    }

    function onclose (evt) {
      isConnected = false
      publish('system.socket.close', evt)
      setState('DISCONNECTED')
    }

    function onerror (evt) {
      publish('system.socket.error', evt)
      failHandler(
        new PN_Error('Socket'),
        { socket: true, socketError: evt }
      )
    }

    function onmessage (evt) {
      let message
      publish('system.socket.message', evt)
      // validate server message
      try {
        message = new PN_WebSocketMessage(evt.data)
      } catch (err) {
        failHandler(err, { timeout: false, data: evt.data })
        return
      }
      // handle all the different message
      switch (message.typ) {
        case 'system':
          switch (message.message) {
            case 'authenticateResponse':
              authenticateResponse(message.payload)
              break
            case 'hi':
              isConnected = message.payload
              break
            case 'rateLimit':
              failHandler(
                new PN_Error('RateLimit', 'PrintNode is applying rate limits'),
                message.payload
              )
              break
            default:
              console.log('unhandled system message', message, message.payload)
              break
          }
          break
        case 'protocol':
          switch (message.message) {
            case 'ack':
              send.ack(message.payload)
              break
            default:
              console.log('unhandled protocol message', message, message.payload)
              break
          }
          break
        case 'publish':
          var topics = message.message; var subId = message.payload[0]; var payload
          // trip subscription callback
          payload = subscriptions.trigger(subId, message.payload[1], failHandler)
          // check to see if the subscription has been removed before publishing it
          if (payload !== false) {
            // publisg this topic
            const pub = function (topic) {
              publish(topic, payload.payload)
            }
            // publish on additionalTopics
            _each(payload.additionalTopics, pub) // subscription topic list
            _each(topics, pub) // server produced topic list
          }
          break
        default:
          console.log('unhandled message type', message, message.payload)
          break
      }
    }

    const startWs = function startWs (webSocketUrl) {
      // startup the websocket and wire it up
      soc = new WebSocket(webSocketUrl)
      soc.onopen = onopen
      soc.onclose = onclose
      soc.onerror = onerror
      soc.onmessage = onmessage

      function logSend (data) {
        publish('system.socket.sent', data)
      }

      // socket wrapper methods
      send = getWSSendFn(soc, options.ack, options.ackTimeout, failHandler, logSend)

      // expose socket closing to the outside world and prevent calling on
      // an already closed socket
      this.closeSocket = function () {
        // is socket already shutdown
        if (!isConnected) {
          return false
        }
        setState('CLOSINGSOCKET')
        send.shutdown()
        try {
          soc.close()
        } catch (err) {
          // Apparently this should never happen; docs say websockets are
          // safe for multiple disconnections. They're wrong.
          console.log('failed to close socket', err)
          return false
        }
        return true
      }

      // subscription management fns
      function makeServerSubscription (path, callback, ctx, handler, additionalTopics) {
        if (!_isString(path)) {
          throw new PN_Error('BadArguments', 'Subscription path should be a string')
        }
        if (!subscriptions) {
          throw new PN_Error('RunTime', 'Cannot create subscriptions until authenticated')
        }
        callback = callback || noop
        additionalTopics = additionalTopics || []
        const id = subscriptions.add(path, callback, ctx, handler, additionalTopics)
        send('subscribe', [id, path], true)
        return id
      }

      this.removeServerSubscription = function (arg) {
        const deleted = subscriptions.remove(arg)
        if (deleted.length) {
          send('unsubscribe', deleted, true)
        }
        return deleted
      }

      this.getScales = function (options, callback, ctx) {
        const path = generateScalesUrlFromOptions(options, true)

        return makeServerSubscription(
          '/' + path + '/',
          callback,
          ctx,
          ScalesMeasurement.factory,
          ['scales']
        )
      }

      this.getComputerConnections = function (options, callback, ctx) {
        const path = generateComputerConnectionsUrlFromOptions(options, true)

        return makeServerSubscription(
          '/' + path + '/',
          callback,
          ctx,
          ComputerConnections.factory,
          ['computers.connections']
        )
      }
    }.bind(this)

    // if the server option has been manually specified use this to build the url otherwise use central proxy discovery
    if (options.server) {
      const webSocketUrl = [
        'wss://',
        options.server,
        '/ws/',
        options.version
      ].join('')
      startWs(webSocketUrl)
      return
    }

    // build urls
    function fallback (reason) {
      // falling back to api.printnode.com
      if (console && console.log) {
        console.log('Using api.printnode.com for websocket because; ' + reason + '. This is expected prior to 2020-12-27.')
      }
      // start websocket using api.printnode.com
      startWs('wss://api.printnode.com/ws/' + options.version)
    }

    const useAbs = false
    if (!useAbs) {
      // start websocket using api.printnode.com
      startWs('wss://api.printnode.com/ws/' + options.version)
    } else {
      const centralUrl = 'https://' + options.centralOrigin + '/v3/proxy?key=' + encodeURIComponent(options.apiKey)
      // perform proxy / websocket discovery
      try {
        const reqCentral = ajax(
          {
            url: centralUrl,
            auth: new ApiKey(options.apiKey),
            success (proxyHost, response) {
              if (response.xhr.status !== 200) {
                fallback('non HTTP 200 response from ' + options.centralOrigin)
                return
              }
              const webSocketUrl = 'wss://' + responseBody.httpPublicHost + '/ws/' + options.version
              startWs(webSocketUrl)
              ajax(
                {
                  url: 'https://' + proxyHost + '/v3/computeunit?key=' + encodeURIComponent(options.apiKey),
                  auth: new HTTPAuth(),
                  success (responseBody, response) {
                    if (response.xhr.status !== 200) {
                      fallback('non HTTP 200 response from ' + proxyHost)
                    }
                  },
                  error (responseBody, response) {
                    fallback('unexpected response from ' + proxyHost)
                  },
                  timeout (url, timeout) {
                    fallback('response timeout from ' + proxyHost)
                  }
                },
                'GET'
              )
            },
            error (responseBody, response) {
              fallback('unexpected response from ' + options.centralOrigin)
            },
            timeout (url, timeout) {
              fallback('response timeout from ' + options.centralOrigin)
            }
          },
          'GET'
        )
      } catch (err) {
        console.log('uncaught exception', err)
      }
    }
  }

  PN_WebSocket.isSupported = function () {
    return !!window.WebSocket
  }

  // Minimalistic implementation of the Promises/A+ spec
  // Based on the public domain PinkySwear.js 2.2.2 modified to remove everything not required for browser
  const pinkySwear = function pinkySwear (extend) {
    let state // undefined/null = pending, true = fulfilled, false = rejected
    let values = [] // an array of values as arguments for the then() handlers
    const deferred = [] // functions to call when set() is invoked

    let set = function (newState, newValues) {
      if (undefined === state && undefined !== newState) {
        state = newState
        values = newValues
        if (deferred.length) {
          setTimeout(
            function () {
              for (let i = 0; i < deferred.length; i++) {
                deferred[i]()
              }
            },
            0
          )
        }
      }
      return state
    }

    set.then = function (onFulfilled, onRejected) {
      const promise2 = pinkySwear(extend)
      const callCallbacks = function () {
        try {
          const f = (state ? onFulfilled : onRejected)
          if (_isFunction(f)) {
            const resolve = function resolve (x) {
              let then; let cbCalled = 0
              try {
                if (x && (_isObject(x) || _isFunction(x)) && _isFunction(then = x.then)) {
                  if (x === promise2) {
                    throw new TypeError()
                  }
                  then.call(x,
                    function () {
                      if (!cbCalled++) {
                        resolve.apply(undefined, arguments)
                      }
                    },
                    function (value) {
                      if (!cbCalled++) {
                        promise2(false, [value])
                      }
                    })
                } else {
                  promise2(true, arguments)
                }
              } catch (e) {
                if (!cbCalled++) {
                  promise2(false, [e])
                }
              }
            }
            resolve(f.apply(undefined, values || []))
          } else {
            promise2(state, values)
          }
        } catch (e) {
          promise2(false, [e])
        }
      }
      if (undefined !== state) {
        setTimeout(callCallbacks, 0)
      } else {
        deferred.push(callCallbacks)
      }
      return promise2
    }
    if (extend) {
      set = extend(set)
    }
    return set
  }

  //
  // API client
  //
  // cross browser compatible XMLHttpRequest object generator
  const genXHR = (function () {
    const xhrs = [
      function () { return new XMLHttpRequest() },
      function () { return new ActiveXObject('Microsoft.XMLHTTP') },
      function () { return new ActiveXObject('MSXML2.XMLHTTP.3.0') },
      function () { return new ActiveXObject('MSXML2.XMLHTTP') }
    ]
    let _xhrf = null
    return function () {
      if (_xhrf !== null) {
        return _xhrf()
      }
      for (let i = 0, l = xhrs.length; i < l; i++) {
        try {
          const f = xhrs[i]; const req = f()
          if (req !== null) {
            _xhrf = f
            return req
          }
        } catch (e) {
          continue
        }
      }
      return function () { }
    }
  })()

  // ajax response
  function AjaxResponse (xhr, method, url, body, reqStart) {
    this.xhr = xhr
    this.reqMethod = method
    this.reqUrl = url
    this.reqBody = body
    this.reqStart = reqStart
    this.reqEnd = new Date()
    this.bodyParsingError = null
    this.headers = {}

    this.parseResponseBody()
    this.parseResponseHeaders()
  }
  AjaxResponse.prototype.getDuration = function () {
    return this.reqEnd.getTime() - this.reqStart.getTime()
  }
  AjaxResponse.prototype.getServerDuration = function () {
    return 'abc'
  }
  AjaxResponse.prototype.isOk = function () {
    // there shouldn't be any redirects from the printnode api so only classify 2xx with a valid body as OK
    return this.bodyParsingError === null && this.xhr.status >= 200 && this.xhr.status < 300
  }
  AjaxResponse.prototype.parseResponseBody = function () {
    const contentTypeHeader = this.xhr.getResponseHeader('Content-Type'); let response
    if (contentTypeHeader && contentTypeHeader.split(';')[0] !== 'application/json') {
      this.bodyParsingError = 'Server has not responded with valid JSON Content-Type'
      return
    }
    // don't parse response bodies for HTTP 204 responses
    if (this.xhr.status === 204) {
      return
    }
    // assume we've got a HTTP response body because we didn't get a 204
    try {
      this.response = JSON.parse(this.xhr.responseText)
    } catch (e) {
      // console.log(this.xhr.responseText)
      // console.log(this.xhr)
      this.bodyParsingError = 'Server has not responded valid JSON, it returned; ' + this.xhr.responseText
    }
  }
  AjaxResponse.prototype.parseResponseHeaders = function () {
    const headerStr = this.xhr.getAllResponseHeaders()
    if (!headerStr) {
      return
    }
    const headerPairs = headerStr.split('\u000D\u000A')
    for (let i = 0, ilen = headerPairs.length; i < ilen; i++) {
      const headerPair = headerPairs[i]
      const index = headerPair.indexOf('\u003A\u0020')
      if (index > 0) {
        const key = headerPair.substring(0, index)
        const val = headerPair.substring(index + 2)
        this.headers[key] = val
      }
    }
  }

  function ajax (options, method, endpoint, body) {
    const xhr = genXHR(); let timer; let n = 0; let url
    if (options.url) {
      url = options.url
    } else {
      url = ['https://', options.server, '/', endpoint].join('')
    }
    const reqStart = new Date()
    const promise = pinkySwear()
    options = _extend(
      {
        timeoutDuration: 5000
      },
      options
    )
    if (options.timeoutDuration) {
      timer = setTimeout(
        function () {
          xhr.onreadystatechange = function () {}
          xhr.abort()
          if (options.timeout) {
            try {
              options.timeout.call(options.ctx, url, options.timeoutDuration)
            } catch (e) {
              console.error('exception thrown in timeout callback', e, options.timeout)
            }
          }
          promise(
            false,
            ['timeout']
          )
        },
        options.timeoutDuration
      )
    }
    xhr.onreadystatechange = function () {
      if (xhr.readyState == 4) {
        // clear the timeout
        if (timer) {
          clearTimeout(timer)
        }

        const response = new AjaxResponse(xhr, method, url, body, reqStart)

        // console.log( "REV %s %s ", xhr.status, url, response, headers);
        // there shouldn't be any redirects from the printnode api so classify
        // 3xx as errors
        if (response.isOk()) {
          if (options.success) {
            try {
              options.success.call(options.ctx, response.response, response)
            } catch (e) {
              console.error('exception thrown in success callback', e, options.success, response)
            }
          }
          promise(
            true,
            [response.response, response]
          )
        } else {
          if (options.error) {
            try {
              options.error.call(options.ctx, response.response, response)
            } catch (e) {
              console.error('exception thrown in error callback', e, options.error, response)
            }
          }
          promise(
            false,
            [response.response, response]
          )
        }
        if (options.complete) {
          try {
            options.complete.call(options.ctx, response)
          } catch (e) {
            console.error('exception thrown in complete callback', e, options.complete, response)
          }
        }
      } else if (options.progress) {
        try {
          options.progress(++n)
        } catch (e) {
          console.error('exception thrown progress callback', e, options.progress)
        }
      }
    }

    // build and make the request
    xhr.open(method, url)
    options.auth.setXhrHeaders(xhr)
    // xhr.setRequestHeader("X-Client", "printnode-javascript-client version; "+JS_CLIENT_VERSION)

    if (method === 'POST' || method === 'PUT' || method === 'PATCH') {
      xhr.setRequestHeader('Content-Type', 'application/json')
      body = JSON.stringify(body)
    }
    xhr.send(body)
    // console.log("REQ %s %s", o.type, url, postData);
    return promise
  }

  // HTTP authentication object, everything must extend from this
  function HTTPAuth () {
    this.headers = {}
  }
  HTTPAuth.prototype.setAuthorizationHeader = function (username, password) {
    this.headers.Authorization = ('Basic ' + btoa(username + ':' + password))
  }
  HTTPAuth.prototype.setXhrHeaders = function (xhr) {
    let key
    for (key in this.headers) {
      xhr.setRequestHeader(key, this.headers[key])
    }
  }

  // account switching isn't currently supported by authentication methods other
  // than apikey; this is in here (as opposed to the ApiKey prototype) for development
  HTTPAuth.prototype.childAccountById = function (accountId) {
    if (_isInt(accountId)) {
      throw 'accountId must be a int'
    }
    this.headers['X-Child-Account-By-Id'] = accountId.toString()
    return this
  }
  HTTPAuth.prototype.childAccountByCreatorRef = function (creatorRef) {
    if (_isString(creatorRef)) {
      throw 'creatorRef must be a string'
    }
    this.headers['X-Child-Account-By-CreatorRef'] = creatorRef
    return this
  }
  HTTPAuth.prototype.childAccountByEmail = function (email) {
    if (_isString(email)) {
      throw 'email must be a string'
    }
    this.headers['X-Child-Account-By-Email'] = email
    return this
  }

  // authenticate by apiKey
  function ApiKey (apiKey) {
    if (!_isString(apiKey)) {
      throw 'apiKey must be a string'
    }
    this.setAuthorizationHeader(apiKey, '')
  }
  ApiKey.prototype = new HTTPAuth()

  // account credentials
  function UsernamePassword (username, password) {
    if (!_isString(username)) {
      throw 'username must be a string'
    }
    if (!_isString(password)) {
      throw 'password must be a string'
    }
    this.headers['X-Auth-With-Account-Credentials'] = 'true'
    this.setAuthorizationHeader(username, password)
  }
  UsernamePassword.prototype = new HTTPAuth()

  // client key
  function ClientKey (clientKey) {
    if (!_isString(clientKey)) {
      throw 'clientKey must be a string'
    }
    this.headers['X-Auth-With-Client-Key'] = 'true'
    this.setAuthorizationHeader(clientKey, '')
  }
  ClientKey.prototype = new HTTPAuth()

  /**
     * Takes options array {apiKey, version}
     */
  function HTTP (auth, options) {
    options = options || {}
    if (!(auth instanceof HTTPAuth)) {
      throw "auth argument isn't of the right type"
    }

    this.options = _extend(
      {
        version: null, // which defaults to latest - currently this isn't used
        server: 'api.printnode.com',
        context: this
      },
      options
    )

    // make request options
    this._makeReqOptions = function (options) {
      return _extend(
        {}, // ensure we get a entirely new obj
        this.options, // defaults
        options || {}, // passed arguments
        { auth } // always use stored authentication
      )
    }
  }
  HTTP.ApiKey = ApiKey
  HTTP.UsernamePassword = UsernamePassword
  HTTP.ClientKey = ClientKey

  // methods
  HTTP.prototype.whoami = function getWhoami (options) {
    ajax(this._makeReqOptions(options), 'GET', 'whoami')
    return this
  }
  HTTP.prototype.computers = function getComputers (options, params) {
    let url = 'computers'
    if (params && params.computerSet) {
      url += '/' + params.computerSet.toString()
    }
    return ajax(this._makeReqOptions(options), 'GET', url)
  }
  HTTP.prototype.printers = function getPrinters (options, params) {
    let url = 'printers'
    if (params) {
      if (params.computerSet) {
        url = 'computers/' + params.computerSet.toString() + '/printers'
      }
      if (params.printerSet) {
        url += '/' + params.printerSet.toString()
      }
    }
    return ajax(this._makeReqOptions(options), 'GET', url)
  }
  HTTP.prototype.printjobs = function getPrintJobs (options, params) {
    let url = 'printjobs'
    if (params) {
      if (params.printerSet) {
        url = 'printers/' + params.printerSet.toString() + '/printjobs'
      }
      if (params.printjobSet) {
        url += '/' + params.printjobSet.toString()
      }
    }
    return ajax(this._makeReqOptions(options), 'GET', url)
  }
  HTTP.prototype.createPrintjob = function makePrintJob (options, payload) {
    return ajax(this._makeReqOptions(options), 'POST', 'printjobs', payload)
  }
  HTTP.prototype.scales = function getScales (options, params) {
    return ajax(this._makeReqOptions(options), 'GET', generateScalesUrlFromOptions(params, false))
  }

  return {
    WebSocket: PN_WebSocket,
    HTTP,
    ScalesMeasurement,
    ComputerConnections,
    Connection,
    Error: PN_Error,
    ajax
  }
})()

window.printnode = PrintNode
