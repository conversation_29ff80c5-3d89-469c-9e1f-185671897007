{
  "editor.tabSize": 2,
  "eslint.format.enable": true,
  "eslint.codeActionsOnSave.mode": "all",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "editor.defaultFormatter": "dbaeumer.vscode-eslint",
  "vetur.experimental.templateInterpolationService": false,
  "nuxt.isNuxtApp": true,
  "[vue]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
    // "editor.defaultFormatter": "octref.vetur"
  },
  "[javascript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[html]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "i18n-ally.displayLanguage": "th-TH",
  "i18n-ally.localesPaths": [
    "lang"
  ],
  "i18n-ally.dirStructure": "auto",
  "i18n-ally.enabledFrameworks": [
    "vue"
  ],
  "i18n-ally.keystyle": "flat",
  "i18n-ally.extract.ignoredByFiles": {
    "components/SettingDialog.vue": [
      "Dobybot Videos (${this.gapiUser.email})",
      "Packing Videos"
    ],
    "pages/record/index.vue": [
      "Dobybot.com - Record",
      "Unauthorized"
    ],
    "i18n-ally.keystyle": "flat",
    "i18n-ally.extract.ignoredByFiles": {
      "components/SettingDialog.vue": [
        "Dobybot Videos (${this.gapiUser.email})",
        "Packing Videos"
      ],
      "pages/record/index.vue": [
        "Dobybot.com - Record",
        "Unauthorized"
      ],
      "components/layout/app-bar/AppBarUserMenu.vue": [
        "CHANGE COMPANY"
      ]
    },
    "i18n-ally.sourceLanguage": "th-TH",
    "vue.codeActions.enabled": false,
    "[typescript]": {
      "editor.defaultFormatter": "dbaeumer.vscode-eslint"
    },
    "[jsonc]": {
      "editor.defaultFormatter": "vscode.json-language-features"
    },
    "components/layout/app-bar/AppBarUserMenu.vue": [
      "CHANGE COMPANY"
    ]
  },
  "i18n-ally.sourceLanguage": "th-TH",
  "[typescript]": {
    "editor.defaultFormatter": "dbaeumer.vscode-eslint"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "cSpell.words": [
    "dbbc",
    "Dobybot"
  ]
}