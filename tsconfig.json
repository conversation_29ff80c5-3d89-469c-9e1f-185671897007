{"compilerOptions": {"target": "ES2018", "module": "ESNext", "moduleResolution": "Node", "lib": ["ESNext", "ESNext.AsyncIterable", "DOM"], "esModuleInterop": true, "allowJs": true, "noImplicitAny": false, "sourceMap": true, "strict": true, "noEmit": true, "experimentalDecorators": true, "jsx": "preserve", "baseUrl": ".", "paths": {"~/*": ["./*"], "@/*": ["./*"]}, "types": ["@types/node", "@nuxt/types", "@nuxtjs/i18n", "@nuxtjs/axios", "@nuxtjs/auth-next", "<PERSON>i", "gapi.auth2", "gapi.client.drive"], "typeRoots": ["./types"]}, "exclude": ["node_modules", ".nuxt", "dist"]}