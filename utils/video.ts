import { createFFmpeg, fetchFile, FFmpeg } from '@ffmpeg/ffmpeg'
import { PickItem } from '~/models'

interface transcodeMP4Options {
  ffmpeg?: FFmpeg | null,
  speed?: '1x'|'2x'
}
export async function transcodeMP4 (blob: Blob, opt?: transcodeMP4Options) {
  if (!opt) { opt = {} }
  const speed = opt.speed || '1x'
  if (!window.ffmpeg) {
    window.ffmpeg = createFFmpeg({
      log: process.env.NODE_ENV === 'development',
      corePath: '/static/js/@ffmpeg/core@0.11.0/ffmpeg-core.js'
    })
    await window.ffmpeg.load()
  }

  const ffmpeg = window.ffmpeg
  let err = null as any | null

  try {
    ffmpeg.FS('writeFile', 'input.webm', await fetchFile(blob))
    await ffmpeg.run('-i', 'input.webm', '-movflags', 'faststart', '-c:v', 'copy', 'output1.mp4')

    if (speed === '1x') {
      const data = ffmpeg.FS('readFile', 'output1.mp4')

      ffmpeg.FS('unlink', 'input.webm')
      ffmpeg.FS('unlink', 'output1.mp4')
      // try { ffmpeg.exit() } catch (error) { }
      // window.ffmpeg = null
      return new Blob([data.buffer], { type: 'video/mp4' })
    }

    if (speed === '2x') {
      await ffmpeg.run('-itsscale', '0.5', '-i', 'output1.mp4', '-c', 'copy', '-an', 'output2.mp4')
      const data = ffmpeg.FS('readFile', 'output2.mp4')

      ffmpeg.FS('unlink', 'input.webm')
      ffmpeg.FS('unlink', 'output1.mp4')
      ffmpeg.FS('unlink', 'output2.mp4')
      // try { ffmpeg.exit() } catch (error) {}
      // window.ffmpeg = null
      return new Blob([data.buffer], { type: 'video/mp4' })
    }
  } catch (error) {
    console.error(error)
    err = error
  }

  try { ffmpeg.FS('unlink', 'input.webm') } catch (error) {}
  try { ffmpeg.FS('unlink', 'output1.mp4') } catch (error) {}
  try { ffmpeg.FS('unlink', 'output2.mp4') } catch (error) {}
  // try { ffmpeg.exit() } catch (error) {}
  if (err) {
    throw new Error(err)
  } else {
    throw new Error('Invalid speed settings')
  }
}

/**
 * EBML.js copyrights goes to: https://github.com/legokichi/ts-ebml
 *
 * @example
 * getSeekableBlob(blob or file, callback);
 */
export function getSeekableBlob (inputBlob: Blob, callback: Function) {
  // @ts-ignore
  const EBML = window.EBML
  if (typeof EBML === 'undefined') {
    throw new TypeError('Please link: https://cdn.webrtc-experiment.com/EBML.js')
  }

  const reader = new EBML.Reader()
  const decoder = new EBML.Decoder()
  const tools = EBML.tools
  const fileReader = new FileReader()

  fileReader.onload = function () {
    let ebmlElms = decoder.decode(this.result)
    const validEmlType = ['m', 'u', 'i', 'f', 's', '8', 'b', 'd'] // This is from elm type of the lib
    ebmlElms = ebmlElms?.filter(elm => validEmlType.includes(elm.type))
    ebmlElms.forEach(function (element) {
      reader.read(element)
    })
    reader.stop()
    const refinedMetadataBuf = tools.makeMetadataSeekable(reader.metadatas, reader.duration, reader.cues)
    // @ts-ignore
    const body = this.result.slice(reader.metadataSize)
    const newBlob = new Blob([refinedMetadataBuf, body], {
      type: 'video/webm'
    })
    callback(newBlob)
  }
  fileReader.readAsArrayBuffer(inputBlob)
}

export interface OverlayData {
  datetime: string | null
  order_number: string
  username: string
  scanned_item: string | null
  weight: string | null
}

export interface GetOverlayDataFunction {
  (): OverlayData
}

export function formatWeight (weight_in_grams: number | null, weight_unit?: 'g'| 'kg' | 'auto') {
  if (!weight_unit) {
    weight_unit = 'auto'
  }

  if (weight_in_grams === null) {
    return 'N/A'
  }

  if (weight_unit === 'auto') {
    if (weight_in_grams > 1000) {
      return (weight_in_grams / 1000).toFixed(2) + 'kg'
    } else {
      return weight_in_grams.toFixed(1) + 'g'
    }
  }

  if (weight_unit === 'g') {
    return weight_in_grams.toFixed(1) + 'g'
  }

  if (weight_unit === 'kg') {
    return (weight_in_grams / 1000).toFixed(2) + 'kg'
  }

  throw new Error('invalid weight_unit: ' + weight_unit)
}

export function formatScannedText (scanned_item: PickItem) {
  const max_length = 55
  const text = `${scanned_item.sku}: ${scanned_item.name}`
  if (text.length > max_length) {
    return text.slice(0, max_length) + '...'
  }
  return text
}

const SCALING_FACTOR = {
  '1280x720': 1,
  '640x360': 0.5
}
export function insertOverlayToCameraStream (stream: MediaStream, getDataToDisplay: GetOverlayDataFunction) {
  // @ts-ignore
  stream.onRender = function (ctx: CanvasRenderingContext2D, x: number, y: number, width: number, height: number) {
    const { datetime, weight, order_number, username, scanned_item } = getDataToDisplay()
    const s = SCALING_FACTOR[`${width}x${height}`] || 1

    // Top Bar
    if (datetime && username && order_number) {
      const background_height = 40 * s
      const font_size = 30 * s

      ctx.strokeStyle = 'rgba(0,0,0,0.5)'
      ctx.fillStyle = 'rgba(0,0,0,0.5)'
      ctx.fillRect(0, 0, width, background_height)

      ctx.font = `${font_size}px Helvetica`
      ctx.strokeStyle = 'white'
      ctx.fillStyle = 'white'
      ctx.fillText(datetime, 0, font_size)
      ctx.fillText(username, width / 2 - ctx.measureText(username).width / 2, font_size)
      ctx.fillText(order_number, width - ctx.measureText(order_number).width, font_size)
    }

    if (weight !== null) {
      const font_size = 120 * s
      ctx.font = `${font_size}px Helvetica`
      ctx.strokeStyle = 'white'
      ctx.fillStyle = 'black'
      ctx.lineWidth = 10 * s
      const x = width - ctx.measureText(weight).width - (10 * s)
      const y = height - (40 * s)
      ctx.strokeText(weight, x, y)
      ctx.fillText(weight, x, y)
    }

    if (scanned_item !== null) {
      const background_height = 40 * s
      const font_size = 30 * s
      ctx.font = `${font_size}px Helvetica`

      const textMeasurement = ctx.measureText(scanned_item)
      ctx.strokeStyle = 'rgba(0,0,0,0.5)'
      ctx.fillStyle = 'rgba(0,0,0,0.5)'
      ctx.fillRect(20, height - (60 * s), textMeasurement.width + 20, background_height)

      ctx.strokeStyle = 'white'
      ctx.fillStyle = 'white'
      ctx.fillText(scanned_item, 30, height - (30 * s))
    }
  }
}
