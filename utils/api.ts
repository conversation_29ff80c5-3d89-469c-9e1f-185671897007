import { NuxtAxiosInstance } from '@nuxtjs/axios'
import { VDataTableOption } from '~/models'

let $axios: NuxtAxiosInstance

export function getBackendHost () {
  const hostname = window.location.hostname
  if (hostname === 'localhost') {
    return 'http://localhost:8000'
  } else if (hostname === 'dobybot-ui-service-yuo4mnnlaa-as.a.run.app') {
    return 'https://dobybot-app-service-yuo4mnnlaa-as.a.run.app'
  } else if (hostname === 'dobybot.com' || hostname === 'cloud.dobybot.com') {
    return 'https://api.dobybot.com'
  } else if (hostname === 'pim.dobybot.com') {
    return 'https://api-pim.dobybot.com'
  } else if (hostname === 'uat.dobybot.com') {
    return 'https://api-uat.dobybot.com'
  } else if (hostname === 'staging.dobybot.com') {
    return 'https://api-staging.dobybot.com'
  } else {
    throw new Error(`Invalid hostname '${hostname}', please add this hostname to plugin/axios.ts`)
  }
}

export function getReportServiceHost () {
  const LOCALHOST_REPORT_SERVICE = 'http://localhost:8000'
  const STAGING_REPORT_SERVICE = 'https://api-staging.dobybot.com'
  const UAT_REPORT_SERVICE = 'https://api-uat.dobybot.com'
  const DOBYOT_REPORT_SERVICE = 'https://dobybot-report-service-yuo4mnnlaa-as.a.run.app'
  const PIMRYPIE_REPORT_SERVICE = 'https://primrypie-dobybot-report-service-yuo4mnnlaa-as.a.run.app'

  switch (window.location.hostname) {
    case 'localhost':
      return LOCALHOST_REPORT_SERVICE
    case 'staging.dobybot.com':
      return STAGING_REPORT_SERVICE
    case 'uat.dobybot.com':
      return UAT_REPORT_SERVICE
    case 'dobybot.com':
      return DOBYOT_REPORT_SERVICE
    case 'cloud.dobybot.com':
      return DOBYOT_REPORT_SERVICE
    case 'pim.dobybot.com':
      return PIMRYPIE_REPORT_SERVICE
    default:
      throw new Error('Invalid hostname')
  }
}

export function initializeAxios (axiosInstance: NuxtAxiosInstance) {
  $axios = axiosInstance
}

export function getDynamicRESTPagination (opt: VDataTableOption) {
  return {
    page: opt.page,
    per_page: opt.itemsPerPage
  }
}

export function getDynamicRESTSorting (opt: VDataTableOption) {
  if (opt.sortBy.length === 0) {
    return {}
  }

  return {
    'sort[]': opt.sortDesc[0] ? '-' + opt.sortBy : opt.sortBy
  }
}

export { $axios }
