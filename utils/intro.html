<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <style>
      div.square_box {
          background-color: black;
          opacity: 0.5;
          position: absolute;
      }
    </style>
  </head>

  <body>
    <button
      id="btn1"
      onclick="outSideSquareBoxById('btn1')"
      style="height: 200px; width: 360px; margin-top:0px; margin-left: 100px;"
    >
      Big Button
    </button>
    <button
      id="btn2"
      onclick="outSideSquareBoxById('btn2')"
      style="height: 200px; width: 360px; margin-top: 00px; margin-left: 200px;"
    >
      Big Button
    </button>
    <button
      id="btn3"
      onclick="outSideSquareBoxById('btn3')"
      style="height: 200px; width: 360px; margin-top: 100px; margin-left: 500px;"
    >
      <PERSON>
    </button>
    <label for="cars">Choose a car:</label>

    <select name="cars" id="cars" onclick="outSideSquareBoxById('cars')">
      <option value="volvo">Volvo</option>
      <option value="saab">Saab</option>
      <option value="mercedes">Mercedes</option>
      <option value="audi">Audi</option>
    </select>

    <script>
        let selected_id = null;

      function outSideSquareBox(top_left, bottom_right) {
          let x1 = top_left[0];
          let y1 = top_left[1];
          let x2 = bottom_right[0];
          let y2 = bottom_right[1];
          let width = x2 - x1;
          let height = y2 - y1;
          let screen_width = window.innerWidth;
          let screen_height = window.innerHeight;
          // change this to element.createElement
          let top_square = document.createElement("div");
          top_square.classList.add("square_box");
          top_square.style.top = "0px";
          top_square.style.left = "0px";
          top_square.style.width = `${screen_width}px`;
          top_square.style.height = `${y1}px`;
          top_square.onclick = clearSquareBox;
          document.body.appendChild(top_square);

          let left_square = document.createElement("div");
          left_square.classList.add("square_box");
          left_square.style.top = `${y1}px`;
          left_square.style.left = "0px";
          left_square.style.width = `${x1}px`;
          left_square.style.height = `${height}px`;
          left_square.onclick = clearSquareBox;
          document.body.appendChild(left_square);

          let right_square = document.createElement("div");
          right_square.classList.add("square_box");
          right_square.style.top = `${y1}px`;
          right_square.style.left = `${x2}px`;
          right_square.style.width = `${screen_width - x2}px`;
          right_square.style.height = `${height}px`;
          right_square.onclick = clearSquareBox;
          document.body.appendChild(right_square);

          let bottom_square = document.createElement("div");
          bottom_square.classList.add("square_box");
          bottom_square.style.top = `${y2}px`;
          bottom_square.style.left = "0px";
          bottom_square.style.width = `${screen_width}px`;
          bottom_square.style.height = `${screen_height - y2}px`;
          bottom_square.onclick = clearSquareBox;
          document.body.appendChild(bottom_square);
      }

      // create function that get id of element and call outSideSquareBox function
      function outSideSquareBoxById(id) {
            selected_id = id;
          let element = document.getElementById(id);
          let element_position = element.getBoundingClientRect();
          console.log(element_position)
          let top_left = [element_position.x, element_position.y];
          let bottom_right = [element_position.x + element_position.width, element_position.y + element_position.height];
          outSideSquareBox(top_left, bottom_right);
      }

      // if click on outside of square box then clear square box
      function clearSquareBox() {
          console.log("clear square box")
          let square_box = document.querySelectorAll(".square_box");
          for (let i = 0; i < square_box.length; i++) {
              square_box[i].remove();
          }
          square_box = document.querySelectorAll(".square_box");
          console.log(square_box)
      }

      // add window on resize event
      window.addEventListener("resize", function () {
          clearSquareBox();
          outSideSquareBoxById(selected_id);
      })
    </script>
  </body>
</html>
