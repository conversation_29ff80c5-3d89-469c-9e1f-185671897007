/**
 * Browser version checking utility
 * Checks if the current browser meets minimum version requirements
 */

export interface BrowserInfo {
  name: string;
  version: number;
  isSupported: boolean;
  minVersion?: number | string;
}

// Minimum required versions for each browser
export const MIN_VERSIONS = {
  Chrome: 131,
  Firefox: 133,
  Safari: 17.5,
  Edge: 131
}

/**
 * Parse user agent string to get browser name and version
 */
export function getBrowserInfo (): BrowserInfo {
  const ua = navigator.userAgent
  let browserName = ''
  let browserVersion = 0

  // Chrome
  const chromeMatch = ua.match(/Chrome\/(\d+\.\d+)/)
  if (chromeMatch && !ua.includes('Edg/')) {
    browserName = 'Chrome'
    browserVersion = parseFloat(chromeMatch[1])
  }

  // Edge (Chromium-based)
  const edgeMatch = ua.match(/Edg\/(\d+\.\d+)/)
  if (edgeMatch) {
    browserName = 'Edge'
    browserVersion = parseFloat(edgeMatch[1])
  }

  // Firefox
  const firefoxMatch = ua.match(/Firefox\/(\d+\.\d+)/)
  if (firefoxMatch) {
    browserName = 'Firefox'
    browserVersion = parseFloat(firefoxMatch[1])
  }

  // Safari
  const safariMatch = ua.match(/Version\/(\d+\.\d+).*Safari/)
  if (safariMatch && !chromeMatch) {
    browserName = 'Safari'
    browserVersion = parseFloat(safariMatch[1])
  }

  // Check if browser is supported
  const minVersion = MIN_VERSIONS[browserName] || 0
  const isSupported = browserVersion >= minVersion

  return {
    name: browserName,
    version: browserVersion,
    isSupported,
    minVersion
  }
}

/**
 * Check if the current browser is supported
 */
export function isBrowserSupported (): boolean {
  const browserInfo = getBrowserInfo()
  return browserInfo.isSupported
}

/**
 * Get browser support message
 */
export function getBrowserSupportMessage (): BrowserInfo {
  return getBrowserInfo()
}
