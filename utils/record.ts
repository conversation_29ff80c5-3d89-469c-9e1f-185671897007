import { sumBy } from 'lodash'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>og } from '~/models'

export function getDiffLogs (scan_logs: ScanLog[], order_items: PickItem[]): DiffLog[] | null {
  const diff_logs: DiffLog[] = []

  for (const order_item of order_items) {
    const scans = scan_logs.filter(x => x.item.key === order_item.key)
    const total_scan = sumBy(scans, 'amount')

    if (total_scan !== order_item.number) {
      diff_logs.push({
        sku: order_item.sku,
        name: order_item.name,
        diff: total_scan - order_item.number,
        total: order_item.number,
        packed: total_scan
      })
    }
  }

  if (diff_logs.length === 0) {
    return null
  }

  return diff_logs
}
