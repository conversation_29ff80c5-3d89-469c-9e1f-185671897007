import { PrintnodeWebsocket } from '~/models'

export function setupPrintnodeWebsocket (printnode_apikey: string): Promise<PrintnodeWebsocket> {
  return new Promise((resolve, reject) => {
    const socket = new window.printnode.WebSocket({ apiKey: printnode_apikey })

    socket.subscribe('error', (...args) => {
      console.error(...args)
    })

    socket.subscribe('authenticate', (auth: any) => {
      if (auth.error) {
        reject(auth.error)
        alert('Failed to authenticate websocket' + '\n' + 'Operation failed with error PNWS0x0001')
      }
      resolve(socket)
    })
  })
}
