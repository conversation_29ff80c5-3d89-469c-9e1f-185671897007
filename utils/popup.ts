
/**
 * Open popup window centered of the screen and poll for close event
 *
 * @param url
 * @param width
 * @param height
 * @param callback Callback function to be called when popup window is closed
 */
export function openPopupWindow (url: string, width: number, height: number, callback: Function) {
  const top = screen.height / 2 - height / 2
  const left = screen.width / 2 - width / 2
  const windowFeatures = `left=${left},top=${top},width=${width},height=${height}`
  const handle = window.open(url, '', windowFeatures)

  if (callback && handle) {
    const pollTimer = setInterval(() => {
      if (handle.closed) {
        callback()
        clearInterval(pollTimer)
      }
    }, 1000)
  }
}
