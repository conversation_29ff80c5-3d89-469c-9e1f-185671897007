export const wait = (ms:number) => new Promise(resolve => setTimeout(resolve, ms))

export const waitUntil = async (name: string, condition: () => boolean, maxTimeout: number = 30_000) => {
  console.log('[wait] waitUntil', name)
  const start = Date.now()

  while (!condition()) {
    if (Date.now() - start > maxTimeout) {
      throw new Error('[wait] Timeout waiting for ' + name)
    }
    await wait(250)
  }

  console.log('[wait] waitUntil', name, 'OK')
}
