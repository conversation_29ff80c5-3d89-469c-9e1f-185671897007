export default class DobybotSocketClient {
  constructor (port = 8181) {
    this.socket = new WebSocket(`ws://localhost:${port}`)
    this.handlers = {}

    this.socket.onopen = () => {
      console.log('Connected to dobybot server')
      this.onopen()
    }
    this.socket.onerror = (error) => {
      console.error('Error:', error)
      this.onerror(error)
    }
    this.socket.onmessage = (event) => {
      console.log('Message from dobybot server:', event.data)
      const data = JSON.parse(event.data)
      const cmd = data.cmd

      if (this.handlers[cmd]) {
        this.handlers[cmd](data)
        delete this.handlers[cmd]
      }
    }
  }

  onopen () {
    // override this method
  }

  // eslint-disable-next-line handle-callback-err, @typescript-eslint/no-unused-vars
  onerror (error) {
    // override this method
  }

  registerHandlerOnce (event, handler) {
    this.handlers[event] = handler
  }

  startRecord (order_number) {
    const payload = JSON.stringify({
      cmd: 'start',
      order_number
    })

    return new Promise((resolve) => {
      this.socket.send(payload)

      this.registerHandlerOnce('start/reply', (data) => {
        resolve(data)
      })
    })
  }

  stopRecord () {
    const payload = JSON.stringify({
      cmd: 'stop'
    })

    return new Promise((resolve) => {
      this.socket.send(payload)

      this.registerHandlerOnce('stop/reply', (data) => {
        resolve(data)
      })
    })
  }

  cancelRecord () {
    const payload = JSON.stringify({
      cmd: 'cancel'
    })

    return new Promise((resolve) => {
      this.socket.send(payload)

      this.registerHandlerOnce('cancel/reply', (data) => {
        resolve(data)
      })
    })
  }

  stopStartRecord (order_number) {
    const payload = JSON.stringify({
      cmd: 'stop-start',
      order_number
    })

    return new Promise((resolve) => {
      this.socket.send(payload)

      this.registerHandlerOnce('stop-start/reply', (data) => {
        resolve(data)
      })
    })
  }
}
