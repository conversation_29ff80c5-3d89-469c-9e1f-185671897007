const originalConsoleLog = console.log

if (process.env.NODE_ENV === 'production' || true) {
  console.log = function () {}
  console.info = function () {}
  console.debug = function () {}
}

// Simple PHlog function
export function PRODlog (event_name:string, ...args:any[]) {
  originalConsoleLog('$P >', event_name, args.length === 0 ? '' : '::', ...args)

  // if (process.env.NODE_ENV === 'production') {
  // }
}
