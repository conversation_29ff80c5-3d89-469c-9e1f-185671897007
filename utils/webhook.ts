export function webhookConfigToForm (config: any) {
  const url = config ? config.url : ''
  const method = config ? config.method : ''
  const headers = config ? config.headers : {}

  const result = {} as any
  result.url = url
  result.method = method
  result.headers = []
  for (const key in headers) {
    result.headers.push({ key, value: headers[key] })
  }

  return result
}

export function webhookFormToConfig (form: any) {
  const result = {} as any
  result.url = form.url
  result.method = form.method
  result.headers = {} as any

  for (const header of form.headers) {
    if (!header.key) {
      continue
    }
    result.headers[header.key] = header.value
  }

  return result
}
