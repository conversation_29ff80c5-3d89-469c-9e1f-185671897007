import moment from 'moment'

export function getCutOffDate (documentDate: moment.Moment, cutOffDay: number): moment.Moment {
  if (!Number.isInteger(cutOffDay) || cutOffDay < 1 || cutOffDay > 15) {
    throw new Error('cutOffDay must be an integer between 1 and 15')
  }

  documentDate = moment(documentDate)

  const nextMonth = documentDate.clone().add(1, 'month').startOf('month')
  const cutOffDate = nextMonth.clone().date(cutOffDay)

  return cutOffDate
}
