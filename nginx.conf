# upstream django {
#     server packing_server:8000;
# }
server {
    listen 80;
    server_tokens off;

    # Public Pages
    # Allow all iframe & media
    location ~ /(receipt|video-viewer) {
        root   /app/client/dist/;
        index  index.html;
        try_files $uri $uri/ /index.html;

        # Security Headers
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
        add_header X-Content-Type-Options "nosniff";
        add_header X-Frame-Options "SAMEORIGIN";
    }

    location ~ /(dobybot-public-bucket|dobybot-test-public) {
        proxy_pass https://storage.googleapis.com;
    }

    # Wasm Pages
    location /record {
        root   /app/client/dist/;
        index  index.html;
        try_files $uri $uri/ /index.html;

        # Security Headers
        add_header Content-Security-Policy "default-src 'self' blob: 'unsafe-inline' 'unsafe-eval' *.dobybot.com dobybot-ui-service-yuo4mnnlaa-as.a.run.app dobybot-app-service-yuo4mnnlaa-as.a.run.app dobybot-app-service-tw-128294395128.asia-east1.run.app cdn.jsdelivr.net unpkg.com wss://mqtt.dobybot.com:8083 ws://localhost:8181 wss://api.printnode.com google.com *.google.com googleapis.com *.googleapis.com *.googletagmanager.com googletagmanager.com gstatic.com *.gstatic.com google-analytics.com *.sentry.io stats.g.doubleclick.net ssl.google-analytics.com www.pagespeed-mod.com dobybot-report-service-yuo4mnnlaa-as.a.run.app *.logr-ingest.com static.cloudflareinsights.com *.i.posthog.com; img-src * blob: *.i.posthog.com; frame-ancestors 'self';";
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-Content-Type-Options "nosniff";

        # Security Headers
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Permissions-Policy "camera=(self), microphone=(self)" always;
        add_header Cross-Origin-Resource-Policy "cross-origin" always;
        # add_header Cross-Origin-Opener-Policy same-origin;
        # add_header Cross-Origin-Embedder-Policy credentialless;
    }

    location ~ ^/(report-v2|_nuxt2) {
        proxy_pass https://dobybot-report-ui-128294395128.asia-southeast1.run.app;
        # proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Cross-Origin-Resource-Policy "cross-origin" always;
        add_header Cross-Origin-Opener-Policy same-origin;
        add_header Cross-Origin-Embedder-Policy require-corp;
    }

    # Normal Pages
    location / {
        root   /app/client/dist/;
        index  index.html;
        try_files $uri $uri/ /index.html;


        # Security Headers
        add_header Content-Security-Policy "default-src 'self' blob: 'unsafe-inline' 'unsafe-eval' *.dobybot.com dobybot-ui-service-yuo4mnnlaa-as.a.run.app dobybot-app-service-yuo4mnnlaa-as.a.run.app dobybot-app-service-tw-128294395128.asia-east1.run.app cdn.jsdelivr.net unpkg.com wss://mqtt.dobybot.com:8083 ws://localhost:8181 wss://api.printnode.com google.com *.google.com googleapis.com *.googleapis.com *.googletagmanager.com googletagmanager.com gstatic.com *.gstatic.com google-analytics.com *.sentry.io stats.g.doubleclick.net ssl.google-analytics.com www.pagespeed-mod.com dobybot-report-service-yuo4mnnlaa-as.a.run.app *.logr-ingest.com static.cloudflareinsights.com *.i.posthog.com; img-src * blob: *.i.posthog.com; frame-ancestors 'self';";
        add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
        add_header X-Content-Type-Options "nosniff";
        add_header X-Frame-Options "SAMEORIGIN";
        # improve sosecure
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Permissions-Policy "camera=(self), microphone=(self)" always;
        add_header Cross-Origin-Resource-Policy "same-site" always;
        add_header Cross-Origin-Opener-Policy same-origin;
        add_header Cross-Origin-Embedder-Policy credentialless;
    }
}
