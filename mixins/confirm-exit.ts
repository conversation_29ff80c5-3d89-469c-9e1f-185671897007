import Vue from 'vue'

export default Vue.extend({

  async beforeRouteLeave (_, __, next) {
    if (this.hasUnsavedChanges()) {
      const exit = await this.confirmExit()
      if (!exit) {
        return
      }
    }

    window.removeEventListener('beforeunload', this.onbeforeunload)
    return next()
  },

  mounted () {
    this.hasUnsavedChanges()
    window.addEventListener('beforeunload', this.onbeforeunload)
  },

  methods: {
    async confirmExit (): Promise<boolean> {
      return await this.$confirm({
        text: 'You have unsaved changes, confirm exit?'
      })
    },

    onbeforeunload (e: BeforeUnloadEvent) {
      if (this.hasUnsavedChanges()) {
        // Show confirm dialog
        e.preventDefault()
        e.returnValue = ''
      }
    },

    hasUnsavedChanges (): boolean {
      throw new Error('You are using ConfirmExit Mixix. Please override method hasUnsavedChanges()')
    }
  }
})
