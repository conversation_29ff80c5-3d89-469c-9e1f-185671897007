
import Vue from 'vue'
import { LicenseAgreement, LoggedInUser } from '~/models'

export interface IEulaMixin {
  displayLicenseAgreementDialog (): Promise<void>
}

export default Vue.extend({
  methods: {
    async displayLicenseAgreementDialog () {
      const user = this.$auth.user as never as LoggedInUser
      const la = await this.getActiveLicenseAgreement()

      if (!la) {
        return // no license agreement found
      }

      if (user.accepted_eula === la.id) {
        return // already accepted
      }

      this.showLicenseAgreementPopup(la)
    },

    showLicenseAgreementPopup (agreement: LicenseAgreement) {
      this.$store.dispatch('alert/show', {
        title: agreement.title,
        text: agreement.text,
        theme: 'info',
        buttons: [
          {
            text: 'ยอมรับข้อตกลง และ ดำเนินการต่อ',
            color: 'primary',
            onclick: () => {
              try {
                this.acceptLicenseAgreement(agreement)
                this.$store.dispatch('alert/hide')
              } catch (error) {
                console.error(error)
              }
            },
            align: 'center'
          }
        ]
      })
    },

    async acceptLicenseAgreement (agreement: LicenseAgreement) {
      await this.$axios.post(`/api/eula/accept/${agreement.id}/`)
    },

    async getActiveLicenseAgreement (): Promise<LicenseAgreement | undefined> {
      const data = await this.$axios.$get('/api/eula/resource/license-agreements/')
      return data.license_agreements[0]
    }
  }
})
