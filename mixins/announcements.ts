import Vue from 'vue'
import { Announcement } from '~/models'

export interface IAnnouncementMixin {
  displayAnnouncement (): Promise<void>
}

export default Vue.extend({
  methods: {
    async displayAnnouncement () {
      const announcement = await this.getActiveAnnouncement()
      if (announcement) {
        const dismiss = localStorage.getItem('dismissed_announcement')
        if (dismiss === announcement.id.toString()) { return }

        if (announcement.ui_mode === 'popup') {
          this.showAnnouncementPopup(announcement)
        }

        if (announcement.ui_mode === 'banner') {
          this.showAnnouncementBanner(announcement)
        }
      }
    },

    showAnnouncementPopup (announcement: Announcement) {
      this.$store.dispatch('alert/show', {
        title: announcement.title,
        text: announcement.text,
        theme: announcement.theme,
        buttons: [
          {
            text: this.$t('announcement.do-not-show-again'),
            color: 'primary',
            onclick: () => {
              this.$store.dispatch('alert/hide')
              localStorage.setItem('dismissed_announcement', announcement.id.toString())
            },
            align: 'left'
          },
          {
            text: this.$t('announcement.close'),
            color: '',
            onclick: () => {
              this.$store.dispatch('alert/hide')
            },
            align: 'right'
          }
        ]
      })
    },

    showAnnouncementBanner (announcement: Announcement) {
      this.$store.dispatch('banner/show', {
        text: announcement.text,
        theme: announcement.theme,
        buttons: [
          {
            text: this.$t('announcement.do-not-show-again'),
            color: 'primary',
            onclick: () => {
              this.$store.dispatch('banner/hide')
              localStorage.setItem('dismissed_announcement', announcement.id.toString())
            }
          },
          {
            text: this.$t('announcement.close'),
            color: '',
            onclick: () => {
              this.$store.dispatch('banner/hide')
            }
          }
        ]
      })
    },

    async getActiveAnnouncement (): Promise<Announcement> {
      const data = await this.$axios.$get('/api/announcements/resource/announcements/', {
        params: {
          'filter{is_active}': true
        }
      })

      return data.announcements[0]
    }
  }

})
