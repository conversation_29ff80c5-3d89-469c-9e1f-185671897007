import { PickOrder, <PERSON><PERSON><PERSON>rTrackingNo } from '~/models'

interface GenerateTrackinResponse {
  data: PickOrderTrackingNo
  status: string
}

class ShippingModule {
  private axios

  constructor (axios) {
    this.axios = axios
  }

  async generateTracking (pick_order: PickOrder): Promise<GenerateTrackinResponse> {
    let action = ''
    let params = {}
    switch (pick_order.order_json.shippingchannel) {
      case 'DBB_FLASH':
        action = '/api/shipping/flash/create-tracking-number/'
        break

      case 'DBB_THAIPOST_EMS':
        action = '/api/shipping/thaipost/create-tracking-number/'
        params = {
          shipping_channel: 'DBB_THAIPOST_EMS'
        }
        break

      case 'DBB_THAIPOST_REG':
        action = '/api/shipping/thaipost/create-tracking-number/'
        params = {
          shipping_channel: 'DBB_THAIPOST_REG'
        }
        break

      case 'DBB_KERRY':
        action = '/api/shipping/kerry/create-tracking-number/'
        break
    }
    try {
      const po_tracking: PickOrderTrackingNo = await this.axios.$post(action, { pick_order_id: pick_order.id }, { params })

      return { data: po_tracking, status: 'success' }
    } catch (error : any) {
      return { data: error, status: 'error' }
    }
  }
}

export default ShippingModule
