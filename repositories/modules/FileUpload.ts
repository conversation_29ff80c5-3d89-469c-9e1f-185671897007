import { NuxtAxiosInstance } from '@nuxtjs/axios'

class FileUploadModule {
  private axios:NuxtAxiosInstance;

  constructor (axios:NuxtAxiosInstance) {
    this.axios = axios
  }

  async uploadFiles (images: any[], folder:string, is_public:boolean = true): Promise<string[]> {
    // this function will upload the images to the server if the image is file
    // and if url it will just return the url
    const file_urls: string[] = []
    for (const file of images) {
      if (typeof file !== 'string') {
        const formData = new FormData()
        formData.append('file', file)
        formData.append('file_folder', folder)
        formData.append('is_public', is_public.toString())
        const res = await this.axios.post('/api/fileupload/', formData)
        file_urls.push(res.data.file_url)
      } else {
        file_urls.push(file)
      }
    }
    return file_urls
  }

  async deleteFiles (images: string[]): Promise<void> {
    for (const image of images) {
      await this.axios.post('/api/fileupload/delete/', { file_path: image })
    }
  }
}

export default FileUploadModule
