# PostHog Implementation Design Document

## Overview
Redesigning PostHog implementation to focus on debugging customer issues while managing costs and maintaining performance.

## Key Design Decisions

### 1. Page Exclusion Strategy
**Decision**: Exclude pages based on URL patterns
- **Excluded URLs**: 
  - `/experiments/*` - Test pages with potentially sensitive data
  - `/receipt/*` - High-volume, low-debug-value pages
- **Implementation**: URL pattern matching in PostHog configuration
- **Rationale**: Reduces cost while maintaining coverage of critical user flows
- On settings page exclude the api key parts

### 2. Console Log Capture
**Decision**: Capture all console logs without filtering
- **Capture Level**: All levels (PostHog limitation)
- **Filtering**: No pre-filtering (PostHog doesn't support selective log levels)
- **Alternative Solution**: Implement custom console wrapper to categorize logs before they reach PostHog:
  ```javascript
  // Custom logger that adds prefixes for easier PostHog searching
  const logger = {
    error: (msg) => console.error(`[ERROR] ${msg}`),
    critical: (msg) => console.error(`[CRITICAL] ${msg}`),
    debug: (msg) => console.log(`[DEBUG] ${msg}`)
  }
  ```
- **Sensitive Data**: No masking needed (developer commitment to not log sensitive data)

### 3. User Identification
**Decision**: Immediate identification on login with comprehensive user context
- **Timing**: Identify immediately upon successful authentication
- **User Properties**:
  - `username` (primary identifier)
  - `company_id`
  - `company_name` 
  - `package_type` (RECORD_ONLY, FULL_INTEGRATION, etc.)
- **Anonymous Users**: Not tracked (no anonymous access in system)

### 4. Session Recording Sampling
**Decision**: 100% recording with superuser exclusion
- **Sampling Rate**: 100% for all regular users
- **Exclusions**: Superuser sessions not recorded
- **Rationale**: Debugging focus requires complete session capture; superusers likely doing admin tasks

### 5. Performance Considerations
**Decision**: No performance optimizations initially
- **Video Recording**: No PostHog reduction during recording (monitor and adjust if needed)
- **Pause Tracking**: None initially
- **Monitoring**: Watch for performance impact on critical video recording functionality

### 6. Privacy & Compliance
**Decision**: Default masking with company-level controls
- **Input Masking**: Use PostHog default masking for form inputs
- **User Consent**: No consent mechanism (internal business tool)
- **Company Controls**: Add company setting `POSTHOG_ENABLED` to allow per-company opt-out
- **Implementation**: Check company setting before initializing PostHog

### 7. Event Tracking Strategy

#### Automatic Events
- Page views (with exclusions)
- Console logs (all levels)
- Default PostHog events

#### Custom Events via Console Logs
Track business events through structured console logging:
```javascript
// Video recording events
console.log('[TRACK] video_recording_started', { order_number, duration_target })
console.log('[TRACK] video_recording_stopped', { order_number, actual_duration })

// Barcode scanning events  
console.log('[TRACK] barcode_scanned', { barcode, sku, scan_time })
console.log('[TRACK] scan_error', { barcode, error_type })
```

#### Business Metrics Tracking
**Advanced metrics to implement**:
```javascript
// Recording quality metrics
console.log('[METRIC] recording_quality', {
  resolution: '1280x720',
  duration: 45,
  file_size_mb: 12.3,
  scan_accuracy: 0.95, // scanned_items / expected_items
  completion_rate: 1.0 // completed_scans / total_scans
})

// Performance metrics
console.log('[METRIC] performance', {
  page_load_time: 1200,
  video_start_time: 800,
  scan_response_time: 150
})

// Error tracking
console.log('[METRIC] error_occurred', {
  error_type: 'camera_access_denied',
  page: '/record',
  user_action: 'start_recording'
})
```

### 8. Migration from LogRocket
**Decision**: Complete removal of LogRocket
- **Timeline**: Remove LogRocket entirely in favor of PostHog
- **Benefits**: Cost reduction, single analytics platform
- **Risk Mitigation**: Implement PostHog first, validate functionality, then remove LogRocket

## Implementation Plan

### Phase 1: Core Setup
1. Configure PostHog with page exclusions
2. Implement user identification on login
3. Add company-level enable/disable setting

### Phase 2: Enhanced Tracking
1. Implement structured console logging for business events
2. Add business metrics tracking
3. Create PostHog dashboard for key metrics

### Phase 3: Migration
1. Validate PostHog functionality
2. Remove LogRocket dependencies
3. Monitor and optimize

## Configuration Requirements

### Company Settings Addition
```typescript
// Add to company settings interface
POSTHOG_ENABLED: boolean // Default: true
```

### URL Exclusion Patterns
```javascript
const excludedPaths = [
  /^\/experiments\/.*/,
  /^\/receipt\/.*/,
]
```

This design balances comprehensive debugging capabilities with cost management while respecting privacy and performance requirements.
