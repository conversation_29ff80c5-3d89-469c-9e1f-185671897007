{"name": "packing-recorder", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt", "uidev": "sh ./devserver/start.sh", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "lint:js": "eslint --ext .js,.vue --ignore-path .gitignore .", "lint": "yarn lint:js"}, "dependencies": {"@ffmpeg/core": "^0.10.0", "@ffmpeg/ffmpeg": "^0.10.1", "@nuxtjs/auth-next": "5.0.0-1624817847.21691f1", "@nuxtjs/axios": "^5.12.2", "@nuxtjs/dotenv": "^1.4.1", "@nuxtjs/gtm": "^2.4.0", "@nuxtjs/i18n": "^7.2.2", "@nuxtjs/sentry": "^5.1.7", "core-js": "^3.6.5", "dexie": "^3.0.3", "dompurify": "^3.2.5", "file-saver": "^2.0.5", "intro.js": "^7.2.0", "json-editor-vue": "^0.17.3", "jwt-decode": "^3.1.2", "lodash": "^4.17.20", "moment": "^2.29.1", "mqtt": "^4.3.7", "nuxt": "2.17.3", "posthog-js": "^1.248.0", "prismjs": "^1.25.0", "recordrtc": "^5.6.2", "uuid": "^8.3.2", "v-viewer": "^3.0.13", "viewerjs": "^1.11.6", "vue-json-viewer": "2", "vue-prism-editor": "^1.3.0", "vue-qr-reader": "^1.6.2", "vue-qrcode-reader": "3.2.0", "vue-server-renderer": "2.7.16", "vuedraggable": "^2.24.3"}, "devDependencies": {"@nuxt/types": "^2.17.3", "@nuxt/typescript-build": "^2.0.3", "@nuxtjs/eslint-config": "^3.1.0", "@nuxtjs/eslint-config-typescript": "^3.0.0", "@nuxtjs/eslint-module": "^2.0.0", "@nuxtjs/google-analytics": "^2.4.0", "@nuxtjs/pwa": "^3.3.5", "@nuxtjs/vuetify": "^1.12.3", "@types/file-saver": "^2.0.5", "@types/gapi": "^0.0.39", "@types/gapi.auth2": "^0.0.52", "@types/gapi.client.drive": "^3.0.13", "@types/lodash": "^4.14.165", "@types/recordrtc": "^5.6.2", "@types/uuid": "^8.3.4", "@vue/runtime-dom": "^3.4.21", "babel-eslint": "^10.1.0", "eslint": "^7.10.0", "eslint-plugin-nuxt": "^1.0.0", "eslint-plugin-vue": "^7.1.0", "vue-template-compiler": "2.7.16"}, "resolutions": {"webpack-dev-middleware": "5.0.0"}}