<template>
  <v-app>
    <nuxt />

    <snackbar top right />
    <app-alert />
    <loader />
  </v-app>
</template>

<script lang='ts'>
import { mapGetters } from 'vuex'
import Vue from 'vue'
import Snackbar from '~/components/global/Snackbar.vue'
import AppAlert from '~/components/global/AppAlert.vue'
import Loader from '~/components/global/Loader.vue'
export default Vue.extend({
  components: {
    Snackbar,
    AppAlert,
    Loader

  },

  computed: {
    ...mapGetters(['isAuthenticated', 'loggedInUser'])
  },

  watch: {
    loggedInUser: {
      handler (user) {
        if (user) {
          setTimeout(() => {
            this.$store.dispatch('settings/loadFromLocalStorage')
            this.$store.dispatch('settings/loadFromServer')
          }, 200)
        }
      },
      immediate: true
    }
  },

  created () {
    const setup_i18n = localStorage.getItem('setup.i18n')
    if (!setup_i18n) {
      this.$i18n.setLocale('th')
      localStorage.setItem('setup.i18n', '1')
    }
  }
})
</script>
