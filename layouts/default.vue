<template>
  <v-app>
    <app-drawer v-if="$auth.loggedIn" v-model="drawer" />
    <app-bar :user="$auth.user" @toggle-drawer="drawer = !drawer" />

    <!-- Alerts -->
    <app-alert />

    <!-- Main -->
    <v-main>
      <browser-warning-banner />
      <system-update-alert />
      <system-general-alert v-if="$auth.loggedIn" />

      <app-banner />
      <v-container fluid>
        <nuxt />
      </v-container>
    </v-main>

    <snackbar top right />
    <loader />
  </v-app>
</template>

<script lang="ts">
import { mapGetters } from 'vuex'
import Vue from 'vue'
import moment from 'moment'
import Snackbar from '~/components/global/Snackbar.vue'
import Loader from '~/components/global/Loader.vue'
import AppDrawer from '~/components/layout/AppDrawer.vue'
import AppBar from '~/components/layout/app-bar/AppBar.vue'
import SystemUpdateAlert from '~/components/global/SystemUpdateAlert.vue'
import AppAlert from '~/components/global/AppAlert.vue'
import AnnouncementMixin, { IAnnouncementMixin } from '~/mixins/announcements'
import EulaMixin, { IEulaMixin } from '~/mixins/eula'
import AppBanner from '~/components/global/AppBanner.vue'
import SystemGeneralAlert from '~/components/global/SystemGeneralAlert.vue'
import BrowserWarningBanner from '~/components/BrowserWarningBanner.vue'

export default Vue.extend({
  components: {
    Snackbar,
    Loader,
    AppDrawer,
    AppBar,
    SystemUpdateAlert,
    AppAlert,
    AppBanner,
    SystemGeneralAlert,
    BrowserWarningBanner
  },

  mixins: [AnnouncementMixin, EulaMixin],
  middleware: ['is-setup'],

  data () {
    return {
      drawer: false
    }
  },

  computed: {
    ...mapGetters(['isAuthenticated', 'loggedInUser'])
  },

  watch: {
    // $route: {
    //   handler (route) {
    //     const result = this.items.find(x => x.to === route.path)
    //     this.title = result ? result.title : ''
    //   }
    // },
    loggedInUser: {
      handler (user) {
        if (user) {
          setTimeout(() => {
            this.$store.dispatch('settings/loadFromLocalStorage')
            this.$store.dispatch('settings/loadFromServer').then(() => {
            })

            const announcementMixin = this as never as IAnnouncementMixin
            announcementMixin.displayAnnouncement()

            const eulaMixin = this as never as IEulaMixin
            eulaMixin.displayLicenseAgreementDialog()
          }, 200)
        }
      },
      immediate: true
    }
  },

  created () {
    this.$store.dispatch('getVersions')

    const setup_i18n = localStorage.getItem('setup.i18n')
    if (!setup_i18n) {
      this.$i18n.setLocale('th')
      localStorage.setItem('setup.i18n', '1')
    }
  },

  mounted () {
    this.showAlerts()

    const session_id = this.$route.query.session_id
    if (session_id) {
      this.$store.commit('set', { session_id })
    }
  },

  methods: {
    async showAlerts () {
      if (!this.$auth.loggedIn) {
        return
      }

      if (!this.$isCompanyActive()) {
        this.$store.dispatch('alert/showInActiveCompanyAlert')
      }

      const client_time = moment()
      const server_time = moment(await this.$axios.$get('/time/'))
      if (Math.abs(client_time.diff(server_time, 'second')) > 60) {
        this.$alert({
          title: 'กรุณาตรวจสอบการตั้งค่า วันที่/เวลา ในอุปกรณ์ของท่าน',
          text: `
            <div>
              <label class="w-120">SERVER TIME</label>: ${server_time.format('DD/MMM/YYYY HH:mm:ss')}<br>
              <label class="w-120">DEVICE TIME</label>: ${client_time.format('DD/MMM/YYYY HH:mm:ss')}
            </div>
          `
        })
      }

      const user = this.$auth.user as any
      if (user.company.force_redirect && window.location.hostname === 'dobybot.com') {
        this.$alert({
          title: 'กรุณาเปลี่ยนไปใช้ https://cloud.dobybot.com',
          text: 'https://dobybot.com เปลี่ยนไปใช้เป็น https://cloud.dodybot.com แล้ว กรุณากดปุ่มด้านล่างเพื่อเข้าสู่เว็ปที่ถูกต้องโดยอัตโนมัติ',
          theme: 'info',
          buttons: [
            {
              text: 'ไปที่ cloud.dobybot.com',
              color: 'primary',
              onclick: () => {
                window.location.href = 'https://cloud.dobybot.com'
              }
            }
          ]
        })
      }
    }

  }
})
</script>
