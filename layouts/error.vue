<template>
  <v-app dark>
    <div v-if="error.statusCode === 404">
      <h1>{{ pageNotFound }}</h1>
      <div>Page not found</div>
    </div>
    <div v-else-if="error.statusCode === 403" data-test="permission-require">
      <h1>
        {{ noPermissionError }}
      </h1>
      <div>You do not have permission to access this page</div>
    </div>
    <h1 v-else>
      {{ otherError }}
    </h1>
    <NuxtLink to="/">
      Home page
    </NuxtLink>
  </v-app>
</template>

<script>
export default {
  layout: 'empty',
  props: {
    error: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      pageNotFound: '404 Not Found',
      otherError: 'An error occurred',
      noPermissionError: '403 Forbidden'
    }
  },
  head () {
    if (this.error.statusCode === 404) {
      return {
        title: this.pageNotFound
      }
    } else if (this.error.statusCode === 403) {
      return {
        title: this.noPermissionError
      }
    } else {
      return {
        title: this.otherError
      }
    }
  },
  mounted () {
    console.log(this.error)
    if (![404, 403].includes(this.error.statusCode) && this.error.statusMessage) {
      this.otherError = this.error.statusMessage
    }
  }
}
</script>

<style scoped>
h1 {
  font-size: 20px;
}
</style>
