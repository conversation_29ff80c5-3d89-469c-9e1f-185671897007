/// <reference path="../node_modules/@types/gapi/index.d.ts" />

import { Context, Inject, Plugin } from '@nuxt/types/app'

declare module 'vue/types/vue' {
  interface Vue {
    $gapi: typeof window.gapi,
  }
}

// const plugin: Plugin = (context: Context, inject: Inject) => {
//   // const CLIENT_ID = '322290540619-31huvkifdmaagpj7t0fl2et6ll0c74nq.apps.googleusercontent.com'
//   const CLIENT_ID = '1072650018213-2v0obh7hh7ej1kir4g4e7e8bgpqp7043.apps.googleusercontent.com'
//   // const API_KEY = 'AIzaSyB9xAI51q4JJP8yUsmRlC3nxuKy3XJ_0ho'
//   const API_KEY = 'AIzaSyBfHrIc9lpXhJ6xTcRUMXeA9W0t1m-1ojw'

//   const DISCOVERY_DOCS = ['https://www.googleapis.com/discovery/v1/apis/drive/v3/rest']
//   const SCOPES = 'https://www.googleapis.com/auth/drive.file'

//   window.gapi.load('client:auth2', async () => {
//     await window.gapi.client.init({
//       apiKey: API_KEY,
//       clientId: CLIENT_ID,
//       discoveryDocs: DISCOVERY_DOCS,
//       scope: SCOPES
//     })

//     window.gapi.auth2.getAuthInstance().isSignedIn.listen(() => {
//       const isAuth = window.gapi.auth2.getAuthInstance().isSignedIn.get()
//       context.app.store?.commit('gapi/set', { key: 'isAuth', value: isAuth })
//     })
//     const isAuth = window.gapi.auth2.getAuthInstance().isSignedIn.get()
//     context.app.store?.commit('gapi/set', { key: 'isAuth', value: isAuth })
//   })
//   inject('gapi', window.gapi)
// }

const plugin: Plugin = (_: Context, inject: Inject) => {
  // Load GoogleDrive js library
  window.gapi.load('client', async () => {
    console.log('[gapi] init client', window.gapi.client)
    await window.gapi.client.init({})
    console.log('[gapi] load drive')
    await window.gapi.client.load('drive', 'v3')
    console.log('[gapi] ready')
  })
  inject('gapi', window.gapi)
}

export default plugin
