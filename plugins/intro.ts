import { Context, Inject } from '@nuxt/types/app'
import { IntroJs } from 'intro.js/src/intro'

declare module 'vue/types/vue' {
  interface Vue {
    $getIntroStatus: () => boolean
    $runIntro: (intro: IntroJs) => Promise<boolean>
  }
}

function runIntro (intro: IntroJs) {
  return new Promise((resolve) => {
    intro.oncomplete(() => {
      resolve(true)
    }).start()
  })
}

export default (context: Context, inject: Inject) => {
  inject('getIntroStatus', () => {
    return context.store.state.is_intro_running
  })
  inject('runIntro', runIntro)
}
