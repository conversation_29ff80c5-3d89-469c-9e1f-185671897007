import { Context, Inject } from '@nuxt/types/app'

declare module 'vue/types/vue' {
  interface Vue {
    $dobybot_ui_version: () => string
    $dobybot_version: () => string
    $version: () => string
  }
}

export default (ctx: Context, inject: Inject) => {
  inject('dobybot_ui_version', () => ctx.store.state.frontend.dobybot_ui_version)
  inject('dobybot_version', () => ctx.store.state.backend.dobybot_version)
  inject('version', () => {
    return `A${ctx.store.state.frontend.dobybot_ui_version}B${ctx.store.state.backend.dobybot_version}`
  })
}
