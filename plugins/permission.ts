import { Context, Inject } from '@nuxt/types/app'
import { FEATURE_FLAGS, LoggedInUser } from '~/models'

interface hasPerms {
  (...perms: string[]): boolean;
}

interface hasGroup {
  (group: string): boolean;
}

interface isPim {
  (): boolean;
}

interface hasCompanyFeatureFlag {
  (flag: string): boolean;
}

interface isCompanyActive {
  (): boolean;
}
interface middlewareCompanyFeatureFlag {
  (flag:string): boolean;
}

declare module 'vue/types/vue' {
  interface Vue {
    $hasPerms: hasPerms;
    $hasGroup: hasGroup;
    $hasCompanyFeatureFlag: hasCompanyFeatureFlag;
    $middlewareCompanyFeatureFlag: middlewareCompanyFeatureFlag;
    $isPim: isPim;
    $isCompanyActive: isCompanyActive;
  }
}

export default (context: Context, inject: Inject) => {
  inject('hasPerms', (...perms: string[]) => {
    const user: LoggedInUser = context.$auth.user as never
    if (!user) {
      return false
    }
    if (user.is_superuser) {
      return true
    }
    const permissions = [...user.user_permissions, ...user.group_permissions]
    return perms.every((p: any) => permissions.includes(p))
  })

  inject('hasGroup', (role: string) => {
    const user = context.$auth.user
    if (!user) {
      return false
    }
    if (user.is_superuser) {
      return true
    }
    return (user as any).groups.some((x: any) => x.name === role)
  })

  inject('isPim', () => {
    const user: any = context.$auth.user
    if (!user) {
      return false
    }

    const company = user.company
    return company.name.includes('THE PIMRYPIE')
    // const hostname = window.location.hostname
    // if (hostname === 'localhost' || hostname === 'uat.dobybot.com') {
    //   return true
    // }
    // return hostname === 'pim.dobybot.com'
  })

  inject('isCompanyActive', () => {
    const user: any = context.$auth.user
    if (!user) {
      return false
    }
    const company = user.company

    // if (!company.is_active) {
    //   return false
    // }

    // if (company.expire_date !== null && moment().isAfter(company.expire_date)) {
    //   return false
    // }

    // return true

    return company.is_active
  })

  inject('hasCompanyFeatureFlag', (flag:string) => {
    const user: any = context.$auth.user
    if (!user) {
      return false
    }
    const company = user.company

    if (flag === FEATURE_FLAGS.ALL) {
      return true
    }

    if (flag in company.feature_flag) {
      return company.feature_flag[flag]
    }

    if (!(FEATURE_FLAGS.ETAX in company.feature_flag) && flag === FEATURE_FLAGS.ETAX) {
      return false
    }

    return true
  })
}
