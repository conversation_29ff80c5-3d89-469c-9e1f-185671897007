import Dexie from 'dexie'
import Vue from 'vue'
import { DiffLog, ScanLog } from '~/models'

declare module 'vue/types/vue' {
  interface Vue {
    $db: MyDatabase
  }
}

export const STATUS = {
  NOT_UPLOADED: 'NOT_UPLOADED',
  UPLOADING: 'UPLOADING',
  UPLOADED: 'UPLOADED',
  TRANSCODING: 'TRANSCODING',
  QUEUED_FOR_UPLOAD: 'QUEUED_FOR_UPLOAD'
}

export interface IVideo {
  id?: number,
  name: string,
  control_code_log?: string,
  status: 'NOT_UPLOADED' | 'UPLOADING' | 'TRANSCODING' | 'UPLOADED' | 'QUEUED_FOR_UPLOAD',
  remark: string,
  blob: Blob,
  createdAt: string,
  uploadedAt: string,
  shareLink: string,
  resolution: string,
  duration: number, // seconds
  isTranscoded: boolean,
  filetype: 'webm'|'mp4'
  speed: '1x'|'2x'
  drive_id: string | null
  drive_folder_id: string | null
  drive_file_id: string | null
  drive_account: string | null
  is_return: boolean
  upload_attempt: number
  scan_logs: ScanLog[],
  diff_logs: DiffLog[] | null,
  audio: boolean
  weight: number | null
  extra: any[]
  name_edit_count?:number
}

class MyDatabase extends Dexie {
    videos: Dexie.Table<IVideo, number>;

    constructor (databaseName: string) {
      super(databaseName)

      // Version 180
      this
        .version(18)
        .stores({
          videos: '++id,name,status,blob,remark,createdAt,uploadedAt,resolution,duration,filetype,speed,isTranscoded,drive_id,drive_file_id,drive_account,is_return,upload_attempt,scan_logs,audio,weight,diff_logs,extra'
        })
        .upgrade((tx) => {
          return tx.table('videos').toCollection().modify((video: any) => {
            if (video) {
              video.extra = []
            }
          })
        })

      // Version 170
      this
        .version(17)
        .stores({
          videos: '++id,name,status,blob,remark,createdAt,uploadedAt,resolution,duration,filetype,speed,isTranscoded,drive_id,drive_file_id,drive_account,is_return,upload_attempt,scan_logs,audio,weight,diff_logs'
        })
        .upgrade((tx) => {
          return tx.table('videos').toCollection().modify((video: any) => {
            if (video) {
              video.diff_logs = null
            }
          })
        })

      // Version 160
      this
        .version(16)
        .stores({
          videos: '++id,name,status,blob,remark,createdAt,uploadedAt,resolution,duration,filetype,speed,isTranscoded,drive_id,drive_file_id,drive_account,is_return,upload_attempt,scan_logs,audio,weight'
        })
        .upgrade((tx) => {
          return tx.table('videos').toCollection().modify((video: any) => {
            if (video) {
              video.weight = null
            }
          })
        })

      // Version 150
      this
        .version(15)
        .stores({
          videos: '++id,name,status,blob,remark,createdAt,uploadedAt,resolution,duration,filetype,speed,isTranscoded,drive_id,drive_file_id,drive_account,is_return,upload_attempt,scan_logs,audio'
        })
        .upgrade((tx) => {
          return tx.table('videos').toCollection().modify((video: any) => {
            video.audio = false
          })
        })

      // Version 140
      this
        .version(14)
        .stores({
          videos: '++id,name,status,blob,remark,createdAt,uploadedAt,resolution,duration,filetype,speed,isTranscoded,drive_id,drive_file_id,drive_account,is_return,upload_attempt,scan_logs'
        })
        .upgrade((tx) => {
          return tx.table('videos').toCollection().modify((video: any) => {
            video.scan_logs = []
          })
        })

      // Version 130
      this
        .version(13)
        .stores({
          videos: '++id,name,status,blob,remark,createdAt,uploadedAt,resolution,duration,filetype,speed,isTranscoded,drive_id,drive_file_id,drive_account,is_return,upload_attempt'
        })
        .upgrade((tx) => {
          return tx.table('videos').toCollection().modify((video: any) => {
            video.upload_attempt = 1
          })
        })

      // Version 120
      this
        .version(12)
        .stores({
          videos: '++id,name,status,blob,remark,createdAt,uploadedAt,resolution,duration,filetype,speed,isTranscoded,drive_id,drive_file_id,drive_account,is_return'
        })
        .upgrade((tx) => {
          return tx.table('videos').toCollection().modify((video: any) => {
            video.is_return = false
          })
        })

      // Version 110
      this
        .version(11)
        .stores({
          videos: '++id,name,status,blob,remark,createdAt,uploadedAt,resolution,duration,filetype,speed,isTranscoded,drive_id,drive_file_id,drive_account'
        })
        .upgrade((tx) => {
          return tx.table('videos').toCollection().modify((video: any) => {
            video.drive_id = null
            video.drive_file_id = null
            video.drive_file_account = null
          })
        })

      // Version 100
      this
        .version(10)
        .stores({
          videos: '++id,name,status,blob,remark,createdAt,uploadedAt,resolution,duration,filetype,speed,isTranscoded'
        })
        .upgrade((tx) => {
          return tx.table('videos').toCollection().modify((video: any) => {
            video.isTranscoded = true
          })
        })

      // Version 80
      this
        .version(8)
        .stores({
          videos: '++id,name,status,blob,remark,createdAt,uploadedAt,resolution,duration,filetype,speed'
        })
        .upgrade((tx) => {
          return tx.table('videos').toCollection().modify((video: any) => {
            video.filetype = 'wemb'
            video.speed = '1x'
          })
        })

      // this.version(2).stores({
      //   videos: '++id,name,status,blob,remark,createdAt,uploadedAt,resolution,duration'
      // })
      this.videos = this.table('videos')
    }
}

Vue.prototype.$db = new MyDatabase('db')
