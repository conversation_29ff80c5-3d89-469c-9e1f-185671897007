import { Context, Inject } from '@nuxt/types/app'
import moment from 'moment'

const $t = (key: string, ...args: any[]) => window.$nuxt.$options.i18n.t(key, ...args)

export const rules = {
  required: (value: any) => (value !== '' && value !== undefined && value !== null) || $t('rules.required'),
  is_not_blank: (value: any) => (value?.toString().trim() !== '' && value !== undefined && value !== null) || $t('rules.required'),
  required_if (is_require: boolean) {
    if (is_require) {
      return function (value: any) {
        return (value !== '' && value !== undefined && value !== null) || $t('rules.required')
      }
    }

    return function () {
      return true
    }
  },
  sameAs: (target: any) => (value: any) => value === target || $t('rules.confirm-password'),
  datetime_lte: (target: any) => (value: any) => {
    if (!target) { return true }
    target = moment(target).format('YYYY-MM-DD HH:mm')
    return value <= target || $t('must_be_less_than_or_equal', [target])
  },
  datetime_gte: (target: any) => (value: any) => {
    if (!target) { return true }
    target = moment(target).format('YYYY-MM-DD HH:mm')
    return value >= target || $t('must_be_greater_than_or_equal', [target])
  },
  username: (company_suffix: string) => (value: string) => {
    if (value.includes('@')) {
      return $t('rules.username_will_include_at_sign', [company_suffix])
    }
    return true
  },
  password: (value: string) => {
    if (value.length < 8) {
      return $t('rules.password-min-length')
    }
    if (!/(?=.*[A-Z]).*/.test(value)) {
      return $t('rules.password-must-contain-uppercase-letter')
    }
    if (!/(?=.*[a-z]).*/.test(value)) {
      return $t('rules.password-must-contain-lowercase-letter')
    }
    if (!/(?=.*[0-9]).*/.test(value)) {
      return $t('rules.password-must-contain-digit')
    }
    if (!/(?=.*[^a-zA-Z0-9]).*/.test(value)) {
      return $t('rules.password-must-contain-special-character')
    }
    return true
  },

  maxlength: (length: number) => (value: string) => {
    if (!value) {
      return true
    }
    return value.length <= length || $t('rules.maxlength', [length])
  },
  exactLength: (length: number) => (value: string) => {
    if (!value) { return true }
    return value.length === length || $t('rules.exact_length', [length])
  },

  barcode_thai (value: string) {
    return /^[a-zA-Z0-9-+_/#$%&@:.\s=ก-๙]*$/.test(value) || $t('rules.barcode_thai')
  },
  barcode (value: string) {
    return /^[a-zA-Z0-9-+_/#$%&@:*><!.\s=()]*$/.test(value) || $t('rules.barcode')
  },

  email (value: string) {
    if (!value) {
      return true
    }

    const tester = /^[-!#$%&'*+\/0-9=?A-Z^_a-z{|}~](\.?[-!#$%&'*+\/0-9=?A-Z^_a-z`{|}~])*@[a-zA-Z0-9](-*\.?[a-zA-Z0-9])*\.[a-zA-Z](-?[a-zA-Z0-9])+$/

    const validate = function (email: string): boolean | string {
      if (!email) { return $t('rules.email') }
      if (email.length > 254) { return $t('rules.email') }
      if (!tester.test(email)) { return $t('rules.email') }

      const parts = email.split('@')
      if (parts[0].length > 64) { return $t('rules.email') }

      const domainParts = parts[1].split('.')
      if (domainParts.some(part => part.length > 63)) { return $t('rules.email') }

      return true // Email is valid
    }

    return validate(value)
  },

  englishAndNumberOnly (value: string) {
    return /^[a-z0-9]+$/.test(value) || $t('rules.english_and_number_only')
  },

  phoneNumberFormat (value: string) {
    if (!value) { return true }
    const phoneRegex = /^(66\d{9}|0\d{9}|0\d{2}-\d{3}-\d{4}|02\d{7})$/
    return phoneRegex.test(value) || $t('rules.phone_number')
  },

  between (min: number, max: number) {
    return (value: number) => {
      return (value >= min && value <= max) || $t('rules.between', [min, max])
    }
  },

  number_min (min: number) {
    return (value: number) => {
      return value >= min || $t('rules.number_min', [min])
    }
  },
  number_gt (min: number) {
    return (value: number) => {
      return value > min || $t('rules.number_gt', [min])
    }
  },
  includesValue (searchValue: string) {
    return (value: string) => {
      if (!value) {
        return true // Allow empty values (modify as needed)
      }
      return value.includes(searchValue) || $t('rules.includes_value', [searchValue])
    }
  },
  // numberOnlyWithLength: (length: number) => (value: string) => {
  //   if (!value) { return true }

  //   // First check if it's numbers only
  //   if (!/^\d+$/.test(value)) {
  //     return $t('rules.number_only')
  //   }

  //   // Then check length
  //   return value.length === length || $t('rules.exact_length', [length])
  // },
  numberOnlyWithLength: (length: number) => (value: any) => {
    if (!value) { return true }

    // Handle case when value is an object (selected from dropdown)
    const stringValue = typeof value === 'object' ? value.branchCode : value

    // First check if it's numbers only
    if (!/^\d+$/.test(stringValue)) {
      return $t('rules.number_only')
    }

    // Then check length
    return stringValue.length === length || $t('rules.exact_length', [length])
  },
  validateAddressIncludesThaiZipCode: (address: string) => {
    const THAI_ZIP_CODE_PATTERN = /\b\d{5}\b/
    return THAI_ZIP_CODE_PATTERN.test(address) || $t('rules.invalid_thai_zipcode')
  },
  numberIntegerOnly: (value: any) => {
    if (!value) { return true }
    return /^\d+$/.test(value) || $t('rules.integer_only')
  },
  NoSpacesAllowed: (value: any) => {
    if (!value) { return true }
    const hasSpace = / /.test(value)
    return !hasSpace || $t('rules.no_zero_width_space')
  }

}

declare module 'vue/types/vue' {
  interface Vue {
    $rules: typeof rules
  }
}

export default (_: Context, inject: Inject) => {
  // const i18n = context.app.i18n
  inject('rules', rules)
}
