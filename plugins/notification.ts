import { Context, Inject } from '@nuxt/types/app'

interface NotificationPlugin {
  show(title: string, body: string): void
  requestPermission(): Promise<NotificationPermission>
}

declare module 'vue/types/vue' {
  interface Vue {
    $notification: NotificationPlugin
  }
}

export default (_: Context, inject: Inject) => {
  const requestPermission = async () => {
    if (Notification.permission === 'default') {
      const perm = await Notification.requestPermission()
      return perm
    }
    return Notification.permission
  }

  const show = async (title: string, body: string) => {
    if (Notification.permission === 'default') {
      const perm = await Notification.requestPermission()
      if (perm !== 'granted') {
        return
      }
    }

    const icon = 'https://cloud.dobybot.com/logo.png'
    const notification = new Notification(title, { body, icon })
    notification.onclick = () => {
      notification.close()
      window.parent.focus()
    }
  }

  inject('notification', {
    show,
    requestPermission
  })
}
