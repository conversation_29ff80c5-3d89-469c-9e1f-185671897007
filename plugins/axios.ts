import { Context } from '@nuxt/types/app'
import { wait } from '~/utils'
// import { capturePostHogEvent } from '~/utils/posthog'

export default function (context: Context) {
  const hostname = window.location.hostname

  if (process.env.AXIOS_BASE_URL) {
    context.$axios.defaults.baseURL = process.env.AXIOS_BASE_URL
    return
  }

  if (hostname === 'localhost') {
    context.$axios.defaults.baseURL = 'http://localhost:8000'
  } else if (hostname === 'dobybot-ui-service-yuo4mnnlaa-as.a.run.app') {
    context.$axios.defaults.baseURL = 'https://dobybot-app-service-yuo4mnnlaa-as.a.run.app'
  } else if (hostname === 'dobybot.com' || hostname === 'cloud.dobybot.com') {
    context.$axios.defaults.baseURL = 'https://api.dobybot.com'
  } else if (hostname === 'pim.dobybot.com') {
    context.$axios.defaults.baseURL = 'https://api-pim.dobybot.com'
  } else if (hostname === 'uat.dobybot.com') {
    context.$axios.defaults.baseURL = 'https://api-uat.dobybot.com'
  } else if (hostname === 'staging.dobybot.com') {
    context.$axios.defaults.baseURL = 'https://api-staging.dobybot.com'
  } else if (hostname === 'dobybot-ui-service-128294395128.asia-east1.run.app') {
    context.$axios.defaults.baseURL = 'https://dobybot-app-service-tw-128294395128.asia-east1.run.app'
  } else {
    throw new Error(`Invalid hostname '${hostname}', please add this hostname to plugin/axios.ts`)
  }

  // add language header
  context.$axios.interceptors.request.use((config) => {
    const i18n = context.app.i18n as any
    if (i18n.locale) {
      config.headers['Accept-Language'] = i18n.locale
    }
    return config
  })

  // register interceptor
  context.$axios.interceptors.response.use(
    (response) => {
      return response
    },
    async (error) => {
      if (error.response.status === 401) {
        if (error.response.config.url === '/api/users/me/') {
          context.$auth.logout()
          setTimeout(() => {
            window.location.reload()
          }, 1000)
        }

        if (error.response.data.code === 'unrecornized_device_id') {
          while (true) {
            const msg = await context.store.dispatch('alert/prompt', {
              title: 'มีการเข้าสู่ระบบจากอุปกรณ์อื่น',
              text: (
                'ท่านถูกบังคับให้ออกจากระบบเนื่องจากมีการเข้าสู่ระบบจากอุปกรณ์อื่น กรุณาหลีกเลื้ยงการใช้บัญชีผู้ใช้ซ้ำซ้อนกัน<br><br>' +
                'กรุณาพิมพ์คำว่า "<b>ฉันเข้าใจแล้ว</b>" เพื่อปิดหน้าต่างนี้<br><br><br>' +
                'You are logged out because other device have been logged into your account, Please avoid using the same account across multiple device<br><br>' +
                'Please type "<b>Understood</b>" to close this window.'
              ),
              theme: 'error',
              preset: 'text'
            })

            if (msg === 'ฉันเข้าใจแล้ว' || msg === 'Understood') {
              context.$auth.logout()
              break
            }
          }
        } else {
          await wait(1500)
          context.store.dispatch('snackbar/show', { color: 'error', text: error.response.data.detail })
          context.$auth.logout()
        }
      }

      if (error.response.status === 403) {
        context.store.dispatch('snackbar/show', { color: 'error', text: 'ท่านไม่มีสิทธิทำรายการนี้' })
      }
      return Promise.reject(error)
    }
  )
}
