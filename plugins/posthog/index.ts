import { Context, Inject } from '@nuxt/types/app'

import posthog from 'posthog-js'

export default function (_: Context, inject: Inject) {
  // Init PostHog
  posthog.init('phc_O9TgSqQY3CY1hlE6dIUPZ9QEQPbbqbzV82KNujGREMt', {
    api_host: 'https://us.i.posthog.com',
    capture_pageview: true,
    autocapture: false,
    disable_session_recording: true
  })

  // Inject PostHog into the application and make it available via this.$posthog (or app.$posthog)
  inject('posthog', posthog)

  // // Make sure that pageviews are captured with each route change
  // const router = context.app.router
  // if (!router) { return }

  // router.afterEach((to) => {
  //   Vue.nextTick(() => {
  //     /* Note: this might also be a good place to call posthog.register(...) in order to update your properties
  //     on each page view
  //     */
  //     posthog.capture('$pageview', {
  //       $current_url: to.fullPath
  //     })
  //   })
  // })
}
