import { Context, Inject } from '@nuxt/types/app'
import Vue, { DirectiveOptions } from 'vue'
import { LoggedInUser, PACKAGE_FULL_INTEGRATION, PACKAGE_RECORD_ONLY, User } from '~/models'
import { AlertOptions } from '~/store/alert'

function applyReadonlyOverlay (parent: HTMLElement, onclick: Function) {
  // if (parent.style.position !== 'relative') {
  //   throw new Error(
  //     'v-package directive must be apply to element with css property `position: relative;`'
  //   )
  // }
  parent.style.position = 'relative'

  const overlay = document.createElement('div')
  overlay.classList.add('vc-overlay')
  overlay.style.position = 'absolute'
  overlay.style.top = '0'
  overlay.style.left = '0'
  overlay.style.width = '100%'
  overlay.style.height = '100%'
  overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.07)'
  overlay.style.zIndex = '4'
  overlay.style.cursor = 'not-allowed'
  overlay.title = ''

  overlay.onclick = (ev) => {
    ev.preventDefault()
    ev.stopPropagation()
    onclick()
    // store.dispatch('Add Snackbar', {
    //   color: 'info',
    //   message: i18next.t('cannot-edit-in-readonly-mode'),
    // })
  }

  parent.appendChild(overlay)
}

function removeOverlay (parent: HTMLElement) {
  const overlay = parent.querySelector('.vc-overlay')
  if (overlay) {
    overlay.remove()
  }
}

const levels = {
  [PACKAGE_RECORD_ONLY]: 1,
  [PACKAGE_FULL_INTEGRATION]: 2
}
const vPackageDirective: DirectiveOptions = {
  bind (el, binding, vnode) {
    const store = vnode.context?.$store
    const user = store?.$auth.user as LoggedInUser | null

    if (!user || !store) {
      return
    }

    const company = user.company
    const required_package = binding.value
    if (levels[required_package] > levels[company.package]) {
      applyReadonlyOverlay(el, () => {
        const alertOptions: AlertOptions = {
          text: `
            <img src="https://storage.googleapis.com/dobybot-public-bucket/images/dobybot/upgrade_package.png" style="width: 100%;"></img>
            <div style="text-align: center;">
              หรือศึกษาข้อมูลเพิ่มเติมได้ที่ <a href="https://www.dobybot.com/record-sms">www.dobybot.com</a>
            </div>
          `,
          title: 'กรุณาอัพเกรดแพคเกจเพื่อใช้งานระบบนี้'
        }
        store.dispatch('alert/show', alertOptions)
      })
    } else {
      removeOverlay(el)
    }
  }
}

Vue.directive('package', vPackageDirective)

const packages = {
  FULL_INTEGRATION: PACKAGE_FULL_INTEGRATION,
  RECORD_ONLY: PACKAGE_RECORD_ONLY
}
declare module 'vue/types/vue' {
  interface Vue {
    $package: typeof packages
  }
}
export default (_: Context, inject: Inject) => {
  inject('package', packages)
}
