import { Context, Inject } from '@nuxt/types/app'
import moment from 'moment'
import type { Moment } from 'moment'

interface DateTimeFormatFunction {
  (dt: string): string
}

declare module 'vue/types/vue' {
  interface Vue {
    $moment: (...args: any) => Moment,
    $date: DateTimeFormatFunction,
    $datetime: DateTimeFormatFunction,
    $datetime_short: DateTimeFormatFunction
  }
}

export default (_: Context, inject: Inject) => {
  // Inject $hello(msg) in Vue, context and store.
  inject('moment', moment)
  inject('date', (dt: string) => moment(dt).format('DD/MM/YYYY'))
  inject('datetime', (dt: string) => moment(dt).format('DD/MM/YYYY HH:mm'))
  inject('datetime_short', (dt: string) => moment(dt).format('DD/MM/YY HH:mm'))
  inject('time', (dt: string) => moment(dt).format('HH:mm'))
}
