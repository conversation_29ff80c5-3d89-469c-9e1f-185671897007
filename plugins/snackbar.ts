import { Context, Inject } from '@nuxt/types/app'

interface ShowSnackbar {
  (color: 'success' | 'error' | 'info' | 'warning', text: string): void
}

declare module 'vue/types/vue' {
  interface Vue {
    $snackbar: ShowSnackbar
  }
}

export default (context: Context, inject: Inject) => {
  inject('snackbar', (color: string, text: string) => {
    context.store.dispatch('snackbar/hide')
    setTimeout(() => {
      context.store.dispatch('snackbar/show', { color, text })
    }, 250)
  })
}
