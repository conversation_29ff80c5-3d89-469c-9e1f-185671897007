import { Context, Inject } from '@nuxt/types/app'

interface loader {
  (visible: boolean, text?: string): void
}

declare module 'vue/types/vue' {
  interface Vue {
    $loader: loader
  }
}

export default (context: Context, inject: Inject) => {
  inject('loader', (visible: boolean, text?: string) => {
    if (visible) {
      context.store.dispatch('loader/show', text)
    } else {
      context.store.dispatch('loader/hide')
    }
  })
}
