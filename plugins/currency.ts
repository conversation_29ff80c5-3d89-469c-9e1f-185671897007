import { Context, Inject } from '@nuxt/types/app'

interface CurrencyFormatFunction {
  (number: number): string
}

interface DecimalFormatFunction {
  (value: number): number
}

declare module 'vue/types/vue' {
  interface Vue {
    $fmtCurrency: CurrencyFormatFunction,
    $dec: DecimalFormatFunction
  }
}

export default (_: Context, inject: Inject) => {
  // Inject $hello(msg) in Vue, context and store.
  inject('fmtCurrency', (number: number) => {
    const formatter = new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2, // Ensures at least 2 decimal places
      maximumFractionDigits: 2 // Limits to at most 2 decimal places
    })
    return formatter.format(number)
  })
  inject('dec', (value: number) => {
    return Number(value.toFixed(2))
  })
}
