import { Context, Inject } from '@nuxt/types/app'
import { AlertOptions, PromptOptions, ConfirmOptions } from '~/store/alert'

interface ShowAlert {
  (opt: AlertOptions): void
}

interface ShowPrompt {
  (opt: PromptOptions): Promise<string | null>
}

interface ShowConfirm {
  (opt: ConfirmOptions): Promise<boolean>
}

declare module 'vue/types/vue' {
  interface Vue {
    $alert: ShowAlert
    $prompt: ShowPrompt
    $confirm: ShowConfirm
  }
}

export default (context: Context, inject: Inject) => {
  inject('alert', (opt: AlertOptions) => {
    context.store.dispatch('alert/hide')
    setTimeout(() => {
      context.store.dispatch('alert/show', opt)
    }, 250)
  })

  inject('prompt', (opt: PromptOptions) => {
    context.store.dispatch('alert/hide')
    return context.store.dispatch('alert/prompt', opt)
  })

  inject('confirm', (opt: ConfirmOptions) => {
    context.store.dispatch('alert/hide')
    return context.store.dispatch('alert/confirm', opt)
  })
}
