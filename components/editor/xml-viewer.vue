<template>
  <prism-editor :value="value" lang="markup" @input="$emit('input', $event)" />
</template>

<script lang="ts">
import Vue from 'vue'
import PrismEditor from '~/components/editor/PrismEditor.vue'

export default Vue.extend({
  components: {
    PrismEditor
  },
  props: {
    value: {
      type: String,
      required: true
    }
  },
  mounted () {
    this.$emit('input', this.prettifyXml(this.value))
  },
  methods: {
    prettifyXml  (sourceXml: string): string {
      const xmlDoc = new DOMParser().parseFromString(sourceXml, 'application/xml')
      const xsltDoc = new DOMParser().parseFromString([
        // describes how we want to modify the XML - indent everything
        '<xsl:stylesheet xmlns:xsl="http://www.w3.org/1999/XSL/Transform">',
        '  <xsl:strip-space elements="*"/>',
        '  <xsl:template match="para[content-style][not(text())]">', // change to just text() to strip space in text nodes
        '    <xsl:value-of select="normalize-space(.)"/>',
        '  </xsl:template>',
        '  <xsl:template match="node()|@*">',
        '    <xsl:copy><xsl:apply-templates select="node()|@*"/></xsl:copy>',
        '  </xsl:template>',
        '  <xsl:output indent="yes"/>',
        '</xsl:stylesheet>'
      ].join('\n'), 'application/xml')

      const xsltProcessor = new XSLTProcessor()
      xsltProcessor.importStylesheet(xsltDoc)
      const resultDoc = xsltProcessor.transformToDocument(xmlDoc)
      const resultXml = new XMLSerializer().serializeToString(resultDoc)
      return resultXml
    }
  }
})
</script>
