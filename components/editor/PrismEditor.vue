<template>
  <v-card outlined>
    <prism-editor
      :value="value"
      style="min-height: 250px;"
      class="my-editor"
      :highlight="highlighter"
      line-numbers
      :placeholder="placeholder"
      @input="$emit('input', $event)"
    />
  </v-card>
</template>

<script>
// import Prism Editor
import { PrismEditor } from 'vue-prism-editor'
import 'vue-prism-editor/dist/prismeditor.min.css' // import the styles somewhere

// import highlighting library (you can use any library you want just return html string)
import { highlight, languages } from 'prismjs/components/prism-core'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-javascript'
import 'prismjs/components/prism-markup'
import 'prismjs/themes/prism-tomorrow.css' // import syntax highlighting styles

export default {
  components: {
    PrismEditor
  },
  props: {
    value: { type: String, default: '' },
    placeholder: { type: String, default: '' },
    lang: {
      type: String,
      default: 'html'
    }
  },
  methods: {
    highlighter (code) {
      return highlight(code, languages[this.lang]) // returns html
    }
  }
}
</script>

<style>
.my-editor {
  background: #2d2d2d;
  color: #ccc;

  font-family: Fira code, <PERSON>ra Mono, <PERSON><PERSON>, <PERSON>lo, Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  padding: 5px;
}

.prism-editor__textarea:focus {
  outline: none;
}
</style>
