<template>
  <v-row class="ma-0">
    <v-col
      v-for="item in items.filter(i => Array.isArray(filterStatus) ? filterStatus.includes(i.status) : i.status === filterStatus)"
      :key="item.sku"
      :xl="xl"
      :lg="lg"
      :sm="sm"
    >
      <pick-item-image-card
        :button-mode="buttonMode"
        :item="item"
        :qr-code="reverseMap[item.sku]"
        :disable-add-btn="!!settings.company.RECORD_CHECK_BUTTON_DISABLE"
        @click-add="$emit('click-add', $event);"
        @click-add-all="$emit('click-add-all', $event)"
        @click-delete="$emit('click-delete', $event)"
        @click-delete-all="$emit('click-delete-all', $event)"
      />
    </v-col>
  </v-row>
</template>
<script lang="ts">
import Vue, { PropType } from 'vue'
import { orderBy } from 'lodash'
import PickItemImageCard from '../picking/PickItemImageCard.vue'
import { OrderItem, PickItem, ScanLog } from '~/models'
import { SettingState } from '~/store/settings'

interface TableItem {
  sku: string
  name: string
  image?: string
  checked_amount: number
  total_amount: number
  barcode: string,
  key: string,
  status: 'ok'|'under'|'over'
}

export default Vue.extend({
  components: {
    PickItemImageCard
  },
  props: {
    orderItems: {
      type: Array,
      default: () => [] as OrderItem[]
    },
    scanLogs: {
      type: Array,
      default: () => [] as ScanLog[]
    },
    mapQrcode: {
      type: Object,
      default: null
    },
    xl: {
      type: Number,
      default: 3
    },
    lg: {
      type: Number,
      default: 4
    },
    sm: {
      type: Number,
      default: 6
    },
    filterStatus: {
      type: [String, Array] as PropType<string | string[]>,
      default: () => ['ok', 'under', 'over']
    },
    buttonMode: {
      type: String,
      default: 'plus-minus',
      validator: (value: string) => ['plus-minus', 'delete'].includes(value)
    }
  },
  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    },
    // Reverse { qrcode: sku } to { sku: qrcode}
    reverseMap (): Object {
      if (!this.mapQrcode || typeof this.mapQrcode !== 'object') {
        return {}
      }
      return Object.fromEntries(
        Object.entries(this.mapQrcode).map(([key, value]) => [value, key])
      )
    },
    items (): TableItem[] {
      const result = [] as TableItem[]
      const order_items = this.orderItems as PickItem[]
      for (const item of order_items) {
        // const checked_amount = this.getScannedAmount(item, order_items)
        result.push({
          sku: item.sku,
          name: item.name,
          image: item.image,
          barcode: item.barcode || '-',
          key: item.key,
          checked_amount: 0,
          total_amount: item.number,
          status: this.getStatus(0, item.number)
        })
      }

      const scan_logs = orderBy(this.scanLogs, 'amount') as ScanLog[]
      const over_scan_logs = [] as ScanLog[]
      for (const log of scan_logs) {
        const item = result.find((i) => {
          return i.checked_amount + log.amount <= i.total_amount && (i.key === log.item.key)
        })

        if (!item) {
          over_scan_logs.push(log)
          continue
        }

        if (item?.checked_amount + log.amount <= item.total_amount) {
          item.checked_amount += log.amount
          item.status = this.getStatus(item.checked_amount, item.total_amount)
        }
      }

      // Handle Scan over
      for (const log of over_scan_logs) {
        const items = result.filter((i) => {
          return (i.key === log.item.key)
        })

        if (items.length > 0) {
          items[items.length - 1].checked_amount += log.amount
          for (const item of items) {
            item.status = 'over'
          }
        }
      }

      const status_order = {
        over: 1,
        under: 2,
        ok: 3
      }
      result.sort((a, b) => {
        return b.total_amount - a.total_amount
      })

      result.sort((a, b) => {
        return status_order[a.status] - status_order[b.status]
      })

      return result
    }
  },
  methods: {
    // getScannedAmount (item: PickItem): number {
    //   const scan_logs = this.scanLogs as ScanLog[]
    //   const has_barcode = !!item.barcode
    //   return sum(scan_logs.filter(l => l.barcode === item.sku || (has_barcode && l.barcode === item.barcode)).map(l => l.amount))
    // },

    getStatus (scanned_amount: number, required_amount: number): 'ok'|'over'|'under' {
      if (scanned_amount < required_amount) {
        return 'under'
      }
      if (scanned_amount > required_amount) {
        return 'over'
      }
      return 'ok'
    },
    getColor (status: 'ok'|'under'|'over') {
      const colors = {
        ok: 'green lighten-3',
        under: 'amber lighten-3',
        over: 'red lighten-4'
      }
      return colors[status]
    },
    isScanLogsCompleted () {
      return this.items.every(i => i.status === 'ok')
    }
  }

})
</script>

<style scoped>
.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.blur-background {
  background-color: rgba(255, 255, 255, 0.6); /* Black with 50% opacity */
  backdrop-filter: blur(7px); /* Blur effect */
  -webkit-backdrop-filter: blur(7px); /* For Safari compatibility */
}

.border-error {
  border: 3px solid red; /* Red border */
  border-radius: 5px; /* Optional: Rounded corners */
  box-shadow: 0 0 10px rgba(255, 0, 0, 0.5); /* Optional: Adds a red glow effect */
}

.border-success {
  border: 3px solid green; /* Green border */
  border-radius: 5px; /* Optional: Rounded corners */
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.5); /* Optional: Green glow effect */
}

</style>
