<template>
  <v-row data-test-id="record-form" wrap>
    <v-col :cols="layout === 'full' ? 6 : 12">
      <!-- Video Player -->
      <v-col cols="12">
        <v-form ref="form" v-model="is_valid" autocomplete="off" @submit.prevent="">
          <div style="position: relative;">
            <video
              v-if="!settings.company.WEIGHT_ONLY_MODE_ENABLE"
              id="main-video-player"
              ref="video"
              data-test-id="video"
              class="main-video"
              :class="{'recording': is_recording, 'mobile': $vuetify.breakpoint.smAndDown}"
              controls="false"
              muted
              autoplay
              playsinline
            />
            <div v-if="settings.company.WEIGHT_ONLY_MODE_ENABLE" style="height: 300px; border: 1px solid black; text-align: center;">
              <div style="font-size: 100px; padding-top: 75px;">
                <v-icon style="font-size: 100px; position: relative; top: -5px;">
                  mdi-scale
                </v-icon>
                <span>
                  {{ formatWeight(scale.weight) }}
                </span>
              </div>
            </div>

            <div v-show="is_recording" style="position: absolute; bottom: -8px; margin-left: auto; margin-right: auto; left: 0; right: 0;" class="caption text-center red--text">
              <v-chip data-test-id="v-chip-recording" dark :small="$vuetify.smAndDown">
                <v-icon color="red">
                  mdi-circle-medium
                </v-icon>
                <v-icon>
                  mdi-video
                </v-icon>
                <v-icon>
                  {{ is_record_audio ? 'mdi-microphone' : 'mdi-microphone-off' }}
                </v-icon>
                &nbsp; {{ $t('record.recoding') }} {{ order_number }} - {{ duration_text }}
              </v-chip>
            </div>
          </div>

          <div v-if="session_id" class="text-center mb-2">
            <v-chip class="text-caption" color="primary" outlined>
              Session: {{ session_id }}
            </v-chip>
          </div>
          <v-row>
            <v-col cols="9">
              <v-text-field
                id="v-text-field-control-code"
                ref="control_text_field"
                v-model="control_code"
                data-test="v-text-field-control-code"
                :label="$t('record.control-code')"
                prepend-inner-icon="mdi-video-outline"
                persistent-hint
                clearable
                autofocus
                class="main-control-input"
                :hint="$t('home.scan-password-for-record-video', [is_recording ? $t('home.stop') : $t('home.start')])"
                :error-messages="control_code_error"
                :rules="[$rules.barcode, $rules.maxlength(200)]"
                :counter="control_code.length > 200 ? 200 : undefined"
                @keyup.enter="onEnterControlCode()"
              >
                <template #prepend>
                  <span v-if="isReturnMode" class="pt-1">
                    RT-
                  </span>
                  <span v-if="isTransferMode && !is_recording" class="pt-1">
                    TF-
                  </span>
                </template>
              </v-text-field>
            </v-col>
            <v-col cols="3">
              <v-row class="weight-scale">
                <v-icon small>
                  mdi-scale
                </v-icon>
                <v-spacer />
                <span>{{ formatWeight(scale.weight) }}</span>
              </v-row>
            </v-col>
          </v-row>
        </v-form>
      </v-col>

      <!-- Buttons -->
      <v-col cols="12">
        <v-row dense>
          <v-col v-if="settings.company.RECORD_SHOW_START_BUTTON" cols="12" md="4">
            <v-btn
              id="v-btn-start"
              data-test="v-btn-start"
              block
              x-large
              :loading="loading"
              :disabled="is_recording"
              @click="start(getDefaultVideoName())"
            >
              <v-icon>
                mdi-play
              </v-icon>
              {{ $t('record.start-recording') }}
            </v-btn>
          </v-col>
          <v-col cols="12" md="4">
            <v-btn
              id="v-btn-stop"
              data-test="v-btn-stop"
              block
              x-large
              :disabled="!is_recording || disable_manual_stop"
              class="px-1"
              @click="finish()"
            >
              <v-icon>
                mdi-stop
              </v-icon>
              {{ $t('home.stop-record') }}
            </v-btn>
          </v-col>
          <v-col cols="12" md="4">
            <v-divider v-if="$vuetify.breakpoint.smAndDown" class="mt-5 mb-6" />
            <v-btn
              data-test="v-btn-cancel"
              block
              small
              color="error"
              :x-large="$vuetify.breakpoint.mdAndUp"
              :disabled="!is_recording"
              @click="cancel()"
            >
              <v-icon>mdi-close</v-icon>
              {{ $t('home.cancel') }}
            </v-btn>
          </v-col>
          <v-col v-if="$vuetify.breakpoint.smAndDown" class="mt-3 text-center" cols="12">
            <v-btn data-test-id="v-btn-history" text color="primary" @click="$emit('click-view-history')">
              {{ $t('home.history') }}
            </v-btn>
          </v-col>
        </v-row>
      </v-col>

      <!-- Extra Code -->
      <v-col v-if="layout === 'full' && settings.local.pickItemImageMode" cols="12">
        <h4>Remark</h4>
        <v-divider />
        <div>
          <v-chip
            v-for="item_remark in scan_logs.filter(log => log.barcode && log.barcode.startsWith('__') && log.barcode.endsWith('__'))"
            :key="item_remark.time_ms"
            class="mt-2 me-2"
            close
            color="orange"
            outlined
            @click:close="removeScanLogItem(scan_logs.findIndex(log => log.time_ms === item_remark.time_ms))"
          >
            {{ item_remark.barcode }}
          </v-chip>
        </div>
      </v-col>
    </v-col>

    <!-- <v-col cols="6"> -->
    <v-col v-if="layout !== 'simple'" :cols="layout === 'full' ? 6 : 12">
      <v-row class="ma-0" align="end" justify="space-between">
        <h3 style="width: 20%;">
          รายการสินค้า
        </h3>
        <!-- <v-spacer /> -->
        <div class="text-center">
          <span style="font-size: 36px; border: 2px solid black; border-radius: 10px; background: #FCF7CC; font-weight: bold;" class="px-5">
            {{ count_scanned }}
          </span>
        </div>
        <!-- <v-spacer /> -->
        <div style="width: 20%;" class="text-end">
          <v-btn id="v-btn-setting ms-5" icon @click="setLocalSetting({pickItemImageMode: !settings.local.pickItemImageMode })">
            <v-icon>mdi-repeat</v-icon>
          </v-btn>
        </div>
      </v-row>
      <div :style="settings.local.pickItemImageMode ? 'max-height: 50vh;': 'max-height: 480px;'" style="overflow-y: scroll; background-color: #e8e8e8;">
        <record-order-item-image-panel
          v-if="settings.local.pickItemImageMode"
          ref="item_image_panel"
          :order-items="order_items"
          :scan-logs="scan_logs"
          :map-qrcode="map_pickitem_qrcode"
          :filter-status="['under', 'over']"
          @click-add="addItemBarcode($event, 1)"
          @click-add-all="addItemBarcode($event[0], $event[1])"
          @click-delete="removeScanLogByImageItem($event, 'last')"
          @click-delete-all="removeScanLogByImageItem($event, 'all')"
        />
        <record-order-item-data-table
          v-else
          ref="scan_log_table"
          :order-items="order_items"
          :scan-logs="scan_logs"
          @click-add="addItemBarcode($event, 1)"
          @click-add-all="addItemBarcode($event[0], $event[1])"
        />
      </div>

      <record-order-item-image-panel
        v-if="settings.local.pickItemImageMode"
        class="mt-3"
        :order-items="order_items"
        :scan-logs="scan_logs"
        filter-status="ok"
        button-mode="delete"
        :xl="2"
        :lg="2"
        :sm="3"
        @click-add="addItemBarcode($event, 1)"
        @click-add-all="addItemBarcode($event[0], $event[1])"
        @click-delete-all="removeScanLogByImageItem($event, 'all')"
      />

      <template v-else>
        <h4 class="mt-5">
          การสแกนสินค้า
        </h4>
        <div id="table-record-log" style="max-height: 300px; overflow-y: scroll">
          <record-scan-log-data-table
            :scan-logs="scan_logs"
            @click-delete="removeScanLogItem($event)"
          />
        </div>
      </template>
    </v-col>

    <select-order-number-dialog ref="select_order_dialog" v-model="select_order_dialog.show" :orders="select_order_dialog.orders" />
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import type { PropOptions } from 'vue'
import { mapMutations, mapState } from 'vuex'
import RecordRTC from 'recordrtc'
import moment from 'moment'
import type { Moment } from 'moment'
import * as mqtt from 'mqtt'
import { MqttClient } from 'mqtt'
import { v4 as uuidv4 } from 'uuid'
import { random, sumBy } from 'lodash'
import SelectOrderNumberDialog from '../dialogs/SelectOrderNumberDialog.vue'
import RecordOrderItemDataTable from './RecordOrderItemDataTable.vue'
import RecordOrderItemImagePanel from './RecordOrderItemImagePanel.vue'
import RecordScanLogDataTable from './RecordScanLogDataTable.vue'
import { STATUS } from '~/plugins/db'
import { formatDuration } from '~/api/utils'
import { SettingState } from '~/store/settings'
import { playSound } from '~/utils/sound'
import { LoggedInUser, PickOrder, ScanLog, PickItem, PrintnodeWebsocket, SerialNo } from '~/models'
import { wait } from '~/utils/timer'
import { formatScannedText, formatWeight, getDiffLogs, insertOverlayToCameraStream, isAndroid, isIos, isMobile, setupPrintnodeWebsocket } from '~/utils'

interface RecordOptions {
  name?: string
  filetype: 'webm'|'mp4'|'webm-raw'
  mimetype: 'video/webm;codecs=vp8'|'video/webm;codecs=vp9'|'video/webm;codecs=h264'
  speed: '1x'|'2x'
}

interface FinishOptions {
  bypass_warning: boolean
}

interface CancelOptions {
  bypass_warning: boolean
}

export default Vue.extend({
  components: { RecordOrderItemDataTable, RecordOrderItemImagePanel, RecordScanLogDataTable, SelectOrderNumberDialog },

  props: {
    layout: {
      type: String,
      default: 'simple'
    } as PropOptions<'simple'|'full'>,
    isPieceScan: {
      type: Boolean,
      default: false
    },
    isReturnMode: {
      type: Boolean,
      default: false
    },
    isTransferMode: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      is_valid: false,
      is_recording: false,
      is_record_audio: false,
      order_number: '',
      datetime_start: null as Moment | null,
      duration_text: '',
      timer: null as NodeJS.Timeout | null,

      camera_stream: null as MediaStream | null,
      recorder: null as RecordRTC | null,
      loading: false,
      loading_control_code: false,

      control_code: '',
      control_code_error: '',
      control_mode: '',
      disable_manual_stop: false,

      control_code_log: '',

      // scan order item barcode
      start_record_timestamp: null as Moment | null,
      scan_logs: [] as ScanLog[],
      order_items: [] as PickItem[],

      // channel messages
      channel_id: '',
      channel: null as any,

      // select order dialog
      select_order_dialog: {
        show: false,
        orders: [] as any[]
      },

      // Testing Tools
      dialog: false,

      mqtt: {
        client: null as MqttClient | null
      },

      scale: {
        ws: null as PrintnodeWebsocket | null,
        weight: null as number | null
      },
      scanned_item: null as PickItem | null,

      video_blobs: [] as Blob[],

      // Serial No.
      serial_no_map: {} as Record<string, SerialNo>,

      is_adding_item_by_click: false
    }
  },

  computed: {
    // ...mapState('settings', { localSetting: 'local' }),
    ...mapState('gapi', { gapiAuth: 'isAuth', gapiEmail: 'email' }),
    ...mapState('wallet', { wallet_record_balance: 'record_balance', wallet_version: 'version' }),

    settings (): SettingState {
      return this.$store.state.settings
    },

    session_id (): string {
      return this.$store.state.session_id
    },

    count_scanned (): string {
      const c1 = sumBy(this.scan_logs, 'amount')
      const c2 = sumBy(this.order_items, 'number')
      return `${c1} / ${c2}`
    },

    map_pickitem_qrcode (): object {
      const map_qr: Record<string, string> = {}

      if (this.settings.local.pickItemShowQRCode) {
        for (let index = 0; index < this.order_items.length; index++) {
          const item = this.order_items[index]

          if ('show_image_qr_code' in item && !item.show_image_qr_code) { continue }

          const formattedIndex = String(index + 1).padStart(2, '0')
          map_qr[`DBBITEM${formattedIndex}`] = item.sku
        }
      }

      return map_qr
    },

    map_scan_logs_sku_amount (): Record<string, number> {
      const result: Record<string, number> = {}

      this.scan_logs.forEach((x) => {
        const key = x.item.key
        if (key) {
          if (key in result) {
            result[key] += x.amount
          } else {
            result[key] = x.amount
          }
        }
      })

      return result
    }
  },

  watch: {
    'settings.company.USE_MQTT' (val: boolean) {
      if (!val) {
        return
      }
      this.registerMQTTListenter()
    },
    'settings.local.weightScaleComputerId' (val: string) {
      if (!val) {
        return
      }
      setTimeout(() => {
        this.registerWeightScaleListener(val)
      }, 1000)
    }
  },

  created () {
    this.$nuxt.$on('focus-control-text-field', () => {
      this.focusTextField()
    })

    this.registerBoardcastChannelListener()
  },

  mounted () {
    window.addEventListener('focus', this.focusTextField)

    const start_record = this.$route.query.start_record as string | null
    if (start_record) {
      setTimeout(() => {
        this.control_code = start_record
        this.onEnterControlCode()
      }, 1200)
    }

    const user = this.$auth.user as never as LoggedInUser
    if (this.settings.company.POSTHOG_ENABLED && !user.is_superuser) {
      console.log('[PostHog] start session recording')
      this.$posthog.startSessionRecording()
    }
  },

  beforeDestroy () {
    this.$nuxt.$off('focus-control-text-field')
    window.removeEventListener('focus', this.focusTextField)

    if (this.mqtt.client) {
      this.mqtt.client.end()
    }
  },

  methods: {
    registerBoardcastChannelListener () {
      try {
        this.channel_id = uuidv4().slice(0, 6)

        this.channel = new BroadcastChannel('toggle-recording')
        this.channel.addEventListener('message', async (e: any) => {
          if (e.data.command === 'start') {
            // ป้องกันกรณี 2 แทปอัดวิดีโอพร้อมกัน ให้ทั้งสองแทปแย่งสิทธิ์การอัดวิดีโอโดยการเขียน channel_id ของตัวเองลงใน
            // localStorage, หลังจากรอ 20 ms แล้ว channel_id ของแทปไหนอยู่ใน localStorage จะให้แทปนั้นอัดวิดีโอ
            localStorage.setItem('BoardcastChannel.active', this.channel_id)
            await wait(20)
            const active_tab = localStorage.getItem('BoardcastChannel.active') || ''

            if (this.is_recording) {
              setTimeout(async () => {
                await this.finish()
                await this.$nextTick()
                if (active_tab === this.channel_id) {
                  this.start(e.data.order_number)
                }
              }, 0)
            } else if (active_tab === this.channel_id) {
              this.start(e.data.order_number)
            }
          }

          if (e.data.command === 'stop' && this.is_recording) {
            const video_duration = moment().diff(this.datetime_start, 'ms')
            if (video_duration > 3000) {
              this.finish()
            }
          }
        })
      } catch (error) {
        console.log("The browser doesn't support BoardcastChannel API")
      }
    },

    registerMQTTListenter () {
      this.$notification.requestPermission()

      // Connect
      console.log('[mqtt]', 'connecting')
      this.mqtt.client = mqtt.connect(
        'wss://mqtt.dobybot.com:8083',
        {
          username: 'mqtt',
          password: 'MCGnezn1MV7RTYlujqHz',
          reconnectPeriod: 5000
        }
      )

      // Subscribe
      const user = this.$auth.user as never as LoggedInUser
      this.mqtt.client.subscribe(user.username, { qos: 0 }, () => {
        console.log('[mqtt]', 'subscribed', user.username)
      })

      if (this.session_id) {
        this.mqtt.client.subscribe(this.session_id, { qos: 0 }, () => {
          console.log('[mqtt]', 'subscribed', this.session_id)
        })
      }

      // Listen for Messages
      this.mqtt.client.on('message', async (topic: string, payload: Buffer) => {
        interface MQTTPayload {
          cmd: 'start'|'stop'|'cancel'|'stop-start'
          order_number?: string
        }
        const data: MQTTPayload = JSON.parse(new TextDecoder().decode(payload))

        // eslint-disable-next-line no-console
        console.log('[mqtt]', 'on message', { topic, data })

        if (!this.is_recording) {
          if (data.cmd === 'start' && data.order_number) {
            this.$notification.show('START Recording', `เริ่มอัดวิดีโอ ${data.order_number}`)
            this.order_number = data.order_number
            this.start(this.order_number)

            this.mqtt.client?.publish(`${topic}/start/reply`, JSON.stringify({ success: true, error: '', msg: '' }))
          }

          if (data.cmd === 'stop') {
            this.mqtt.client?.publish(
              `${topic}/stop/reply`,
              JSON.stringify({ success: false, error: 'RECORD_NOT_STARTED', msg: 'The specified client is not recording' })
            )
          }

          if (data.cmd === 'stop-start' && data.order_number) {
            this.$notification.show('START Recording', `เริ่มอัดวิดีโอ ${data.order_number}`)
            this.order_number = data.order_number
            this.start(this.order_number)
            this.mqtt.client?.publish(`${topic}/stop-start/reply`, JSON.stringify({ success: true, error: '', msg: '' }))
          }

          if (data.cmd === 'cancel') {
            this.mqtt.client?.publish(
              `${topic}/cancel/reply`,
              JSON.stringify({ success: false, error: 'RECORD_NOT_STARTED', msg: 'The specified client is not recording' })
            )
          }
        }

        if (this.is_recording) {
          if (data.cmd === 'stop') {
            this.$notification.show('STOP Recording', `หยุดและบันทึกวิดีโอ ${this.order_number}`)
            this.finish({ bypass_warning: true })

            this.mqtt.client?.publish(
              `${topic}/stop/reply`,
              JSON.stringify({ success: true, error: '', msg: '' })
            )
          }

          if (data.cmd === 'stop-start' && data.order_number) {
            await this.finish({ bypass_warning: true })

            this.$notification.show('STOP/START Recording', `เริ่มอัดวิดีโอ ${data.order_number}`)
            this.order_number = data.order_number
            this.start(this.order_number)
            this.mqtt.client?.publish(`${topic}/stop-start/reply`, JSON.stringify({ success: true, error: '', msg: '' }))
          }

          if (data.cmd === 'start') {
            this.mqtt.client?.publish(
              `${topic}/start/reply`,
              JSON.stringify({ success: false, error: 'BETWEEN_RECORD', msg: 'The specified client is recording another video' })
            )
          }

          if (data.cmd === 'cancel') {
            this.$notification.show('CANCEL Recording', `ยกเลิกวิดีโอ ${this.order_number}`)
            this.cancel({ bypass_warning: true })

            this.mqtt.client?.publish(
              `${topic}/cancel/reply`,
              JSON.stringify({ success: true, error: '', msg: '' })
            )
          }
        }
      })
    },

    async registerWeightScaleListener (computerId: string|number) {
      if (!this.scale.ws) {
        this.scale.ws = await setupPrintnodeWebsocket(this.settings.company.PRINTNODE_API_KEY)
        this.scale.weight = 0

        this.scale.ws.getScales({ computerId }, (data: any) => {
          if (data.measurement.g !== undefined) {
            this.scale.weight = data.measurement.g / 10e8
          } else if (data.measurement.kg !== undefined) {
            this.scale.weight = data.measurement.kg / 10e5
          }
        })
      }
    },

    formatWeight (weight: number) {
      return formatWeight(weight, this.settings.company.WEIGHT_SCALE_PREFER_UNIT)
    },

    focusTextField () {
      const input = this.$refs.control_text_field as HTMLInputElement
      input.focus()
    },

    // Event Handler
    async onEnterControlCode () {
      if (this.control_code in this.map_pickitem_qrcode) {
        this.control_code = this.map_pickitem_qrcode[this.control_code]
      }

      if (!this.validate() || this.loading_control_code) {
        return
      }

      this.loading_control_code = true
      await this.processControlCode()
      this.loading_control_code = false
    },

    async processControlCode () {
      this.control_code = this.control_code.trim()
      this.control_code_error = ''

      if (this.settings.company.CONTROL_CODE_FORCE_UPPERCASE_LETTERS) {
        this.control_code = this.control_code.toUpperCase()
      }

      let control_code = this.control_code
      this.control_code = ''

      if (this.isReturnMode) {
        control_code = 'RT-' + control_code
      }
      if (this.isTransferMode) {
        control_code = 'TF-' + control_code
      }

      const patterns = {
        // a-zA-Z0-9-_/#$%&@:.\s=
        start: /^(?<order_number>[a-zA-Z0-9-_/#$%&@:.\s()]+)$/,
        finish: new RegExp('^' + this.order_number + '$'),
        force_finish: /^STOP001$/,
        cancel: /^CANCEL$/,
        start_with_delay_order_number: /^START001$/,
        finish_with_order_number: /^(?<order_number>[a-zA-Z0-9-_/.\s]+)$/,
        start_jd01: /^STARTJD01$/,
        finish_jd01: /^(?<order_number>(VRIC|vric|PIM|pim)[A-Za-z0-9]+)-\d+-\d+-$/,
        start_jd02: /^(?<order_number>(VRIC|vric|PIM|pim)[A-Za-z0-9]+)-\d+-\d+-$/,
        finish_jd02: new RegExp('^(' + this.order_number + '|' + this.order_number.toLowerCase() + ')-\\d+-\\d+-$'),
        finish_create_order: null as RegExp | null
      }

      if (this.settings.company.POST_RECORD_ACTION_CREATE_ORDER) {
        patterns.finish_create_order = new RegExp(this.settings.company.POST_RECORD_ACTION_CREATE_ORDER_PATTERN)
      }

      if (!this.is_recording) {
        if (patterns.finish_create_order) {
          if (patterns.finish_create_order.test(control_code)) {
            this.control_mode = 'finish_create_order'
            this.start(control_code)
          } else {
            this.control_code_error = this.$t('home.invalid-control-code')
          }
          return
        }

        if (patterns.start_with_delay_order_number.test(control_code)) {
          this.start('... WAIT FOR ORDER NUMBER ...')
          this.control_mode = 'start_with_delay_order_number'
          this.disable_manual_stop = true
          return
        }

        if (patterns.start_jd01.test(control_code)) {
          this.start('... WAIT FOR ORDER NUMBER (JD01) ...')
          this.control_mode = 'start_jd01'
          this.disable_manual_stop = true
          return
        }

        if (patterns.start_jd02.test(control_code)) {
          let order_number = control_code.match(patterns.start_jd02)?.groups?.order_number
          if (order_number) {
            order_number = order_number.toUpperCase()
            this.control_mode = 'start_jd02'
            this.start(order_number)
            return
          }
        }

        if (patterns.start.test(control_code)) {
          const order_number = control_code.match(patterns.start)?.groups?.order_number
          if (order_number) {
            this.start(order_number)
            return
          }
        }
      }

      if (this.is_recording) {
        if (this.control_mode === 'start_jd01' && patterns.finish_jd01.test(control_code)) {
          // edit video name
          const order_number = control_code.match(patterns.finish_jd01)?.groups?.order_number
          if (order_number) {
            const cont = await this.preRecordAction(order_number)
            if (!cont) {
              this.cancel()
              return
            }

            this.order_number = order_number.toUpperCase()
            this.finish()
            return
          } else {
            this.control_code_error = this.$t('home.invalid-control-code')
            return
          }
        }

        if (this.control_mode === 'start_with_delay_order_number' && patterns.finish_with_order_number.test(control_code)) {
          // edit video name
          const order_number = control_code.match(patterns.start)?.groups?.order_number
          if (order_number) {
            const cont = await this.preRecordAction(order_number)
            if (!cont) {
              this.cancel()
              return
            }

            this.order_number = order_number
            this.finish()
            return
          } else {
            this.control_code_error = this.$t('home.invalid-control-code')
            return
          }
        }

        if (this.control_mode === 'start_jd02' && patterns.finish_jd02.test(control_code)) {
          this.finish()
          return
        }

        if (patterns.finish.test(control_code)) {
          this.finish()
          return
        }
        if (patterns.force_finish.test(control_code)) {
          this.finish()
          return
        }
        if (patterns.cancel.test(control_code)) {
          this.cancel()
          return
        }

        // PieceScan : Handle order item barcode
        // const item_code = this.isReturnMode ? control_code.slice(3) : control_code

        let item_code
        if (this.isReturnMode) {
          item_code = control_code.slice(3)
        } else if (this.isTransferMode) {
          item_code = '__' + control_code + '__'
        } else {
          item_code = control_code
        }

        if (this.handleExtra(item_code)) {
          return
        }

        if (this.settings.company.RECORD_SCAN_SERIAL_NO_MODE !== 'NONE' && !(item_code in this.serial_no_map)) {
          try {
            const result = await this.$axios.$get('/api/picking/products/serial-no/scan/', {
              params: {
                serial_no: item_code,
                in_stock: !this.isReturnMode
              }
            })
            this.serial_no_map[item_code] = result
            if (this.isReturnMode) {
              this.serial_no_map[item_code].in_stock = true
            }
          } catch (error) {
            console.error(error)
          }
        }

        if (this.handleItemBarcode(item_code, 1)) {
          return
        }
      }

      this.control_code_error = this.$t('home.invalid-control-code')
      this.$alert({
        title: 'รหัสบาร์โค๊ดไม่ถูกต้อง',
        text: `<div style="text-align: center">กรุณาตรวจสอบบาร์โค๊ด/สินค้าที่ท่านสแกนอีกครั้ง<br><br><code>${control_code}</code></div>`,
        theme: 'error'
      })
      playSound('alarm')
    },

    handleItemBarcode (control_code: string, amount: number = 1): boolean {
      if (amount <= 0) {
        return true
      }
      if (control_code.startsWith('__') && control_code.endsWith('__')) {
        return false
      }

      let _code = String(control_code)
      let has_serial_no = false

      // Check has serial no.
      if (this.settings.company.RECORD_SCAN_SERIAL_NO_MODE !== 'NONE') {
        has_serial_no = (control_code in this.serial_no_map)
      }

      // if has serial no map clone serial no to code
      if (has_serial_no && this.serial_no_map[control_code].in_stock) {
        _code = this.serial_no_map[control_code].sku
      } else if (this.settings.company.RECORD_SCAN_SERIAL_NO_MODE === 'SERIAL_NO_ONLY') {
        this.$alert({
          title: 'รหัสซีเรียลไม่ถูกต้อง',
          text: `<div style="text-align: center">กรุณาตรวจสอบรหัสซีเรียล/สินค้าที่ท่านสแกนอีกครั้ง<br><br><code>${control_code.replace(/^RT-/, '')}</code></div>`,
          theme: 'error'
        })
        playSound('alarm')
        return true
      }

      let pick_items: PickItem[] = []
      let item: PickItem | null = null

      // Try to find matching items from the order list based on SKU or barcode
      pick_items = this.order_items.filter((x) => {
        const compact_code = _code.replace(/ /g, '')

        // Match exact SKU or SKU with spaces removed
        if (x.sku === _code || x.sku.replace(/ /g, '') === compact_code) {
          return true
        }
        if (!x.barcode) {
          return false
        }

        const barcodes = x.barcode === '-' ? [] : x.barcode.split(',').filter(b => b).map(b => b.replace(/ /g, ''))

        return barcodes.includes(compact_code)
      })

      // Find items matched
      if (pick_items.length === 1) {
        item = pick_items[0]
      } else if (pick_items.length > 1) {
        item = pick_items.find((x, index) => {
          const is_never_scan = !(x.key in this.map_scan_logs_sku_amount)
          const is_not_full = this.map_scan_logs_sku_amount[x.key] < x.number
          const is_last_item = index === pick_items.length - 1

          return is_never_scan || is_not_full || is_last_item
        }) || null
      }

      // If this was a serial number scan, overwrite _code back to the original control_code
      if (has_serial_no) {
        _code = control_code
      }

      if (item) {
        if (has_serial_no) {
          this.serial_no_map[control_code].in_stock = false
        }

        this.scan_logs.push({
          time_ms: this.getMilliSecondsFromStart(),
          barcode: _code || '',
          sku: item.sku,
          amount,
          item: has_serial_no ? { ...item, serial_no: control_code } : item
        })

        this.scanned_item = item

        if (this.settings.local.enableCheerupSound && this.isScanLogsCompleted()) {
          this.playCheerupSound()
        }

        return true
      }

      if (this.settings.company.RECORD_SCAN_SERIAL_NO_MODE !== 'NONE') {
        this.$alert({
          title: 'รหัสซีเรียลไม่ถูกต้อง',
          text: `<div style="text-align: center">กรุณาตรวจสอบรหัสซีเรียล/สินค้าที่ท่านสแกนอีกครั้ง<br><br><code>${control_code.replace(/^RT-/, '')}</code></div>`,
          theme: 'error'
        })
        playSound('alarm')
        return true
      }

      return false
    },

    handleExtra (control_code: string): boolean {
      const record_tags = this.settings.company.RECORD_TAGS || []
      for (const tag of record_tags) {
        if (tag.barcodes.includes(control_code)) {
          this.scan_logs.push({
            time_ms: this.getMilliSecondsFromStart(),
            barcode: `__${tag.label}:${control_code}__`,
            sku: '',
            amount: 1,
            item: {
              sku: `${tag.label}:${control_code}`,
              name: `${tag.label}:${control_code}`,
              number: 1
            }
          })

          if (this.settings.local.enableCheerupSound && this.isScanLogsCompleted()) {
            this.playCheerupSound()
          }

          return true
        }
      }

      const patterns = [
        /^__(?<extra_info>[A-Za-z0-9-_/.\s]+)__$/,
        /^__(?<extra_info>[A-Za-z0-9-_/.\s]+)$/,
        /^==(?<extra_info>[A-Za-z0-9-_/.\s]+)$/
      ]

      let match: RegExpMatchArray | null = null
      let extra_info: string | null | undefined = null
      for (const pattern of patterns) {
        match = control_code.match(pattern)
        if (match) {
          extra_info = match.groups?.extra_info
          break
        }
      }

      if (extra_info) {
        this.scan_logs.push({
          time_ms: this.getMilliSecondsFromStart(),
          barcode: '__' + extra_info + '__',
          sku: '',
          amount: 1,
          item: {
            sku: extra_info,
            name: extra_info,
            number: 1
          }
        })

        if (this.settings.local.enableCheerupSound && this.isScanLogsCompleted()) {
          this.playCheerupSound()
        }

        return true
      }
      return false
    },

    playCheerupSound () {
      const select_sounds = this.settings.local.selectedCheerupSounds
      const random_index = random(0, select_sounds.length - 1)
      playSound(`cheerup/${select_sounds[random_index]}`)
    },

    removeScanLogItem (index: number) {
      if (this.settings.company.RECORD_SCAN_SERIAL_NO_MODE !== 'NONE') {
        const log = this.scan_logs[index]
        if (log.item?.serial_no && log.item.serial_no in this.serial_no_map) {
          this.serial_no_map[log.item.serial_no].in_stock = true
        }
      }

      this.scan_logs.splice(index, 1)
    },

    removeScanLogByImageItem (sku: string, cmd: 'last'|'all') {
      if (cmd === 'last') {
        // Find the last matching log and remove it
        const lastIndex = this.scan_logs.map(log => log.sku).lastIndexOf(sku)
        if (lastIndex !== -1) {
          this.scan_logs.splice(lastIndex, 1)
        }
      } else {
        this.scan_logs = this.scan_logs.filter(log => log.sku !== sku)
      }
    },

    // Form Control
    validate (): boolean {
      const form = this.$refs.form as any
      return form.validate()
    },

    resetValidation () {
      const form = this.$refs.form as any
      form.resetValidation()
    },

    resetForm () {
      this.is_recording = false
      this.$emit('update:is-recording', false)
      this.disable_manual_stop = false
      this.control_mode = ''
      this.control_code = ''
      this.order_number = ''
      this.is_adding_item_by_click = false
      this.scanned_item = null
      this.scan_logs = []
      this.order_items = []
      this.serial_no_map = {}
      this.resetValidation()
    },

    // Camera Core Functionality
    async start (order_number: string, opts?: RecordOptions): Promise<void> {
      if (isIos()) {
        this.$alert({
          title: this.$t('ios-not-supported'),
          text: this.$t('ios-not-supported-text'),
          theme: 'error'
        })
        return
      }

      if (this.settings.company.WINDOW_NETWORK_FOLDER_PATH) {
        this.$alert({
          title: `${this.$t('record_browser_not_supported')}`,
          text: `${this.$t('store_video_to_local_database')}<br>` + this.$t('switch_to_desktop_for_video_recording') + ' <b>DobybotDesktop Application</b>',
          theme: 'error'
        })
        return
      }

      if (!this.$auth.user?.is_superuser) {
        if (!this.gapiAuth) {
          this.$alert({
            text: `<ol>
            <li style="line-height: 1.5; padding: 8px 0;">${this.$t('error.click-mechanism')}</li>
            <li style="line-height: 1.5; padding: 8px 0;">${this.$t('error.not-login-step2')}</li>
            <li style="line-height: 1.5; padding: 8px 0;">${this.$t('error.un-login-step3')}</li>
            <li style="line-height: 1.5; padding: 8px 0;">${this.$t('error.not-login-step5')}</li>
            <li style="line-height: 1.5; padding: 8px 0;">${this.$t('error.un-login-step6')}</li>
          </ol>`,
            title: this.$t('error.un-login-title'),
            theme: 'error'
          })
          return
        }

        const RECORD_GMAIL_ALLOWED_LIST = this.settings.company.RECORD_GMAIL_ALLOWED_LIST
        if (RECORD_GMAIL_ALLOWED_LIST.length > 0 && !RECORD_GMAIL_ALLOWED_LIST.includes(this.gapiEmail)) {
          // create html ui li allow email list
          const list_allowed_gmails = RECORD_GMAIL_ALLOWED_LIST.join('<br>')
          this.$alert({
            text: `<p class="mb-3">${this.$t('error.use-these-account-only')}</p>` + list_allowed_gmails,
            title: this.$t('error.cant-use-this-account'),
            theme: 'error'
          })
          return
        }
      }

      if ((this.$auth.user?.is_staff || this.$auth.user?.is_superuser) && window.location.hostname !== 'localhost') {
        if (!await this.$confirm({ text: 'คุณกำลังอัดวิดีโอด้วย Account Staff<br>ต้องการดำเนินการต่อหรือไม่' })) {
          return
        }
      }

      if (this.settings.company.POST_RECORD_ACTION_CREATE_ORDER) {
        const patterns = new RegExp(this.settings.company.POST_RECORD_ACTION_CREATE_ORDER_PATTERN)
        if (!patterns.test(order_number)) {
          this.$alert({ text: 'ท่านอยู่ในอยู่ในโหมดสร้างออเดอร์จากชื่อวิดีโอ กรุณาสแกนหมายเลขให้ถูกต้องตามรูปแบบที่กำหนดไว้' })
          return
        }
      }

      if (!this.$isCompanyActive()) {
        this.$store.dispatch('alert/showInActiveCompanyAlert')
        return
      }

      if (!this.settings.company.ALLOW_NEGATIVE_CREDIT && this.wallet_record_balance <= 0) {
        this.$alert({
          title: this.$t('error.no-balance'),
          text: this.$t('error.no-balance-text'),
          theme: 'error'
        })
        return
      }

      // Reset order items
      this.order_number = order_number
      this.order_items = []

      console.log('Start Recording', `order_number=${this.order_number}`)
      this.control_code_log = this.order_number
      // ถ้าเป็นกดปุ่ม "เริ่มบันทึก" หรือ มีการตั้งค่า DISABLED_PRE_RECORD_CHECK ไม่ต้องรัน preRecordAction
      //
      // this.settings.company.DISABLED_PRE_RECORD_CHECK
      // -> True ไม่ต้องรัน preRecordAction
      // -> False ต้องรัน preRecordAction
      // order_number.startsWith('RECORD-')
      // -> True ไม่ต้องรัน preRecordAction
      // -> False ต้องรัน preRecordAction
      const run_pre_record_action = (
        !this.settings.company.DISABLED_PRE_RECORD_CHECK &&
        !order_number.startsWith('RECORD-') &&
        !order_number.startsWith('RT-RECORD-') &&
        !this.isTransferMode
      )
      if (run_pre_record_action && !this.$getIntroStatus()) {
        this.$loader(true)
        try {
          const cont = await this.preRecordAction(order_number)
          this.$loader(false)
          if (!cont) {
            return
          }
          order_number = this.order_number
        } catch (error) {
          this.$loader(false)
          console.error(error)
        }
      }
      // if (!(this.isReturnMode || order_number.startsWith('RECORD-'))) {
      // }

      const user = this.$auth.user as never as LoggedInUser
      const company = user.company
      if (company.package === '002') { // record-only
        try {
          await this.stat()
        } catch (err) {
          console.log('check stat failed')
        }
      }

      this.resetValidation()

      // Print receipt
      const isStartByButton = this.order_number.substring(0, 6) === 'RECORD'
      if (this.settings.local.printReceiptBeforeRecord && !isStartByButton && !this.isReturnMode) {
        this.printReceipt(order_number)
      }

      // Print Airway Bill
      if (this.settings.local.printAirwayBillBeforeRecord && !isStartByButton && !this.isReturnMode) {
        this.printAirwayBill(order_number, { retry_delay: 2000, retry_count: 0 })
      }

      // Play start sound
      this.playStartSound()

      if (this.settings.company.WEIGHT_ONLY_MODE_ENABLE) {
        return this.startWeightOnlyMode()
      }

      if (this.settings.company.ENABLE_NO_RECORD_MODE) {
        return this.startWithoutVideo()
      }

      const speed = opts?.speed || this.settings.local.speed
      let mimeType = opts?.mimetype || 'video/webm;codecs=h264'
      if (isMobile() && isAndroid()) {
        mimeType = 'video/webm;codecs=vp9'
      }
      const audio = this.settings.local.recordAudio && this.settings.local.speed === '1x'
      const { deviceId, resolution } = this.settings.local
      if (!deviceId) {
        alert('Please select a camera')
        this.$emit('open-setting')
        return
      }
      if (!resolution) {
        alert('Please select a resolution')
        this.$emit('open-setting')
        return
      }

      let camera_stream: MediaStream
      try {
        camera_stream = await this.captureCamera(deviceId, resolution, speed, audio)
      } catch (error) {
        alert('Unable to capture your camera. Please check console logs.')
        console.error(error)
        return
      }

      this.video_blobs = []
      const video = this.$refs.video as HTMLVideoElement

      // Start recording
      if (this.settings.local.enableOverlay) {
        insertOverlayToCameraStream(camera_stream, this.getOverlayData)
        const { recorder, mixedStream } = this.startRecordMultiStreamRecorder(camera_stream, mimeType, resolution)
        this.recorder = recorder
        video.srcObject = mixedStream
      } else {
        this.recorder = new RecordRTC(camera_stream, {
          type: 'video',
          mimeType: mimeType as any,
          timeSlice: 24 * 60 * 60 * 1000
        })
        this.recorder.startRecording()
        video.srcObject = camera_stream
      }
      this.camera_stream = camera_stream
      this.is_recording = true
      this.is_record_audio = audio
      this.$emit('update:is-recording', true)
      this.datetime_start = moment()

      // Start timer
      this.start_record_timestamp = moment()
      this.duration_text = '00:00:00'
      this.timer = setInterval(() => {
        const seconds = moment().diff(this.datetime_start, 'seconds')
        this.duration_text = formatDuration(seconds)
      }, 1000)
    },

    startWithoutVideo () {
      this.is_recording = true
      this.is_record_audio = false
      this.$emit('update:is-recording', true)
      this.datetime_start = moment()

      // Start timer
      this.start_record_timestamp = moment()
      this.duration_text = '00:00:00'
      this.timer = setInterval(() => {
        const seconds = moment().diff(this.datetime_start, 'seconds')
        this.duration_text = formatDuration(seconds)
      }, 1000)
    },

    startWeightOnlyMode () {
      const video_name = this.order_number
      this.$db.table('videos').put({
        name: video_name,
        control_code_log: this.control_code_log,
        blob: null,
        status: STATUS.NOT_UPLOADED,
        remark: '',
        createdAt: moment().format(),
        resolution: this.settings.local.resolution,
        filetype: 'webm',
        speed: '1x',
        isTranscoded: false,
        duration: 0,
        is_return: this.isReturnMode,
        scan_logs: [],
        diff_logs: [],
        extra: this.scan_logs
          .filter(x => x.barcode.startsWith('__') && x.barcode.endsWith('__'))
          .map(x => x.item.sku),
        audio: this.is_record_audio,
        weight: this.scale.weight
      })

      if (this.settings.company.WEIGHT_ONLY_MODE_PRINT_METHOD === 'printnode') {
        if (this.settings.local.printWeightSlipAfterRecord && this.settings.local.weightSlipPrinterId) {
          this.printWeightSlip(this.order_number, this.scale.weight, this.settings.local.weightSlipPrinterId)
        }
      }

      if (this.settings.company.WEIGHT_ONLY_MODE_PRINT_METHOD === 'webprint') {
        // print after upload, see RecordHistoryTable.vue, onNewVideoAdded
      }

      // Reset Form
      this.resetForm()
    },

    async printAirwayBill (order_number: string, opt = { retry_delay: 0, retry_count: 0 }) {
      if (!this.settings.local.airwayBillPrinterId) {
        this.$snackbar('error', this.$t('home.airway-bill-printer'))
        this.$emit('open-setting')
        return
      }

      if (!opt) {
        opt = {
          retry_delay: 0,
          retry_count: 0
        }
      }

      this.loading = true
      try {
        await this.$store.dispatch('printnode/printAirwayBill', {
          order_number,
          printer_id: this.settings.local.airwayBillPrinterId
        })
        this.$snackbar('info', this.$t('home.send-print-command', [order_number]))
      } catch (error: any) {
        if (error.response) {
          let message = ''

          if (error.response.status === 500) {
            message = `${this.$t('record-form.cant-print-airway-bill')}\n\n${this.$t('record-form.continue-record-video')}`
          }

          if (error.response.status === 404) {
            const error_code = error.response.data.code
            const error_message = error.response.data.message

            if (error_code === 'SHOPEE_RETRY' && opt.retry_delay && opt.retry_count < 3) {
              opt.retry_delay += 1000
              opt.retry_count += 1
              setTimeout(() => { this.printAirwayBill(order_number, opt) }, opt.retry_delay)
              return
            } else {
              message = `(Retry ${opt.retry_count})\nไม่สามารถดาวโหลดน์ใบ Airway Bill จาก Shopee ได้\nข้อความ Error จาก Shopee:\n${error_message}\n\n${this.$t('record-form.continue-record-video')}`
            }

            if (error_code === 'TIKTOK_RETRY' && opt.retry_delay && opt.retry_count < 3) {
              opt.retry_delay += 1000
              opt.retry_count += 1
              setTimeout(() => { this.printAirwayBill(order_number, opt) }, opt.retry_delay)
              return
            } else {
              message = `(Retry ${opt.retry_count})\nไม่สามารถดาวโหลดน์ใบ Airway Bill จาก TikTok ได้\nข้อความ Error จาก TikTok:\n${error_message}\n\n${this.$t('record-form.continue-record-video')}`
            }

            if (error_code === 'SHOPEE_API_ERROR') {
              message = `ไม่สามารถดาวโหลดน์ใบ Airway Bill จาก Shopee ได้\nข้อความ Error จาก Shopee:\n${error_message}\n\n${this.$t('record-form.continue-record-video')}`
            }

            if (error_code === 'LAZADA_API_ERROR') {
              message = `ไม่สามารถดาวโหลดน์ใบ Airway Bill จาก Lazada ได้\nข้อความ Error จาก Lazada:\n${error_message}\n\n${this.$t('record-form.continue-record-video')}`
            }

            if (error_code === 'TIKTOK_API_ERROR') {
              message = `ไม่สามารถดาวโหลดน์ใบ Airway Bill จาก TikTok ได้\nข้อความ Error จาก TikTok:\n${error_message}\n\n${this.$t('record-form.continue-record-video')}`
            }

            if (error_code === 'NOCNOC_API_ERROR') {
              message = `ไม่สามารถดาวโหลดน์ใบ Airway Bill จาก NocNoc ได้\nข้อความ Error จาก NocNoc:\n${error_message}\n\n${this.$t('record-form.continue-record-video')}`
            }
            if (error_code === 'LINEMYSHOP_API_ERROR') {
              message = `ไม่สามารถดาวโหลดน์ใบ Airway Bill จาก LineMyShop ได้\nข้อความ Error จาก LineMyShop:\n${error_message}\n\n${this.$t('record-form.continue-record-video')}`
            }

            if (error_code === 'NOT_FOUND_UPLOADED_AIRWAY_BILL') {
              message = `${error.response.data.message}\n\n${this.$t('record-form.continue-record-video')}`
            }
          }

          if (message) {
            if (!confirm(message)) {
              setTimeout(() => { this.cancel() }, 500)
            }
          }
        }
      }
      this.loading = false
    },

    async printReceipt (order_number: string) {
      if (!this.settings.local.receiptPrinterId) {
        this.$snackbar('error', this.$t('please-select-receipt-printer-id'))
        this.$emit('open-setting')
        return
      }

      this.loading = true
      try {
        await this.$axios.post('/api/picking/receipt/print/', {
          order_number,
          printer_id: this.settings.local.receiptPrinterId
        })
        this.$snackbar('info', this.$t('send-print-receipt-command-success', [order_number]))
      } catch (error: any) {
        if (error.response) {
          let message = ''
          if (error.response.status === 500) {
            message = `${this.$t('record-form.cant-print-receipt')}\n\n${this.$t('record-form.continue-record-video')}`
          }

          if (error.response.status === 404) {
            message = `${this.$t('print-receipt-order-notfound')}\n\n${this.$t('record-form.continue-record-video')}`
          }

          if (message) {
            if (!confirm(message)) {
              setTimeout(() => { this.cancel({ bypass_warning: true }) }, 500)
            }
          }
        }
      }
      this.loading = false
    },

    async printWeightSlip (video_name: string, weight: number | null, printer: string) {
      const payload = {
        video_name,
        weight: weight !== null ? weight.toFixed(2) : null,
        printer
      }

      try {
        await this.$axios.post('/api/picking/print/weight-slip/', payload)
      } catch (error) {
        console.error(error)
        alert('Failed to print weight slip, Please check console error')
      }
    },

    async finish (opt?: FinishOptions) {
      opt = opt || { bypass_warning: false }

      const video_duration = moment().diff(this.datetime_start, 'seconds')
      if (video_duration < 3) {
        this.playAlarmSound()
        this.$alert({ text: `ไม่สามารถบันทึกวิดีโอได้เนื่องจาก: วิดีโอมีระยะเวลาเพียง ${video_duration} วินาที` })
        return
      }

      if (this.settings.company.STOP_RECORD_REQUIRE_COMPLETED_SCAN_LOG && !opt.bypass_warning) {
        if (!this.isScanLogsCompleted()) {
          this.playAlarmSound()

          const result = await this.checkConfirmRecordPassword(
            '<h1 style="color: red; text-align: center;">สแกนรายการสินค้าเกิน / ไม่ครบ</h1>'
          )
          if (!result) { return }
        }
      }

      const weight = this.scale.weight
      const blob = await this.stop()
      this.playStopSound()

      // Save video data to indexedDB
      let duration = moment().diff(this.datetime_start, 'seconds')
      if (this.settings.local.speed === '2x') {
        duration = duration / 2
      }
      this.$db.table('videos').put({
        name: this.order_number,
        control_code_log: this.control_code_log,
        blob,
        status: STATUS.NOT_UPLOADED,
        remark: '',
        createdAt: moment().format(),
        resolution: this.settings.local.resolution,
        filetype: 'webm',
        speed: '1x',
        isTranscoded: false,
        duration,
        is_return: this.isReturnMode,
        scan_logs: this.scan_logs.map(x => ({ time_ms: x.time_ms, barcode: x.barcode, sku: x.sku, amount: x.amount, name: x.item.name })),
        diff_logs: getDiffLogs(this.scan_logs, this.order_items),
        extra: this.scan_logs
          .filter(x => x.barcode.startsWith('__') && x.barcode.endsWith('__'))
          .map(x => x.item.sku),
        audio: this.is_record_audio,
        weight
      }).catch((error) => {
        this.playAlarmSound()
        this.showVideoNotSavedError(error)
        console.error(error)
      })

      if (this.settings.local.printWeightSlipAfterRecord && this.settings.local.weightSlipPrinterId) {
        this.printWeightSlip(this.order_number, weight, this.settings.local.weightSlipPrinterId)
      }

      // Reset Form
      this.resetForm()
    },

    async cancel (opt?: CancelOptions) {
      opt = opt || { bypass_warning: false }

      if (opt.bypass_warning) {
        await this.stop()
        this.resetForm()
        return
      }

      if (!confirm('ท่านต้องการยกเลิกการบันทึกวิดีโอหรือไม่')) {
        return
      }

      await this.stop()
      this.resetForm()
    },

    stop (): Promise<Blob | null> {
      return new Promise((resolve, reject) => {
        if (this.settings.company.ENABLE_NO_RECORD_MODE) {
          return resolve(null)
        }

        if (!this.recorder) { return reject(new Error('Invalid `this.recorder`')) }

        this.recorder.stopRecording(() => {
          if (!this.recorder) { return reject(new Error('Invalid `this.recorder`')) }
          if (!this.camera_stream) { return reject(new Error('Invalid `this.camera_stream`')) }

          // Stop Recording
          if (this.settings.local.enableOverlay) {
            const blob = new File(this.video_blobs, 'video.webm', { type: 'video/webm' })
            resolve(blob)
          } else {
            const blob = this.recorder.getBlob()
            resolve(blob)
          }

          // PRODlog('Stop Recording', `order_number=${this.order_number}`)
          console.log('Stop Recording', `order_number=${this.order_number}`)

          const video = this.$refs.video as HTMLVideoElement
          video.src = ''
          video.srcObject = null
          video.controls = true
          this.video_blobs = []

          this.recorder.destroy()
          this.recorder = null
          this.camera_stream.getTracks().forEach((track) => { track.stop() })
        })
      })
    },

    captureCamera (deviceId: string, resolution: string, speed: '1x'|'2x', audio: boolean): Promise<MediaStream> {
      const [width, height] = resolution.split('x')
      let framerate = 24
      if (speed === '2x') {
        framerate = 12
      }
      const constraints = {
        audio,
        video: {
          width: { min: 720, ideal: Number(width), max: 1920 },
          height: { min: 480, ideal: Number(height), max: 1080 },
          frameRate: { min: 12, ideal: framerate, max: 24 },
          deviceId
        }
      }

      return navigator.mediaDevices.getUserMedia(constraints)

      // navigator.mediaDevices
      //   .getUserMedia(constraints)
      //   .then((camera: MediaStream) => {
      //     callback(camera)
      //   })
      //   .catch((error) => {
      //     alert('Unable to capture your camera. Please check console logs.')
      //     console.error(error)
      //   })
    },

    // Utils
    getDefaultVideoName (): string {
      let video_name = ''

      if (this.control_code) {
        video_name = this.control_code
      } else {
        video_name = 'RECORD-' + moment().format('YYYYMMDD-HHmmss')
      }

      if (this.isReturnMode) {
        video_name = 'RT-' + video_name
      }
      if (this.isTransferMode) {
        video_name = 'TF-' + video_name
      }

      return video_name
    },

    async playStartSound () {
      if (this.settings.local.enableSound) {
        try {
          await playSound('start')
        } catch (error) {
          // console.error(error)
        }
      }
    },

    async playStopSound () {
      if (this.settings.local.enableSound) {
        try {
          await playSound('stop')
        } catch (error) {
          // console.error(error)
        }
      }
    },

    async playAlarmSound () {
      if (this.settings.local.enableSound) {
        try {
          await playSound('alarm')
        } catch (error) {
          // console.error(error)
        }
      }
    },

    getMilliSecondsFromStart () {
      const now = moment()
      return now.diff(this.start_record_timestamp, 'millisecond')
    },

    startRecordMultiStreamRecorder (camera_stream: MediaStream, mimeType: string, resolution: string): { recorder: RecordRTC, mixedStream: MediaStream} {
      // @ts-ignore
      const recorder = new RecordRTC([camera_stream], {
        type: 'video',
        mimeType: mimeType as any,
        timeSlice: 1000,
        ondataavailable: (blob) => {
          this.video_blobs.push(blob)
        }
      })
      recorder.startRecording()
      const [width, height] = resolution.split('x')
      const internalRecorder = recorder.getInternalRecorder() as any
      internalRecorder.getMixer().width = width
      internalRecorder.getMixer().height = height
      return {
        recorder,
        mixedStream: internalRecorder.getMixer().getMixedStream()
      }
    },

    getOverlayData () {
      const user = this.$auth.user as never as LoggedInUser

      let datetime = null as string | null
      if (this.settings.local.overlayShowDatetime) {
        datetime = moment().format('YYYY-MM-DD HH:mm:ss')
      }

      let weight = null as string | null
      if (this.settings.local.overlayShowWeight) {
        weight = this.scale.weight !== null ? formatWeight(this.scale.weight, this.settings.company.WEIGHT_SCALE_PREFER_UNIT) : null
      }

      let scanned_item = null as string | null
      if (this.settings.local.overlayShowScannedSKU) {
        scanned_item = this.scanned_item ? formatScannedText(this.scanned_item) : null
      }

      return {
        datetime,
        weight,
        scanned_item,
        order_number: this.order_number,
        username: user.username
      }
    },

    async checkLocalDuplicateRecord (order_number: string): Promise<boolean> {
      const video = await this.$db.table('videos').where('name').equals(order_number).first()
      console.log('check', { video })
      return !!video
    },

    // APIs
    async preRecordAction (order_number: string, config = { alarm: true, exact: false }): Promise<boolean> {
      config = config || { alarm: true, exact: false }
      const alarm = config.alarm
      const exact = config.exact
      const res = await this.$axios.post('/api/picking/pick-orders/pre-record-action/', {
        order_number,
        exact
      })

      if (this.isPieceScan) {
        this.getOrderItems(res.data.order_number)
      }

      if (!alarm) {
        return true
      }

      if (res.data.status === 'warning') {
        await this.playAlarmSound()
        const confirm_text = `
          <p style="white-space: pre-wrap;">${res.data.detail}</p><br><br>
          ${this.$t('home.do-you-want-to-continue-recording')}
        `

        if (res.data.code === 'voided') {
          if (this.settings.company.RECORD_VOIDED_ORDER_REQUIRE_SUPERVISOR_PASSWORD) {
            return this.checkConfirmRecordPassword(res.data.detail)
          } else {
            return this.$confirm({ text: confirm_text })
          }
        }

        if (res.data.code === 'multiple_orders') {
          const order_number = await this.confirmRecordOrderNumber(res.data.data)
          if (!order_number) {
            return false
          }
          this.order_number = order_number
          return this.preRecordAction(order_number, { alarm, exact: true })
        }

        if (this.settings.company.CONFIRM_RECORD_PASSWORD_ENABLE) {
          const config = this.settings.company.CONFIRM_RECORD_PASSWORD_CONFIG
          const remark: string = res.data.remark
          const show_password_dialog = remark && remark.trim().startsWith('!')

          if (res.data.code === 'not_found') {
            if (config.includes('ORDER_NOT_FOUND') || show_password_dialog) {
              return this.checkConfirmRecordPassword(res.data.detail)
            } else {
              return this.$confirm({ text: confirm_text })
            }
          }

          if (res.data.code === 'has_fixcases') {
            if (config.includes('FIXCASE') || show_password_dialog) {
              return this.checkConfirmRecordPassword(res.data.detail)
            } else {
              return this.$confirm({ text: confirm_text })
            }
          }

          if (res.data.code === 'has_videos') {
            if (config.includes('DUPLICATE_RECORD') || show_password_dialog) {
              return this.checkConfirmRecordPassword(res.data.detail)
            } else {
              return this.$confirm({ text: confirm_text })
            }
          }

          if (res.data.code === 'remark') {
            if (config.includes('REMARK') || show_password_dialog) {
              return this.checkConfirmRecordPassword(res.data.detail)
            } else {
              return this.$confirm({ text: confirm_text })
            }
          }

          return this.$confirm({ text: confirm_text })
        } else {
          return this.$confirm({ text: confirm_text })
        }
      }

      if (res.data.status === 'info') {
        this.$snackbar('info', res.data.detail)
        return true
      }

      if (await this.checkLocalDuplicateRecord(order_number)) {
        this.playAlarmSound()
        const confirm_text = `<p style="white-space: pre-wrap;">คำสั่งซื้อ ${order_number} เคยถูกบันทึกวิดีโอไปแล้ว</p><br>
          ${this.$t('home.do-you-want-to-continue-recording')}`

        const remark: string = res.data.remark
        const show_password_dialog = remark && remark.trim().startsWith('!')
        const config = this.settings.company.CONFIRM_RECORD_PASSWORD_CONFIG
        if (config.includes('DUPLICATE_RECORD') || show_password_dialog) {
          return this.checkConfirmRecordPassword(confirm_text)
        } else {
          return this.$confirm({ text: confirm_text })
        }
      }

      if (res.data.status === 'success') {
        return true
      }

      return false
    },

    async stat () {
      console.log('Hello Stat')
      await this.$axios.get('/api/picking/stat/')
    },

    async getOrderItems (order_number: string) {
      // const res = await this.$axios.get(`api/picking/resource/pick-orders/${pick_order_id}/`)
      try {
        const res = await this.$axios.post('api/picking/pick-orders/pick-items/', { order_number })
        const order = res.data as PickOrder

        const order_items = {}
        for (let i = 0; i < order.order_json.list.length; i++) {
          const item = order.order_json.list[i]
          item.sku = item.sku !== '-' ? item.sku : i.toString()

          item.key = item.sku + '---' + item.barcode + '---' + item.name
          if (!order_items[item.key]) {
            order_items[item.key] = item
          } else {
            order_items[item.key].number += item.number
          }
        }

        this.order_items = Object.values(order_items)
      } catch (error) {
        console.error(error)
      }
      // this.order_items = order.order_json.list.map((x, i) => ({
      //   ...x,
      //   sku: x.sku !== '-' ? x.sku : i.toString()
      // }))
    },

    async checkConfirmRecordPassword (detail: string): Promise<boolean> {
      const passwd = await this.$prompt({
        text: `<p style="white-space: pre-wrap;">${detail}</p><br>
        <p style="text-align: center;">กรุณากรอกรหัสผ่านเพื่อทำการบันทึกวิดีโอ</p>`,
        preset: 'password'
      })

      if (passwd === null) { return false }
      if (passwd !== this.settings.company.SUPERVISOR_PASSWORD) {
        this.$alert({ text: 'รหัสผ่านไม่ถูกต้อง' })
        return false
      }
      return true
    },

    confirmRecordOrderNumber (orders: any[]): Promise<string> {
      //
      return new Promise((resolve) => {
        this.select_order_dialog.show = true
        this.select_order_dialog.orders = orders

        const dialog = this.$refs.select_order_dialog as any
        if (!dialog) {
          throw new Error('Invalid `this.$refs.select_order_dialog`')
        }

        dialog.$on('select', (order_number: string) => {
          dialog.$off('select')
          this.order_number = order_number
          this.control_code = ''
          this.select_order_dialog.show = false
          resolve(order_number)
        })
        dialog.$on('input', () => {
          resolve('')
        })
      })
    },

    isScanLogsCompleted (): boolean {
      if (this.settings.local.pickItemImageMode) {
        if (!this.$refs.item_image_panel) { return true }
        return (this.$refs.item_image_panel as any).isScanLogsCompleted()
      } else {
        if (!this.$refs.scan_log_table) { return true }
        return (this.$refs.scan_log_table as any).isScanLogsCompleted()
      }
    },

    // Testing
    async startTest (config: { name: string, duration: number, file_formats: RecordOptions[]}) {
      // close config dialog
      this.dialog = false

      const wait = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))
      for (const format of config.file_formats) {
        this.setLocalSetting({ filetype: format.filetype, speed: format.speed })
        this.start(config.name + ' ' + format.name, format)
        await wait(config.duration * 1000 + 2000)
        this.finish()
        await wait(3000)
      }
    },

    showVideoNotSavedError (error: any) {
      this.$alert({
        title: 'การบันทึกวิดีโอล้มเหลว',
        text: `
          ไม่สามารถบันทึกไฟล์วิดีโอ <br><br>

          <div style="border: 1px solid black; padding: 20px; font-weight: bold;">
            กรุณาติดต่อ Dobybot Support
            <br>
            <a target="#" class="mt-2" href="https://page.line.me/856wntly?openQrModal=true">
              LINE: @dobybot
            </a> หรือ <a href="tel:*********">โทร: 02-662-8500</a>
          </div>
          <br><br>

          <pre>Error Messages:</pre>
          <div style="border: 1px solid #aaaaaa; padding: 20px; background-color: #f2f2f2; margin-top: 5px;">
            <code style="background-color: #f2f2f2;">${error}</code>
          </div>
        `,
        theme: 'error'
      })
    },

    async addItemBarcode (control_code: string, amount: number = 1) {
      if (this.settings.company.ADD_ITEM_BARCODE_REQUIRE_SUPERVISOR_PASSWORD) {
        if (!this.is_adding_item_by_click) {
          const confirm = await this.checkConfirmRecordPassword(
            this.$t('please-confirm-supervisor-password-before-manually-add-item-by-clicking') as string
          )

          if (!confirm) {
            return
          }
          this.is_adding_item_by_click = confirm
        }
      }

      this.handleItemBarcode(control_code, amount)
    },

    ...mapMutations('settings', {
      setLocalSetting: 'setLocalSetting'
    })
  }
})
</script>

<style scoped>
.main-video {
  width: 100%;
  aspect-ratio: 16/9;
}

.main-video.mobile {
  max-height: 190px;
}

.recording {
  border: 3px red solid;
}

.overlay::-webkit-scrollbar {
  display: none;
}
.weight-scale {
  white-space: nowrap;
  border: 1px solid rgba(0,0,0,0.42);
  border-radius: 4px;
  padding: 5px 5px 5px 5px;
  margin: 13px 0 0 1px;
}
</style>
