<template>
  <div id="table-record-log">
    <div v-if="search.enable" no-gutters class="py-2">
      <v-text-field
        v-model="search.keyword"
        data-test="v-text-field-record-search"
        dense
        placeholder="ค้นหาด้วยชื่อวิดีโอ"
        prepend-inner-icon="mdi-magnify"
        append-outer-icon="mdi-close"
        @click:append-outer="search.enable = false; search.keyword = ''"
      />
    </div>
    <v-data-table
      data-test-id="record-history-table"
      :footer-props="{'items-per-page-options': [5, 10, 15]}"
      :headers="table.headers"
      :items="table.items"
      :options.sync="table.options"
      :server-items-length="table.total_items"
      :expanded.sync="table.expanded"
      :loading="table.loading"
      show-expand
      single-expand
      mobile-breakpoint="0"
    >
      <template #[`header.data-table-expand`]>
        <v-btn icon small @click="search.enable = !search.enable">
          <v-icon>mdi-magnify</v-icon>
        </v-btn>
      </template>

      <template #[`item.createdAt`]="{ item }">
        {{ formatDate(item.createdAt) }}
      </template>

      <template #[`item.name`]="{ item }">
        <v-icon>
          {{ item.audio ? 'mdi-volume-high' : 'mdi-volume-off' }}
        </v-icon>
        {{ item.name }}
      </template>

      <template #[`item.filetype`]="{item}">
        <v-menu>
          <template #activator="{ on, attrs }">
            <v-chip v-bind="attrs" label small outlined v-on="on">
              <v-icon v-if="!item.isTranscoded" small>
                mdi-alert
              </v-icon>
              &nbsp;{{ item.filetype }} ({{ item.speed }})
            </v-chip>
          </template>
          <v-list v-if="!item.isTranscoded">
            <v-list-item @click="onTranscodeVideoButtonClick(item, 'webm', '1x')">
              <v-list-item-title>{{ $t('video.webm-seekable') }}</v-list-item-title>
            </v-list-item>
            <v-list-item @click="onTranscodeVideoButtonClick(item, 'mp4', '1x')">
              <v-list-item-title>{{ $t('video.mp4') }}</v-list-item-title>
            </v-list-item>
            <v-list-item @click="onTranscodeVideoButtonClick(item, 'mp4', '2x')">
              <v-list-item-title>{{ $t('video.mp4-2x') }}</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
      </template>

      <template #[`item.status`]="{ item }">
        <v-tooltip v-if="item.status === 'NOT_UPLOADED'" top>
          <template #activator="{ on, attrs }">
            <v-btn icon v-bind="attrs" v-on="on" @click="table.expanded = [item]">
              <v-icon color="error">
                mdi-alert
              </v-icon>
            </v-btn>
          </template>
          <span>{{ $t('home.not-upload') }}</span>
        </v-tooltip>
        <v-tooltip v-if="item.status === 'TRANSCODING'" top>
          <template #activator="{ on, attrs }">
            <v-btn icon :loading="true" v-bind="attrs" v-on="on">
              <v-icon>mdi-loading</v-icon>
            </v-btn>
          </template>
          <span>{{ $t('home.proccess-file') }}</span>
        </v-tooltip>
        <v-tooltip v-if="item.status === 'QUEUED_FOR_UPLOAD'" top>
          <template #activator="{ on, attrs }">
            <v-btn icon :loading="true" v-bind="attrs" v-on="on">
              <v-icon>mdi-loading</v-icon>
            </v-btn>
          </template>
          <span>{{ $t('queue-for-upload') }}</span>
        </v-tooltip>
        <v-tooltip v-if="item.status === 'UPLOADING'" top>
          <template #activator="{ on, attrs }">
            <v-btn icon :loading="true" v-bind="attrs" v-on="on">
              <v-icon>mdi-loading</v-icon>
            </v-btn>
          </template>
          <span>{{ $t('home.uploading') }}</span>
        </v-tooltip>
        <v-tooltip v-if="item.status === 'UPLOADED'" top>
          <template #activator="{ on, attrs }">
            <v-btn icon v-bind="attrs" v-on="on">
              <v-icon color="success">
                mdi-check
              </v-icon>
            </v-btn>
          </template>
          <span>{{ $t('home.upload-success') }}</span>
        </v-tooltip>

        <div class="caption text-center">
          <span v-if="item.status === 'TRANSCODING'">{{ $t('home.transcoding') }}</span>
          <span v-if="item.status === 'UPLOADING'">{{ $t('home.uploading') }}</span>
          <span v-if="item.status === 'QUEUED_FOR_UPLOAD'">{{ $t('queue-for-upload') }}</span>
        </div>
      </template>

      <template #[`item.actions`]="{ item }">
        <div class="text-right">
          <v-btn icon color="black" @click="download(item)">
            <v-icon>mdi-download</v-icon>
          </v-btn>
          <v-btn
            v-if="item.status === 'NOT_UPLOADED'"
            data-test-id="v-btn-upload"
            icon
            color="black"
            @click.left="onUploadButtonClick(item, item.filetype, item.speed)"
            @click.right.prevent="onForceUploadButtonClick(item)"
          >
            <v-icon>mdi-upload</v-icon>
          </v-btn>
          <v-btn
            v-if="item.status === 'UPLOADING' || item.status === 'TRANSCODING' || item.status === 'QUEUED_FOR_UPLOAD'"
            data-test-id="v-btn-cancel-upload"
            icon
            color="black"
            @click="cancelUpload(item)"
          >
            <v-icon>mdi-cancel</v-icon>
          </v-btn>
          <v-btn
            v-if="item.status === 'UPLOADED'"
            data-test="v-btn-copy-video-url"
            icon
            color="black"
            @click="copy(item.shareLink)"
          >
            <v-icon>mdi-content-copy</v-icon>
          </v-btn>
        </div>
      </template>

      <template #expanded-item="{ headers, item }">
        <td data-test-id="td-expanding-panel" :colspan="headers.length">
          <v-row wrap class="mt-3">
            <v-col cols="12" md="8">
              <video
                ref="preview"
                class="preview"
                controls
                autoplay
                playsinline
              />
            </v-col>
            <v-col cols="12" md="4">
              <table data-test-id="table-video-description" class="video-description">
                <tr>
                  <th>{{ $t('home.order') }}</th>
                  <td>
                    <!-- && editing_video.id === item.id -->
                    <div v-if="is_editing_video" style="display: flex; align-items: center; gap: 8px;">
                      <v-text-field
                        v-model="editing_video_name"
                        dense
                        hide-details
                        @keyup.enter="saveVideoName(item)"
                        @keyup.esc="cancelEditVideoName()"
                      />
                      <v-btn :disabled="editing_video_name === ''" icon small color="success" @click="saveVideoName(item)">
                        <v-icon small>
                          mdi-check
                        </v-icon>
                      </v-btn>
                      <v-btn icon small color="error" @click="cancelEditVideoName()">
                        <v-icon small>
                          mdi-close
                        </v-icon>
                      </v-btn>
                    </div>
                    <div v-else>
                      <span>{{ item.name }}</span>

                      <v-chip
                        v-if="item.name_edit_count && item.name_edit_count > 0 && isDevModeEnabled"
                        x-small
                        color="orange"
                        text-color="white"
                        class="ml-2"
                      >
                        Edit: {{ item.name_edit_count }}
                      </v-chip>
                      <v-btn v-if="isDevModeEnabled" icon small @click="editVideoName(item)">
                        <v-icon small>
                          mdi-pencil
                        </v-icon>
                      </v-btn>
                    </div>
                  </td>
                </tr>
                <tr>
                  <th>{{ $t('home.file-size') }}</th>
                  <td>{{ getFileSize(item) }}</td>
                </tr>
                <tr>
                  <th>{{ $t('home.file-type') }}</th>
                  <td>{{ item.filetype }}, {{ item.speed }} {{ !item.isTranscoded ? '(raw)' : '' }}</td>
                </tr>
                <tr>
                  <th>{{ $t('home.resolution') }}</th>
                  <td>{{ item.resolution }}</td>
                </tr>
                <tr>
                  <th>{{ $t('home.duration') }}</th>
                  <td>{{ getDurationText(item) }}</td>
                </tr>
                <tr>
                  <th>Google Drive</th>
                  <td><a :href="item.shareLink" target="_blank">view</a></td>
                </tr>
                <tr>
                  <th>{{ $t('weight') }}</th>
                  <td>{{ item.weight === null ? 'N/A' : formatWeight(item.weight) }}</td>
                </tr>
              </table>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12" class="mb-3">
              <div class="pa-2" style="background-color: #eeeeee; max-height: 200px; max-width: 700px; overflow: scroll;">
                <div>{{ $t('home.remark') }}</div>
                <code style="white-space: pre; background-color: #eeeeee;" v-text="item.remark.trim()" />
              </div>
            </v-col>
          </v-row>
        </td>
      </template>
    </v-data-table>

    <v-alert v-if="not_uploaded_video_count > 0" type="error" class="mt-3">
      <v-row>
        <div class="my-auto ml-2 pa-2">
          <h3 class="headline font-weight-bold">
            {{ $t('found-not-uploaded-video-warning', [not_uploaded_video_count]) }}
          </h3>
          <div>{{ $t('cant-upload-video-contact-admin') }}</div>
        </div>
        <v-spacer />
        <v-btn
          data-test="v-btn-upload-all"
          color="white"
          class="black--text my-auto mr-3"
          :loading="is_uploading_all"
          @click="uploadAll()"
          @click.right.prevent="uploadAll({recursive: true})"
        >
          <v-icon color="primary" left>
            mdi-upload
          </v-icon>
          {{ $t('home.press-upload') }}
          ({{ not_uploaded_video_count }})
        </v-btn>
      </v-row>
    </v-alert>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import moment from 'moment'
import RecordRTC from 'recordrtc'
import { mapState, mapGetters } from 'vuex'
import { orderBy } from 'lodash'
import { createFFmpeg } from '@ffmpeg/ffmpeg'
import { IVideo, STATUS } from '~/plugins/db'
import { byteToMB, share, uploadVideo } from '~/api/googledrive'
import { copyToClipboard, formatDuration } from '~/api/utils'
import { playSound, promiseAllLimit, wait } from '~/utils'
import { SettingState } from '~/store/settings'
import { transcodeMP4, getSeekableBlob, formatWeight } from '~/utils/video'
import { Company, VDataTableHeader, VDataTableOption } from '~/models'

export default Vue.extend({
  data () {
    return {
      table: {
        headers: [
          { text: '', value: 'data-table-expand' },
          { text: this.$t('home.date-time'), value: 'createdAt' },
          { text: this.$t('home.order'), value: 'name' },
          { text: '', value: 'filetype', align: 'center', cellClass: 'fit' },
          { text: this.$t('home.status'), value: 'status', align: 'center', cellClass: 'fit' },
          { text: '', value: 'actions', cellClass: 'fit', sortable: false }
        ] as VDataTableHeader[],
        items: [] as IVideo[],
        total_items: 1,
        options: {
          page: 1,
          itemsPerPage: 5,
          sortBy: ['createdAt'],
          sortDesc: [true],
          groupBy: [],
          groupDesc: [],
          mustSort: true,
          multiSort: false
        } as VDataTableOption,
        expanded: [],
        loading: false
      },

      creating_hook: null as any,
      updating_hook: null as any,

      not_uploaded_video_count: 0,
      retry_upload_timer: null as NodeJS.Timer | null,

      search: {
        enable: false,
        keyword: ''
      },

      upload_device_status_timer: null as NodeJS.Timer | null,

      is_uploading_all: false,

      editing_video: null as IVideo | null,
      is_editing_video: false,
      editing_video_name: ''
    }
  },

  computed: {
    ...mapState('gapi', { isSignedInWithGoogle: 'isAuth' }),

    ...mapGetters('gapi', {
      gapiUser: 'currentUser'
    }),

    ...mapGetters('devmode', {
      isDevModeEnabled: 'isDevModeEnabled',
      devModeAdminName: 'adminName'
    }),

    settings (): SettingState {
      return this.$store.state.settings
    }

  },

  watch: {
    'table.expanded': {
      handler (val: IVideo[]) {
        if (val.length > 0) {
          const video = val[0]
          this.$nextTick(() => {
            const el = this.$refs.preview as HTMLVideoElement
            el.src = ''
            el.srcObject = null
            el.muted = false
            el.volume = 1

            if (video.blob) {
              el.src = URL.createObjectURL(video.blob)
            }
          })
        }
      }
    },

    'table.options': {
      async handler () {
        this.table.items = await this.getTableItems(this.table.options, this.search.keyword)
      },
      immediate: true
    },

    'search.keyword': {
      async handler () {
        this.table.items = await this.getTableItems(this.table.options, this.search.keyword)
      },
      immediate: true
    }
  },

  mounted () {
    ;(async () => {
      await this.resetUploadQueue()
      this.table.items = await this.getTableItems(this.table.options, this.search.keyword)
      this.table.total_items = (await this.getTotalVideoCount()) || 1
      this.not_uploaded_video_count = await this.getNotUploadedVideoCount()

      await wait(10 * 1000)
      this.uploadDeviceStatus()
      this.upload_device_status_timer = setInterval(this.uploadDeviceStatus, 30 * 60 * 1000)
    })()

    setTimeout(() => {
      if (this.settings.local.automaticallyRemoveOldVideo) {
        this.removeOldVideos()
      }

      if (this.settings.local.automaticallyRetryUploadVideo) {
        if (!localStorage.getItem('open-setting')) {
          this.uploadAll()
          this.retry_upload_timer = setInterval(this.uploadAll, 60 * 60 * 1000)
        }
      }
    }, 2 * 1000)

    this.registerHooks()
  },

  beforeDestroy () {
    this.unsubscribeHooks()

    if (this.retry_upload_timer) {
      clearInterval(this.retry_upload_timer as unknown as number)
    }
    if (this.upload_device_status_timer) {
      clearInterval(this.upload_device_status_timer as unknown as number)
    }
  },

  methods: {
    // --------------------------------------------------------------------------------
    // Setup & Teardown
    // --------------------------------------------------------------------------------
    registerHooks () {
      const self = this

      // Creating Hook
      this.creating_hook = function (_: any, video: IVideo) {
      // @ts-ignore
        this.onsuccess = (pk: number) => {
          setTimeout(() => {
            self.onNewVideoAdded({ id: pk, ...video })
            self.table.total_items += 1
          }, 100)
        }
      }
      this.$db.table('videos').hook('creating', this.creating_hook)

      // Updating Hook
      this.updating_hook = function (modifications: any) {
        if ('status' in modifications) {
          setTimeout(self.updateNotUploadedVideoCount, 1000)
        }
        return {}
      }
      this.$db.table('videos').hook('updating', this.updating_hook)
    },

    unsubscribeHooks () {
      this.$db.table('videos').hook('creating').unsubscribe(this.creating_hook)
      this.$db.table('videos').hook('updating').unsubscribe(this.updating_hook)
    },

    // --------------------------------------------------------------------------------
    // Event Handler
    // --------------------------------------------------------------------------------
    async onNewVideoAdded (video: IVideo) {
      if (this.settings.company.POST_RECORD_ACTION_SEND_WEBHOOK_BEFORE_VIDEO_UPLOAD) {
        alert('send webhook')
        this.sendPostRecordWebhook(video)
      }

      // this.table.items.unshift(video)
      this.table.items = [video, ...this.table.items.slice(0, this.table.options.itemsPerPage - 1)]
      this.$snackbar('info', `Uploading ${video.name}`)
      try {
        // check if video is uploading (prevent duplicate upload)
        if (video.status !== STATUS.NOT_UPLOADED) {
          return
        }

        await this.upload(video, this.settings.local.filetype, this.settings.local.speed)

        const com_setting = this.settings.company
        if (com_setting.WEIGHT_ONLY_MODE_ENABLE && com_setting.WEIGHT_ONLY_MODE_PRINT_METHOD === 'webprint') {
          const url = await this.$axios.$post('/api/picking/print/weight-slip/preview/', { video_name: video.name })
          window.open(url + '?autoprint=1&autoclose=1', '_blank')
        }
      } catch (error) {
        console.error(error)
      }

      await this.updateNotUploadedVideoCount()
    },

    async sendPostRecordWebhook (video: IVideo) {
      try {
        await this.$axios.post('/api/picking/pick-orders/post-record-action/send-webhook/', {
          name: video.name,
          video_url: null,
          file_size: video.blob !== null ? video.blob.size : 0,
          duration: video.duration,
          resolution: video.resolution,
          record_date: video.createdAt,
          filetype: video.filetype,
          speed: video.speed,
          drive_id: video.drive_id,
          drive_file_id: video.drive_file_id,
          drive_folder_id: video.drive_folder_id,
          drive_account: video.drive_account,
          is_return: video.is_return,
          scan_logs: video.scan_logs,
          diff_logs: video.diff_logs,
          audio: video.audio,
          weight: video.weight !== null ? video.weight.toFixed(2) : null,
          extra: video.extra
        })
        this.$snackbar('success', 'Webhook sent')
      } catch (error) {
        this.$snackbar('error', 'Failed to send webhook')
        console.error(error)
      }
    },

    cancelUpload (video: IVideo) {
      if (!confirm(this.$t('confirm.confirm-cancel-upload'))) {
        return
      }
      video.status = 'NOT_UPLOADED'
      video.remark += this.$t('error.cancel-by-user', [moment().format('YYYY-MM-DD HH:mm')])
      this.$db.table('videos').update(video.id, video)
    },

    async uploadAll (opt?: { recursive: boolean }) {
      opt = opt || { recursive: false }
      localStorage.removeItem('open-setting')

      if (opt.recursive) {
        const cont = confirm('ยืนยันการอัพโหลดทั้งหมด (Recursive)')
        if (!cont) { return }
      }

      if (this.is_uploading_all) {
        this.$snackbar('info', this.$t('home.uploading-videos'))
        return
      }

      if (this.settings.local.filetype === 'mp4') {
        const result = await this.test_ffmpeg()
        if (!result.success) {
          this.$alert({ text: 'ไม่สามารถอัพโหลดวิดีโออัตโนมัติได้ กรุณาติดต่อ Dobybot Support' })
          return
        }
      }

      let videos = await this.getLeaseRetryUploadVideo()
      videos = orderBy(videos, 'upload_attempt').slice(0, 50)

      if (videos.length === 0) {
        return
      }

      for (const video of videos) {
        video.status = 'QUEUED_FOR_UPLOAD'
        this.$db.table('videos').put(video)
      }

      const promises = [] as (() => Promise<any>)[]
      this.table.items = videos
      for (const video of this.table.items) {
        // if (video.upload_attempt > 20) {
        //   continue
        // }
        promises.push(async () => {
          try {
            const _video = await this.$db.table('videos').where('id').equals(video.id).first()
            if (_video.status !== STATUS.QUEUED_FOR_UPLOAD) {
              return null
            }
            await this.upload(video, this.settings.local.filetype)
            return null
          } catch (error) {
            return error
          }
        })
      }

      if (promises.length === 0) {
        return
      }
      this.$snackbar('info', this.$t('home.uploading-n-videos', [promises.length]))

      this.is_uploading_all = true

      try {
        const errors = await promiseAllLimit(1, promises)
        if (errors.every(e => e === null)) {
          this.$snackbar('success', this.$t('home.upload-success'))
        } else {
          console.error(errors)
        }
      } catch (error) {
        console.error(error)
      }

      this.is_uploading_all = false

      const count_not_uploaded = await this.getNotUploadedVideoCount()
      if (opt.recursive && count_not_uploaded > 0) {
        await this.uploadAll({ recursive: true })
      }
    },

    async test_ffmpeg () {
      try {
        if (!window.ffmpeg) {
          window.ffmpeg = createFFmpeg({
            log: process.env.NODE_ENV === 'development',
            corePath: '/static/js/@ffmpeg/core@0.11.0/ffmpeg-core.js'
          })
          await window.ffmpeg.load()
        }
      } catch (error) {
        console.error(error)
        return {
          success: false, msg: `Failed to load ffmpeg, ${error}`
        }
      }

      const test_video_url = require('~/assets/video/test-h264.webm')
      const blob = await fetch(test_video_url).then(r => r.blob())
      try {
        await transcodeMP4(blob, { speed: this.settings.local.speed })
      } catch (error: any) {
        console.error(error)
        return {
          success: false, msg: `Failed to transcode mp4 ${error.toString()}`
        }
      }

      return {
        success: true, msg: ''
      }
    },

    async onUploadButtonClick (video: IVideo, filetype:'webm'|'mp4' = 'webm', speed:'1x'|'2x' = '1x') {
      try {
        await this.upload(video, filetype, speed)
        this.$nuxt.$emit('focus-control-text-field')
      } catch (error) {
        console.error(error)
      }
    },

    async onVideoUploaded (video: IVideo) {
      try {
        const res = await this.$axios.post('/api/picking/pick-orders/post-record-action/', {
          name: video.name,
          video_url: video.shareLink,
          file_size: video.blob !== null ? video.blob.size : 0,
          duration: video.duration,
          resolution: video.resolution,
          record_date: video.createdAt,
          filetype: video.filetype,
          speed: video.speed,
          drive_id: video.drive_id,
          drive_file_id: video.drive_file_id,
          drive_folder_id: video.drive_folder_id,
          drive_account: video.drive_account,
          is_return: video.is_return,
          scan_logs: video.scan_logs,
          diff_logs: video.diff_logs,
          audio: video.audio,
          weight: video.weight !== null ? video.weight.toFixed(2) : null,
          extra: video.extra
        })
        const record_balance = res.data.record_balance
        this.$store.commit('wallet/set', { record_balance })
      } catch (error: any) {
        console.error(error)
        if (error.response) {
          this.$snackbar('error', this.$t('error.fail-to-connect-database', [error.response.status]))
          video.status = 'NOT_UPLOADED'
          video.remark += '\n' + `[${error.response.status}] ${JSON.stringify(error.response.data)}`
          await this.$db.table('videos').put(video)
        }

        video.status = 'NOT_UPLOADED'
        video.remark += '\n' + `เกิดข้อผิดพลาด ${error}`
        await this.$db.table('videos').put(video)

        throw error
      }
    },

    async uploadDeviceStatus () {
      const device_id = localStorage.getItem('login.device_id')
      const payload = {
        device_id,
        videos: {
          total: this.table.total_items,
          not_uploaded: this.not_uploaded_video_count,
          datetime: this.$moment().format()
        },
        settings: {
          ...this.settings.local
        },
        version: this.$version
      }

      try {
        await this.$axios.post('/api/users/device-status/', payload)
      } catch (error) {
        console.error(error)
      }
    },

    onForceUploadButtonClick (video: IVideo) {
      if (confirm(this.$t('confirm.confirm-upload-raw-video'))) {
        this.upload(video, 'webm', '1x', { skip_transcoding: true, is_retry: false })
      }
    },

    async onTranscodeVideoButtonClick (video: IVideo, filetype: 'webm'|'mp4', speed: '1x'|'2x') {
      const old_status = video.status
      video.status = 'TRANSCODING'
      try {
        video = await this.transcodeVideo(video, filetype, speed)
      } catch (error) {
        this.$snackbar('error', error)
      }
      video.status = old_status
    },

    async updateNotUploadedVideoCount () {
      this.not_uploaded_video_count = await this.getNotUploadedVideoCount()
    },

    // --------------------------------------------------------------------------------
    // IndexedDB Actions
    // --------------------------------------------------------------------------------
    getTableItems (opt: VDataTableOption, search_keyword: string): Promise<IVideo[]> {
      let query = this.$db.table('videos')

      query = query.orderBy(opt.sortBy[0])
      query = query.filter((video: IVideo) => !!video)

      if (search_keyword) {
        query = query.filter((video: IVideo) => {
          const keyword = search_keyword.toLowerCase()
          const name = video.name.toLowerCase()
          return name.includes(keyword)
        })
      }

      if (opt.sortDesc[0]) {
        query = query.reverse()
      }

      return query
        .offset((opt.page - 1) * opt.itemsPerPage)
        .limit(opt.itemsPerPage)
        .toArray()
    },

    getTotalVideoCount (): Promise<number> {
      return this.$db.table('videos').where('id').above(0).count()
    },

    async getNotUploadedVideoCount (): Promise<number> {
      const c1 = await this.$db.table('videos').where('status').equals('NOT_UPLOADED').count()
      // const c2 = await this.$db.table('videos').where('status').equals('QUEUED_FOR_UPLOAD').count()
      return c1
    },

    async resetUploadQueue () {
      const videos1 = await this.$db.table('videos').where('status').equals('QUEUED_FOR_UPLOAD').toArray()
      const videos2 = await this.$db.table('videos').where('status').equals('UPLOADING').toArray()
      const promises: Promise<any>[] = []

      for (const video of [...videos1, ...videos2]) {
        video.status = 'NOT_UPLOADED'
        promises.push(this.$db.table('videos').put(video))
      }
      await Promise.all(promises)
    },

    // getNotUploadedVideos (): Promise<IVideo[]> {
    //   return this.$db.table('videos').where('status').equals('NOT_UPLOADED').toArray()
    // },

    async getLeaseRetryUploadVideo (): Promise<IVideo[]> {
      const video_list_1 = await this.$db.table('videos')
        .where('status').equals('NOT_UPLOADED')
        .and((video: IVideo) => video !== null && video.upload_attempt < 15 && video.duration > 0)
        .toArray()
      // const video_list_2 = await this.$db.table('videos')
      //   .where('status').equals('QUEUED_FOR_UPLOAD')
      //   .and((video: IVideo) => video !== null && video.upload_attempt < 11 && video.duration > 0)
      //   .toArray()
      return video_list_1
    },

    removeOldVideos () {
      const duration = this.settings.local.storageDuration
      const cutoff = moment().subtract(duration, 'days')
      this.$db.table('videos')
        .where('status').equals('UPLOADED')
        .and((item: IVideo) => cutoff.isAfter(item.createdAt))
        .delete()
        .then((delete_count: number) => {
          console.log('deleted', delete_count)
        })
    },

    // --------------------------------------------------------------------------------
    // Google Drive Actions
    // --------------------------------------------------------------------------------
    async upload (
      video: IVideo,
      filetype: 'webm'|'mp4'|'webm-raw' = 'webm',
      speed: '1x'|'2x' = '1x',
      opt = { skip_transcoding: false, is_retry: false }
    ) {
      const dbVideo = await this.$db.table('videos').where('id').equals(video.id).first()
      if (![STATUS.QUEUED_FOR_UPLOAD, STATUS.NOT_UPLOADED].includes(dbVideo.status)) {
        return
      }
      if (video === null) {
        return
      }
      if (!video.upload_attempt) { video.upload_attempt = 0 }
      video.upload_attempt += 1
      video.remark += `\n[${this.$datetime()}] Video Name=${video.control_code_log}`
      video.remark += `\n[${this.$datetime()}] Upload#${video.upload_attempt}`

      const no_record_mode = this.settings.company.ENABLE_NO_RECORD_MODE || this.settings.company.WEIGHT_ONLY_MODE_ENABLE

      if (no_record_mode) {
        video.shareLink = ''
        video.status = 'UPLOADED'
        video.drive_id = this.settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID || null
        video.drive_file_id = ''
        video.drive_folder_id = ''
        video.drive_account = ''
        video.remark += '\n' + 'No Record Mode'
        await this.$db.table('videos').put(video)

        await this.onVideoUploaded(video)
        this.$snackbar('success', this.$t('snackbar.save_success'))
        this.$nuxt.$emit('focus-control-text-field')
      } else {
        if (!opt.skip_transcoding && filetype !== 'webm-raw') {
          video.status = 'TRANSCODING'
          try {
            video = await this.transcodeVideo(video, filetype, speed)
          } catch (error) {
            const message = this.$t('error.can-not-upload-transcoding-fail', [this.$datetime()])
            video.remark += '\n' + message + '\n' + error
            video.status = 'NOT_UPLOADED'
            this.$snackbar('error', message)
            await this.$db.table('videos').put(video)

            if (this.settings.company.RECORD_FAILED_TRANSCODE_SHOW_ERROR) {
              this.playAlarmSound()
              this.showVideoCorruptError(error)
            }

            throw new Error('Transcode Failed')
          }
        }

        if (!this.isSignedInWithGoogle) {
          const message = this.$t('error.can-not-upload-transcoding-fail-g-drive', [this.$datetime()])
          video.status = 'NOT_UPLOADED'
          video.remark += '\n' + message
          this.$snackbar('error', message)
          await this.$db.table('videos').put(video)
          throw new Error('Upload Failed')
        }

        const company = this.$auth.user.company as Company
        if (
          this.settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID &&
          company.google_drive_limit &&
          company.google_drive_usage >= company.google_drive_limit
        ) {
          const message = this.$t('error.full-storage-g-drive', [this.$datetime()])
          video.status = 'NOT_UPLOADED'
          video.remark += '\n' + message
          this.$snackbar('error', message)
          await this.$db.table('videos').put(video)
          throw new Error('Upload Failed: Insufficient space on destination google drive')
        }

        if (!this.settings.local.googleDriveTodayFolder) {
          throw new Error('Upload Failed: Unabled to find googleDriveTodayFolder')
        }

        const today = moment().format('YYYY-MM-DD')
        if (this.settings.local.googleDriveTodayFolder.name !== today) {
          const message = this.$t('error.incorrect-destination-folder')
          video.status = 'NOT_UPLOADED'
          video.remark += `\n[${this.$datetime()}] ${message}`
          await this.$db.table('videos').put(video)

          this.$snackbar('error', message)
          this.$nuxt.$emit('UPDATE_GOOGLE_DRIVE_TODAY_FOLDER')

          setTimeout(() => {
            if (!opt.is_retry) {
              this.upload(video, filetype, speed, { ...opt, is_retry: true })
            }
          }, 5000)

          return new Error('Upload Failed: Incorrect upload folder')
        }

        if (this.settings.company.RECORD_GMAIL_ALLOWED_LIST.length > 0) {
          if (!this.settings.company.RECORD_GMAIL_ALLOWED_LIST.includes(this.gapiUser.email)) {
            const message = this.$t('error.g-drive-account-mismatch', [this.$datetime()])
            video.status = 'NOT_UPLOADED'
            video.remark += '\n' + message + '\n' + this.$t('home.google-account') + this.settings.company.RECORD_GMAIL_ALLOWED_LIST.join(', ')
            this.$snackbar('error', message)
            await this.$db.table('videos').put(video)
            throw new Error('Upload Failed')
          }
        }

        // Start Uploading
        video.status = 'UPLOADING'

        try {
          const gdriveFile = await uploadVideo(
            video,
            this.settings.local.googleDriveTodayFolder.id
          )

          if (!gdriveFile.id) {
            throw new Error('File id is undefined\n' + JSON.stringify(gdriveFile))
          }

          if (this.settings.company.POST_RECORD_ACTION_SHARE_VIDEO_LINK) {
            const shareLink = await share(gdriveFile.id)
            video.shareLink = shareLink
          } else {
            video.shareLink = `https://drive.google.com/file/d/${gdriveFile.id}`
          }

          video.status = 'UPLOADED'
          video.drive_id = this.settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID || null
          video.drive_file_id = gdriveFile.id
          video.drive_folder_id = this.settings.local.googleDriveTodayFolder.id
          video.drive_account = this.gapiUser.email
          video.remark += `\n[${this.$datetime()}] ` + this.$t('home.upload-success-gmail', [video.drive_account])
          await this.$db.table('videos').put(video)

          await this.onVideoUploaded(video)
          this.$snackbar('success', this.$t('home.upload-video-success', [`${video.name}.${video.filetype}`]))
          this.$nuxt.$emit('focus-control-text-field')
        } catch (error) {
          video.status = 'NOT_UPLOADED'
          video.remark += `\n[${this.$datetime()}] ${error} ${JSON.stringify(error)}`
          await this.$db.table('videos').put(video)

          throw error
        }
      }
    },

    transcodeVideo (video: IVideo, filetype: 'webm'|'mp4', speed: '1x'|'2x'): Promise<IVideo> {
      return new Promise((resolve, reject) => {
        if (video.isTranscoded) {
          console.log('File already transcoded')
          return resolve(video)
        }

        if (video.blob.size > 1e9) {
          console.log('File too large (over 1GB)')
          return resolve(video)
        }

        // Timeout, บางที getSeekableVideo ไม่เรียก callback ให้ timeout เป็น error ไป
        const timeout = setTimeout(() => {
          reject(new Error('Timeout, Failed to transcode video'))
        }, 60 * 1000)

        if (filetype === 'webm') {
          getSeekableBlob(video.blob, (seekableBlob) => {
            video.blob = seekableBlob
            video.isTranscoded = true
            this.$db.table('videos').put(video)
            clearTimeout(timeout)
            return resolve(video)
          })
        }

        if (filetype === 'mp4') {
          transcodeMP4(video.blob, { speed })
            .then((mp4Blob) => {
              video.blob = mp4Blob
              video.filetype = 'mp4'
              video.speed = speed
              video.isTranscoded = true
              this.$db.table('videos').put(video)
              clearTimeout(timeout)
              return resolve(video)
            }).catch((error) => {
              clearTimeout(timeout)
              return reject(error)
            })
        }
      })
    },

    // --------------------------------------------------------------------------------
    // Helper Funtions
    // --------------------------------------------------------------------------------
    download (video: IVideo) {
      RecordRTC.invokeSaveAsDialog(video.blob, `${video.name}.${video.filetype}`)
    },

    copy (text: string) {
      copyToClipboard(text)
      this.$snackbar('info', `'${text}' is copied`)
    },

    getFileSize (video: IVideo): string {
      if (!video.blob) {
        return 'None'
      }
      return byteToMB(video.blob.size)
    },

    getDurationText (video: IVideo): string {
      return formatDuration(video.duration)
    },

    formatDate (datetime: string): string {
      return moment(datetime).format('DD/MM/YY - HH:mm')
    },

    formatWeight (weight: number) {
      return formatWeight(weight, this.settings.company.WEIGHT_SCALE_PREFER_UNIT)
    },

    showVideoCorruptError (error: any) {
      this.$alert({
        title: 'การแปลงไฟล์ล้มเหลว',
        text: `
          ไม่สามารถแปลงไฟล์วิดีโอได้ <br><br>

          วิธีการแก้ปัญหาเบื้องต้น
          <ol>
            <li>กดปุ่ม F5 หรือ Refresh หน้าเว็บหนึ่งครั้ง</li>
            <li>ทำการ Clear Browser Cache <a href="https://support.google.com/accounts/answer/32050?hl=th&co=GENIE.Platform%3DDesktop" target="_blank">ดูวิธีการ</a></li>
          </ol>
          <br><br>
          <div style="border: 1px solid black; padding: 20px; font-weight: bold;">
            หากยังแก้ไขไม่ได้ กรุณาติดต่อ Dobybot Support โดยตรง
            <br>
            <a target="#" class="mt-2" href="https://page.line.me/856wntly?openQrModal=true">
              LINE: @dobybot
            </a> หรือ <a href="tel:*********">โทร: 02-662-8500</a>
          </div>
          <br><br>

          <pre>Error Messages:</pre>
          <div style="border: 1px solid #aaaaaa; padding: 20px; background-color: #f2f2f2; margin-top: 5px;">
            <code style="background-color: #f2f2f2;">${error}</code>
          </div>
        `,
        theme: 'error'
      })
    },

    async playAlarmSound () {
      if (this.settings.local.enableSound) {
        try {
          await playSound('alarm')
        } catch (error) {
          // console.error(error)
        }
      }
    },

    editVideoName (video: IVideo) {
      this.editing_video = video
      this.is_editing_video = true
      this.editing_video_name = video.name
    },

    cancelEditVideoName () {
      this.editing_video = null
      this.is_editing_video = false
      this.editing_video_name = ''
    },

    async saveVideoName (video: IVideo) {
      try {
        const oldName = video.name
        const newName = this.editing_video_name.trim()

        if (!video.name_edit_count) {
          video.name_edit_count = 0
        }

        if (oldName !== newName) {
          video.name_edit_count += 1
          video.name = newName

          // Determine who made the change
          let adminInfo = ''
          if (this.isDevModeEnabled && this.devModeAdminName) {
            adminInfo = ` by Admin (${this.devModeAdminName})`
          }

          const editCountText = video.name_edit_count > 0 ? `\nName Edit Count: ${video.name_edit_count}` : ''
          video.remark += `\n[${moment().format('YYYY-MM-DD HH:mm')}] Name changed from "${oldName}" to "${newName}"${adminInfo}${editCountText}`

          await this.$db.table('videos').update(video.id, {
            name: video.name,
            name_edit_count: video.name_edit_count,
            remark: video.remark,
            status: 'NOT_UPLOADED'
          })

          this.$snackbar('success', `Video name updated successfully (Edit #${video.name_edit_count})`)
        } else {
          this.$snackbar('info', 'No changes made to video name')
        }

        this.cancelEditVideoName()
      } catch (error) {
        console.error(error)
        this.$snackbar('error', 'Failed to update video name')
      }
    }

  }
})
</script>

<style scoped>
.preview {
  width: 100%;
}
table.video-description {
  width: 100%;
}
table.video-description th {
  text-align: left;
}
table.video-description td {
  text-align: right;
}
</style>
