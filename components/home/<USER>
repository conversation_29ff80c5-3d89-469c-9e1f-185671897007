<template>
  <v-dialog max-width="800" :value="value" @input="$emit('input', $event)">
    <v-card>
      <v-card-title class="headline grey lighten-2">
        {{ $t('home.tool') }}
        <v-spacer />
        <v-btn data-test-id="v-btn-close" icon @click="$emit('input', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-form ref="form" @submit.prevent="submit()">
          <v-text-field
            v-model="form.name"
            :rules="[$rules.required]"
            :label="$t('home.testing-tool')"
          />
          <v-text-field
            v-model.number="form.duration"
            type="number"
            :rules="[$rules.required]"
            :label="$t('home.start-test-duration')"
          />
          <v-select
            v-model="form.file_formats"
            :label="$t('home.file-type')"
            multiple
            chips
            :items="file_formats"
            :rules="[$rules.required]"
            return-object
            item-text="name"
            item-value="name"
          />
          <v-btn type="submit" block color="primary">
            {{ $t('home.start-test') }}
          </v-btn>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { VForm } from '~/models'
export default Vue.extend({
  props: {
    value: {
      type: Boolean,
      required: true
    }
  },
  data () {
    return {
      form: {
        name: '',
        duration: 60,
        file_formats: [] as any[]
      }
    }
  },
  computed: {
    file_formats (): any[] {
      const items = [
        { filetype: 'webm', mimetype: 'video/webm;codecs=vp8', speed: '1x', name: '' },
        { filetype: 'webm', mimetype: 'video/webm;codecs=vp9', speed: '1x', name: '' },
        { filetype: 'webm', mimetype: 'video/webm;codecs=h264', speed: '1x', name: '' },
        { filetype: 'mp4', mimetype: 'video/webm;codecs=h264', speed: '1x', name: '' },
        { filetype: 'mp4', mimetype: 'video/webm;codecs=h264', speed: '2x', name: '' }
      ]

      for (const item of items) {
        item.name = `${item.filetype}, ${item.mimetype.split(';')[1]}, speed=${item.speed}`
      }

      return items
    }
  },
  mounted () {
    this.form.file_formats = [...this.file_formats]
  },
  methods: {
    submit () {
      if (!(this.$refs.form as never as VForm).validate()) {
        return
      }
      this.$emit('submit', this.form)
    }
  }
})
</script>
