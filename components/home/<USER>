<template>
  <v-data-table
    :headers="headers"
    :items="items"
    hide-default-footer
    disable-pagination
  >
    <template #item="{ item }">
      <tr class="fit-height" :class="`${getColor(item.status)}`">
        <td>
          <div style="white-space: nowrap">
            <v-btn
              icon
              outlined
              x-small
              color="success"
              :disabled="!!settings.company.RECORD_CHECK_BUTTON_DISABLE"
              @click="$emit('click-add', item.sku)"
              @click.right.prevent="
                $emit('click-add-all', [
                  item.sku,
                  item.total_amount - item.checked_amount,
                ])
              "
            >
              <v-icon>mdi-plus</v-icon>
            </v-btn>
            <span class="ml-2">
              {{ item.sku }}
            </span>
          </div>
        </td>
        <td>
          <span style="white-space: pre">{{ item.barcode || "-" }}</span>
        </td>
        <td>
          {{ item.name }}
        </td>
        <td>
          <span style="font-size: 1.5em">
            {{ item.checked_amount }}/{{ item.total_amount }}
          </span>
        </td>
      </tr>
    </template>
  </v-data-table>
</template>

<script lang="ts">
import Vue from "vue";
import { orderBy } from "lodash";
import { OrderItem, PickItem, ScanLog, VDataTableHeader } from "~/models";
import { SettingState } from "~/store/settings";

export interface TableItem {
  sku: string;
  name: string;
  image: string;
  checked_amount: number;
  total_amount: number;
  barcode: string;
  key: string;
  status: "ok" | "under" | "over";
}

export default Vue.extend({
  props: {
    // add some props
    orderItems: {
      type: Array,
      default: () => [] as OrderItem[],
    },
    scanLogs: {
      type: Array,
      default: () => [] as ScanLog[],
    },
  },
  computed: {
    settings(): SettingState {
      return this.$store.state.settings;
    },
    headers(): VDataTableHeader[] {
      return [
        { text: "SKU", value: "sku", sortable: false },
        { text: "Barcode", value: "barcode", sortable: false },
        { text: "ชื่อสินค้า", value: "name", sortable: false },
        { text: "จำนวน", value: "amount", sortable: false },
        // { text: 'เวลา', value: 'scan-time', sortable: false },
      ];
    },
    items(): TableItem[] {
      const result = [] as TableItem[];
      const order_items = this.orderItems as PickItem[];
      for (const item of order_items) {
        // const checked_amount = this.getScannedAmount(item, order_items)
        result.push({
          sku: item.sku,
          name: item.name,
          image: item.image,
          barcode: item.barcode || "-",
          key: item.key,
          checked_amount: 0,
          total_amount: item.number,
          status: this.getStatus(0, item.number),
        });
      }

      const scan_logs = orderBy(this.scanLogs, "amount") as ScanLog[];
      const over_scan_logs = [] as ScanLog[];
      for (const log of scan_logs) {
        const item = result.find((i) => {
          return (
            i.checked_amount + log.amount <= i.total_amount &&
            i.key === log.item.key
          );
        });

        if (!item) {
          over_scan_logs.push(log);
          continue;
        }

        if (item?.checked_amount + log.amount <= item.total_amount) {
          item.checked_amount += log.amount;
          item.status = this.getStatus(item.checked_amount, item.total_amount);
        }
      }

      // Handle Scan over
      for (const log of over_scan_logs) {
        const items = result.filter((i) => {
          return i.key === log.item.key;
        });

        if (items.length > 0) {
          items[items.length - 1].checked_amount += log.amount;
          for (const item of items) {
            item.status = "over";
          }
        }
      }

      const status_order = {
        over: 1,
        under: 2,
        ok: 3,
      };

      result.sort((a, b) => {
        return status_order[a.status] - status_order[b.status];
      });

      return result;
    },
  },

  methods: {
    // getScannedAmount (item: PickItem): number {
    //   const scan_logs = this.scanLogs as ScanLog[]
    //   const has_barcode = !!item.barcode
    //   return sum(scan_logs.filter(l => l.barcode === item.sku || (has_barcode && l.barcode === item.barcode)).map(l => l.amount))
    // },
    getStatus(
      scanned_amount: number,
      required_amount: number
    ): "ok" | "over" | "under" {
      if (scanned_amount < required_amount) {
        return "under";
      }
      if (scanned_amount > required_amount) {
        return "over";
      }
      return "ok";
    },
    getColor(status: "ok" | "under" | "over") {
      const colors = {
        ok: "green lighten-3",
        under: "amber lighten-3",
        over: "red lighten-4",
      };
      return colors[status];
    },
    isScanLogsCompleted() {
      return this.items.every((i) => i.status === "ok");
    },
  },
});
</script>

<style scoped>
table > tbody > tr.fit-height > td {
  height: 22px;
}
</style>
