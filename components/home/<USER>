<template>
  <v-data-table
    dense
    :headers="headers"
    :items="items"
    :mobile-breakpoint="0"
    hide-default-footer
    disable-pagination
    @click:row="$emit('click:row', $event)"
  >
    <template #item.time_ms="{ item }">
      {{ getDurationText(item.time_ms / 1000) }}
    </template>

    <template #item.name="{ item }">
      {{ item.name || "-" }}
    </template>

    <template #item.barcode="{ item }">
      <span style="white-space: pre">{{ item.barcode || "-" }}</span>
    </template>

    <template #item.barcode="{ item }">
      <span style="white-space: pre">{{ item.barcode || "-" }}</span>
    </template>

    <template #item.action="{ item }">
      <v-btn icon color="error" @click="$emit('click-delete', item.index - 1)">
        <v-icon>mdi-delete</v-icon>
      </v-btn>
    </template>
  </v-data-table>
</template>

<script lang="ts">
import Vue, { PropOptions } from "vue";
import { orderBy } from "lodash";
import { ScanLog, VDataTableHeader } from "~/models";
import { formatDuration } from "~/api/utils";

export default Vue.extend({
  props: {
    scanLogs: {
      type: Array,
      default: () => [] as ScanLog[],
    },
    columns: {
      type: Array,
      default: () => ["index", "time_ms", "barcode", "amount", "action"],
    } as PropOptions<string[]>,
    orderBy: {
      type: Object,
      default: () => ({
        column: "time_ms",
        direction: "desc",
      }),
    },
  },
  computed: {
    headers(): VDataTableHeader[] {
      const headers = [
        { text: "ลำดับ", value: "index", sortable: false, width: 80 },
        { text: "เวลา", value: "time_ms", sortable: false, width: 100 },
        { text: "บาร์โค๊ด", value: "barcode", sortable: false },
        { text: "ชื่อสินค้า", value: "name", sortable: false },
        { text: "จำนวน", value: "amount", sortable: false },
        { text: "", value: "action", sortable: false },
      ];

      return headers.filter((h) => this.columns.includes(h.value));
    },
    items() {
      const items = this.scanLogs.map((item: any, index: number) => {
        return { index: index + 1, ...item };
      });

      return orderBy(items, [this.orderBy.column], [this.orderBy.direction]);
    },
  },
  methods: {
    getDurationText(value: number) {
      return formatDuration(value);
    },
  },
});
</script>
