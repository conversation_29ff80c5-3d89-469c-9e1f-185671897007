<template>
  <v-dialog max-width="760" :value="value" @input="$emit('input', $event)">
    <v-card>
      <v-card-title class="headline grey lighten-2">
        {{ $t('found-more-than-1-order') }}
        <!-- <v-spacer />
        <v-btn data-test-id="v-btn-close" icon @click="$emit('input', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn> -->
      </v-card-title>
      <v-card-text>
        <div class="my-5">
          {{ $t('please-select-order-you-wish-to-record') }}
        </div>
        <v-simple-table style="border: 1px solid #ddd;">
          <thead>
            <th class="text-left px-3 py-2">
              Order No.
            </th>
            <th class="text-left px-3">
              Tracking No.
            </th>
            <th class="text-left px-3">
              {{ $t('order-center.customer-name') }}
            </th>
            <th>
              {{ $t('order-center.record&image') }}
            </th>
            <th>
              {{ $t('order-center.ready') }}
            </th>
            <th>{{ $t('order-center.note') }}</th>
          </thead>
          <tbody>
            <tr v-for="order in orders" :key="order.order_number">
              <td style="white-space: nowrap;">
                <v-btn icon color="primary" @click="order_number = order.order_number">
                  <v-icon>mdi-arrow-right</v-icon>
                </v-btn>
                {{ order.order_number }}
              </td>
              <td style="white-space: nowrap;">
                <v-btn icon color="primary" @click="order_number = order.order_trackingno">
                  <v-icon>mdi-arrow-right</v-icon>
                </v-btn>
                {{ order.order_trackingno }}
              </td>
              <td>{{ order.order_customer }}</td>
              <td>
                <v-icon v-if="order.has_videos" small color="success" v-on="on">
                  mdi-check-bold
                </v-icon>
                <v-icon v-else color="error" small v-on="on">
                  mdi-close-thick
                </v-icon>
              </td>
              <td>
                <v-icon v-if="order.ready_to_ship" small color="success" v-on="on">
                  mdi-check-bold
                </v-icon>
                <v-icon v-else color="error" small v-on="on">
                  mdi-close-thick
                </v-icon>
              </td>
              <td>{{ order.remark }}</td>
            </tr>
          </tbody>
        </v-simple-table>

        <div class="mt-5 pt-5 px-5">
          <v-form @submit.prevent="$emit('select', order_number)">
            <v-text-field id="text-field-confirm-order-number" v-model="order_number" :label="$t('order-number-or-tracking-number-for-video-name')" outlined />
            <v-btn block color="primary" type="submit">
              {{ $t('continue') }}
            </v-btn>
          </v-form>
        </div>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    value: {
      type: Boolean,
      required: true
    },
    orders: {
      type: Array,
      required: true
    }
  },

  data () {
    return {
      order_number: ''
    }
  },

  watch: {
    value (val) {
      if (val) {
        this.order_number = ''
        this.$nextTick(() => {
          setTimeout(() => {
            document.getElementById('text-field-confirm-order-number')?.focus()
          }, 100)
        })
      }
    }
  }
})
</script>
