<template>
  <v-dialog
    max-width="500px"
    transition="dialog-transition"
    :value="value"
    @input="$emit('input', $event)"
  >
    <v-card>
      <v-card-text class="pt-5">
        <h1 class="text-center">
          <v-icon large>
            mdi-wallet
          </v-icon>
        </h1>
        <div class="text-center text-h6 mt-3">
          {{ $t('deposit.contact') }} <br>
          <a href="https://lin.ee/HZOUXJg">line@dobybot</a> {{ $t('or') }} <a href="tel:0633807777">************</a>
        </div>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    value: { type: Boolean, default: false }
  }
})
</script>
