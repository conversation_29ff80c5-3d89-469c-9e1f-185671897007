<template>
  <div>
    <v-data-table
      class="mt-4"
      dense
      :headers="log_headers"
      :items="logs"
    >
      <template #item.action="{ item }">
        <v-btn icon @click="openLog(item)">
          <v-icon>mdi-file-eye-outline</v-icon>
        </v-btn>
      </template>
      <template #item.timestamp="{ item }">
        {{ $datetime(item.timestamp) }}
      </template>
      <template #item.email="{ item }">
        {{ item.extra.request.data.EMAIL_FLAG }} /
        {{ item.extra.request.data.BUYER_CONTACT_EMAIL || "-" }}
      </template>
      <template #item.error="{ item }">
        <div>
          <details>
            <summary>
              {{ _get(item, 'extra.response.status_code', '[NO_DATA]') }} /
              {{ _get(item, 'extra.response.data.message.status') || _get(item, 'extra.response.data.code') || '[NO_DATA]' }} /
              <span
                v-if="
                  _get(item, 'extra.response.status_code') != 200 ||
                    _get(item, 'extra.response.data.code') != '200' ||
                    _get(item, 'extra.response.data.message.data.massage.status', 200) != 200"
              >
                error: {{ _get(item, 'extra.response.data.message', '[NO_DATA]') }}
              </span>

              <span v-else>
                <span v-if="!_get(item, 'extra.response.data.message.data.data.signed64pdf')">
                  error: NO PDF DATA
                </span>
                <span v-else>
                  OK
                </span>
              </span>
            </summary>
            <pre style="border: 1px solid black;">{{ item.extra.response }}</pre>
          </details>
          <!-- <v-divider />
          <div>
            DBB NOTE: อาจมีปัญหากับที่อยู่ผู้ซื้อ
          </div> -->
        </div>
        <!-- <div v-if="item.extra.response.data.code === '400'">
          400, {{ item.extra.response.data.message }}
        </div>
        <div v-if="item.extra.response.data.code === '500'">
          500, {{ item.extra.response }}
        </div> -->
      </template>
    </v-data-table>

    <v-dialog v-if="log_info" v-model="dialog" max-width="1024">
      <v-card style="height: 80vh; overflow: scroll;">
        <!-- <div style="width:100%;" class="text-end">
          <v-btn color="error" class="ms-auto" @click="dialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </div> -->
        <div style="max-height: 80vh; display: flex;">
          <JsonEditorVue v-model="log_info" :read-only="true" class="jse-theme-dark" style="width: 100%;" />
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import 'vanilla-jsoneditor/themes/jse-theme-dark.css'
import _ from 'lodash'

function JsonEditorVue () {
  return process.client
    ? import('json-editor-vue')
    : Promise.resolve({ render: h => h('div') })
}
export default Vue.extend({
  components: { JsonEditorVue },
  props: {
    logs: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      dialog: false,
      log_info: null as any,
      log_headers: [
        { text: '', value: 'action', align: 'center', width: '15px', sortable: false },
        { text: 'วัน-เวลา', value: 'timestamp' },
        { text: 'Event', value: 'extra.request.data.DOC_STATUS' },
        { text: 'Email', value: 'email' },
        { text: 'โดย', value: 'create_by' },
        { text: 'Error', value: 'error' }
      ]
    }
  },

  methods: {
    openLog (log: any) {
      this.log_info = log
      this.dialog = true
    },

    _get (obj: any, key: string, default_value: any) {
      return _.get(obj, key, default_value)
    }
  }
})
</script>
