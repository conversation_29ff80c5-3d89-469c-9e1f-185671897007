<template>
  <div>
    <v-tabs
      background-color="secondary"
      dark
      :value="tab"
      @change="$emit('update:tab', $event)"
    >
      <v-tabs-slider color="yellow" />

      <v-tab
        v-for="item in tab_items"
        :key="item"
        :tab-value="item"
      >
        {{ item }}
      </v-tab>
    </v-tabs>
    <v-tabs-items :value="tab">
      <v-tab-item key="Order Json" value="ORDER JSON">
        <div class="json-viewer">
          <JsonEditorVue v-if="orderJson" :value="orderJson" :read-only="true" class="jse-theme-dark full-width" />
          <div v-else>
            NO DATA
          </div>
        </div>
      </v-tab-item>
      <v-tab-item key="D1A Json" value="D1A JSON">
        <div class="json-viewer">
          <JsonEditorVue :value="d1aJson" :read-only="true" class="jse-theme-dark full-width" />
        </div>
      </v-tab-item>
      <v-tab-item key="D1A Json 2" value="D1A JSON 2">
        <div class="json-viewer">
          <JsonEditorVue :value="d1aJson2" :read-only="true" class="jse-theme-dark full-width" />
        </div>
      </v-tab-item>
      <v-tab-item key="Pick Order" value="PICK ORDER">
        <div class="json-viewer">
          <JsonEditorVue v-if="pickOrder" :value="pickOrder" :read-only="true" class="jse-theme-dark full-width" />
          <div v-else>
            NO DATA
          </div>
        </div>
      </v-tab-item>
      <v-tab-item key="PDF" value="PDF">
        <iframe
          v-if="pdf"
          class="mx-auto"
          style="width: 100%; aspect-ratio: 1/1.414; border: 1px solid #e0e0e0"
          :src="pdf?.replace(/view(?=[^view]*$)/, 'preview')"
          frameborder="0"
        />
      </v-tab-item>
      <v-tab-item key="PDF CUSTOM" value="PDF CUSTOM">
        <a v-if="pdfCustom" :href="pdfCustom" target="_blank">{{ pdfCustom }}</a>
        <span v-else>No custom template, please check company setting</span>
      </v-tab-item>
      <v-tab-item key="XML" value="XML">
        <div class="json-viewer">
          <xml-viewer :value="xml" @input="$emit('update:xml', $event)" />
        </div>
      </v-tab-item>
      <v-tab-item key="ERROR" value="ERROR">
        <div class="json-viewer">
          <JsonEditorVue :value="error" :read-only="true" class="jse-theme-dark full-width" />
        </div>
      </v-tab-item>
      <v-tab-item key="ERROR 2" value="ERROR 2">
        <div class="json-viewer">
          <JsonEditorVue :value="error2" :read-only="true" class="jse-theme-dark full-width" />
        </div>
      </v-tab-item>
      <v-tab-item key="Mapping Fields" value="Mapping Fields">
        <iframe
          style="height: 90vh;width: 100% ;border: 1px solid #e0e0e0"
          src="https://docs.google.com/spreadsheets/d/1wmlVb-7S5-GAJoyU61sJJLOhCAhJpyEvBckqWQ1G394/edit?usp=sharing"
          frameborder="0"
        />
      </v-tab-item>
      <v-tab-item key="Standard Sheet" value="Standard Sheet">
        <iframe
          style="height: 90vh;width: 100% ;border: 1px solid #e0e0e0"
          src="https://docs.google.com/spreadsheets/d/1bnXelWnDHKaUkfrZ92lzrQorJPSaHIgH/edit?gid=1948314310#gid=1948314310"
          frameborder="0"
        />
      </v-tab-item>
    </v-tabs-items>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import XmlViewer from '../../components/editor/xml-viewer.vue'
import { TaxMonitorDocument, OrderJson } from '~/models'
import 'vanilla-jsoneditor/themes/jse-theme-dark.css'

function JsonEditorVue () {
  return process.client
    ? import('json-editor-vue')
    : Promise.resolve({ render: h => h('div') })
}
export default Vue.extend({
  components: {
    JsonEditorVue,
    XmlViewer
  },
  props: {
    orderJson: {
      type: Object,
      required: false,
      default: () => null
    },
    pickOrder: {
      type: Object,
      required: false,
      default: () => null
    },
    d1aJson: {
      type: Object,
      required: true
    },
    d1aJson2: {
      type: Object,
      required: false,
      default: () => null
    },
    pdf: {
      type: String,
      required: false,
      default: ''
    },
    pdfCustom: {
      type: String,
      required: false,
      default: ''
    },
    xml: {
      type: String,
      required: false,
      default: ''
    },
    error: {
      type: Object,
      required: false,
      default: () => ({})
    },
    error2: {
      type: Object,
      required: false,
      default: () => null
    },
    tab: {
      type: String,
      required: true
    }
  },

  computed: {
    tab_items (): string[] {
      let _tab_items = [
        'ORDER JSON',
        'D1A JSON',
        'D1A JSON 2',
        'PICK ORDER',
        'PDF',
        'PDF CUSTOM',
        'XML',
        'ERROR',
        'ERROR 2',
        'Mapping Fields',
        'Standard Sheet'
      ]

      if (!this.orderJson) {
        _tab_items = _tab_items.filter(item => item !== 'ORDER JSON')
      }

      if (!this.pickOrder) {
        _tab_items = _tab_items.filter(item => item !== 'PICK ORDER')
      }

      if (!this.d1aJson2) {
        _tab_items = _tab_items.filter(item => item !== 'D1A JSON 2')
      }

      if (!this.error2) {
        _tab_items = _tab_items.filter(item => item !== 'ERROR 2')
      }

      return _tab_items
    }
  }
})
</script>

<style scoped>
.json-viewer {
  display: flex;
  max-height: 90vh;
  overflow: scroll;
}
.full-width {
  width: 100%;
}
</style>
