<template>
  <table>
    <tr v-for="key in keys" :key="key">
      <th class="pr-2 text-right" style="vertical-align: top;">
        {{ key }}
      </th>
      <td>{{ data[key] }}</td>
    </tr>
  </table>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    data: {
      type: Object,
      required: true
    },
    keys: {
      type: Array,
      required: true
    }
  }
})
</script>
