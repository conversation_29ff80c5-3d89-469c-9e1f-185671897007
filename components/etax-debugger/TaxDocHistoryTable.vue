<template>
  <v-data-table
    :headers="headers"
    :items="items"
    :expanded.sync="expanded"
    item-key="history_id"
    show-expand
  >
    <!--  -->
    <template #item.history_date="{ item }">
      {{ $datetime(item.history_date) }}
    </template>

    <template #expanded-item="{ headers, item }">
      <td :colspan="headers.length" class="pa-0 ma-0">
        <v-row style="width: calc(100vw - 24px); background-color: #aaaaaa; margin: 0; padding: 0">
          <v-col v-if="item" cols="12" md="6">
            <tax-doc-viewer
              :tab.sync="tab_left"
              :d1a-json="item.doc_info"
              :pdf="item.doc_url"
              :xml.sync="item.doc_xml"
              :error="item.status_reason"
            />
          </v-col>
          <v-col v-if="item" cols="12" md="6">
            <tax-doc-viewer
              :tab.sync="tab_right"
              :d1a-json="item.doc_info"
              :pdf="item.doc_url"
              :xml.sync="item.doc_xml"
              :error="item.status_reason"
            />
          </v-col>
        </v-row>
      </td>
    </template>
  </v-data-table>
</template>

<script lang="ts">
import Vue from 'vue'
import TaxDocViewer from '~/components/etax-debugger/TaxDocViewer.vue'
export default Vue.extend({
  components: {
    TaxDocViewer
  },
  props: {
    items: {
      type: Array,
      required: true
    }
  },

  data () {
    return {
      expanded: [],
      tab_left: 'D1A JSON',
      tab_right: 'PDF'
    }
  },

  computed: {
    headers () {
      return [
        { text: 'History Date', value: 'history_date', align: 'start', sortable: true },
        { text: 'History Type', value: 'history_type' },
        { text: 'History User', value: 'history_user' },
        { text: 'Status', value: 'status' }
      ]
    }
  }
})
</script>
