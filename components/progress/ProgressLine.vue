<template>
  <div>
    <!--  -->
    <v-progress-circular v-if="status === 'loading'" size="16" indeterminate color="primary" />
    <v-icon v-if="status === 'success'" color="success">
      mdi-check
    </v-icon>
    <v-icon v-if="status === 'error'" color="error">
      mdi-close
    </v-icon>
    {{ text }}
    <slot />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    status: {
      type: String,
      required: true,
      validator (value: string) {
        return ['loading', 'success', 'error'].includes(value)
      }
    },
    text: {
      type: String,
      required: true
    }
  }
})
</script>
