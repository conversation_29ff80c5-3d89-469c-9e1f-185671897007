<template>
  <v-form @submit.prevent="">
    <v-row>
      <v-col cols="12" md="8">
        <v-text-field
          outlined
          dense
          prepend-inner-icon="mdi-magnify"
          :placeholder="$t('fixcase.search')"
          clearable
          data-test="search-fixcase"
          :value="formData.search"
          @input="update({ search: $event })"
        />
      </v-col>
      <v-col cols="12" md="4">
        <v-checkbox
          data-test="show-close-fixcase-checkbox"
          dense
          :label="$t('fixcase.close-case')"
          :input-value="formData.is_complete"
          @change="update({ is_complete: $event })"
        />
      </v-col>
    </v-row>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  methods: {
    update (data: any) {
      this.$emit('update:form-data', { ...this.formData, ...data })
    }
  }
})
</script>
