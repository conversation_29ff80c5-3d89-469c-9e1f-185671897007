<template>
  <v-form ref="form">
    <pick-order-autocomplete
      filled
      hide-details
      auto-select-first
      :rules="[$rules.required]"
      :value="formData.pick_order"
      @input="update({ pick_order: $event, customer_name: $event.order_customer, customer_phone: $event.order_customerphone })"
    />
    <div v-if="formData.pick_order" class="pt-1 pl-3">
      <h4>{{ $t('fixcase.customer') }}</h4>
      <ul>
        <li data-test="preview-order-customer-name">
          {{ formData.pick_order.order_customer }}<br>
        </li>
        <li data-test="preview-order-customer-phone">
          {{ $t('fixcase.phone') }} {{ formData.pick_order.order_customerphone }}<br>
        </li>
      </ul>

      <h4>{{ $t('fixcase.order-product') }}</h4>
      <ul data-test="preview-order-items">
        <li v-for="item in formData.pick_order.order_json.list" :key="item.sku">
          {{ item.sku }} - {{ item.name }} x {{ item.number }}
        </li>
      </ul>
    </div>

    <v-divider class="my-5" />

    <v-text-field
      filled
      :label="$t('fixcase.customer')"
      :rules="[$rules.required]"
      :value="formData.customer_name"
      :error-messages="formError.customer_name"
      data-test="fixcase-form-customer-name-field"
      @input="update({ customer_name: $event })"
    />

    <v-text-field
      filled
      :label="$t('fixcase.phone')"
      :rules="[$rules.required,$rules.phoneNumberFormat]"
      :value="formData.customer_phone"
      :error-messages="formError.customer_phone"
      data-test="fixcase-form-customer-phone-field"
      @input="update({ customer_phone: $event })"
    />

    <v-textarea
      filled
      :label="$t('fixcase.details')"
      :rules="[$rules.required]"
      :value="formData.description"
      :error-messages="formError.description"
      data-test="fixcase-form-detail-field"
      @input="update({ description: $event })"
    />
    <v-text-field
      type="number"
      data-test="fixcase-form-cost-field"
      filled
      :label="$t('fixcase.cost')"
      :rules="[$rules.required]"
      :value="formData.cost"
      :error-messages="formError.cost"
      @input="update({ cost: $event })"
    />
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import PickOrderAutocomplete from '../picking/PickOrderAutocomplete.vue'
import { PickOrder, VForm } from '~/models'
import { clearFormErrors } from '~/utils'
export default Vue.extend({
  name: 'FixCaseForm',
  components: { PickOrderAutocomplete },

  props: {
    formData: {
      type: Object,
      required: true
    },
    formError: {
      type: Object,
      default: () => ({})
    }
  },

  data () {
    return {
      pick_order: null as PickOrder | null,
      description: '',
      cost: 0
    }
  },

  methods: {
    update (data: any) {
      this.$emit('update:form-data', { ...this.formData, ...data })
      this.$emit('update:form-error', clearFormErrors(this.formError, data))
    },

    validate () {
      const form: VForm = this.$refs.form as never
      return form.validate()
    },

    reset () {
      const form: VForm = this.$refs.form as never
      return form.reset()
    }
  }
})
</script>
