<template>
  <v-dialog :value="value" max-width="960">
    <v-card>
      <v-card-title>
        <span class="headline">สร้าง Order จาก Fix Case</span>
        <v-spacer />
        <v-btn icon @click="$emit('input', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider />

      <v-card-text class="pt-3">
        <div v-if="fixCase">
          <fix-order-form
            ref="form"
            :order-json.sync="form.order_json"
            :errors="form_errors"
          />
        </div>

        <!-- debug -->
        <!-- <pre v-if="form.order_json" style="max-height: 300px; overflow: scroll">{{ JSON.stringify(form.order_json, null, 4) }}</pre> -->
      </v-card-text>
      <v-divider />

      <v-card-actions>
        <v-spacer />
        <v-btn :loading="loading" color="success" @click="createFixOrder()">
          สร้าง FIX ORDER
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'

import FixOrderForm from './FixOrderForm.vue'
import { FixCase, OrderJson } from '~/models'

export default Vue.extend({
  components: {
    FixOrderForm
  },

  props: {
    value: {
      type: Boolean,
      required: true
    },

    fixCase: {
      type: Object,
      requred: false,
      default: () => null
    } as Vue.PropOptions<FixCase | null>
  },

  data () {
    return {
      loading: false,
      form: {
        order_json: null as OrderJson | null,
        fixcase: null as number | null
      },
      form_errors: {} as any
    }
  },

  watch: {
    fixCase: {
      handler (fixcase: FixCase) {
        if (!fixcase) { return }
        const pick_order = fixcase.pick_order
        this.form.order_json = JSON.parse(JSON.stringify(pick_order.order_json))

        if (!this.form.order_json) {
          throw new Error('This fixcase does not have pick_order.order_json')
        }
        this.form.order_json.number = this.form.order_json.number + '-FIX'
        this.form.order_json.remark = fixcase.description
        this.form.order_json.trackingno = ''
        this.form.order_json.status = 'Pending'
        // this.form.order_json.orderdate = this.$moment().toDate()
        // this.form.order_json.orderdateString = this.$moment().format('YYYY-MM-DD')
        this.form.order_json.createdatetime = this.$moment().toDate()
        this.form.order_json.createdatetimeString = this.$moment().format('YYYY-MM-DD HH:mm:ss')
        this.form.fixcase = fixcase.id
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    async createFixOrder () {
      if (this.loading) {
        return
      }

      if (!(this.$refs.form as any).validate()) {
        return
      }

      this.loading = true
      try {
        const res = await this.$axios.post('/api/fixcases/fix-order/create/', this.form)
        this.$emit('update:fixcase', res.data)
        this.$emit('input', false)
        this.$snackbar('success', this.$t('fixcase.fix-order-created'))
      } catch (error: any) {
        console.error(error)

        if (error.response) {
          if (error.response.status === 400) {
            this.form_errors = error.response.data.order_json
          } else {
            this.$snackbar('error', this.$t('error.unknown'))
          }
        }
      }
      this.loading = false
    }
  }
})
</script>
