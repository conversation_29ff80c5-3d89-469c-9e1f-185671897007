<template>
  <v-form ref="form" @submit.prevent="$emit('submit')">
    <v-row dense>
      <!-- Customer -->
      <v-col cols="3">
        <h4>ข้อมูลลูกค้า</h4>
      </v-col>
      <v-col cols="9">
        <v-row dense>
          <v-col cols="6">
            <v-text-field
              filled
              label="ลูกค้า"
              hide-details
              :rules="[$rules.required]"
              :value="orderJson.customername"
              @change="update({ customername: $event })"
            />
          </v-col>
          <v-col cols="6">
            <v-text-field
              filled
              label="เบอร์โทร"
              hide-details
              :value="orderJson.customerphone"
              @change="update({ customerphone: $event, shippingphone: $event })"
            />
          </v-col>
          <v-col cols="12">
            <v-textarea
              rows="2"
              filled
              label="ที่อยู่จัดส่ง"
              :value="orderJson.shippingaddress"
              @change="update({ shippingaddress: $event })"
            />
          </v-col>
        </v-row>
      </v-col>

      <!-- Order & Tracking -->
      <v-col cols="3">
        <h4>รายละเอียดคำสั่งซื้อ</h4>
      </v-col>
      <v-col cols="9">
        <v-row dense>
          <v-col cols="6">
            <v-text-field
              label="เลขที่คำสั่งซื้อ"
              filled
              hide-details="auto"
              :rules="[$rules.required]"
              :error-messages="errors.number"
              :value="orderJson.number"
              :disabled="disabled.includes('number')"
              @change="update({ number: $event })"
            />
          </v-col>
          <v-col cols="6">
            <date-picker
              filled
              label="วันที่สั่งซื้อ"
              :value="orderJson.orderdateString"
              :rules="[$rules.required]"
              @input="update({ orderdate: $event, orderdateString: $event })"
            />
          </v-col>
          <v-col cols="6">
            <v-select
              v-if="auto_generate_tracking"
              filled
              label="ขนส่ง"
              hide-details="auto"
              :items="['DBB_FLASH', 'DBB_KERRY', 'DBB_THAIPOST_REG', 'DBB_THAIPOST_EMS']"
              :rules="[$rules.required]"
              :value="orderJson.shippingchannel"
              @change="update({ shippingchannel: $event })"
            />
            <v-text-field
              v-else
              filled
              label="ขนส่ง"
              hide-details="auto"
              :value="orderJson.shippingchannel"
              @change="update({ shippingchannel: $event })"
            />
            <!-- <v-checkbox
              v-model="auto_generate_tracking"
              dense
              label="สร้าง Tracking Number อัตโนมัติ"
              @change="onAutoGenrateTrackingChange($event)"
            /> -->
          </v-col>
          <v-col cols="6">
            <v-text-field
              filled
              label="เลขที่ติดตามพัสดุ"
              hide-details
              :disabled="auto_generate_tracking"
              :value="auto_generate_tracking ? '--- ระบบจะสร้างอัตโนมัติ ---' : orderJson.trackingno"
              @change="update({ trackingno: $event })"
            />
          </v-col>
          <v-col cols="12">
            <v-textarea
              filled
              rows="1"
              label="หมายเหตุ"
              auto-grow
              :value="orderJson.remark"
              @change="update({ remark: $event })"
            />
          </v-col>
        </v-row>
      </v-col>
    </v-row>

    <v-divider class="my-5" />

    <product-detail-table
      :order-list="orderJson.list"
      @update:order-list="update({ list: $event })"
    />

    <v-divider class="my-4" />

    <v-row justify="end">
      <v-col cols="12" sm="6" md="4">
        <order-summary
          :order-json="orderJson"
          @update:order-json="update($event)"
        />
      </v-col>
    </v-row>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import ProductDetailTable from '../products/ProductDetailTable.vue'
import DatePicker from '../global/DatePicker.vue'
import OrderSummary from '~/components/order-center/OrderSummary.vue'
import { OrderJson } from '~/models'

export default Vue.extend({
  components: { ProductDetailTable, OrderSummary, DatePicker },
  props: {
    orderJson: {
      type: Object,
      required: true
    } as Vue.PropOptions<OrderJson>,

    errors: {
      type: Object,
      default: () => ({})
    },

    disabled: {
      type: Array,
      default: () => []
    }
  },

  data () {
    return {
      auto_generate_tracking: false
    }
  },

  methods: {
    validate (): boolean {
      return (this.$refs.form as any).validate()
    },

    update (data: any) {
      this.$emit('update:order-json', { ...this.orderJson, ...data })
    },

    updateList (index: number, data: any) {
      const orderItems = this.orderJson.list
      orderItems[index] = { ...orderItems[index], ...data }
      this.update({ list: orderItems })
    },

    appendList () {
      const emptyItem = {
        id: null,
        sku: '',
        name: '',
        number: '',
        status: null,
        skutype: null,
        discount: '',
        mfg_date: null,
        unittext: '',
        productid: null,
        totalprice: 0,
        producttype: 0,
        sku_barcode: null,
        serialnolist: [],
        discountamount: 0,
        original_price: 0,
        pricepernumber: 0,
        seller_discount: 0,
        shipping_amount: 0,
        platform_discount: 0,
        original_shipping_amount: 0,
        seller_shipping_discount: 0,
        platform_shipping_discount: 0
      }

      this.update({ list: [...this.orderJson.list, emptyItem] })
    },

    removeList (index: number) {
      const orderItems = [...this.orderJson.list]
      orderItems.splice(index, 1)
      this.update({ list: orderItems })
    },

    onAutoGenrateTrackingChange () {
      this.update({ trackingno: '', shippingchannel: '' })
    }
  }

})
</script>

<style scoped>
  table {
    border-collapse: collapse;
  }

  th {
    text-align: left;
  }

  th.fit {
    width: 1%;
    white-space: nowrap;
  }
</style>
