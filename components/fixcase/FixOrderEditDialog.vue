<template>
  <v-dialog :value="value" max-width="960">
    <v-card>
      <v-card-title>
        <span class="headline">แก้ไข Fix Order</span>
        <v-spacer />
        <v-btn icon @click="$emit('input', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider />

      <v-card-text class="pt-3">
        <div v-if="fixCase">
          <fix-order-form
            ref="form"
            :order-json.sync="form.order_json"
            :disabled="['number']"
          />
        </div>
      </v-card-text>
      <v-divider />

      <v-card-actions>
        <v-spacer />
        <v-btn
          :loading="loading"
          color="success"
          @click="updateFixOrder()"
        >
          Save
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import FixOrderForm from './FixOrderForm.vue'
import { FixCase, OrderJson } from '~/models'

export default Vue.extend({
  components: {
    FixOrderForm
  },
  props: {
    value: {
      type: Boolean,
      required: true
    },
    fixCase: {
      type: Object,
      requred: false,
      default: () => null
    } as Vue.PropOptions<FixCase | null>
  },

  data () {
    return {
      loading: false,
      form: {
        order_json: null as OrderJson | null
      },
      form_errors: {} as any
    }
  },

  watch: {
    fixCase: {
      handler (fixcase: FixCase) {
        if (!fixcase) {
          return
        }

        this.form.order_json = JSON.parse(JSON.stringify(fixcase.fix_order.order_json))
      },
      deep: true,
      immediate: true
    }

  },

  methods: {
    async updateFixOrder () {
      if (!this.fixCase) {
        return
      }

      if (!this.form.order_json) {
        return
      }

      if (this.loading) {
        return
      }

      if (!(this.$refs.form as any).validate()) {
        return
      }

      this.loading = true
      const order_number = this.form.order_json.number

      try {
        await this.$axios.put(
          `/api/picking/orders/${order_number}/`,
          this.form.order_json
        )

        const response = await this.$axios.get(`/api/fixcases/resource/fixcases/${this.fixCase.id}/`)

        this.$emit('update:fixcase', response.data.fixcase)
        this.$emit('input', false)
      } catch (error: any) {
        console.error(error)

        if (error.response) {
          if (error.response.status === 400) {
            this.form_errors = error.response.data.order_json
          } else {
            this.$snackbar('error', this.$t('error.unknown'))
          }
        }
      }

      this.loading = false
    }
  }
})
</script>
