<template>
  <v-data-table
    dense
    checkbox-color="primary"
    :headers="table.headers"
    :items="table.items"
    :value="selected"
    :item-class="getItemClass"
    v-bind="$attrs"
    v-on="$listeners"
    @input="$emit('update:selected', $event)"
  >
    <template #item.tracking_code="props">
      <v-textarea
        :id="`tracking_code-${props.item.id}`"
        class="mt-3"
        outlined
        dense
        rows="1"
        auto-grow
        counter
        maxlength="135"
        :append-icon="`tracking_code-${props.item.id}` === focus ? 'mdi-check' : null"
        :value="props.item.tracking_code"
        :disabled="!$hasPerms('change_fixcase')"
        @focus="focus = `tracking_code-${props.item.id}`"
        @blur="focus=null"
        @change="updateFixcase(props.item, { tracking_code: $event })"
        @click:append="focusNext(props.item, 'tracking_code')"
      />
    </template>
    <template #item.remark="props">
      <v-textarea
        :id="`remark-${props.item.id}`"
        class="mt-3"
        outlined
        dense
        rows="1"
        auto-grow
        counter
        :disabled="!$hasPerms('change_fixcase')"
        :append-icon="`remark-${props.item.id}` === focus ? 'mdi-check' : null"
        :value="props.item.remark"
        @focus="focus = `remark-${props.item.id}`"
        @blur="focus=null"
        @change="updateFixcase(props.item, { remark: $event })"
        @click:append="focusNext(props.item, 'remark')"
      />
    </template>
    <template #item.pick_order.order_customer="props">
      {{ props.item.customer_name }}<br>
      <div style="width: 200px; font-size: 0.75rem;" class="text--grey">
        <span style="display: inline-block; width: 25px;">โทร</span>:
        <a :href="`tel:+${props.item.customer_phone}`" style="color: grey;">
          {{ props.item.customer_phone }}
        </a>
        <br>
        <span style="display: inline-block; width: 25px;">ที่อยู่</span>: {{ props.item.pick_order.order_json.shippingaddress }}
      </div>
    </template>
    <template #item.cost="props">
      <div class="text-right">
        {{ props.item.cost }}
      </div>
    </template>
    <template #item.create_date="props">
      {{ $datetime(props.item.create_date) }}
    </template>
    <template #item.fix_order="props">
      <div v-if="props.item.fix_order" style="font-size: 0.75rem;">
        <div>
          <a
            style="color: grey;"
            href="!#"
            @click.prevent="$router.push({path: `/order-center?search=${props.item.fix_order.order_number}&date=${props.item.fix_order.order_date}`})"
          >{{ props.item.fix_order.order_number }}</a>
          <v-btn x-small icon @click="$emit('click:edit-fix-order', props.item)">
            <v-icon x-small>
              mdi-pencil
            </v-icon>
          </v-btn>
        </div>
        <div>
          <span class="text-label">เลขพัสดุ</span>
          {{ props.item.fix_order.order_trackingno || "-" }}
          <v-btn v-if="props.item.fix_order.order_trackingno" x-small icon @click="copyText(props.item.fix_order.order_trackingno)">
            <v-icon x-small>
              mdi-content-copy
            </v-icon>
          </v-btn>
        </div>
        <div>
          <span class="text-label">วิดิโอ</span>
          <span v-if="props.item.fix_order.has_videos">
            <v-btn
              x-small
              outlined
              :href="props.item.fix_order.signed_receipt_url"
              target="_blank"
            >View</v-btn>
            <v-btn
              icon
              x-small
              @click="copyText(props.item.fix_order.signed_receipt_url)"
            >
              <v-icon x-small>mdi-content-copy</v-icon>
            </v-btn>
          </span>
          <span v-else>-</span>
        </div>
        <!-- <v-chip
          style="display: inline-block; width: 100%; text-align: center;"
          color="purple darken-2"
          text-color="white"
          small
        >
          {{ props.item.fix_order.order_number }}
        </v-chip>
        <div class="d-flex justify-between">
          <boolean-chip
            :value="props.item.fix_order.has_videos"
            true-msg="บันทึกวิดิโอแล้ว"
            false-msg="ไม่มีวิดิโอ"
          />
          <boolean-chip
            :value="props.item.fix_order.ready_to_ship"
            true-msg="พร้อมส่ง"
            false-msg="ไม่พร้อมส่ง"
          />
        </div> -->
      </div>
      <v-btn v-if="!props.item.fix_order" x-small outlined color="primary" @click="$emit('click:create-fix-order', props.item)">
        สร้าง Order จาก Fix Case
      </v-btn>
    </template>
  </v-data-table>
</template>

<script lang="ts">
import Vue from 'vue'
import { copyToClipboard } from '~/api/utils'
import { FixCase } from '~/models'
import { VDataTable, VDataTableHeader } from '~/models/vuetify'

export default Vue.extend({
  props: {
    items: {
      type: Array,
      default () {
        return [] as FixCase[]
      }
    } as Vue.PropOptions<FixCase[]>,
    selected: {
      type: Array,
      default () {
        return []
      }
    }
  },
  data () {
    return {
      focus: {}
    }
  },
  computed: {
    table (): VDataTable {
      const headers: VDataTableHeader[] = [
        { text: this.$t('fixcase.sms-message'), value: 'tracking_code', sortable: false, width: '20%', cellClass: 'px-1' },
        { text: this.$t('fixcase.note'), value: 'remark', sortable: false, width: '20%', cellClass: 'px-1' },
        { text: this.$t('fixcase.shop'), value: 'pick_order.order_saleschannel', sortable: false },
        { text: this.$t('fixcase.ordered'), value: 'pick_order.order_number', sortable: false },
        { text: this.$t('fixcase.customer'), value: 'pick_order.order_customer', sortable: false },
        // { text: this.$t('fixcase.address'), value: 'pick_order.order_json.shippingaddress', sortable: false },
        { text: this.$t('fixcase.issues'), value: 'description', sortable: false, width: '15%' },
        { text: this.$t('fixcase.cost-require'), value: 'cost', align: 'end', sortable: false },
        { text: this.$t('fixcase.date-opening'), value: 'create_date' },
        { text: 'Fix Order', value: 'fix_order' }
      ]

      const items = this.items.map((x: FixCase) => ({
        ...x,
        customer_name: x.customer_name || x.pick_order.order_customer,
        customer_phone: x.customer_phone || x.pick_order.order_customerphone
      }))

      return {
        headers,
        items
      }
    }
  },

  methods: {
    async updateFixcase (fixcase: FixCase, data: Partial<FixCase>) {
      const config = {
        params: { return: 0 }
      }
      try {
        await this.$axios.patch(
          `/api/fixcases/resource/fixcases/${fixcase.id}/`,
          data,
          config
        )

        const item: FixCase = { ...fixcase, ...data }
        const items = this.items as FixCase[]
        const updated_items = items.map(x => x.id === item.id ? item : x)
        this.$emit('update:items', updated_items)
      } catch (error) {
        console.error(error)
      }
    },

    focusNext (fixcase: FixCase, type: 'tracking_code'|'remark') {
      const idx = this.table.items.findIndex(x => x.id === fixcase.id)

      let el: HTMLTextAreaElement | null
      let focus: string
      if (type === 'tracking_code') {
        const next = idx
        focus = `remark-${this.table.items[next].id}`
        el = document.querySelector(`textarea#${focus}`)
      } else {
        const next = idx === this.table.items.length - 1 ? 0 : idx + 1
        focus = `tracking_code-${this.table.items[next].id}`
        el = document.querySelector(`textarea#${focus}`)
      }

      if (el) {
        el.focus()

        this.$nextTick(() => {
          this.focus = focus
        })
      }
    },

    getItemClass (item: FixCase) {
      if (item.fix_order) {
        return 'purple lighten-5'
      }
      return ''
    },

    copyText (link: string) {
      this.$snackbar('info', this.$t('copied'))
      copyToClipboard(link)
    }
  }
})
</script>

<style scoped>
.text-label {
  display: inline-block;
  text-align: left;
  width: 46px;
}
</style>
