<template>
  <v-dialog
    max-width="500"
    persistent
    :value="value"
    :fullscreen="$vuetify.breakpoint.smAndDown"
    @input="$emit('input', $event)"
  >
    <v-card>
      <v-card-title class="headline grey lighten-2">
        {{ $t('fixcase.create') }}
        <v-spacer />
        <v-btn data-test-id="v-btn-close" icon @click="closeDialog()">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text class="pt-5">
        <fix-case-create-form
          ref="form"
          :form-data.sync="form"
          :form-error.sync="form_error"
        />
        <div>
          {{ $t('fixcase.sms-setting') }}
          <a href="/settings" target="_blank">{{ $t('fixcase.setting') }}</a>
        </div>
      </v-card-text>
      <v-card-actions>
        <v-spacer />
        <v-btn color="primary" data-test="create-fixcase" text :loading="loading" @click="createFixCase(form)">
          <v-icon left>
            mdi-content-save
          </v-icon>
          {{ $t('fixcase.save') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import FixCaseCreateForm from './FixCaseCreateForm.vue'
import { FixCase, PickOrder, VForm } from '~/models'
export default Vue.extend({
  components: { FixCaseCreateForm },
  props: {
    value: {
      type: Boolean,
      required: true
    }
  },
  data () {
    return {
      form: {
        pick_order: null as PickOrder | null,
        description: '',
        cost: 0,
        customer_name: '',
        customer_phone: ''
      },
      form_error: {} as any,
      loading: false
    }
  },
  methods: {
    async createFixCase (data: { pick_order: PickOrder; description: string; cost: number, customer_name: string, customer_phone: string}) {
      const form: VForm = this.$refs.form as never
      if (!form.validate()) {
        return
      }

      this.loading = true
      try {
        const config = {
          params: {
            include: ['pick_order.*']
          }
        }
        const payload = {
          pick_order: data.pick_order.id,
          description: data.description,
          cost: data.cost || 0,
          customer_name: data.customer_name,
          customer_phone: data.customer_phone
        }
        const res = await this.$axios.post('/api/fixcases/resource/fixcases/', payload, config)
        const fixcase: FixCase = res.data.fixcase
        this.$emit('create:fixcase', fixcase)
        this.resetForm()
        this.closeDialog()
        this.$snackbar('success', this.$t('fixcase.create-success'))
      } catch (error: any) {
        console.error(error)
        if (error.response && error.response.status === 400) {
          this.form_error = error.response.data || {}
        }
      }
      this.loading = false
    },
    closeDialog () {
      this.$emit('input', false)
    },
    resetForm () {
      this.form.pick_order = null
      this.form.description = ''
      this.form.cost = 0
    }

  }
})
</script>
