<template>
  <div>
    <v-card>
      <v-card-text>
        <v-row>
          <v-col v-for="(image, index) in images" :key="index" class="pa-2" cols="4" style="position:relative">
            <v-img
              data-test="v-img-image-capture"
              :src="image.urlData"
              class="mb-1"
              aspect-ratio="1"
              max-height="130px"
              style="border-radius:15px"
              @click="showDetail(image.urlData)"
            />

            <v-btn
              v-if="image.status !== 'success'"
              data-test="v-btn-delete-image"
              x-small
              fab
              dark
              color="red"
              style="position:absolute; top:-3px; right:-3px;"
              @click="$emit('click:remove', index)"
            >
              <v-icon dark>
                mdi-close
              </v-icon>
            </v-btn>

            <div v-if="image.status == 'success'" class="text-no-wrap success" style="width: 100%">
              <p class="text-center" style="color:white">
                SUCCESS
              </p>
            </div>

            <div v-if="image.status == 'failed'" class="text-no-wrap error" style="width: 100%">
              <p class="text-center" style="color:white">
                FAILED
              </p>
            </div>
            <!-- <p class="text-end text-green" color="green">
            </p> -->
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <image-detail-dialog :dialog="is_show_detail" :image="image_detail" @update:dialog="is_show_detail = $event" />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import ImageDetailDialog from './ImageDetailDialog.vue'

export default Vue.extend({
  components: { ImageDetailDialog },
  props: {
    images: { type: Array, default: () => [] }
  },
  data () {
    return {
      show_img: null,
      is_show_detail: false,
      image_detail: ''
    }
  },
  methods: {

    showDetail (image:string) {
      this.is_show_detail = true
      this.image_detail = image
    }
  }
})
</script>

<style scoped>

</style>
