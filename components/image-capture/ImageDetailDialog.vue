<template>
  <div>
    <v-dialog
      :value="dialog"
      transition="dialog-bottom-transition"
      fullscreen
      @input="close()"
    >
      <v-card>
        <v-card-title />

        <v-card-text>
          <v-img class="mx-auto" style="border-radius:40px" max-width="550px" aspect-ratio="1" :src="image" />
        </v-card-text>

        <v-card-actions>
          <v-btn
            class="mx-auto"
            width="200px"
            large
            outlined
            color="info"
            @click="close()"
          >
            {{ $t('image-capture-close') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({

  props: {
    dialog: { type: Boolean, default: false },
    image: { type: String, default: '' }
  },
  data () {
    return {

    }
  },

  methods: {

    close () {
      this.$emit('update:dialog', false)
    }
  }
})
</script>

<style scoped>

</style>
