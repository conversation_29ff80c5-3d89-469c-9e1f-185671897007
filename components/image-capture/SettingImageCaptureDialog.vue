<template>
  <v-dialog :value="dialog" max-width="800" @input="$emit('click:close', false)">
    <v-card>
      <v-card-title class="headline grey lighten-2">
        {{ $t('setting') }}
        <v-spacer />
        <v-btn data-test="v-btn-close" icon @click="$emit('click:close', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text>
        <v-row dense class="mt-4 pt-4">
          <v-col cols="12">
            <h3>{{ $t('setting.google-drive') }}</h3>
            <span v-if="settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID">
              <div>
                <v-icon small color="success" />
                {{ $t('setting.using-share-drive') }} <span v-if="$auth.user.is_superuser">{{ settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID }}</span><br>
                <ul class="pl-8" style="max-height: 75px; overflow-y: scroll">
                  <li v-for="email in settings.company.RECORD_GMAIL_ALLOWED_LIST" :key="email">
                    {{ email }}
                  </li>
                </ul>
              </div>
            </span>
            <div v-if="!gapiAuth">
              <a data-test-id="a-gapi-signin" href="!#" @click.prevent="gapiSignIn()">เชื่อมต่อ Google Drive</a>
            </div>
            <div v-if="gapiAuth">
              <v-icon small color="success">
                mdi-check
              </v-icon>
              {{ $t('setting.connected-to-google-drive', [gapiUser.email]) }} <br>
              <a data-test="a-gapi-signout" href="!#" class="black--text" @click.prevent="gapiSignOut()">ยกเลิกการเชื่อมต่อ</a>
            </div>
            <div v-if="gapiAuth && storage && !settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID">
              <span data-test-id="span-total-storage">พื้นที่เก็บข้อมูลทั้งหมด: {{ storage.limitGB }}</span>,
              <span data-test-id="span-used-storage">ใช้ไปแล้ว: {{ storage.usageGB }}</span>,
              <span data-test-id="span-available-storage">ใช้งานได้: {{ storage.availableGB }}</span>
            </div>
            <div v-if="gapiAuth">
              <br>
              <span data-test-id="span-root-folder">
                <b>Root Folder</b>:
                <span v-if="settings.local.googleDriveImageRootFolder">
                  {{ settings.local.googleDriveImageRootFolder.name }}
                  <a
                    v-if="$hasGroup('owner')"
                    target="_blank"
                    :href="`https://drive.google.com/drive/folders/${settings.local.googleDriveImageRootFolder.id}`"
                  >view</a>
                </span>
                <span v-else>-</span>
              </span>
            </div>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex'
import moment from 'moment'
import { IStorageQuota, createFolder, getStorageQuota, listFoldersInMyDrive, findFolderInShareDrive } from '~/api/googledrive'
import { SettingState } from '~/store/settings'
export default Vue.extend({
  props: {
    dialog: Boolean
  },
  data () {
    return {
      storage: null as IStorageQuota | null
    }
  },

  computed: {
    ...mapState('gapi', { gapiAuth: 'isAuth' }),

    ...mapGetters('gapi', {
      gapiToken: 'token',
      gapiUser: 'currentUser'
    }),

    settings (): SettingState {
      return this.$store.state.settings
    }
  },

  watch: {
    gapiAuth: {
      handler (val) {
        if (val) {
          setTimeout(async () => {
            this.storage = await getStorageQuota()
            this.setupFolders()

            this.$nuxt.$on('UPDATE_GOOGLE_DRIVE_TODAY_FOLDER', () => {
              // แก้ปํญหาสร้าง Google Drive Folder ทับซ้อนกัน ให้แต่ละเครื่อง delay
              // เป็นเวลา random 0 - 5 วินาทีก่อนทำการสร้าง google drive folder
              setTimeout(this.setupFolders, Math.round(Math.random() * 5000))
            })
          }, 2000)
        } else {
        }
      },
      immediate: true
    }
  },

  methods: {

    ...mapMutations('settings', {
      setLocalSetting: 'setLocalSetting'
    }),

    ...mapActions('gapi', {
      gapiSignIn: 'signIn',
      gapiSignOut: 'signOut'
    }),

    setupFolders () {
      if (this.settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID) {
        this.setupShareDriveFolder()
      } else {
        this.setupMyDriveFolder()
      }
    },

    async setupShareDriveFolder () {
      const share_drive_id = this.settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID

      // Setup Root Folder
      const rootFolderName = `Dobybot Videos (${this.gapiUser.email})`
      try {
        let rootFolder = await findFolderInShareDrive(share_drive_id, rootFolderName)
        if (!rootFolder) {
          rootFolder = await createFolder(rootFolderName, { parentFolderId: share_drive_id })
        }
        this.setLocalSetting({ googleDriveRootFolder: rootFolder })
      } catch (error: any) {
        this.$alert({
          title: this.$t('error.unable-to-connect-to-google-drive'),
          text: error.result,
          theme: 'error'
        })
        throw error
      }

      // Setup Today Folder
      const todayFolderName = moment().format('YYYY-MM-DD')
      try {
        let todayFolder = await findFolderInShareDrive(share_drive_id, todayFolderName)
        if (!todayFolder && this.settings.local.googleDriveRootFolder) {
          todayFolder = await createFolder(todayFolderName, {
            parentFolderId: this.settings.local.googleDriveRootFolder.id
          })
        }
        this.setLocalSetting({ googleDriveTodayFolder: todayFolder })
      } catch (error: any) {
        this.$alert({
          title: this.$t('error.unable-to-connect-to-google-drive'),
          text: error.result,
          theme: 'error'
        })
        throw error
      }
    },

    async setupMyDriveFolder () {
      console.log('setup local folder')
      const folders = await listFoldersInMyDrive()
      const rootFolderName = 'Packing Videos'
      const todayFolderName = moment().format('YYYY-MM-DD')

      let rootFolder = folders.find(f => f.name === rootFolderName)
      if (!rootFolder) {
        console.log('create root folder')
        rootFolder = await createFolder(rootFolderName)
      }
      this.setLocalSetting({ googleDriveRootFolder: rootFolder })

      let todayFolder = folders.find(f => f.name === todayFolderName)
      if (!todayFolder && this.settings.local.googleDriveRootFolder) {
        console.log('create today folder')
        todayFolder = await createFolder(todayFolderName, {
          parentFolderId: this.settings.local.googleDriveRootFolder.id
        })
      }
      this.setLocalSetting({ googleDriveTodayFolder: todayFolder })
    }

  }
})
</script>

<style scoped>

</style>
