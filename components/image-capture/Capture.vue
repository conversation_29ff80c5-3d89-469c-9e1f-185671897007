<template>
  <div>
    <v-dialog
      :value="dialog"
      transition="dialog-bottom-transition"
      fullscreen
      @input="close()"
    >
      <v-card class="">
        <div class="container">
          <video
            id="video"
            ref="videoElement"
            class="w-100 elevation-1 bg-black"
            style="position:relative"
            autoplay="true"
            @click="Click()"
          />

          <v-btn
            v-if="currentStream"
            class="capture-button"
            fab
            white
            large

            @click="Click()"
          >
            <v-icon dark>
              mdi-camera
            </v-icon>
          </v-btn>

          <v-row class="px-3 my-2">
            <v-slide-group show-arrows>
              <v-slide-item v-for="(i,index) in images" :key="index" style="position:relative">
                <v-card
                  color="grey lighten-1"
                  class="ma-2"
                >
                  <div>
                    <v-img :src="i.urlData" :aspect-ratio="1" width="90px" @click="showDetail(i.urlData)" />
                    <v-btn
                      x-small
                      fab
                      dark
                      color="red"
                      style="position:absolute; top:-10px; right:-10px"
                      @click="$emit('click:remove', index)"
                    >
                      <v-icon dark>
                        mdi-close
                      </v-icon>
                    </v-btn>
                  </div>
                </v-card>
              </v-slide-item>
            </v-slide-group>
          </v-row>

          <v-btn
            id="sendPhoto"
            :disabled="images.length == 0"
            fab
            white
            medium
            color="blue"
            @click="close()"
          >
            <v-icon>
              mdi-send
            </v-icon>
          </v-btn>
        </div>
      </v-card>

      <image-detail-dialog :dialog="is_show_detail" :image="image_detail" @update:dialog="is_show_detail = $event" />
      <canvas v-show="false" id="myCanvas" />
    </v-dialog>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    dialog: { type: Boolean, default: false },
    images: { type: Array, default: () => [] }
  },

  data () {
    return {
      UrlDatas: [],
      videoElement: null as any | undefined,
      currentStream: null as any,
      isUsingEnvironmentCamera: true as boolean,
      captureImage: null,
      is_show_detail: false,
      image_detail: ''
    }
  },

  watch: {
    dialog (val) {
      if (val) {
        this.$nextTick(() => {
          this.startCamera()
        })
      }
    }
  },

  methods: {
    async startCamera () {
      if (this.currentStream) {
        this.currentStream.getTracks().forEach(track => track.stop())
      }

      try {
        const constraints = {
          video: {
            facingMode: this.isUsingEnvironmentCamera ? 'environment' : 'user'
          }
        }
        const stream = await navigator.mediaDevices.getUserMedia(constraints)
        if (this.$refs.videoElement) {
          (this.$refs.videoElement as any).srcObject = stream
        }
        this.currentStream = stream
      } catch (error) {
        console.error('Error accessing camera:', error)
      }
    },

    Click () {
      const video = document.getElementById('video') as any
      const canvas = document.getElementById('myCanvas') as any
      const width = (this.$refs.videoElement as any).videoWidth as HTMLVideoElement
      const height = (this.$refs.videoElement as any).videoHeight as HTMLVideoElement
      const context = canvas.getContext('2d')
      canvas.width = width
      canvas.height = height
      context.drawImage(video, 0, 0, width, height)
      const imgURL = canvas.toDataURL('image/png')

      const obj = {
        urlData: this.dataURItoBlob(imgURL),
        file: this.dataURLtoFile(imgURL)
      }
      this.$emit('update:images', [...this.images, obj])
    },

    dataURLtoFile (dataURL:string) {
      const parts = dataURL.split(';base64,')
      const contentType = parts[0].split(':')[1]
      const raw = window.atob(parts[1])
      const rawLength = raw.length
      const uInt8Array = new Uint8Array(rawLength)

      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i)
      }

      const blob = new Blob([uInt8Array], { type: contentType })
      const fileName = 'image.png' // Specify the desired filename
      const file = new File([blob], fileName, { type: blob.type })

      return file
    },

    dataURItoBlob (dataURI) {
      // convert base64 to raw binary data held in a string
      // doesn't handle URLEncoded DataURIs - see SO answer #6850276 for code that does this
      const byteString = atob(dataURI.split(',')[1])

      // separate out the mime component
      const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0]

      // write the bytes of the string to an ArrayBuffer
      const ab = new ArrayBuffer(byteString.length)

      // create a view into the buffer
      const ia = new Uint8Array(ab)

      // set the bytes of the buffer to the correct values
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i)
      }

      // write the ArrayBuffer to a blob, and you're done
      const blob = new Blob([ab], { type: mimeString })
      return URL.createObjectURL(blob)
    },

    close () {
      if (this.currentStream) {
        this.currentStream.getTracks().forEach(track => track.stop())
      }

      this.$emit('update:dialog', false)
    },

    showDetail (image:string) {
      this.is_show_detail = true
      this.image_detail = image
    }
  }

})
</script>

<style scoped>
#container {
  margin: 0px auto;
  width: 100%;
  height: 100%;
}

.background {
  background-color: #000000;
}

#video {
  width: 100%;
  max-height: 70%;
  background-color: #666;
  border-radius: 15px;
}

.photoStorage {
  position: absolute;
  bottom: 100px;
  width: 100%;
}

#sendPhoto {
  color: blue;
  position: absolute;
  bottom: 20px;
  font-size: medium;
  right: 20px;
  width: 50px;
  height: 50px;
  background-color: white; /* Add icons or text as needed */
}

.capture-button {
  position: absolute;
  bottom: 20px;
  left: 50%;
  font-size: x-large;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: white;
}
</style>
