<template>
  <div class="text-center">
    <vue-qr-reader
      :responsive="true"
      :use-back-camera="true"
      line-color="#FFFFFF"
      @code-scanned="codeArrived"
      @error-captured="errorCaptured"
    />

    <!-- <StreamBarcodeReader
    @decode="onDecode"
    @loaded="onLoaded"
></StreamBarcodeReader> -->

    <v-btn
      text
      color="red"
      @click="$emit('click:close', false)"
    >
      Close
    </v-btn>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import VueQrReader from 'vue-qr-reader/dist/lib/vue-qr-reader.umd.js'
// import { StreamBarcodeReader } from "vue-barcode-reader";
export default Vue.extend({
  components: {
    VueQrReader
  },

  data () {
    return {
      errorMessage: ''
    }
  },

  methods: {

    codeArrived (event) {
      this.$emit('get-order_number', event)
    },

    // onDecode (event) {
    //  this.$emit('get-order_number', event)
    //   console.log(event)
    // },

    errorCaptured (error) {
      switch (error.name) {
        case 'NotAllowedError':
          this.errorMessage = 'Camera permission denied.'
          break
        case 'NotFoundError':
          this.errorMessage = 'There is no connected camera.'
          break
        case 'NotSupportedError':
          this.errorMessage = 'Seems like this page is served in non-secure context.'
          break
        case 'NotReadableError':
          this.errorMessage = 'Couldn\'t access your camera. Is it already in use?'
          break
        case 'OverconstrainedError':
          this.errorMessage = 'Constraints don\'t match any installed camera.'
          break
        default:
          this.errorMessage = 'UNKNOWN ERROR: ' + error.message
      }
      this.$snackbar('error', this.errorMessage)
    }
  }
})
</script>

<style scoped>

</style>
