<template>
  <div>
    <v-img
      :v-ripple="buttonMode === 'plus-minus'"
      :class="getBorder(item?.status)"
      elevation="3"
      :src="item?.image"
      style="background-color: white;"
      :style="buttonMode === 'delete' ? null : 'cursor: pointer;'"
      :aspect-ratio="1/1"
      crossorigin
      @click="disableAddBtn || buttonMode === 'delete' ? null : $emit('click-add', item.sku)"
      @click.right.prevent="disableAddBtn || buttonMode === 'delete' ? null : $emit('click-add-all', [item.sku, item.total_amount - item.checked_amount])"
    >
      <v-img v-if="qrCode" :src="`https://go-barcode-yuo4mnnlaa-as.a.run.app/qrcode/?data=${qrCode}`" style="background-color: lightblue;width: 45px;" :aspect-ratio="1/1" />

      <div class="ma-0 pa-0 d-flex justify-center w-100 blur-background" style="position:absolute; bottom:0%;">
        <div v-if="buttonMode == 'plus-minus'" class="h-100" style="width:30vh">
          <v-btn
            tile
            outlined
            color="error"
            style="background-color: rgba(255, 205, 210, 0.5);"
            class="w-100"
            @click.stop="$emit('click-delete', item.sku)"
            @click.stop.right.prevent="$emit('click-delete-all', item.sku)"
          >
            <v-icon>mdi-minus</v-icon>
          </v-btn>
        </div>
        <div style="width: 40vh;">
          <p class="mb-0 mt-1 text-center text-h5 font-weight-bold w-100 h-100">
            {{ `${item?.checked_amount}/${item?.total_amount}` }}
          </p>
        </div>
        <div v-if="buttonMode == 'plus-minus'" style="width:30vh">
          <v-btn
            tile
            outlined
            color="success"
            style="background-color: rgba(220, 237, 200, 0.5);"
            class="w-100 h-100"
            :disabled="disableAddBtn"
            @click.stop="$emit('click-add', item.sku)"
            @click.right.prevent="$emit('click-add-all', [item.sku, item.total_amount - item.checked_amount])"
          >
            <v-icon size="x-large">
              mdi-plus
            </v-icon>
          </v-btn>
        </div>
      </div>

      <v-btn
        v-if="buttonMode == 'delete'"
        absolute
        x-small
        color="red-darken-1"
        fab
        style="right: 1%; top: 1%;"
      >
        <v-icon size="x-large" color="error" @click.stop="$emit('click-delete-all', item.sku)">
          mdi-close
        </v-icon>
      </v-btn>
    </v-img>

    <p class="mt-2 mb-0 text-caption text-truncate">
      <v-icon class="mb-1" size="medium">
        mdi-barcode-scan
      </v-icon> {{ item?.sku }}
    </p>
    <p class="mt-0 mb-0 text-caption text-truncate">
      <v-icon class="" size="medium">
        mdi-package-variant-closed
      </v-icon> {{ item?.name }}
    </p>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue'
import { TableItem } from '~/components/home/<USER>'

export default Vue.extend({
  props: {
    item: {
      type: Object as PropType<TableItem>,
      required: true
    },
    qrCode: {
      type: String,
      default: null
    },
    buttonMode: {
      type: String,
      default: 'plus-minus',
      validator: (value: string) => ['plus-minus', 'delete'].includes(value)
    },
    disableAddBtn: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    getBorder (status: 'ok'|'under'|'over') {
      const colors = {
        ok: 'border-success',
        under: '',
        over: 'border-error'
      }
      return colors[status]
    }
  }

})
</script>

<style scoped>
.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}
.blur-background {
  background-color: rgba(255, 255, 255, 0.4); /* Black with 50% opacity */
  backdrop-filter: blur(3px); /* Blur effect */
  -webkit-backdrop-filter: blur(5px); /* For Safari compatibility */
}
.blur-background2 {
  background-color: rgba(0, 0, 0, 0.4); /* Black with 50% opacity */
  backdrop-filter: blur(3px); /* Blur effect */
  -webkit-backdrop-filter: blur(5px); /* For Safari compatibility */
}

.border-error {
  border: 5px solid red; /* Red border */
  border-radius: 5px; /* Optional: Rounded corners */
  box-shadow: 0 0 15px rgba(255, 0, 0, 0.5); /* Optional: Adds a red glow effect */
}

.border-success {
  border: 5px solid green; /* Green border */
  border-radius: 5px; /* Optional: Rounded corners */
  box-shadow: 0 0 15px rgba(0, 255, 0, 0.5); /* Optional: Green glow effect */
}
</style>
