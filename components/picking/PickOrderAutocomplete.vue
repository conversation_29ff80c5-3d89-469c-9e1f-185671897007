<template>
  <v-autocomplete
    ref="autocomplete"
    v-bind="$attrs"
    :items="items"
    :label="$t('picking.order-number')"
    :search-input.sync="keyword"
    item-text="order_number"
    item-value="id"
    clearable
    return-object
    data-test="pick-order-autocomplete"
    :value="value"
    @input="$emit('input', $event)"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import _ from 'lodash'
import { PickOrder } from '~/models'

export default Vue.extend({
  props: {
    value: { type: [Number, Object], default: null }
  },
  data () {
    return {
      items: [] as PickOrder[],
      keyword: '',
      searched_keywords: {} as any
    }
  },

  watch: {
    keyword: {
      async handler (val) {
        if (!val) { return }
        const keyword = val.trim()

        if (keyword.length < 2 || this.searched_keywords[keyword]) {
          return
        }

        const pick_orders = await this.findPickOrderBy({ order_number: keyword })
        this.searched_keywords[keyword] = true
        this.items = _.uniqBy([...this.items, ...pick_orders], 'id')

        const exact_match = this.items.find(x => x.order_number === val)
        if (exact_match) {
          this.$emit('input', exact_match)
          const el: any = this.$refs.autocomplete
          el.isMenuActive = false
        }
      }
    }
  },

  methods: {
    async findPickOrderBy ({ order_number }: { order_number: string}): Promise<PickOrder[]> {
      const res = await this.$axios.get('/api/picking/resource/pick-orders/', {
        params: {
          'filter{order_number.startswith}': order_number
        }
      })

      return res.data.pick_orders
    }
  }
})
</script>
