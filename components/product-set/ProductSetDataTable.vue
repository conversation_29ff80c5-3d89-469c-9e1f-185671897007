<template>
  <div>
    <div>
      <v-row dense>
        <v-col>
          <v-text-field
            v-model="search"
            prepend-inner-icon="mdi-magnify"
            data-test="v-text-field-status-product-set"

            placeholder="ค้นหา"
            hide-details
          />
        </v-col>
      </v-row>
      <v-row dense>
        <v-col md="3">
          <v-select
            v-model="filter.is_deleted"
            data-test="v-select-status-product-set"
            label="Status"
            :items="[
              {text: 'All', value: null},
              {text: 'Active', value: false},
              {text: 'InActive', value: true}
            ]"
          />
        </v-col>
      </v-row>
    </div>

    <v-data-table
      :footer-props="{'items-per-page-options': [10, 25, 50, 100]}"
      :headers="headers"
      :items="items"
      :search="search"
      :custom-filter="customFilter"
    >
      <!--  -->

      <template #item.products="{item}">
        <table class="my-3">
          <thead>
            <th class="text-left">
              SKU/Barcode
            </th>
            <th class="text-left">
              Name
            </th>
            <th>&nbsp;</th>
          </thead>
          <tr v-for="p in item.products" :key="p.sku">
            <td style="min-width: 100px;" class="px-2">
              {{ p.sku }}
            </td>
            <td style="min-width: 100px;" class="px-2">
              {{ p.name || '-' }}
            </td>
            <td style="white-space: nowrap;">
              x {{ p.amount }}
            </td>
          </tr>
        </table>
      </template>

      <template #item.create_date="{item}">
        {{ $datetime(item.create_date) }}<br>
        {{ $t('by') }}: {{ item.create_by ? item.create_by.username : '-' }}
      </template>

      <template #item.update_date="{item}">
        {{ $datetime(item.update_date) }}<br>
        {{ $t('by') }}: {{ item.update_by ? item.update_by.username : '-' }}
      </template>

      <template #item.is_deleted="{item}">
        <!-- <v-chip
          :color="item.is_deleted ? 'error' : 'success'"
          text-color="white"
          small
        >
          {{ item.is_deleted ? 'InActive' : 'Active' }}
        </v-chip> -->
        <v-switch data-test="v-switch-status" :input-value="!item.is_deleted" @change="$emit('click-toggle-delete', item)" />
      </template>

      <template #item.action="{item}">
        <v-btn icon color="error" @click="$emit('click-delete', item)">
          <v-icon>mdi-delete</v-icon>
        </v-btn>
        <v-btn icon color="primary" @click="$emit('click-edit', item)">
          <v-icon>mdi-pencil</v-icon>
        </v-btn>
      </template>
    </v-data-table>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { ProductSet, VDataTableHeader } from '~/models'
export default Vue.extend({
  props: {
    productSet: {
      type: Array,
      default: () => [] as ProductSet[]
    }
  },
  data () {
    return {
      search: '',
      filter: {
        is_deleted: false
      }
    }
  },
  computed: {
    headers (): VDataTableHeader[] {
      return [
        { text: 'Set SKU', value: 'sku' },
        { text: 'Set Name', value: 'name' },
        { text: 'Products', value: 'products' },
        { text: 'Created', value: 'create_date' },
        { text: 'Updated', value: 'update_date' },
        { text: 'Status', value: 'is_deleted', cellClass: 'fit' },
        { text: '', value: 'action', cellClass: 'fit' }
      ]
    },
    items (): any[] {
      if (this.filter.is_deleted === null) {
        return this.productSet
      }
      return this.productSet.filter((x: any) => x.is_deleted === this.filter.is_deleted)
    }
  },
  methods: {
    customFilter (_: any, search: string, item: ProductSet) {
      let text = ''

      if (item.sku) {
        text += item.sku + ';'
      }
      if (item.name) {
        text += item.name + ';'
      }
      if (item.products) {
        text += item.products.map(p => p.sku).join(' ') + ';'
      }
      return text.toLowerCase().includes(search.toLowerCase())
    }
  }
})
</script>
