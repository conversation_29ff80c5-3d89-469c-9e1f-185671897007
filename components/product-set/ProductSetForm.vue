<template>
  <v-form ref="form" @submit.prevent="">
    <div>
      <APIErrorList :errors="errors" />
    </div>
    <v-row>
      <v-col cols="12" sm="3" md="3" class="pr-5">
        <div class="title">
          ข้อมูลทั่วไป
        </div>
        <div class="grey--text text--darken-1">
          ระบุข้อมูลของสินค้าจัดเซ็ต กรุณาระวังไม่ให้ SKU ของสินค้าจัดเซ็ตซ้ำกัน
        </div>
      </v-col>
      <v-col cols="12" sm="9" md="7">
        <v-text-field
          data-test="v-text-field-product-set-sku"
          label="SKU สินค้าจัดเซ็ต"
          filled
          :rules="[$rules.required]"
          :error-messages="errors.sku"
          :value="formData.sku"
          @change="update({ sku: $event })"
        />
        <v-text-field
          data-test="v-text-field-product-set-name"
          label="ชื่อสินค้าจัดเซ็ต (Optional)"
          :error-messages="errors.name"
          :value="formData.name"
          @change="update({ name: $event })"
        />
      </v-col>
    </v-row>

    <v-divider class="my-5" />

    <v-row>
      <v-col cols="12" sm="3" md="3">
        <div class="title">
          ข้อมูลสินค้า
        </div>
        <div class="grey--text text--darken-1">
          รายการสินค้าในเซ็ต เมื่อทำการเริ่มแพ็กสินค้า ระบบจะทำการแตกสินค้าออกเป็นรายการแยกโดยอัตโนมัติ
        </div>
      </v-col>
      <v-col cols="12" sm="9" md="7">
        <v-alert v-if="validationError.products" type="error" outlined>
          {{ validationError.products }}
        </v-alert>
        <table style="width: 100%;">
          <thead>
            <th>SKU/Barcode</th>
            <th>Name (optional)</th>
            <th>Amount</th>
            <th />
          </thead>
          <tbody>
            <tr v-for="p in formData.products" :key="p.sku">
              <td class="pr-2 py-1">
                <v-text-field
                  filled
                  data-test="v-text-field-product-item-sku"
                  hide-details="auto"
                  :rules="[$rules.required]"
                  :value="p.sku"
                  @change="updateProductSetItem(p, { sku: $event})"
                />
              </td>
              <td class="px-2 py-1">
                <v-text-field
                  hide-details="auto"
                  data-test="v-text-field-product-item-name"
                  :value="p.name"
                  @change="updateProductSetItem(p, { name: $event})"
                />
              </td>
              <td class="pl-2 py-1">
                <v-text-field
                  filled
                  data-test="v-text-field-product-item-amount"
                  hide-details=""
                  type="number"
                  style="text-align: right;"
                  min="1"
                  :rules="[$rules.required]"
                  :value="p.amount"
                  @change="updateProductSetItem(p, { amount: Number($event)})"
                />
              </td>
              <td>
                <v-btn icon color="error" @click="removeProductSetItem(p)">
                  <v-icon>mdi-delete</v-icon>
                </v-btn>
              </td>
            </tr>
            <tr v-if="formData.products.length === 0">
              <td colspan="3">
                <div class="text-center grey--text pt-8 pb-5">
                  ไม่พบรายการสินค้า กรุณาสเเกนบาร์โค๊ดสินค้าที่ต้องการเพิ่มในเซ็ต
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <v-text-field
          v-model="barcode"
          data-test="v-text-field-sub-product-set-sku"
          label="SKU / Barcode"
          filled
          class="mt-5"
          hint="สแกนรหัสบาร์โค๊ดเพื่อเพิ่มสินค้าในเซ็ต"
          persistent-hint
          append-icon="mdi-barcode-scan"
          @keypress.enter="addProduct()"
        />
      </v-col>
    </v-row>

    <v-divider v-if="formData.change_logs.length > 0" class="my-5" />

    <v-row v-if="formData.change_logs.length > 0">
      <v-col cols="12" sm="3" md="3">
        <div class="title">
          ประวัติการแก้ไข
        </div>
        <div class="grey--text text--darken-1">
          <!--  -->
        </div>
      </v-col>
      <v-col cols="12" sm="9" md="7">
        <div style="max-height: 200px; overflow-y: auto;">
          <div v-for="log, i in formData.change_logs.slice().reverse()" :key="i">
            {{ $datetime(log.history_date) }} - {{ getHistoryTypeName(log.history_type) }} by {{ log.history_user__username }}
          </div>
        </div>
      </v-col>
    </v-row>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import APIErrorList from '../errors/APIErrorList.vue'
import { ProductSetItem, VForm } from '~/models'
export default Vue.extend({
  components: {
    APIErrorList
  },

  props: {
    formData: {
      type: Object,
      required: true
    },
    errors: {
      type: Object,
      default: () => ({})
    }
  },

  data () {
    return {
      barcode: '',
      validationError: {} as any
    }
  },

  methods: {
    update (data: any) {
      this.$emit('update:form-data', { ...this.formData, ...data })
    },

    validate () {
      const form = this.$refs.form as never as VForm
      const is_valid = form.validate()

      if (this.formData.products.length === 0) {
        this.validationError = { ...this.validationError, ...{ products: 'กรุณาเพิ่มสินค้าในเซ็ตอย่างน้อย 1 รายการ' } }
        return false
      }

      return is_valid
    },

    updateProductSetItem (p: ProductSetItem, data: any) {
      const item = { ...p, ...data }

      const products = this.formData.products.map((x: ProductSetItem) => {
        if (x.sku === p.sku) {
          return item
        }
        return x
      })

      this.update({ products })
    },

    removeProductSetItem (p: ProductSetItem) {
      const products = this.formData.products.filter((x: ProductSetItem) => x.sku !== p.sku)
      this.update({ products })
    },

    addProduct () {
      if (!this.barcode) {
        return
      }

      const p = this.formData.products.find((x: ProductSetItem) => x.sku === this.barcode)

      this.validationError = {}

      if (p) {
        p.amount += 1
        this.update({ products: this.formData.products })
      } else {
        const products = [...this.formData.products, {
          sku: this.barcode,
          name: '',
          amount: 1
        }]
        this.update({ products })
      }

      this.barcode = ''
    },

    getHistoryTypeName (type: string) {
      if (type === '+') {
        return 'created'
      }
      if (type === '-') {
        return 'deleted'
      }
      if (type === '~') {
        return 'changed'
      }
    }
  }
})
</script>

<style scoped>
input {
  width: 100%;
  border: 1px solid #ccc;
  padding: 5px;
  border-radius: 5px;
}

table thead th {
  border-bottom: 1px solid #ccc;
  padding-bottom: 5px;
}
</style>
