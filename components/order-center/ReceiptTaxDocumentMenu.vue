<template>
  <v-menu offset-y>
    <template #activator="{ on }">
      <v-btn
        v-if="($hasPerms('view_taxdocument') || $hasPerms('add_taxdocument')) && !isSplittedOrder(pickOrder)"
        class="my-auto"
        color="primary"
        v-bind="$attrs"
        v-on="on"
      >
        <v-icon left>
          mdi-menu
        </v-icon>
        ตัวเลือก
      </v-btn>
    </template>
    <v-list v-if="menus.length > 0" dense>
      <v-list-item
        v-for="(menuItem, i) in menus"
        :key="i"
        :disabled="isMenuItemDisabled(menuItem)"
        @click="handleMenuClick(menuItem)"
      >
        <v-list-item-icon>
          <v-icon :color="isMenuItemDisabled(menuItem) ? 'grey' : ''">
            {{ menuItem.icon }}
          </v-icon>
        </v-list-item-icon>
        <v-list-item-content>
          <v-list-item-title
            :class="{ 'text--disabled': isMenuItemDisabled(menuItem) }"
          >
            {{ menuItem.text }}
          </v-list-item-title>
        </v-list-item-content>
      </v-list-item>
    </v-list>
  </v-menu>
</template>

<script lang="ts">
import _ from 'lodash'
import moment from 'moment'
import Vue from 'vue'
import {
  Company,
  FEATURE_FLAGS,
  LoggedInUser,
  PickOrder,
  TaxDocument
} from '~/models'
import { getCutOffDate } from '~/utils/cutoffdate'

interface MenuItem {
  text: string; // Label for the menu option
  icon: string; // Icon class name (e.g., for Material Design icons)
  permission: string;
  enable_states: string[]; // Array of states where the option is enabled
  value: string; // Action value associated with the option
}

export default Vue.extend({
  props: {
    taxDocument: {
      type: Object,
      default: null
    },
    documents: {
      type: Array,
      default: () => []
    },
    pickOrder: {
      type: Object,
      required: true
    },
    type: {
      type: String,
      default: 'tax_invoice',
      validator: (value: string) => ['tax_invoice', 'receipt', 'credit_note', 'return_recipt', 'invoice', 'debit_note', 'delivery_invoice'].includes(value)
    },
    docName: {
      type: String,
      default: ''
    }
  },

  data () {
    return {
      map_name_doc: {
        tax_invoice: 'ใบกำกับภาษี',
        receipt: 'ใบเสร็จรับเงิน',
        credit_note: 'ใบลดหนี้',
        return_receipt: 'ใบเสร็จรับเงิน/รับคืนสินค้า',
        delivery_invoice: 'ใบส่งของ/ใบกำกับภาษี'
      },
      is_enable_etax: this.$hasCompanyFeatureFlag(FEATURE_FLAGS.ETAX)
    }
  },
  computed: {
    isSplittedDocument () {
      const docs = this.documents as TaxDocument[]
      const doc_r = docs.find(x => x.doc_type === 'receipt')
      const doc_rt = docs.find(x => x.doc_type === 'tax_invoice')

      return !!doc_r && !!doc_rt
    },
    menus (): MenuItem[] {
      const munus_list = [
        {
          text: `ดู${this.docName}`,
          permission: 'view_taxdocument',
          icon: 'mdi-file-eye',
          enable_states: ['success', 'error', 'cancel'],
          value: 'view_doc'
        }
      ] as MenuItem[]

      if (this.type !== 'credit_note' && this.type !== 'return_receipt' && this.type !== 'delivery_invoice') {
        munus_list.push(
          {
            text: `แก้ไข${this.docName}`,
            permission: 'change_taxdocument',
            icon: 'mdi-file-edit',
            enable_states: ['failed', 'on_hold', 'success'],
            value: 'edit_doc'
          },
          {
            text: `ยกเลิก${this.docName}`,
            permission: 'delete_taxdocument',
            icon: 'mdi-file-cancel',
            enable_states: ['success'],
            value: 'cancel_doc'
          },
          {
            text: `${this.type === 'tax_invoice' ? 'สร้างใบลดหนี้' : 'สร้างใบรับคืนสินค้า'}`,
            permission: 'add_taxdocument',
            icon: 'mdi-file-plus',
            enable_states: ['success'],
            value: 'create_credit_note'
          },
          {
            text: 'คัดลอกลิงค์',
            permission: 'add_taxdocument',
            icon: 'mdi-content-copy',
            enable_states: ['new', 'failed', 'on_hold'],
            value: 'copy_link'
          },
          {
            text: 'แสดง QR Code',
            permission: 'add_taxdocument',
            icon: 'mdi-qrcode',
            enable_states: ['new', 'failed', 'on_hold'],
            value: 'open_qr_code'
          }
        )
      }

      // if (this.type === 'tax_invoice') {
      //   munus_list.push(
      //   )
      // }

      // if (this.type === 'credit_note') {
      //   munus_list.push(
      //     {
      //       text: 'ยกเลิกใบลดหนี้',
      //       permission: 'delete_taxdocument',
      //       icon: 'mdi-file-cancel',
      //       enable_states: ['success'],
      //       value: 'cancel_credit_note'
      //     }
      //   )
      // }
      // munus_list.push()

      return munus_list
    },
    settings () {
      return this.$store.state.settings
    }

  },
  methods: {
    isMenuItemDisabled (menuItem: MenuItem) {
      if (!this.is_enable_etax) {
        return true
      }

      if (menuItem.value === 'cancel_doc' && this.taxDocument && this.isDocumentAfterCutoff()) {
        return true
      }

      const cancel_menu_types = ['cancel_doc', 'create_credit_note']
      if (cancel_menu_types.includes(menuItem.value)) {
        if (_.some(this.documents, { doc_type: 'return_receipt' })) {
          return true
        }
        if (_.some(this.documents, { doc_type: 'credit_note' })) {
          return true
        }
      }

      const status = this.getTaxDocStatus()
      const menu_types = ['create_credit_note', 'edit_doc', 'cancel_doc', 'copy_link', 'open_qr_code']
      if (menu_types.includes(menuItem.value) && _.some(this.documents, { doc_type: 'credit_note' })) {
        return true
      }

      const is_perms = menuItem.permission ? !this.$hasPerms(menuItem.permission) : false

      return !menuItem.enable_states.includes(status) || is_perms
    },
    getTaxDocStatus () {
      if (!this.taxDocument) {
        return 'new'
      }
      return this.taxDocument.status?.toLowerCase() || 'new'
    },
    isSplittedOrder (pickOrder: PickOrder) {
      const re = new RegExp('^[A-Z0-9]+---\\d+-\\d+$')
      return re.test(pickOrder.order_number)
    },
    isDocumentAfterCutoff () {
      const tax_doc = this.taxDocument as TaxDocument
      const cutoff_limit = this.settings.company.ETAX_RETRIEVAL_DAY
      const cutoff_date = getCutOffDate(tax_doc.doc_info.DOC_ISSUE_DATE, cutoff_limit)
      return moment().isAfter(cutoff_date)
    },
    async handleMenuClick (menuItem: MenuItem) {
      const company = this.$auth.user?.company as Company
      const tax_doc = this.taxDocument as TaxDocument
      // const documents = [] as TaxDocument[]
      // const tax_document_ids = pick_order.taxdocument_set

      // for (const tax_doc_id of tax_document_ids) {
      //   const tax_doc = this.tableData.tax_documents.get(tax_doc_id)
      //   if (tax_doc) {
      //     documents.push(tax_doc)
      //   }
      // }

      // const grouped = _.groupBy(documents, 'doc_type')
      // let tax_doc: any
      // let receipt: TaxDocument | undefined

      // if (grouped) {
      //   tax_doc = _.maxBy(grouped.tax_invoice, 'id')
      //   receipt = _.maxBy(grouped.receipt, 'id')
      //   console.log(tax_doc)
      // } else {
      //   tax_doc = this.tableData.tax_documents.get(id)
      //   receipt = this.tableData.tax_documents.get(id)

      //   console.log(tax_doc)
      // }

      const mainUrl = `${window.location.origin}/public/etax-invoice?cid=${company.uuid}&oid=${this.pickOrder.uuid}`
      const user = this.$auth.user as never as LoggedInUser

      if (menuItem.value === 'create_credit_note') {
        const map_cn_text = {
          tax_invoice: 'ใบลดหนี้',
          receipt: 'ใบเสร็จรับเงิน'
        }
        let text_content = `กรุณากดยืนยันหากต้องการสร้าง <b>${map_cn_text[this.type]}</b> โดยอ้างอิงจาก <b>${this.map_name_doc[this.type]}</b>
        <br>
        #${this.taxDocument?.doc_info?.DOC_ID}
        `

        if (this.isSplittedDocument) {
          text_content = `ในกรณีคำสั่งซื้อ "มีรายการสินค้าที่ได้รับการยกเว้นภาษีมูลค่าเพิ่ม" ระบบจะ <b>สร้างใบลดหนี้</b> สำหรับเอกสารทั้ง 2 รายการโดยอัตโนมัติ
          <br><br>
          <ul>
            <li>ใบกำกับภาษี/ใบเสร็จรับเงิน (VAT)</li>
            <li>ใบเสร็จรับเงิน (NON VAT)</li>
          </ul>
          `
        }

        const confirm = await this.$confirm({
          title: 'คุณต้องการสร้างใบลดหนี้ใช่หรือไม่?',
          // text: 'ในกรณีคำสั่งซื้อมีรายการสินค้าที่ได้รับการยกเว้นภาษีมูลค่าเพิ่ม ระบบจะสร้างใบลดหนี้สำหรับใบกำกับภาษีและใบเสร็จรับเงินโดยอัตโนมัติ'
          text: text_content
        })
        if (confirm) {
          await this.createCreditNote(tax_doc.uuid)
        }
        return
      }

      if (menuItem.value === 'edit_doc') {
        if (this.isSplittedDocument) {
          const text_content = `ในกรณีคำสั่งซื้อ "มีรายการสินค้าที่ได้รับการยกเว้นภาษีมูลค่าเพิ่ม" ระบบจะ <b>แก้ไขเอกสารทั้ง 2 รายการ</b> โดยอัตโนมัติ
          <br><br>
          <ul>
            <li>ใบกำกับภาษี/ใบเสร็จรับเงิน (VAT)</li>
            <li>ใบเสร็จรับเงิน (NON VAT)</li>
          </ul>
          `

          const cont = await this.$confirm({
            title: 'แก้ไขใบเสร็จรับเงิน/ใบกำกับภาษี',
            text: text_content
          })

          if (!cont) {
            return
          }
        }

        window.open(
          mainUrl + `&tid=${tax_doc.uuid}&u=staff&update=true&uid=${user.uuid}`,
          '_blank'
        )
        return
      }

      if (menuItem.value === 'cancel_credit_note') {
        const confirm = await this.$confirm({
          title: 'คุณต้องการยกเลิกใบลดหนี้ใช่หรือไม่?',
          text: 'กดยืนยันเพื่อยกเลิกใบลดหนี้'
        })
        if (confirm) {
          await this.cancelDocument(tax_doc.uuid)
        }
        return
      }

      if (menuItem.value === 'cancel_doc') {
        let text_content = `ท่านต้องการยกเลิก <b>${this.map_name_doc[this.type]}</b> ใช่หรือไม่?
        <br>
        หากยกเลิกแล้วการดำเนินการนี้จะไม่สามารถย้อนกลับได้
        `

        if (this.isSplittedDocument) {
          text_content = `ในกรณีคำสั่งซื้อ "มีรายการสินค้าที่ได้รับการยกเว้นภาษีมูลค่าเพิ่ม" ระบบจะ <b>ยกเลิก</b> เอกสารทั้ง 2 รายการโดยอัตโนมัติ
          <br><br>
          <ul>
            <li>ใบกำกับภาษี/ใบเสร็จรับเงิน (VAT)</li>
            <li>ใบเสร็จรับเงิน (NON VAT)</li>
          </ul>
          `
        }

        const confirm = await this.$confirm({
          title: 'คุณต้องการยกเลิก ใบเสร็จรับเงิน/ใบกำกับภาษี ใช่หรือไม่?',
          text: text_content
        })
        if (confirm) {
          await this.cancelDocument(tax_doc.uuid)
        }
        return
      }

      switch (menuItem.value) {
        case 'create_doc':
          window.open(mainUrl + `&u=staff&uid=${user.uuid}`, '_blank')
          break
        // case 'edit_doc':
        //   window.open(
        //     mainUrl + `&tid=${tax_doc.uuid}&u=staff&update=true&uid=${user.uuid}`,
        //     '_blank'
        //   )
        //   break
        case 'view_doc':
          window.open(tax_doc.doc_url, '_blank')
          break
        // case 'view_receipt':
        //   window.open(receipt ? receipt.doc_url : tax_doc.doc_url, '_blank')
        //   break
        case 'copy_link':
          navigator.clipboard
            .writeText(mainUrl)
            .then(() => {
              this.$snackbar('success', 'Copied to clipboard!')
            })
            .catch((err) => {
              console.error('Failed to copy:', err)
              this.$snackbar('error', 'Failed to copy')
            })
          break
        case 'open_qr_code':
          window.open(`/easy-etax/${this.pickOrder.order_number}/qr-code`, '_blank')
          break
        // case 'test':
        //   console.log('Test', this.taxDocument)
        //   break
      }
    },
    async cancelDocument (tax_document_uuid: string) {
      this.$loader(true)
      try {
        if (this.type === 'credit_note') {
          await this.$axios.$post('/api/etax/credit-note/cancel/', { tax_document_uuid })
        } else {
          await this.$axios.$post('/api/etax/cancel/', { tax_document_uuid })
        }
        this.$emit('cancel:tax-doc')
      } catch (error) {
        console.error(error)
      }
      this.$loader(false)
    },
    async createCreditNote (tax_document_uuid: string) {
      this.$loader(true)
      try {
        await this.$axios.$post('/api/etax/credit-note/create/', { tax_document_uuid })
        // this.$emit('create:credit-note')
        this.$emit('cancel:tax-doc')
        this.$snackbar('success', 'Created credit note successfully')
      } catch (error) {
        console.error(error)
      }
      this.$loader(false)
    }
  }
})
</script>
