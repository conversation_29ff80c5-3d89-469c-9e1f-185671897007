<template>
  <v-dialog :value="value" persistent max-width="960" :fullscreen="$vuetify.breakpoint.smAndDown">
    <v-card>
      <v-card-title>
        <span class="headline">สร้างออเดอร์</span>
        <v-spacer />
        <v-btn icon @click="close()">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider />

      <v-card-text class="pt-3">
        <div>
          <easy-order-form
            ref="form"
            :order-json.sync="order_json_form"
            :errors="form_errors.order_json || {}"
            :auto-generate-tracking="is_auto_generate_tracking"
            @clear-form="clearOrderForm()"
            @toggle-gen-tracking="toggleGenTracking($event)"
            @update-order-form="updateOrderForm($event)"
          />
        </div>
      </v-card-text>
      <v-alert
        v-if="form_errors && Object.keys(form_errors).length > 0"
        class="mt-5"
        text
        outlined
        type="error"
      >
        <div v-for="[key, errors] of Object.entries(form_errors.order_json)" :key="key">
          <div>{{ key }}</div>
          <ul>
            <li v-for="error of errors" :key="error">
              {{ error }}
            </li>
          </ul>
        </div>
      </v-alert>

      <v-divider />

      <v-card-actions>
        <v-spacer />
        <v-btn :loading="loading" color="success" @click="createEasyOrder()">
          สร้าง ORDER
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import EasyOrderForm from '~/components/order-center/EasyOrderForm.vue'
import ShippingModule from '~/repositories/modules/shipping'
import { PickItem, PickOrder } from '~/models'

export default Vue.extend({
  components: {
    EasyOrderForm
  },

  props: {
    value: {
      type: Boolean,
      required: true
    }
  },

  data () {
    return {
      loading: false,
      error_message: '',
      order_json_form: {
        number: '',
        remark: '',
        trackingno: '',
        status: 'Pending',
        customername: '',
        customerphone: '',
        shippingaddress: '',
        shippingchannel: '',
        amount: 0,
        createdatetimeString: '',
        discountamount: 0,
        shippingamount: 0,
        isCOD: false,
        paymentamount: '',
        payments: [],
        paymentstatus: 'Pending',
        saleschannel: '',
        shippingname: '',
        orderdate: this.$moment().format('YYYY-MM-DD'),
        orderdateString: this.$moment().format('YYYY-MM-DD'),
        createdatetime: null as any,
        is_confirm_received: true,
        extra: {
          branch_number: '00000'
        },
        list: [
          {
            id: null,
            sku: '',
            name: '',
            number: 1,
            skutype: null,
            discount: '',
            unittext: '',
            totalprice: 0,
            producttype: 0,
            serialnolist: [],
            discountamount: 0,
            pricepernumber: 0,
            eso_vatpercent: 7,
            seller_discount: 0,
            image: ''
          }
        ] as PickItem[] | []
      },
      is_auto_generate_tracking: false,
      form_errors: {} as any
    }
  },

  watch: {
    value () {
      if (this.order_json_form.number === '') {
        this.generateOrderNumber()
      }
    }
  },

  methods: {
    close () {
      this.clearOrderForm()
      this.$emit('input', false)
    },

    clearOrderForm () {
      this.order_json_form = {
        number: '',
        remark: '',
        trackingno: '',
        status: 'Pending',
        customername: '',
        customerphone: '',
        shippingaddress: '',
        shippingchannel: '',
        amount: 0,
        createdatetimeString: '',
        discountamount: 0,
        shippingamount: 0,
        isCOD: false,
        paymentamount: '',
        payments: [],
        paymentstatus: 'Pending',
        saleschannel: '',
        shippingname: '',
        orderdate: this.$moment().format('YYYY-MM-DD'),
        orderdateString: this.$moment().format('YYYY-MM-DD'),
        createdatetime: this.$moment().format('YYYY-MM-DD'),
        is_confirm_received: true,
        extra: {
          branch_number: '00000'
        },
        list: [
          {
            id: null,
            sku: '',
            name: '',
            number: 1,
            skutype: null,
            discount: '',
            unittext: '',
            totalprice: 0,
            producttype: 0,
            serialnolist: [],
            discountamount: 0,
            pricepernumber: 0,
            eso_vatpercent: 7,
            seller_discount: 0,
            image: ''
          }
        ] as PickItem[] | []
      }
      this.is_auto_generate_tracking = false
    },

    toggleGenTracking (bool: boolean) {
      this.is_auto_generate_tracking = bool
    },

    updateOrderForm (data: any) {
      this.order_json_form = { ...this.order_json_form, ...data }
    },

    generateOrderNumber () {
      const order_number = 'ESO-XXX'
      this.order_json_form.number = order_number
    },

    async createEasyOrder () {
      if (this.loading) {
        return
      }

      if (!(this.$refs.form as any).validate()) {
        return
      }

      this.loading = true
      const couriers = ['DBB_FLASH', 'DBB_THAIPOST_EMS', 'DBB_THAIPOST_REG', 'DBB_KERRY']

      try {
        this.order_json_form.createdatetime = this.$moment().format()
        this.order_json_form.createdatetimeString = this.$moment().format('YYYY-MM-DD HH:mm:ss')

        const res = await this.$axios.post('/api/picking/easy-orders/', { order_json: this.order_json_form, auto_gen_order_number: this.order_json_form.number.toLowerCase().startsWith('eso-xxx') })
        const pick_order = res.data

        if (couriers.includes(pick_order.order_shippingchannel)) {
          this.autoGenerateTrackingNumber(pick_order)
        }
        this.clearOrderForm()
        this.$emit('update:pick_order', pick_order)
        this.$emit('input', false)
        this.$snackbar('success', this.$t('order-center.easy-order-created'))
      } catch (error: any) {
        console.error(error)

        if (error.response) {
          if (error.response.status === 400) {
            // this.error_message = `
            //   <div>พบข้อผิดพลาด ไม่สามารถสร้างออเดอร์ที่มีเลขที่คำสั่งซื้อซ้ำได้</div>
            //   <ul>
            //     <li>โปรดตรวจสอบเลขที่คำสั่งซิ้อใหม่อีกครั้ง</li>
            //   </ul>
            // `
            this.form_errors = error.response.data
            this.$snackbar('error', this.$t('error.easy-order-created'))
          } else {
            this.$snackbar('error', this.$t('error.unknown'))
          }
        }
      }
      this.loading = false
    },

    async autoGenerateTrackingNumber (pick_order: PickOrder) {
      const form_data = {
        shippingname: pick_order.order_json.shippingname,
        shippingphone: pick_order.order_json.shippingphone,
        shippingaddress: pick_order.order_json.shippingaddress,
        shippingprovince: pick_order.order_json.shippingprovince,
        shippingdistrict: pick_order.order_json.shippingdistrict,
        shippingsubdistrict: pick_order.order_json.shippingsubdistrict,
        shippingpostcode: pick_order.order_json.shippingpostcode
      }
      const shipping_module = new ShippingModule(this.$axios)
      const response = await shipping_module.generateTracking(pick_order)

      this.$emit('created:tracking-number', { new_po_tracking: response.data, form_data })
    }
  }
})
</script>

<style scoped>

</style>
