<template>
  <div>
    <v-row>
      <v-col cols="6">
        <v-text-field
          :disabled="!readOnly && disabled"
          label="ค่าส่ง"
          type="number"
          min="0"
          outlined
          reverse
          dense
          hide-details
          class="mb-4"
          :value="orderJson.shippingamount"
          :rules="[$rules.required, $rules.number_min(0)]"
          @click="selectText($event)"
          @change="$emit('update:order-json', { shippingamount: Number($event)})"
        />
      </v-col>
      <v-col cols="6">
        <v-text-field
          :disabled="!readOnly && disabled"
          label="ส่วนลดท้ายบิล"
          type="number"
          outlined
          reverse
          dense
          hide-details
          class="mb-4"
          :rules="[$rules.required]"
          :value="orderJson.discountamount"
          @click="selectText($event)"
          @change="$emit('update:order-json', { discountamount: Number($event)})"
        />
      </v-col>
    </v-row>
    <div v-if="orderJson.discountamount" class="mb-6">
      <div class="d-flex justify-space-between mb-2">
        <span>มูลค่าทีก่อนหักส่วนลดรวม</span>
        <span>{{ $fmtCurrency(subTotal + orderJson.shippingamount) }} บาท</span>
      </div>
      <div class="d-flex justify-space-between mb-2">
        <span>ส่วนลดท้ายบิล</span>
        <span>{{ $fmtCurrency(orderJson.discountamount) }} บาท</span>
      </div>
      <div class="d-flex justify-space-between mb-2">
        <span>มูลค่าหลังหักส่วนลดรวม</span>
        <span>{{ $fmtCurrency(subTotal - orderJson.discountamount + orderJson.shippingamount) }} บาท</span>
      </div>
    </div>

    <div v-else>
      <div class="d-flex justify-space-between mb-2">
        <span>ส่วนลดท้ายบิล</span>
        <span>{{ $fmtCurrency(orderJson.discountamount) }} บาท</span>
      </div>
    </div>

    <div v-if="priceBeforeTax0" class="d-flex justify-space-between mb-2">
      <span>มูลค่าที่คำนวณภาษี 0%</span>
      <span>{{ $fmtCurrency(priceBeforeTax0) }} บาท</span>
    </div>

    <div v-if="priceBeforeTax7" class="d-flex justify-space-between mb-2">
      <span>มูลค่าที่คำนวณภาษี 7%</span>
      <span>{{ $fmtCurrency(priceBeforeTax7) }} บาท</span>
    </div>

    <div v-if="priceBeforeTax7" class="d-flex justify-space-between mb-2">
      <span>ภาษีมูลค่าเพิ่ม 7%</span>
      <span>{{ $fmtCurrency(orderJson.amount - priceBeforeTax7 - priceBeforeTax0 ) }} บาท</span>
    </div>

    <v-divider class="my-2" />

    <div class="d-flex justify-space-between font-weight-bold">
      <span>จำนวนเงินทั้งสิ้น:</span>
      <span>{{ $fmtCurrency(orderJson.amount) }} บาท</span>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import _ from 'lodash'
import { OrderJson } from '~/models'

export default Vue.extend({
  props: {
    orderJson: {
      type: Object,
      required: true
    } as Vue.PropOptions<OrderJson>,

    readOnly: {
      type: Boolean,
      default: false,
      required: false
    },

    disabled: {
      type: Boolean,
      default: false,
      required: false
    }

  },

  data () {
    return {}
  },

  computed: {
    subTotal (): number {
      return _.sumBy(this.orderJson.list, 'totalprice')
    },
    totalDiscount (): number {
      return _.sumBy(this.orderJson.list, 'seller_discount')
    },
    priceBeforeTax7 (): number {
      let total = 0
      for (const item of this.orderJson.list || []) {
        if (item?.eso_vatpercent === 7) {
          const price = item.pricepernumber * item.number - item.seller_discount
          total += price
        }
      }

      if (this.orderJson.shippingamount) {
        total += this.orderJson.shippingamount
      }
      if (this.orderJson.discountamount) {
        total -= this.orderJson.discountamount * this.orderVatRatio7
      }

      return total / 1.07
    },
    priceBeforeTax0 (): number {
      let total = 0
      for (const item of this.orderJson.list || []) {
        if (item?.eso_vatpercent === 0) {
          const price = item.pricepernumber * item.number - item.seller_discount
          total += price
        }
      }

      if (this.orderJson.discountamount) {
        total -= this.orderJson.discountamount * this.orderVatRatio0
      }

      return total
    },
    totalPrice7 (): number {
      let total_price = 0
      for (const item of this.orderJson.list || []) {
        if (item?.eso_vatpercent === 7) {
          total_price += item.pricepernumber * item.number - item.seller_discount
        }
      }

      if (this.orderJson.shippingamount) {
        total_price += this.orderJson.shippingamount
      }
      return total_price
    },
    totalPrice0 (): number {
      return _.sumBy(this.orderJson.list, (item) => {
        if (item?.eso_vatpercent === 0) {
          return item.pricepernumber * item.number - item.seller_discount
        }
        return 0
      })
    },

    orderVatRatio7 (): number {
      const total_price = this.subTotal + this.orderJson.shippingamount
      return this.totalPrice7 / total_price
    },
    orderVatRatio0 (): number {
      const total_price = this.subTotal + this.orderJson.shippingamount
      return this.totalPrice0 / total_price
    }

  },

  watch: {
    'orderJson.shippingamount': {
      handler () {
        this.updatePaymentAmount()
      }
    },
    'orderJson.discountamount': {
      handler () {
        this.updatePaymentAmount()
      }
    },
    subTotal: {
      handler () {
        this.updatePaymentAmount()
      }
    }
  },

  methods: {
    updatePaymentAmount () {
      const amount =
        Number(this.subTotal) +
        Number(this.orderJson.shippingamount) -
        Number(this.orderJson.discountamount)
      this.$emit('update:order-json', { amount: Number(amount.toFixed(2)), paymentamount: Number(amount.toFixed(2)) })
    },

    selectText (event: PointerEvent) {
      const target = event.target as HTMLInputElement
      target.select()
      target.focus()
    }
  }

})
</script>

<style lang="scss" scoped>

</style>
