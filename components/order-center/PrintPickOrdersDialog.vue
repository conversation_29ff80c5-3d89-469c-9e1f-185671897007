<template>
  <v-dialog
    max-width="800"
    :value="value"
    @input="$emit('input', $event)"
  >
    <v-card>
      <v-card-title class="text-h6">
        {{ $t('order-center.print-pick-orders') }}
        <v-spacer />
        <v-btn icon @click="$emit('input', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider class="mb-4" />
      <v-card-text>
        <v-form ref="form">
          <p>กรอกหมายเลขคำสั่งซื้อ 1 บรรทัด 1 หมายเลข (แนะนำไม่เกิน 1000 หมายเลขต่อครั้ง)</p>
          <v-textarea
            v-model="order_numbers"
            :label="$t('order.order-number')"
            :rules="[$rules.required]"
            auto-grow
            outlined
            hide-details
          />
          <v-checkbox v-model="force_reprint" label="บังคับพิมพ์ซ้ำ" hide-details dense />
          <v-checkbox v-model="force_print_voided_order" label="บังคับพิมพ์คำสั่งซื้อสถานะ ถูกยกเลิก (Voided)" hide-details dense class="mb-5" />
        </v-form>

        <v-alert :value="!!status" outlined color="success">
          {{ status }}
        </v-alert>

        <!-- <v-expansion-panels accordion>
          <v-expansion-panel v-if="result.accepted.length > 0">
            <v-expansion-panel-header class="success--text">
              ส่งคำสั่งพิมพ์สำเร็จ {{ result.accepted.length }} รายการ
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              {{ result.accepted.join(', ') }}
            </v-expansion-panel-content>
          </v-expansion-panel>

          <v-expansion-panel v-if="result.voided.length > 0">
            <v-expansion-panel-header class="error--text">
              ไม่ส่งคำสั่งพิมพ์เนื่องจากสถานะคำสั่งซื้อถูกยกเลิก {{ result.voided.length }} รายการ
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              {{ result.voided.join(', ') }}
            </v-expansion-panel-content>
          </v-expansion-panel>

          <v-expansion-panel v-if="result.printed.length > 0">
            <v-expansion-panel-header class="error--text">
              ไม่ส่งคำสั่งพิมพ์เนื่องจากคำสั่งซื้อถูกพิมพ์แล้ว {{ result.printed.length }} รายการ
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              {{ result.printed.join(', ') }}
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels> -->
      </v-card-text>
      <v-card-actions>
        <v-spacer />
        <v-btn color="primary" :loading="loading" @click="submit()">
          {{ $t('settings.confirm') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue, { PropOptions } from 'vue'
import _ from 'lodash'
import { VForm } from '~/models'

export default Vue.extend({
  props: {
    value: {
      type: Boolean,
      default: false
    } as PropOptions<boolean>
  },

  data () {
    return {
      loading: false,
      order_numbers: '',
      force_reprint: false,
      force_print_voided_order: false,
      status: '',
      result: {
        accepted: [],
        voided: [],
        printed: []
      }
    }
  },

  methods: {
    async submit () {
      this.status = ''
      const form = this.$refs.form as never as VForm
      if (!form.validate()) {
        return
      }

      const action = '/api/picking/pick-orders/print-pick-orders/'
      const payload = {
        order_numbers: _.uniq(this.order_numbers.split('\n').map(x => x.trim()).filter(x => !!x)),
        force_reprint: this.force_reprint,
        force_print_voided_order: this.force_print_voided_order
      }

      this.loading = true
      try {
        const res = await this.$axios.post(action, payload, { responseType: 'blob' })
        const blob = await res.data
        this.downloadFileFromBlob(blob, 'print-result.xlsx')
        this.status = 'ส่งคำสั่งพิมพ์สำเร็จ กรุณาตรวจสอบรายการสั่งพืมพ์ใน File Excel'

        this.order_numbers = ''
        this.$snackbar('success', this.$t('success'))
        form.resetValidation()
      } catch (error: any) {
        console.error(error)
        if (error.response) {
          this.$snackbar('error', this.$t('error.response', [error.response.status, error.response.data]))
        }
      }
      this.loading = false
    },

    downloadFileFromBlob (blob: any, filename = 'report.xlsx') {
      const download_url = window.URL.createObjectURL(new Blob([blob]))
      const link = document.createElement('a')
      link.href = download_url
      link.setAttribute('download', filename)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      this.$loader(false)
    }
  }
})
</script>
