<template>
  <v-form ref="form" @submit.prevent="updateReadyToShip()">
    <v-text-field
      v-model.trim="order_number"
      data-test="v-text-field-ready-to-ship"
      :label="$t('order-center.scan-ready-to-ship')"
      outlined
      autocomplete="off"
      autofocus
      :hint="$t('order-center.order-no-tracking-no-enter')"
      persistent-hint
      :rules="[$rules.barcode]"
    />

    <select-order-number-dialog v-model="select_order_number.dialog" :orders="select_order_number.orders" @select="onSelectOrderNumber($event)" />
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import { playSound } from '~/utils/sound'
import { SettingState } from '~/store/settings'
import { PickOrder, VForm } from '~/models'
import SelectOrderNumberDialog from '~/components/dialogs/SelectOrderNumberDialog.vue'
export default Vue.extend({
  components: {
    SelectOrderNumberDialog
  },

  data () {
    return {
      order_number: '',

      select_order_number: {
        dialog: false,
        orders: [] as PickOrder[]
      }
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },

  methods: {
    extractOrderNumber (text: string, regex: string) {
      const pattern = new RegExp(regex)

      if (pattern.test(text)) {
        const order_number = text.match(pattern)?.groups?.order_number
        if (order_number) {
          return order_number.toUpperCase()
        }
      }

      return text
    },
    async updateReadyToShip () {
      const form = this.$refs.form as never as VForm
      if (!form.validate()) {
        return
      }

      const regex: string = this.settings.company.READY_TO_SHIP_ORDER_NUMBER_REGEX
      if (regex) {
        this.order_number = this.extractOrderNumber(this.order_number, regex)
      }

      if (this.settings.company.CONTROL_CODE_FORCE_UPPERCASE_LETTERS) {
        this.order_number = this.order_number.toUpperCase()
      }

      const action = `/api/picking/pick-orders/${this.order_number}/ready-to-ship/`
      const payload = {}
      try {
        const response = await this.$axios.post(action, payload)
        const pick_order = response.data
        this.$emit('ready-to-ship-success', pick_order)
        this.$snackbar('success', this.$t('order-center.success'))
      } catch (error: any) {
        console.error(error)

        const response = error.response
        if (response && response.status === 404) {
          this.$snackbar('error', this.$t('order-center.not-found', [this.order_number]))
          this.$alert({
            title: this.$t('order-center.cannot-ready-to-ship'),
            text: response.data.detail,
            theme: 'error'
          })
          playSound('alarm')
        }

        if (response && response.data.code === 'READY_TO_SHIP_REQUIRE_VIDEO') {
          const pick_order = response.data.pick_order
          this.$emit('ready-to-ship-failed', pick_order)
          this.$snackbar('error', response.data.detail)
          this.$alert({
            title: this.$t('order-center.cannot-ready-to-ship'),
            text: response.data.detail,
            theme: 'error'
          })
          playSound('alarm')
        }
        if (response && response.data.code === 'READY_TO_SHIP_REQUIRE_NO_FIXCASES') {
          const pick_order = response.data.pick_order
          this.$emit('ready-to-ship-failed', pick_order)
          this.$snackbar('error', response.data.detail)
          this.$alert({
            title: this.$t('order-center.cannot-ready-to-ship'),
            text: response.data.detail,
            theme: 'error'
          })
          playSound('alarm')
        }
        if (response && response.data.code === 'ALREADY_READY_TO_SHIP') {
          const pick_order = response.data.pick_order
          this.$emit('ready-to-ship-failed', pick_order)
          this.$snackbar('info', response.data.detail)
          playSound('alarm')
        }

        if (response && response.data.code === 'MULTIPLE_ORDERS') {
          const pick_orders = response.data.pick_orders
          this.onMultipleOrdersFound(pick_orders)
          this.$snackbar('info', response.data.detail)
          playSound('alarm')
        }
      }
      this.order_number = ''
    },

    onSelectOrderNumber (order_number: string) {
      this.order_number = order_number
      this.select_order_number.dialog = false
      this.select_order_number.orders = []
      this.updateReadyToShip()
    },

    onMultipleOrdersFound (orders: PickOrder[]) {
      this.select_order_number.orders = orders
      this.select_order_number.dialog = true
    }
  }
})
</script>
