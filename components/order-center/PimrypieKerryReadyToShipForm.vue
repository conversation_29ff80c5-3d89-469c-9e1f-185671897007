<template>
  <v-form @submit.prevent="updateReadyToShip()">
    <v-text-field
      v-model.trim="barcode"
      :label="$t('order-center.scan-ready-to-ship-kerry')"
      outlined
      autocomplete="off"
      autofocus
      :hint="$t('order-center.barcodecode')"
      persistent-hint
    />
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import { playSound } from '~/utils/sound'
import { SettingState } from '~/store/settings'
export default Vue.extend({
  data () {
    return {
      barcode: ''
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },

  methods: {
    async updateReadyToShip () {
      const pattern = /^(?<order_number>(VRIC|PIM)[A-Z0-9]+)-\d+-\d+-$/
      if (!pattern.test(this.barcode)) {
        alert(this.$t('order-center.incorrect-barcode'))
        return ''
      }

      const action = '/api/picking/pick-orders/ready-to-ship/multi-package/001/'
      const payload = { barcode: this.barcode }

      try {
        const response = await this.$axios.post(action, payload)
        const pick_order = response.data
        this.$emit('ready-to-ship-success', pick_order)
        this.$snackbar('success', this.$t('order-center.success'))
      } catch (error: any) {
        console.error(error)

        const response = error.response
        if (response && response.status === 404) {
          this.$snackbar('error', this.$t('order-center.not-found', [this.barcode]))
          playSound('alarm')
        }

        if (response && response.data.code === 'BARCODE_ALREADY_SCANNED') {
          const pick_order = response.data.pick_order
          this.$emit('ready-to-ship-failed', pick_order)
          this.$snackbar('error', response.data.detail)
          playSound('alarm')
        }
        if (response && response.data.code === 'READY_TO_SHIP_REQUIRE_VIDEO') {
          const pick_order = response.data.pick_order
          this.$emit('ready-to-ship-failed', pick_order)
          this.$snackbar('error', response.data.detail)
          playSound('alarm')
        }
        if (response && response.data.code === 'READY_TO_SHIP_REQUIRE_NO_FIXCASES') {
          const pick_order = response.data.pick_order
          this.$emit('ready-to-ship-failed', pick_order)
          this.$snackbar('error', response.data.detail)
          playSound('alarm')
        }
        if (response && response.data.code === 'ALREADY_READY_TO_SHIP') {
          const pick_order = response.data.pick_order
          this.$emit('ready-to-ship-failed', pick_order)
          this.$snackbar('info', response.data.detail)
          playSound('alarm')
        }
      }

      this.barcode = ''
    }
  }
})
</script>
