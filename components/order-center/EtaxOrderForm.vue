<template>
  <v-form ref="form" @submit.prevent="$emit('submit')">
    <v-row dense>
      <!-- Order & Tracking -->
      <!-- <v-col cols="12">
        <h4>รายละเอียดคำสั่งซื้อ</h4>
      </v-col> -->
      <v-col cols="12">
        <v-row dense>
          <v-col cols="12">
            <div class="d-flex justify-space-between">
              <div>
                <b>เลขที่เอกสาร</b>
                <span v-if="mode === 'create'">(ล่าสุด: {{ lastOrderNumber }})</span>
              </div>
              <div>
                <a v-if="mode === 'create'" href="!#" @click.prevent="$emit('click:next-running-number')">เลขถัดไป</a>
              </div>
            </div>
          </v-col>

          <template v-if="mode === 'create'">
            <v-col cols="6" class="pr-0 mr-0">
              <v-text-field
                placeholder="เลขที่ขึ้นต้น (prefix) เครื่อง POS"
                outlined
                reverse
                :value="posNumber"
                @input="$emit('update:pos-number', $event)"
              />
            </v-col>
            <v-col cols="6" class="pl-0 ml-0">
              <v-text-field
                placeholder="เลขที่ Running Number"
                outlined
                clearable
                :rules="[$rules.required]"
                :value="runningNumber"
                @click:clear="$emit('update:running-number', '')"
                @input="$emit('update:running-number', $event)"
              />
            </v-col>
          </template>
          <template v-if="mode === 'update'">
            <v-col cols="12">
              <v-text-field outlined disabled :value="orderJson.number" @input="update({ number: $event})" />
            </v-col>
          </template>

          <v-col cols="12">
            <h4 class="mb-1">
              หมายเหตุ
            </h4>
            <v-textarea
              :readonly="isReadOnly"
              outlined
              rows="3"
              :value="orderJson.remark"
              @input="update({ remark: $event })"
            />
          </v-col>
          <!-- <v-col cols="12">
            <v-text-field
              outlined
              label="เบอร์โทรศัพท์"
              :rules="[$rules.phoneNumberFormat]"
              :value="orderJson.customerphone"
              @input="update({ customerphone: $event })"
            />
          </v-col> -->
        </v-row>
      </v-col>
    </v-row>

    <v-divider class="my-5" />

    <table style="width: 100%">
      <thead>
        <tr>
          <th class="">
            ชื่อสินค้า
          </th>
          <th class="px-1" style="width: 110px">
            ราคารวม
          </th>
          <th class="fit" />
        </tr>
      </thead>
      <tbody v-if="orderJson">
        <tr v-for="(item, index) in orderJson.list" :key="index">
          <td class="py-1 pr-1">
            <v-text-field
              hide-details="auto"
              filled
              dense
              :readonly="isReadOnly"
              :rules="[$rules.required]"
              :value="item.name"
              @input="updateList(index, { name: $event })"
            />
          </td>
          <td class="py-1 px-1">
            <v-text-field
              hide-details="auto"
              filled
              dense
              reverse
              :readonly="isReadOnly"
              type="number"
              :rules="[$rules.required, $rules.number_gt(0)]"
              :value="item.totalprice"
              @click="selectText($event)"
              @input="updateList(index, { totalprice: Number($event), pricepernumber:Number($event) })"
            />
          </td>
          <td v-if="!isReadOnly" class="pl-1">
            <v-btn icon tabindex="-1" @click="removeList(index)">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </td>
        </tr>
        <tr v-if="!isReadOnly">
          <td colspan="4">
            <v-btn outlined x-small color="primary" @click="appendList()">
              <v-icon left>
                mdi-plus
              </v-icon>
              เพิ่มสินค้า
            </v-btn>
          </td>
        </tr>
      </tbody>
    </table>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import { OrderJson, PickItem } from '~/models'

export default Vue.extend({
  components: {},
  props: {
    orderJson: {
      type: Object,
      required: true
    } as Vue.PropOptions<OrderJson>,

    posNumber: {
      type: String,
      default: ''
    },
    runningNumber: {
      type: String,
      default: ''
    },
    lastOrderNumber: {
      type: String,
      default: ''
    },
    customerPhone: {
      type: String,
      default: ''
    },
    isReadOnly: {
      type: Boolean,
      default: false
    },

    errors: {
      type: Object,
      default: () => ({})
    },

    disabled: {
      type: Array,
      default: () => []
    },

    mode: {
      type: String,
      default: 'create'
    },
    calculationMethod: {
      type: String,
      default: ''
    }
  },

  methods: {
    validate (): boolean {
      return (this.$refs.form as any).validate()
    },

    resetValidation () {
      (this.$refs.form as any).resetValidation()
    },

    update (data: any) {
      this.$emit('update:order-json', { ...this.orderJson, ...data })
    },

    updateList (index: number, data: any) {
      const orderItems = this.orderJson.list
      orderItems[index] = { ...orderItems[index], ...data }
      this.update({ list: orderItems })
    },

    appendList () {
      const emptyItem: PickItem = {
        id: null,
        image: '',
        sku: '',
        name: '',
        number: 1,
        skutype: null,
        discount: '',
        unittext: '',
        totalprice: 0,
        producttype: 0,
        serialnolist: [],
        discountamount: 0,
        pricepernumber: 0,
        seller_discount: 0,
        eso_vatpercent: 7
      }

      this.update({ list: [...this.orderJson.list, emptyItem] })
    },

    removeList (index: number) {
      const orderItems = [...this.orderJson.list]
      orderItems.splice(index, 1)
      this.update({ list: orderItems })
    },

    selectText (event: PointerEvent) {
      const target = event.target as HTMLInputElement
      target.select()
      target.focus()
    }
  }
})
</script>

<style scoped>
table {
  border-collapse: collapse;
}

th {
  text-align: left;
}

th.fit {
  width: 1%;
  white-space: nowrap;
}
td{
  vertical-align: baseline;
}
</style>
