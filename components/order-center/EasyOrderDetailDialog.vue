
<template>
  <v-dialog :value="value" persistent max-width="960">
    <v-card>
      <v-card-title>
        <span class="headline">รายละเอียด Order</span>
        <v-spacer />
        <v-btn icon @click="close()">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider />

      <v-card-text class="pt-3">
        <div v-if="orderJson">
          <easy-order-form
            ref="form"
            :order-json.sync="order_json_form"
            :read-only="true"
          />
        </div>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import EasyOrderForm from '~/components/order-center/EasyOrderForm.vue'
import { OrderJson } from '~/models'

export default Vue.extend({
  components: {
    EasyOrderForm
  },
  props: {
    value: {
      type: Boolean,
      required: true
    },
    orderJson: {
      type: Object,
      requred: false,
      default: () => null
    } as Vue.PropOptions<OrderJson | null>
  },

  data () {
    return {
      loading: false,
      error_message: '',
      order_json_form: null as OrderJson | null,
      form_errors: {} as any,
      is_auto_generate_tracking: false
    }
  },

  watch: {
    orderJson: {
      handler (order_json: OrderJson) {
        if (!order_json) {
          return
        }

        this.order_json_form = order_json
      },
      deep: true,
      immediate: true
    }

  },

  methods: {
    close () {
      this.$emit('input', false)
    }
  }
})
</script>
