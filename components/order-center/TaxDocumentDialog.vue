<template>
  <v-dialog :value="value" max-width="1040" persistent :fullscreen="$vuetify.breakpoint.smAndDown">
    <v-card style="width: 100%;">
      <v-card-title>
        <div>
          เอกสาร E-Tax
        </div>
        <v-spacer />
        <v-btn icon @click="close()">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider />

      <div class="d-flex">
        <div class="flex-shrink-1" style="width: 240px; border-right: 1px solid #eeeeee;">
          <v-list>
            <!-- <v-list-item-group :value="selectedDocId" @change="changeTab($event)"> -->
            <v-list-item-group>
              <v-list-item v-for="doc in documents" :key="doc.id" @click="changeTab(doc.id)">
                <v-list-item-content>
                  <v-list-item-title class="d-flex align-center">
                    <v-icon :color="getTaxDocShipInfo(doc).color">
                      mdi-circle-medium
                    </v-icon>
                    {{ doc.doc_info.DOC_NAME }}
                  </v-list-item-title>
                  <v-list-item-subtitle>
                    #{{ doc.doc_info.DOC_ID }}
                  </v-list-item-subtitle>
                </v-list-item-content>
              </v-list-item>
            </v-list-item-group>
          </v-list>
        </div>
        <!-- Tab Items -->
        <div class="flex-grow-1 pa-3">
          <v-tabs-items :value="selectedDocId">
            <v-tab-item v-for="doc in documents" :key="doc.id" :value="doc.id" style="max-height: 80vh; overflow-y: auto;">
              <!-- Header -->
              <div class="d-flex justify-space-between">
                <div>
                  <h3>
                    {{ doc.doc_info.DOC_NAME }}
                  </h3>
                  <p class="ma-0 text-caption">
                    #{{ doc.doc_info.DOC_ID }}
                  </p>
                </div>

                <receipt-tax-document-menu
                  ref="tax_doc_menu"
                  :tax-document="doc"
                  :documents="documents"
                  :pick-order="pickOrder"
                  :type="doc.doc_type"
                  :doc-name="doc.doc_info.DOC_NAME"
                  small
                  rounded
                  @cancel:tax-doc="$emit('cancel:tax-doc'); close();"
                />
              </div>

              <!-- Document Info -->
              <div class="mt-3">
                <v-row dense>
                  <!-- Left -->
                  <v-col cols="12" sm="6">
                    <div>
                      <label class="label">วันที่สร้าง</label>
                      <span>{{ $datetime(doc.doc_info.DOC_CREATE_DATE) }}</span>
                    </div>
                    <div>
                      <label class="label">วันที่เอกสาร</label>
                      <span>{{ $datetime(doc.doc_info.DOC_ISSUE_DATE) }}</span>
                    </div>
                    <div>
                      <label class="label">มูลค่าสุทธิ</label>
                      <span>
                        {{ $fmtCurrency(doc.doc_info.MONEY_GRAND_TOTALAMT) }} บาท
                      </span>
                    </div>
                  </v-col>

                  <!-- Right -->
                  <v-col cols="12" sm="6">
                    <div>
                      <label class="label">สถานะ</label>
                      <tax-document-info-chip small :info="getTaxDocShipInfo(doc)" />
                      <v-btn v-if="$auth.user.is_superuser" small icon outlined @click="openEtaxMonitor()">
                        <v-icon>mdi-bug</v-icon>
                      </v-btn>
                    </div>
                    <div>
                      <label class="label">ออกให้</label>
                      <span>{{ doc.doc_info.BUYER_NAME }}</span>
                    </div>
                    <div>
                      <label class="label" />
                      <span>📫 {{ doc.doc_info.BUYER_CONTACT_EMAIL }}</span><br>
                    </div>
                    <div>
                      <label class="label" />
                      <span>📞 {{ doc.doc_info.BUYER_CONTACT_PHONE }}</span>
                    </div>
                  </v-col>

                  <!-- Preview -->
                  <v-col cols="12" class="px-0">
                    <v-btn small icon plain @click="handlePreviewDoc(!preview_doc)">
                      <v-icon small>
                        {{ preview_doc ? 'mdi-eye' : 'mdi-eye-off' }}
                      </v-icon>
                    </v-btn>
                    <span class="text--disabled">กรุณาเข้าสู่ระบบด้วยบัญชี Google ที่มีสิทธิ์ดูเอกสาร</span>
                    <iframe
                      v-if="preview_doc"
                      style="width: 100%; aspect-ratio: 1/1.414; border: 1px solid #e0e0e0"
                      :src="doc.doc_url?.replace(/view(?=[^view]*$)/, 'preview')"
                      frameborder="0"
                    />
                  </v-col>
                </v-row>

                <v-data-table
                  class="mt-4"
                  dense
                  :headers="headers"
                  :items-per-page="5"
                  :items="doc.log"
                  style="border: 1px solid #e0e0e0;"
                >
                  <template #item.timestamp="{ item }">
                    {{ $datetime(item.timestamp) }}
                  </template>
                  <template #item.response="{ item }">
                    {{ item.extra.response.data.code }}
                    <span v-if="item.extra.response.data.code == 400">
                      {{ item.extra.response.data.message }}
                    </span>
                  </template>
                </v-data-table>
              </div>
            </v-tab-item>
          </v-tabs-items>
        </div>
      </div>

      <!-- Create Document -->
      <div v-if="documents.length == 0">
        <div class="text-center py-16">
          <v-icon x-large class="text-h2 text--disabled">
            mdi-folder-plus
          </v-icon>
          <p class="text-h6 text--disabled">
            <b>ไม่มีเอกสาร</b>
          </p>
          <p class="text-body-1 mx-auto text--secondary" style="max-width: 500px; vertical-align: baseline;">
            <v-icon small>
              mdi-information-slab-circle-outline
            </v-icon>
            หากคำสั่งซื้อมีรายการสินค้าที่ได้รับการยกเว้นภาษีมูลค่าเพิ่ม ระบบจะทำการแยกรายการดังกล่าวและสร้างเอกสารใบเสร็จรับเงินโดยอัตโนมัติ
          </p>

          <div>
            <v-btn :disabled="!$hasPerms('add_taxdocument')" rounded color="primary" class="mr-2" @click="triggerCreateDoc()">
              <v-icon>mdi-plus</v-icon>
              สร้าง
            </v-btn>
            <receipt-tax-document-menu
              ref="tax_doc_menu"
              :tax-document="null"
              :documents="documents"
              :pick-order="pickOrder"
              doc-name="ใบกำกับภาษี"
              type="tax_invoice"
              rounded
              @cancel:tax-doc="$emit('cancel:tax-doc'); close();"
            />
            <v-btn color="primary" class="ml-2" icon outlined @click="triggerMenu('copy_link')">
              <v-icon>mdi-link</v-icon>
            </v-btn>
            <v-btn color="primary" class="ml-2" icon outlined @click="triggerMenu('open_qr_code')">
              <v-icon>mdi-qrcode</v-icon>
            </v-btn>
          </div>
        </div>
      </div>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import _ from 'lodash'
import ReceiptTaxDocumentMenu from './ReceiptTaxDocumentMenu.vue'
import TaxDocumentInfoChip from './TaxDocumentInfoChip.vue'
import { LoggedInUser, TaxDocument, TaxDocumentStatus } from '~/models'

export default Vue.extend({
  components: {
    ReceiptTaxDocumentMenu,
    TaxDocumentInfoChip
  },
  props: {
    value: {
      type: Boolean,
      required: true
    },
    documents: {
      type: Array,
      default: () => []
    },
    selectedDocId: {
      type: [Number, String],
      default: null,
      required: false
    },
    pickOrder: {
      type: Object,
      required: false,
      default: () => {}
    }
  },
  data () {
    return {
      preview_doc: false,
      headers: [
        { text: 'วัน-เวลา', value: 'timestamp' },
        { text: 'เหตุการณ์', value: 'extra.request.data.DOC_STATUS' },
        { text: 'โดย', value: 'create_by' },
        { text: 'สถานะ', value: 'response' }
      ]
    }
  },
  computed: {
    getTabs (): any[] {
      const documents = _.groupBy(this.documents, 'doc_type')

      const tabs = [
        {
          label: 'ใบกำกับภาษี',
          title: 'ใบกำกับภาษี/ใบเสร็จรับเงิน',
          key: 'tax_invoice',
          document: null as any | null,
          disabled: true
        },
        {
          label: 'ใบเสร็จรับเงิน',
          title: 'ใบเสร็จรับเงิน',
          key: 'receipt',
          document: null as any | null,
          disabled: true
        },
        {
          label: 'ใบลดหนี้',
          title: 'ใบลดหนี้',
          key: 'credit_note',
          document: null as any | null,
          disabled: true
        },
        {
          label: 'ใบเสร็จรับเงิน/รับคืนสินค้า',
          title: 'ใบเสร็จรับเงิน/รับคืนสินค้า',
          key: 'return_receipt',
          document: null as any | null,
          disabled: true
        },
        {
          label: 'ใบส่งของ/ใบกำกับภาษี',
          title: 'ใบส่งของ/ใบกำกับภาษี',
          key: 'delivery_invoice',
          document: null as any | null,
          disabled: true
        }
      ]

      const doesNotHaveTaxInvoiceOrReceipt = !('tax_invoice' in documents) && !('receipt' in documents)

      for (const tab of tabs) {
        if (documents[tab.key]) {
          tab.document = documents[tab.key][0]
          tab.disabled = false
        }

        if (tab.key === 'tax_invoice' && doesNotHaveTaxInvoiceOrReceipt) {
          tab.disabled = false
        }
      }

      return tabs
    }
  },
  methods: {
    handlePreviewDoc (value: boolean) {
      this.preview_doc = value
    },
    // log (data: any) {
    //   console.log(data)
    //   // const documents = _.groupBy(this.documents, 'doc_type')
    //   // const doesNotHaveTaxInvoiceOrReceipt = !('tax_invoice' in documents && 'receipt' in documents)
    //   // console.log('documents', documents, doesNotHaveTaxInvoiceOrReceipt)

    //   // console.log('tax_invoice' in documents, 'receipt' in documents)
    // },
    close () {
      this.$emit('input', false)
    },
    changeTab (tab: string) {
      this.$emit('update:selected-doc-id', tab)
    },
    triggerCreateDoc () {
      const tax_doc_menus = this.$refs.tax_doc_menu as any
      if (!tax_doc_menus) {
        throw new Error('ReceiptTaxDocumentMenu not found')
      }
      const menu = tax_doc_menus[0] || tax_doc_menus
      menu.handleMenuClick({ value: 'create_doc' })
    },
    triggerMenu (menu: string) {
      const tax_doc_menus = this.$refs.tax_doc_menu as any
      if (!tax_doc_menus) {
        throw new Error('ReceiptTaxDocumentMenu not found')
      }
      const _menu = tax_doc_menus[0] || tax_doc_menus
      _menu.handleMenuClick({ value: menu })
    },
    openEtaxMonitor () {
      const user = this.$auth.user as never as LoggedInUser
      if (!user.is_superuser) {
        return
      }

      const company = user.company
      this.$router.push({
        path: '/etax/debugger',
        query: {
          cid: company.uuid,
          oid: this.pickOrder.uuid
        }
      })
    },
    getTaxDocShipInfo (tax_doc: TaxDocument | null) {
      if (!tax_doc) {
        return {
          color: '',
          status: 'new',
          text: 'ไม่มีเอกสาร'
        }
      }

      const status = TaxDocumentStatus.find(x => x.value === (tax_doc as TaxDocument).status)
      if (!status) {
        throw new Error('Invalid TaxDocumentStatus value, ' + tax_doc.status)
      }

      let text = status.text
      if (tax_doc.edit_count > 0) {
        text += ` (${tax_doc.edit_count})`
      }

      return {
        color: status.color,
        status: status.value,
        text
      }
    }
  }
})
</script>

<style scoped>
.v-dialog .v-card {
  overflow: visible;
}

label.label {
  width: 80px;
  display: inline-block;
  font-weight: bold;
}
</style>
