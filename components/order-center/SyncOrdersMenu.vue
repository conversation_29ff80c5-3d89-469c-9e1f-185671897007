<template>
  <span>
    <v-menu offset-y>
      <template #activator="{ on }">
        <v-btn small outlined color="primary" class="mx-1" v-on="on">
          <v-icon small>mdi-storefront</v-icon>
          <template v-if="$vuetify.breakpoint.mdAndUp">
            <span class="pl-3">
              {{ $t("order-center.update") }}
            </span>
            <v-icon right small> mdi-menu-down </v-icon>
          </template>
        </v-btn>
      </template>
      <v-list>
        <v-list-item
          v-for="(item, index) in marketplaces"
          :key="index"
          :two-line="!getDefault(item, 'is_active', true)"
          @click="onItemClick(item)"
        >
          <v-list-item-content>
            <v-list-item-title>
              <v-chip small>
                {{ item.marketplace }}
              </v-chip>
              <span>
                {{ item.seller_shop_name }}
              </span>
            </v-list-item-title>
            <v-list-item-subtitle
              v-if="!getDefault(item, 'is_active', true)"
              class="red--text"
            >
              เกิดข้อผิดพลาดในการเชื่อมต่อข้อมูล
              <span class="primary--text">เชื่อมต่อใหม่</span>
            </v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>

        <v-list-item v-if="dbbc_version == 1" @click="onSyncAllClick()">
          <v-list-item-title> ทั้งหมด </v-list-item-title>
        </v-list-item>
      </v-list>
    </v-menu>

    <sync-order-dialog
      v-model="dialog.show"
      :marketplace="dialog.marketplace"
      @submit="onSyncOrderDialogSubmit($event)"
    />

    <sync-task-dialog
      v-model="sync_task_dialog.show"
      :task-id="sync_task_dialog.task_id"
    />
  </span>
</template>

<script lang="ts">
import { uniq, sum, get } from 'lodash'
import moment from 'moment'
import Vue from 'vue'
import SyncTaskDialog from './SyncTaskDialog.vue'
import SyncOrderDialog from './SyncOrderDialog.vue'
import { Marketplace } from '~/models'
import { SettingState } from '~/store/settings'

export default Vue.extend({
  components: { SyncOrderDialog, SyncTaskDialog },
  props: {
    startDate: {
      type: String,
      required: false,
      default: ''
    },
    endDate: {
      type: String,
      required: false,
      default: ''
    }
  },

  data () {
    return {
      marketplaces: [] as Marketplace[],
      dialog: {
        show: false,
        marketplace: null as Marketplace | null
      },
      sync_task_dialog: {
        show: false,
        task_id: ''
      }
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    },
    dbbc_version (): number {
      return this.settings.company.DOBYBOT_CONNECT_VERSION
    }
  },

  created () {
    this.getMarketplaces()
  },

  methods: {
    async onItemClick (item: Marketplace) {
      if (!item.is_active) {
        this.$router.push('/settings/marketplace')
        return
      }

      if (
        [
          'zort_v2',
          'shopee',
          'lazada',
          'tiktok_shop',
          'tiktok_shop_v2',
          'nocnoc',
          'line_my_shop',
          'nex_gen_commerce',
          'woo_commerce'
        ].includes(item.marketplace)
      ) {
        this.dialog.marketplace = item
        this.dialog.show = true
      } else {
        // -------------------------------
        // dobysync not supported syncOrders
        // -------------------------------
        this.$loader(true)
        try {
          const result = await this.syncOrders(item)
          this.$alert({
            text: this.$t('order-center.count_new_orders', [result])
          })
        } catch (error) {
          console.error(error)
          this.$snackbar('error', this.$t('error.unknown'))
        }
        this.$loader(false)
        this.$emit('sync-complete')
      }
    },

    async onSyncAllClick () {
      const promises = [] as Promise<any>[]
      const platform = uniq(this.marketplaces.map(x => x.marketplace))
      this.$loader(true)
      if (platform.includes('zort')) {
        promises.push(this.syncZortOrders())
      }

      if (
        platform.includes('shopee') ||
        platform.includes('lazada') ||
        platform.includes('tiktok_shop') ||
        platform.includes('shipnity')
      ) {
        promises.push(this.syncDobybotConnectOrders())
      }

      try {
        const results = await Promise.all(promises)
        const count_new_orders = sum(
          results.map(x => x.total_orders_update_amount)
        )
        this.$alert({
          text: this.$t('order-center.count_new_orders', [count_new_orders])
        })
      } catch (error) {
        console.error(error)
        this.$snackbar('error', this.$t('error.unknown'))
      }

      this.$loader(false)
      this.$emit('sync-complete')
    },

    async getMarketplaces () {
      const shops: Marketplace[] = (
        await this.$axios.get('/api/companies/marketplaces/')
      ).data
      this.marketplaces = shops.filter(
        x => !x.marketplace.endsWith('_chat') && x.settings.sync_order
      )
    },

    async onSyncOrderDialogSubmit (data: any) {
      if (data.sync_method === 'latest') {
        this.$loader(true)
        try {
          const result = await this.syncDobybotConnectShopOrders(
            data.marketplace
          )

          // setup sync task dialog
          if (this.dbbc_version === 1) {
            this.$alert({
              text: this.$t('order-center.count_new_orders', [
                result.total_orders_update_amount
              ])
            })
          }

          if (this.dbbc_version === 2) {
            this.sync_task_dialog.show = true
            this.sync_task_dialog.task_id = result.task_id
          }
        } catch (error) {
          console.error(error)
          this.$snackbar('error', this.$t('error.unknown'))
        }
        this.dialog.show = false
        this.$loader(false)
      }

      if (data.sync_method === 'datetime') {
        this.syncOrdersBy(data.marketplace, data.start, data.end)
      }
    },

    async syncOrders (marketplace: Marketplace): Promise<number> {
      // -------------------------------
      // dobysync not supported this function
      // -------------------------------
      let count_new_orders = 0

      if (marketplace.marketplace === 'zort_v1') {
        const result = await this.syncZortOrders()
        count_new_orders = result
      }

      if (
        [
          'lazada',
          'shopee',
          'tiktok_shop',
          'tiktok_shop_v2',
          'shipnity',
          'zort_v2'
        ].includes(marketplace.marketplace)
      ) {
        const result = await this.syncDobybotConnectShopOrders(marketplace)
        count_new_orders = result.total_orders_update_amount
      }

      return count_new_orders
    },

    async syncOrdersBy (marketplace: Marketplace, start?: string, end?: string) {
      if (start && end && moment(end).diff(start, 'days') > 14) {
        alert('ไม่สามารถ Sync ข้อมูลเกิน 14 วันได้')
        return
      }

      this.$loader(true)
      try {
        const result = await this.syncDobybotConnectShopOrders(
          marketplace,
          start,
          end
        )
        if (this.dbbc_version === 1) {
          this.$alert({
            text: this.$t('order-center.count_new_orders', [
              result.total_orders_update_amount
            ])
          })
        }
        if (this.dbbc_version === 2) {
          this.sync_task_dialog.show = true
          this.sync_task_dialog.task_id = result.task_id
        }
      } catch (error) {
        console.error(error)
        this.$snackbar('error', this.$t('error.unknown'))
      }
      this.dialog.show = false
      this.dialog.marketplace = null
      this.$loader(false)
      this.$emit('sync-complete')
    },

    async syncZortOrders (): Promise<number> {
      const payload = {
        start_date:
          this.startDate || moment().subtract(3, 'days').format('YYYY-MM-DD'),
        end_date: this.endDate || moment().format('YYYY-MM-DD')
      }
      const response = await this.$axios.post(
        '/api/picking/zort/sync-orders/',
        payload
      )
      console.log('zort', response)
      return response.data
    },

    async syncDobybotConnectShopOrders (
      marketplace: Marketplace,
      start?: string,
      end?: string
    ): Promise<any> {
      const payload = {
        marketplace: marketplace.marketplace,
        shop_id: marketplace.seller_id,
        start: start || undefined,
        end: end || undefined
      }

      const response = await this.$axios.post(
        '/api/companies/dobybot-connect/sync-shop-orders/',
        payload
      )
      return response.data
    },

    async syncDobybotConnectOrders (): Promise<number> {
      const response = await this.$axios.post(
        '/api/companies/dobybot-connect/sync-orders/'
      )
      return response.data
    },

    getDefault (data: any, key: any, defaultValue: any) {
      return get(data, key, defaultValue)
    }
  }
})
</script>
