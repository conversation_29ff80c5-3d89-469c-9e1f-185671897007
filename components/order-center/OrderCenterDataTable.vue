<template>
  <div class="print-container">
    <v-data-table
      id="data-table"
      v-model="selected"
      data-test="data-table"
      item-key="id"
      :show-select="$vuetify.breakpoint.smAndUp"
      :footer-props="{ 'items-per-page-options': [10, 25, 50, 100] }"
      :headers="headers"
      :items="items"
      :server-items-length="tableData.meta.total_results"
      :options="tableData.options"
      :item-class="getItemClass"
      :loading="loading"
      @update:options="update({ options: $event })"
    >
      <template #item.order_date="{ item }">
        <v-tooltip top>
          <template #activator="{ on }">
            <div v-on="on">
              {{ $date(item.order_date) }}<br>

              <template
                v-if="_get(item, 'order_json.createdatetimeString', '')"
              >
                {{ $time(_get(item, "order_json.createdatetimeString", "")) }}
              </template>
            </div>
          </template>
          <div style="max-width: 400px">
            <table>
              <tr>
                <td class="text-right pr-2">
                  {{ $t('create_by') }}
                </td>
                <td>{{ getUserFullName(item.create_by) || '-' }}</td>
              </tr>
              <tr>
                <td class="text-right pr-2">
                  {{ $t("created_at") }}
                </td>
                <td>
                  {{ _get(item, "order_json.createdatetimeString", "-") }}
                </td>
              </tr>
              <tr>
                <td class="text-right pr-2">
                  {{ $t("updated_at") }}
                </td>
                <td>
                  {{ _get(item, "order_json.updatedatetimeString", "-") }}
                </td>
              </tr>
              <tr>
                <td class="text-right pr-2">
                  {{ $t("paid_at") }}
                </td>
                <td>
                  {{
                    _get(
                      item,
                      "order_json.payments[0].paymentdatetimeString",
                      "-"
                    )
                  }}
                </td>
              </tr>
            </table>
          </div>
        </v-tooltip>
      </template>

      <template #item.order_number="{ item }">
        <v-menu offset-y>
          <template #activator="{ on }">
            <a
              :title="$t('order-center.left-right-click')"
              @click.prevent="openReceiptPage(item)"
              @click.right.prevent="on.click"
            >
              {{ item.order_number }}
            </a>
          </template>
          <v-list dense>
            <v-list-item @click="openPickOrderPage(item.uuid)">
              <v-list-item-title>ใบจัดสินค้า</v-list-item-title>
            </v-list-item>
            <v-list-item @click="openAirwayBillPage(item.uuid)">
              <v-list-item-title>ใบ Airway Bill</v-list-item-title>
            </v-list-item>
            <v-list-item
              v-show="getTrackingNoInfo(item)"
              @click="openShippingAirwayBillPage(item)"
            >
              <v-list-item-title>ใบ Airway Bill ของขนส่ง</v-list-item-title>
            </v-list-item>
            <v-list-item @click="openReceipt4x6(item.uuid)">
              <v-list-item-title>ใบเสร็จ 4" x 6"</v-list-item-title>
            </v-list-item>
            <v-list-item
              :disabled="
                !(
                  $hasPerms('add_pickorder') &&
                  ['import', 'easy-order', 'fixcase'].includes(item.order_oms) &&
                  item.order_json.status !== 'Voided'
                )
              "
              @click="editOrder(item)"
            >
              <v-list-item-title>แก้ไขออเดอร์</v-list-item-title>
            </v-list-item>
            <!-- <v-list-item
              @click="showOrderDetail(item)"
            >
              <v-list-item-title>รายละเอียดออเดอร์</v-list-item-title>
            </v-list-item> -->
            <v-list-item
              v-if="$auth.user.is_superuser"
              @click="viewOrderJson(item)"
            >
              <v-list-item-title>Order Json</v-list-item-title>
            </v-list-item>
            <v-list-item
              v-if="$auth.user.is_superuser"
              @click="viewETaxMonitor(item)"
            >
              <v-list-item-title>E-Tax Monitor</v-list-item-title>
            </v-list-item>
          </v-list>
        </v-menu>
        <v-tooltip top>
          <template #activator="{ on }">
            <v-btn
              v-if="item.fixcase_set.length > 0"
              icon
              color="warning"
              :to="{ path: `/fixcase?q=${item.order_customer}` }"
              v-on="on"
            >
              <v-icon>mdi-alert</v-icon>
            </v-btn>
          </template>
          <span>{{
            $t("order-center.n-fixcases-are-opened", [item.fixcase_set.length])
          }}</span>
        </v-tooltip>
        <v-tooltip top>
          <template #activator="{ on }">
            <v-btn v-if="item.has_return_videos" icon color="warning" v-on="on">
              <v-icon>mdi-cash-refund</v-icon>
            </v-btn>
          </template>
          <span>มีรายการของคืน (RT)</span>
        </v-tooltip>
      </template>

      <template #item.order_trackingno="{ item }">
        <div v-if="getTrackingNoInfo(item)">
          {{ item.order_trackingno }}
          <div
            v-if="
              $hasPerms('add_pickordertrackingno') &&
                $hasCompanyFeatureFlag('shipping')
            "
          >
            <v-chip
              small
              outlined
              :color="getChipColorByStatus(getTrackingNoInfo(item).status)"
              @click="
                $emit('clicked:open-shipping-form', {
                  pick_order: item,
                  po_tracking: getTrackingNoInfo(item),
                })
              "
            >
              {{ getTrackingNoInfo(item).status }}
            </v-chip>
          </div>

          <v-tooltip v-if="getTrackingNoInfo(item).is_cod_refunded" top>
            <template #activator="{ on, attrs }">
              <v-chip
                color="error"
                small
                class="mt-1"
                v-bind="attrs"
                outlined
                v-on="on"
              >
                <v-icon left small>
                  mdi-alert
                </v-icon>
                ผู้รับปฏิเสธรับของ COD
              </v-chip>
            </template>
            <span>ติดตามการคืนของด้วยหมายเลขพัสดุ:
              {{ getTrackingNoInfo(item)?.cod_refunded_pno }}</span>
          </v-tooltip>
        </div>
        <div v-else>
          <div v-if="item.order_trackingno">
            {{ item.order_trackingno }}
          </div>
          <div v-else>
            <v-btn
              v-if="
                $hasPerms('add_pickordertrackingno') &&
                  $hasCompanyFeatureFlag('shipping') &&
                  item.order_json.status !== 'Voided'
              "
              x-small
              outlined
              @click="$emit('clicked:open-shipping-form', { pick_order: item })"
            >
              ออกเลข Tracking No.
            </v-btn>
          </div>
        </div>
      </template>

      <template #item.status="{ item }">
        <v-tooltip top :disabled="!item.order_json.platform_status">
          <template #activator="{ on }">
            <div v-on="on">
              <v-chip
                v-if="item.order_json.status === 'Success'"
                small
                color="success"
                v-on="on"
              >
                {{ item.order_json.status }}
              </v-chip>
              <v-chip
                v-else-if="item.order_json.status === 'Waiting'"
                small
                color="warning"
                v-on="on"
              >
                {{ item.order_json.status }}
              </v-chip>
              <v-chip
                v-else-if="item.order_json.status === 'Shipping'"
                small
                color="info"
                v-on="on"
              >
                {{ item.order_json.status }}
              </v-chip>
              <v-chip
                v-else-if="item.order_json.status === 'Voided'"
                small
                color="error"
                v-on="on"
              >
                {{ item.order_json.status }}
              </v-chip>
              <v-chip v-else small v-on="on">
                {{ item.order_json.status }}
              </v-chip>
            </div>
          </template>
          <div>
            <table>
              <tr>
                <td class="text-right pr-2">
                  {{ $t("order-center.order-status") }}
                </td>
                <td>
                  <span v-if="!item.order_json.platform_status">-</span>
                  <span
                    v-else-if="item.order_json.integrationName == 'tiktok_shop'"
                  >
                    {{ tiktok_order_status[item.order_json.platform_status] }}
                  </span>
                  <span v-else>
                    {{ item.order_json.platform_status }}
                  </span>
                </td>
              </tr>
              <tr>
                <td class="text-right pr-2">
                  {{ $t("order-center.payment-status") }}
                </td>
                <td>
                  {{
                    item.order_json.paymentstatus == "Paid"
                      ? `${$t("order-center.paid")}`
                      : $t("order-center.unpaid")
                  }}
                  ({{ item.order_json.paymentmethod || "-" }})
                </td>
              </tr>
            </table>
          </div>
        </v-tooltip>
        <v-tooltip v-if="item.remark_status" top>
          <template #activator="{ on }">
            <v-icon color="warning" v-on="on">
              mdi-alert
            </v-icon>
          </template>
          <div style="max-width: 400px">
            {{ item.remark_status }}
          </div>
        </v-tooltip>
      </template>

      <template #item.order_customer="{ item }">
        <span
          :title="$t('order-center.copy-click')"
          style="cursor: pointer"
          @click="copyToClipboard(item.order_number)"
        >{{ item.order_customer }}</span>
      </template>

      <template #item.is_print="{ item }">
        <div class="mx-2">
          <v-tooltip top>
            <template #activator="{ on }">
              <v-btn text small class="px-0" v-on="on">
                <v-icon
                  v-if="item.print_count > 0"
                  small
                  color="success"
                  v-on="on"
                >
                  mdi-check-bold
                </v-icon>
                <v-icon v-else color="error" small v-on="on">
                  mdi-close-thick
                </v-icon>

                <span class="ml-1">PICK</span>
              </v-btn>
            </template>
            <span>{{
              $t("order-center.print-detail-pick-slip", [
                $datetime(item.print_timestamp),
                item.print_count,
              ])
            }}</span>
          </v-tooltip>
          <br>
          <v-tooltip top>
            <template #activator="{ on }">
              <v-btn text small class="px-0" v-on="on">
                <v-icon
                  v-if="item.airway_bill_print_count > 0"
                  small
                  color="success"
                  v-on="on"
                >
                  mdi-check-bold
                </v-icon>
                <v-icon v-else color="error" small v-on="on">
                  mdi-close-thick
                </v-icon>

                <span class="ml-1">AWB</span>
              </v-btn>
            </template>
            <span>{{
              $t("order-center.print-detail-airway-bill", [
                $datetime(item.airway_bill_print_timestamp),
                item.airway_bill_print_count,
              ])
            }}</span>
          </v-tooltip>

          <!-- <v-tooltip v-if="item.print_count == 0" top>
          <template #activator="{ on }">
            <v-btn
              color="success"
              icon
              small
              outlined
              v-on="on"
              @click="openPickOrderPage(item.order_number)"
            >
              <v-icon small>
                mdi-printer
              </v-icon>
            </v-btn>
          </template>
          <span>{{ $t('order-center.print') }}</span>
        </v-tooltip> -->
        </div>
      </template>

      <template #item.has_videos_and_images="{ item }">
        <div class="my-1">
          <!-- Has Video -->
          <div>
            <template v-if="item.has_videos">
              <v-icon small color="success">
                mdi-video
              </v-icon>
              <span
                class="text-caption text--secondary"
                style="white-space: pre"
              >{{ getVideoRecordLogInfo(item) }}</span>
            </template>
            <div v-else style="white-space: nowrap">
              <v-btn
                small
                icon
                target="_blank"
                :href="`/$/record?start_record=${item.order_number}`"
              >
                <v-icon small color="primary">
                  mdi-video
                </v-icon>
              </v-btn>
              <span class="text-caption text--secondary"> ยังไม่บันทึก </span>
            </div>

            <v-divider />

            <!-- Has Image -->
            <div v-if="item.has_images" style="white-space: nowrap">
              <v-icon small color="success">
                mdi-camera
              </v-icon>
              <span
                class="text-caption text--secondary"
                style="white-space: pre"
              >{{ getImageCaptureLogInfo(item) }}</span>
            </div>
            <template v-else>
              <v-btn small icon>
                <v-icon small color="primary">
                  mdi-camera
                </v-icon>
              </v-btn>
              <span class="text-caption text--secondary"> ยังไม่บันทึก </span>
            </template>
          </div>
        </div>
      </template>

      <!-- Is Ready to ship? -->
      <template #item.ready_to_ship="{ item }">
        <!-- <v-tooltip top>
        <template #activator="{ on }">
          <v-icon v-if="item.ready_to_ship" small color="success" v-on="on">
            mdi-check-bold
          </v-icon>
          <v-icon v-else small color="error">
            mdi-close-thick
          </v-icon>
        </template>
        <span>{{ $datetime(item.ready_to_ship_timestamp) }}</span>
      </v-tooltip> -->
        <template v-if="item.ready_to_ship">
          <v-icon small color="success">
            mdi-check-bold
          </v-icon>
          <span
            v-if="item.total_packages > 0"
            style="cursor: pointer"
            :title="$t('order-center.click-view-ready-to-ship-log')"
            @click="$emit('click-view-ready-to-ship-log', item)"
          >
            ({{ item.ready_to_ship_count }}/{{ item.total_packages }})
          </span>
          <span class="text-caption text--secondary" style="white-space: pre">{{
            getReadyToShipInfo(item)
          }}</span>
        </template>

        <template v-else>
          <v-icon small color="error">
            mdi-close-thick
          </v-icon>
          <span
            v-if="item.total_packages > 0"
            style="cursor: pointer"
            :title="$t('order-center.click-view-ready-to-ship-log')"
            @click="$emit('click-view-ready-to-ship-log', item)"
          >
            ({{ item.ready_to_ship_count }}/{{ item.total_packages }})
          </span>
        </template>
      </template>

      <template #item.etax_invoice="{ item }">
        <summary-tax-document-chip
          :table-data="tableData"
          :pick-order="item"
          @open-doc-dialog="openDocDialog($event, item)"
        />
      </template>

      <template #item.remark="{ item }">
        <v-textarea
          v-model="item.remark"
          class="compact-textarea"
          style="min-width: 200px"
          rows="1"
          auto-grow
          hide-details
          dense
          outlined
          :append-icon="item.id === focus ? 'mdi-check' : null"
          @focus="focus = item.id"
          @change="
            updateRemark(item);
            focus = null;
          "
          @blur="focus = null"
          @click:append="
            updateRemark(item);
            focus = null;
          "
        />
      </template>

      <template #item.remark="{ item }">
        <v-textarea
          v-model="item.remark"
          class="compact-textarea"
          style="min-width: 200px"
          rows="1"
          auto-grow
          hide-details
          dense
          outlined
          :append-icon="item.id === focus ? 'mdi-check' : null"
          @focus="focus = item.id"
          @change="
            updateRemark(item);
            focus = null;
          "
          @blur="focus = null"
          @click:append="
            updateRemark(item);
            focus = null;
          "
        />
      </template>
    </v-data-table>

    <v-slide-x-transition>
      <v-card
        v-if="selected.length"
        class="selected-items-card d-flex align-center"
        color="grey darken-3"
        dark
      >
        <div class="selected-count px-4">
          {{ selected.length }} คำสั่งซื้อที่เลือก
        </div>

        <v-divider vertical class="mx-2" />

        <div class="">
          <v-btn outlined class="mx-1" @click="printPickSlip()">
            <v-icon left size="18">
              mdi-printer
            </v-icon>
            พิมพ์ใบจัดของ
          </v-btn>

          <v-btn outlined class="mx-1" @click="printAirwayBill()">
            <v-icon left size="18">
              mdi-printer
            </v-icon>
            พิมพ์ Airway Bill
          </v-btn>
        </div>

        <v-spacer />

        <v-btn icon small class="mr-2" @click="selected = []">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card>
    </v-slide-x-transition>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import _ from 'lodash'
import SummaryTaxDocumentChip from './SummaryTaxDocumentChip.vue'
import {
  Company,
  PickOrder,
  PickOrderTrackingNo,
  SimpleUser,
  TaxDocument,
  VDataTableHeader
} from '~/models'
import { copyToClipboard } from '~/api/utils'
import { SettingState } from '~/store/settings'

export default Vue.extend({
  components: {
    SummaryTaxDocumentChip
  },
  props: {
    tableData: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    isGetPickOrder: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      selected: [],
      focus: null,
      tiktok_order_status: {
        100: 'Unpaid',
        105: 'On Hold',
        111: 'Await Shipment',
        112: 'Await Collection',
        114: 'Partially Shipped',
        121: 'In Transit',
        122: 'Delivery',
        130: 'Complete',
        140: 'Cancelled'
      }
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    },

    headers (): VDataTableHeader[] {
      const headers: VDataTableHeader[] = [
        { text: 'Shop', value: 'order_saleschannel', sortable: false },
        { text: 'Shipping', value: 'order_shippingchannel', sortable: false },
        { text: 'OrderDate', value: 'order_date' },
        { text: 'Order No.', value: 'order_number' },
        { text: 'Tracking No.', value: 'order_trackingno', align: 'center' },
        {
          text: this.$t('order-center.status'),
          value: 'status',
          class: ['no-print']
        },
        {
          text: this.$t('order-center.customer-name'),
          value: 'order_customer',
          class: ['no-print'],
          sortable: false
        },
        {
          text: this.$t('order-center.printed'),
          value: 'is_print',
          class: ['no-print'],
          sortable: false
        },
        {
          text: this.$t('order-center.record&image'),
          value: 'has_videos_and_images',
          class: ['no-print'],
          sortable: false,
          width: 150
        },
        {
          text: this.$t('order-center.ready'),
          value: 'ready_to_ship',
          class: ['no-print'],
          sortable: false
        },
        {
          text: 'E-Tax ',
          value: 'etax_invoice',
          class: ['no-print'],
          sortable: false,
          width: 200
        },

        {
          text: this.$t('order-center.note'),
          value: 'remark',
          sortable: false
        }
      ]

      if ((this as any).$vuetify.breakpoint.xsOnly) {
        const mobile_headers = [] as VDataTableHeader[]
        const header_keys = ['order_number', 'order_date', 'etax_invoice']
        for (const hk of header_keys) {
          const header = headers.find(h => h.value === hk)
          if (header) {
            mobile_headers.push(header)
          }
        }
        return mobile_headers
      }

      // if (!this.settings.company.PICKORDER_AUTO_PRINT_ENABLE) {
      // headers.splice(7, 1)
      // }

      return headers
    },

    items () {
      const sortBy = this.tableData.options.sortBy[0]
      const sortDir = this.tableData.options.sortDesc[0] ? 'desc' : 'asc'

      if (sortBy === 'order_date') {
        return _.orderBy(this.tableData.pick_orders, ['order_date', 'order_json.createdatetimeString'], [sortDir, sortDir])
      }
      return this.tableData.pick_orders
    }
  },

  watch: {
    isGetPickOrder (new_event) {
      if (new_event === true) {
        this.$emit('update:is-get-pick-order', false)
        this.selected = []
      }
    }
  },

  methods: {
    update (data: any) {
      this.$emit('update:table-data', { ...this.tableData, ...data })
    },

    getItemClass (pick_order: any) {
      const base_class = `pickorder-${pick_order.id}`
      if (pick_order.has_fixcases || pick_order.has_return_videos) {
        return `${base_class} red lighten-5`
      }
      if (pick_order.source_fixcase) {
        return `${base_class} purple lighten-5`
      }
      return base_class
    },

    openReceiptPage (order: PickOrder) {
      if (window.location.host === 'localhost:3000') {
        let url: string
        if (order.signed_receipt_url.includes('uat')) {
          url = order.signed_receipt_url.replace('https://uat.dobybot.com', 'http://localhost:3000')
        } else if (order.signed_receipt_url.includes('cloud')) {
          url = order.signed_receipt_url.replace('https://cloud.dobybot.com', 'http://localhost:3000')
        } else {
          url = order.signed_receipt_url
        }
        window.open(url)
        return
      }

      window.open(order.signed_receipt_url)
    },

    openPickOrderPage (order_uuid: string) {
      if (this.$auth.user) {
        const baseURL = this.$axios.defaults.baseURL
        const company: any = this.$auth.user.company
        const cid = company.uuid
        window.open(
          `${baseURL}/printing/pick-order/${order_uuid}/?cid=${cid}`,
          '_blank'
        )
      }
    },

    openAirwayBillPage (order_uuid: string) {
      if (this.$auth.user) {
        const baseURL = this.$axios.defaults.baseURL
        const company: any = this.$auth.user.company
        const cid = company.uuid
        window.open(
          `${baseURL}/printing/airway-bill/${order_uuid}/?cid=${cid}`,
          '_blank'
        )
      }
    },

    openShippingAirwayBillPage (pick_order: PickOrder) {
      const last_obj_id = _.last(pick_order.pickordertrackingno_set)
      if (!last_obj_id) {
        return ''
      }

      const pick_order_tracking_nos =
        this.tableData.pick_order_tracking_nos.get(last_obj_id)
      if (!pick_order_tracking_nos) {
        return ''
      }

      if (this.$auth.user) {
        const baseURL = this.$axios.defaults.baseURL
        const order_uuid = pick_order_tracking_nos.uuid
        let path = ''

        switch (pick_order.order_shippingchannel) {
          case 'DBB_FLASH':
            path = 'flash'
            break
          case 'DBB_THAIPOST_EMS':
            path = 'thaipost'
            break
          case 'DBB_THAIPOST_REG':
            path = 'thaipost'
            break
          case 'DBB_KERRY':
            path = 'kerry'
            break
        }
        window.open(`${baseURL}/printing/airway-bill/${path}/${order_uuid}/`)
      }
    },

    editOrder (pick_order: PickOrder) {
      this.$emit('click:edit-order', pick_order)
    },

    showOrderDetail (pick_order: PickOrder) {
      this.$emit('click:show-order-detail', pick_order)
    },

    openFlashAirwayBillPage (pick_order: PickOrder) {
      const last_obj_id = _.last(pick_order.pickordertrackingno_set)
      if (!last_obj_id) {
        return ''
      }

      const pick_order_tracking_nos =
        this.tableData.pick_order_tracking_nos.get(last_obj_id)
      if (!pick_order_tracking_nos) {
        return ''
      }

      if (this.$auth.user) {
        const baseURL = this.$axios.defaults.baseURL
        const order_uuid = pick_order_tracking_nos.uuid
        window.open(`${baseURL}/printing/airway-bill/flash/${order_uuid}/`)
      }
    },

    openThaipostAirwayBillPage (pick_order: PickOrder) {
      const last_obj_id = _.last(pick_order.pickordertrackingno_set)
      if (!last_obj_id) {
        return ''
      }

      const pick_order_tracking_nos =
        this.tableData.pick_order_tracking_nos.get(last_obj_id)
      if (!pick_order_tracking_nos) {
        return ''
      }

      if (this.$auth.user) {
        const baseURL = this.$axios.defaults.baseURL
        const order_uuid = pick_order_tracking_nos.uuid
        window.open(`${baseURL}/printing/airway-bill/thaipost/${order_uuid}/`)
      }
    },

    openReceipt4x6 (order_uuid: string) {
      if (this.$auth.user) {
        const baseURL = this.$axios.defaults.baseURL
        window.open(
          `${baseURL}/printing/receipt-invoice/${order_uuid}/`,
          '_blank'
        )
      }
    },

    copyToClipboard (value: string) {
      copyToClipboard(value)
    },

    async updateRemark (pick_order: PickOrder) {
      const action = `/api/picking/resource/pick-orders/${pick_order.id}/`
      const payload = { remark: pick_order.remark }

      try {
        await this.$axios.patch(action, payload)
        this.$snackbar('success', this.$t('order-center.save-successful'))
      } catch (error) {
        console.error(error)
        this.$snackbar('error', `${error}`)
      }
    },

    getVideoRecordLogInfo (pick_order: PickOrder): string {
      const last_record_log = _.last(pick_order.videorecordlog_set)
      if (!last_record_log) {
        return ''
      }

      const log = this.tableData.video_record_logs.get(last_record_log)
      if (!log) {
        return ''
      }

      return `${this.getUserFullName(log.upload_by)}\n${this.$datetime_short(
        log.upload_date
      )}`
    },

    getImageCaptureLogInfo (pick_order: PickOrder): string {
      const last_image_log_id = _.last(pick_order.imagecapturelog_set)
      if (!last_image_log_id) {
        return ''
      }

      const log = this.tableData.image_capture_logs.get(last_image_log_id)
      if (!log) {
        return ''
      }

      return `${this.getUserFullName(log.create_by)}\n${this.$datetime_short(
        log.create_date
      )}`
    },

    getReadyToShipInfo (pick_order: PickOrder): string {
      if (!pick_order.ready_to_ship_by) {
        return ''
      }
      return `${this.getUserFullName(
        pick_order.ready_to_ship_by
      )},\n${this.$datetime_short(pick_order.ready_to_ship_timestamp)}`
    },

    getUserFullName (user_id: number): string {
      const user: SimpleUser = this.tableData.users.get(user_id)
      if (!user) {
        return ''
      }

      return user.full_name
    },

    getTrackingNoInfo (pick_order: PickOrder): PickOrderTrackingNo | null {
      const last_obj_id = _.last(pick_order.pickordertrackingno_set)

      if (!last_obj_id) {
        return null
      }

      const po_tracking: PickOrderTrackingNo =
        this.tableData.pick_order_tracking_nos.get(last_obj_id)
      if (!po_tracking) {
        return null
      }

      return po_tracking
    },

    _get (obj: any, key: string, default_value: any) {
      return _.get(obj, key, default_value)
    },

    viewOrderJson (item: PickOrder) {
      const tab = window.open('about:blank', '_blank')
      if (tab) {
        tab.document.write('<pre>' + JSON.stringify(item, null, 4) + '</pre>')
        tab.document.close() // to finish loading the page
      }
    },

    viewETaxMonitor (item: PickOrder) {
      const company = this.$auth.user?.company as Company
      console.log(item)
      window.open(`${window.location.origin}/etax/debugger?cid=${company.uuid}&oid=${item.uuid}`)
    },

    getChipColorByStatus (status: string) {
      if (status === 'CANCELLED') {
        return 'red'
      }
      if (status === 'IN_DELIVERY') {
        return 'info'
      }
      if (status === 'SUCCESS') {
        return 'green'
      }
    },

    printPickSlip () {
      if (this.$auth.user) {
        const baseURL = this.$axios.defaults.baseURL
        const company: any = this.$auth.user.company
        const cid = company.uuid
        const order_uuid_params = this.selected
          .map((x: PickOrder) => `order_uuid=${encodeURIComponent(x.uuid)}`)
          .join('&')
        this.selected = []

        window.open(
          `${baseURL}/printing/multi-pick-slip/?cid=${cid}&${order_uuid_params} `,
          '_blank'
        )
      }
    },

    printAirwayBill () {
      if (this.$auth.user) {
        const baseURL = this.$axios.defaults.baseURL
        const company: any = this.$auth.user.company
        const cid = company.uuid
        const order_uuid_params = this.selected
          .map((x: PickOrder) => `order_uuid=${encodeURIComponent(x.uuid)}`)
          .join('&')
        this.selected = []

        window.open(
          `${baseURL}/printing/multi-airway-bill/?cid=${cid}&${order_uuid_params} `,
          '_blank'
        )
      }
    },
    openDocDialog (doc_id: string | number | null, item: PickOrder) {
      this.$emit('click:open-doc-dialog', { doc_id, pick_order: item })
    }
  }
})
</script>

<style scoped>
tr.alert {
  background-color: red;
}

.selected-items-card {
  position: fixed;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  height: 48px;
  min-width: 400px;
  border-radius: 4px;
}

.selected-count {
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
}
</style>
