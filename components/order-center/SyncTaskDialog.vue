<template>
  <v-dialog
    :value="value"
    scrollable
    persistent
    max-width="600"
    transition="dialog-transition"
  >
    <v-card>
      <v-card-title>
        <div>
          {{ $t('order-center.on-sync-order') }}
          <div v-if="taskLog" class="text-caption">
            Task ID: {{ taskId }}
          </div>
        </div>
        <v-spacer />
        <v-btn icon @click="close()">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>

      <v-divider />

      <v-card-text class="mt-3">
        <v-row v-if="taskLog.task_ref" no-gutters>
          <v-col cols="12" class="d-flex justify-space-between align-center">
            <span class="text-body-2">{{ $t('order-center.sync-task-created-at') }}: {{ $datetime(taskLog.created_at) }}</span>

            <div>
              <v-chip
                :color="['success', 'error'].includes(taskLog.status) ? taskLog.status : 'warning'"
                :outlined="!['success', 'error'].includes(taskLog.status)"
                dense
              >
                {{ taskLog.status }}
              </v-chip>
            </div>
          </v-col>

          <v-col cols="12">
            <!-- Customer view with status -->
            <div v-if="taskLog" class="my-2 pa-4" style="border:1px solid #ccc!important">
              <!-- running sync content -->
              <div v-if="taskLog.status == 'running'" class="text-center">
                <v-progress-circular
                  :width="4"
                  indeterminate
                  color="primary"
                  class="mb-4"
                />
                <h3>
                  {{ $t('order-center.system-automate-sync-message') }}
                </h3>
              </div>

              <div
                v-else-if="taskLog.status === 'success'"
                class="text-center"
              >
                <v-icon
                  color="green"
                  large
                  class="mb-4"
                >
                  mdi-check-circle
                </v-icon>
                <br>
                <h3>{{ $t('order-center.sync-success') }}</h3>
              </div>

              <div
                v-else-if="taskLog.status === 'error'"
                class="text-center"
              >
                <v-icon
                  color="red"
                  large
                  class="mb-4"
                >
                  mdi-alert-circle
                </v-icon>
                <br>

                <h3 class="mb-3">
                  {{ $t('order-center.sync-fail') }}
                </h3>
                <div style="font-weight: bold;">
                  {{ $t('order-center.plase-contact') }} Dobybot Support
                  <br>
                  <a target="#" class="mt-2" href="https://page.line.me/856wntly?openQrModal=true">
                    LINE: @dobybot
                  </a> {{ $t('or') }} <a href="tel:026628500">{{ $t('tel') }}: 02-662-8500</a>
                </div>
              </div>
            </div>
            <div v-else>
              No logs available.
            </div>

            <div v-if="show_log" class="my-2 pa-2" style="border:1px solid #ccc!important">
              <span>{{ $t('order-center.log-label') }}</span>
              <pre
                style="overflow: auto; max-width:100%; text-align: left;"
              >{{ taskLog.log }}</pre>
            </div>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-actions class="d-flex justify-end">
        <v-btn text @click="show_log = !show_log">
          {{ show_log? $t('global.close-details'): $t('global.details') }}
        </v-btn>
        <v-btn color="primary" @click="close()">
          {{ $t('global.close') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'

interface TaskLog {
  type: string;
  task_ref: string;
  status: string;
  created_at: string;
  log: string;
}

export default Vue.extend({
  props: {
    value: {
      type: Boolean,
      required: true
    },
    taskId: {
      type: String,
      required: true
    }
  },

  data () {
    return {
      taskLog: {} as TaskLog,
      intervalId: null as null | number,
      show_log: false
    }
  },

  watch: {
    value (newValue) {
      if (newValue) {
        this.startFetchingTaskLog()
      } else {
        this.stopFetchingTaskLog()
      }
    }
  },

  beforeDestroy () {
    this.stopFetchingTaskLog()
  },

  methods: {
    async fetchTaskLog () {
      try {
        this.taskLog = await this.$axios.$get(`/api/companies/dobybot-connect/sync-task/${this.taskId}/`)
        if (['success', 'error'].includes(this.taskLog.status)) {
          this.stopFetchingTaskLog()
        }
      } catch (error) {
        this.stopFetchingTaskLog()
        this.taskLog.status = 'error'
        console.error('Error fetching task log:', error)
        this.stopFetchingTaskLog()
      }
    },

    startFetchingTaskLog () {
      this.fetchTaskLog()
      this.intervalId = window.setInterval(this.fetchTaskLog, 2000)
    },

    stopFetchingTaskLog () {
      if (this.intervalId) {
        window.clearInterval(this.intervalId)
        this.intervalId = null
      }
    },

    close () {
      this.$emit('input', false)
      this.show_log = false
      this.taskLog = {} as TaskLog
    }
  }
})
</script>
