<template>
  <v-dialog
    :value="value"
    scrollable
    persistent
    max-width="600"
    transition="dialog-transition"
  >
    <v-card>
      <v-card-title>
        <div>
          {{ title }}
          <div v-if="marketplace" class="text-caption">
            <span class="text-capitalize">{{ marketplace.marketplace.replaceAll('_', ' ') }}:</span> {{ marketplace.seller_shop_name }}
          </div>
        </div>
        <v-spacer />
        <v-btn icon @click="close()">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider />
      <v-card-text>
        <v-form ref="form">
          <v-radio-group v-model="form.sync_method">
            <v-radio label="Sync ข้อมูลคำสั่งซื้อล่าสุด" value="latest" />
            <v-radio label="Sync ข้อมูลคำสั่งซื้อแบบกำหนดเวลา" value="datetime" />
            <v-row dense class="pl-8">
              <v-col>
                <datetime-picker
                  v-model="form.start"
                  label="จาก"
                  :disabled="form.sync_method !== 'datetime'"
                  :rules="[$rules.required, $rules.datetime_lte(form.end)]"
                />
              </v-col>
              <v-col>
                <datetime-picker
                  v-model="form.end"
                  label="ถึง"
                  :disabled="form.sync_method !== 'datetime'"
                  :rules="[$rules.required, $rules.datetime_gte(form.start)]"
                />
              </v-col>
            </v-row>
          </v-radio-group>

          <v-btn color="success" block @click="submit()">
            Sync ข้อมูล
          </v-btn>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import moment from 'moment'
import DatetimePicker from '../global/DatetimePicker.vue'
import { VForm } from '~/models'

export default Vue.extend({
  components: { DatetimePicker },
  props: {
    title: {
      type: String,
      default: 'Sync ข้อมูลคำสั่งซื้อ'
    },
    value: {
      type: Boolean,
      required: true
    },
    marketplace: {
      type: Object,
      default: () => null
    }
  },

  data () {
    return {
      form: {
        sync_method: 'latest' as 'latest' | 'datetime',
        start: '',
        end: ''
      },

      sync_methods: [
        { value: 'latest', text: 'Sync ข้อมูลคำสั่งซื้อล่าสุด' },
        { value: 'datetime', text: 'Sync ข้อมูลคำสั่งซื้อแบบกำหนดเวลา' }
      ]
    }
  },

  watch: {
    value () {
      if (!this.form.start) {
        this.form.start = moment().startOf('days').format()
      }
      if (!this.form.end) {
        this.form.end = moment().endOf('days').format()
      }
    }
  },

  methods: {
    submit () {
      const form = this.$refs.form as never as VForm
      if (!form.validate()) {
        return
      }

      this.$emit('submit', { marketplace: this.marketplace, ...this.form })
    },

    close () {
      this.$emit('input', false)
    }
  }
})
</script>
