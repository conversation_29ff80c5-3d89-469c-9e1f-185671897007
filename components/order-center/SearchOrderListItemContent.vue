<template>
  <div>
    <v-list-item-content>
      <v-list-item-title>{{ pickOrder.order_customer }} {{ pickOrder.order_customerphone }}</v-list-item-title>
      <v-list-item-subtitle> {{ pickOrder.order_json.shippingaddress }} </v-list-item-subtitle>
    </v-list-item-content>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { PickOrder } from '~/models'

export default Vue.extend({
  props: {
    pickOrder: {
      type: Object,
      required: true
    } as Vue.PropOptions<PickOrder>
  },

  data () {
    return {

    }
  }
})
</script>

<style lang="scss" scoped>

</style>
