<template>
  <v-dialog
    max-width="800"
    :value="value"
    @input="$emit('input', $event)"
  >
    <v-card>
      <v-card-title class="text-h6">
        {{ $t('order-center.change-print-status') }}
        <v-spacer />
        <v-btn icon @click="$emit('input', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider class="mb-4" />
      <v-card-text>
        <!--  -->
        <div class="body-1">
          {{ $t('change-print-status.change-print-status-of-n-orders', [form.orders.length]) }}

          <div class="text-right">
            <a class="text-caption px-3" @click.prevent="form.orders = [...orders]">Select All</a>
            <a class="text-caption" @click.prevent="form.orders = []">Clear</a>
          </div>
          <v-data-table
            v-model="form.orders"
            style="max-height: 300px; overflow-y: auto; border: 1px solid black;"
            :headers="table.headers"
            :items="table.items"
            show-select
            item-key="order_number"
            selectable-key="order_number"
          />
        </div>
        <v-radio-group v-model="form.print_count">
          <v-radio
            :label="$t('change-print-status.printed')"
            :value="1"
          />
          <v-radio
            :label="$t('change-print-status.not-printed')"
            :value="0"
          />
        </v-radio-group>
      </v-card-text>
      <v-card-actions>
        <v-spacer />
        <v-btn color="primary" :loading="loading" @click="submit()">
          {{ $t('settings.confirm') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue, { PropOptions } from 'vue'
import { PickOrder, VDataTable, VDataTableHeader } from '~/models'
export default Vue.extend({
  props: {
    value: {
      type: Boolean,
      default: false
    } as PropOptions<boolean>,
    search: {
      type: String,
      required: true
    } as PropOptions<string>,
    filter: {
      type: Object,
      required: true
    } as PropOptions<{
      start: string,
      end: string,
      shop: string,
      order_warehousecode: string,
      order_saleschannel: string,
      order_shippingchannel: string,
      ready_to_ship: null | boolean,
      has_videos: null | boolean,
      has_fixcases: boolean,
      has_remark: boolean
    }>
  },

  data () {
    return {
      loading: false,
      orders: [] as PickOrder[],
      form: {
        orders: [] as PickOrder[],
        print_count: 0
      },
      p: 0
    }
  },

  computed: {
    table (): VDataTable {
      const headers: VDataTableHeader[] = [
        { text: this.$t('order.order-number'), value: 'order_number' },
        { text: this.$t('change-print-status.is_print'), value: 'print_count' },
        { text: this.$t('change-print-status.has-video'), value: 'has_videos' },
        { text: this.$t('change-print-status.ready-to-ship'), value: 'ready_to_ship' }
      ]
      return { headers, items: this.orders }
    }
  },

  watch: {
    value: {
      handler (val) {
        if (val) {
          this.getOrderNumbers()
        }
      },
      immediate: true
    }
  },

  methods: {
    async getOrderNumbers () {
      const action = '/api/picking/resource/pick-orders/'
      const params = {
        // page: 1,
        // per_page: 5000
      } as any

      // Searching
      params.search = this.search

      // Filtering
      // params['filter{print_count.gte}'] = 1
      if (this.filter.start) {
        params['filter{order_date.gte}'] = this.filter.start
      }
      if (this.filter.end) {
        params['filter{order_date.lte}'] = this.filter.end
      }
      if (this.filter.order_warehousecode) {
        params['filter{order_warehousecode}'] = this.filter.order_warehousecode
      }
      if (this.filter.order_saleschannel) {
        params['filter{order_saleschannel}'] = this.filter.order_saleschannel
      }
      if (this.filter.order_shippingchannel) {
        params['filter{order_shippingchannel}'] = this.filter.order_shippingchannel
      }
      if (this.filter.has_videos !== null) {
        params['filter{has_videos}'] = this.filter.has_videos
      }
      if (this.filter.ready_to_ship !== null) {
        params['filter{ready_to_ship}'] = this.filter.ready_to_ship
      }
      if (this.filter.has_fixcases) {
        params['filter{has_fixcases}'] = this.filter.has_fixcases
      }
      if (this.filter.has_remark) {
        params['filter{-remark}'] = ''
      }

      // Include / Exclude
      params.include = ['order_number', 'has_videos', 'print_count', 'ready_to_ship']
      params.exclude = ['*']

      this.loading = true
      try {
        const response = await this.$axios.get(action, { params })
        this.orders = response.data.pick_orders
        this.form.orders = [...this.orders]
      } catch (error: any) {
        console.error(error)

        if (error.response) {
          this.$snackbar('error', this.$t('error.response', [error.response.status, error.response.data]))
        }
      }
      this.loading = false
    },

    async submit () {
      const action = '/api/picking/pick-orders/update-print-status/'
      const payload = {
        order_numbers: this.form.orders.map(x => x.order_number),
        print_count: this.form.print_count
      }

      this.loading = true
      try {
        await this.$axios.post(action, payload)
        this.orders = []
        this.form.orders = []
        this.$emit('input', false)
        this.$emit('success')
        this.$snackbar('success', this.$t('success'))
      } catch (error: any) {
        console.error(error)
        if (error.response) {
          this.$snackbar('error', this.$t('error.response', [error.response.status, error.response.data]))
        }
      }
      this.loading = false
    }
  }
})
</script>
