<template>
  <v-row dense>
    <v-col cols="12">
      <v-combobox
        v-bind="$attrs"
        no-filter
        item-value="order_json.shippingaddress"
        :items="items"
        :loading="loading"
        :search-input="search"
        @update:search-input="searchPickOrder($event)"
        @change="updateOrderJson($event)"
      >
        <template #no-data>
          <v-list-item>
            <v-list-item-title>
              {{ listItemTitle }}
            </v-list-item-title>
          </v-list-item>
        </template>

        <template #selection="{ item }">
          {{ item.endsWith(':SEARCH_ITEM')? item.split(':')[1]: item }}
        </template>

        <template #item="{ item }">
          <search-order-list-item-content v-if="item" :pick-order="getPickOrder(item.split(':')[0])" />
        </template>
      </v-combobox>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import _ from 'lodash'
import { PickOrder } from '~/models'
import SearchOrderListItemContent from '~/components/order-center/SearchOrderListItemContent.vue'

export default Vue.extend({
  components: { SearchOrderListItemContent },
  props: {
    queryWith: {
      type: String,
      required: true
    },
    listItemTitle: {
      type: String,
      default: 'ค้นหาออเดอร์'
    },
    updateField: {
      type: String,
      required: true
    }
  },

  data () {
    return {
      search: '',
      searchResults: [] as PickOrder[] | [],
      loading: false,
      debouncedSearch: null as Function |null
    }
  },

  computed: {
    items (): string[] {
      return this.searchResults.map(x => `${x.id}:${x[this.queryWith]}:SEARCH_ITEM`)
    }
  },

  created () {
    this.debouncedSearch = _.debounce(() => {
      this.getPickOrders()
    }, 500)
  },

  methods: {
    async getPickOrders () {
      if (!this.search) {
        return
      }

      if (this.loading) {
        return
      }

      const action = '/api/picking/resource/pick-orders/'
      const params = this.getPickOrderQueryParams()

      this.loading = true
      try {
        const response = await this.$axios.get(action, { params })
        this.searchResults = response.data.pick_orders
      } catch (error: any) {
        console.error(error)
      }
      this.loading = false
    },

    getPickOrderQueryParams () {
      const params: any = {}
      if (this.search) {
        params[`filter{${this.queryWith}.icontains}`] = this.search
      }
      params.page = 1
      params.per_page = 15

      return params
    },

    updateOrderJson (value: string) {
      value = value || ''
      const is_selected = value.endsWith(':SEARCH_ITEM')

      // case 1 : check user select from dropdown
      if (is_selected) {
        // search pick order form id
        const id = Number(value.split(':')[0])
        const pick_order = this.searchResults.find(x => x.id === id)
        if (!pick_order) { return }

        this.$emit('update-order-json', {
          customername: pick_order.order_customer,
          customerphone: pick_order.order_customerphone,
          shippingname: pick_order.order_customer,
          shippingphone: pick_order.order_customerphone,
          shippingaddress: pick_order.order_json.shippingaddress
        })
      }

      // case 2 : user type new data
      if (!is_selected) {
        let update_second_field = ''
        this.$emit('update-order-json', { [this.updateField]: value })

        // Update Shipping Data
        if (this.updateField === 'customername') {
          update_second_field = 'shippingname'
        } else if (this.updateField === 'customerphone') {
          update_second_field = 'shippingphone'
        }
        this.$emit('update-order-json', { [update_second_field]: value })
      }
    },

    searchPickOrder (search) {
      if (typeof search === 'string' && this.debouncedSearch !== null) {
        this.search = search
        this.debouncedSearch()
      }
    },

    getPickOrder (po_id: number) {
      const pick_order = this.searchResults.find(x => Number(x.id) === Number(po_id))
      return pick_order
    }

  }
})
</script>
