<template>
  <v-dialog
    max-width="480"
    :value="value"
    @input="$emit('input', $event)"
  >
    <v-card>
      <v-card-title class="text-h6">
        {{ $t('order-center.ready-to-ship-history') }}
        <v-spacer />
        <v-btn icon @click="$emit('input', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider class="mb-4" />
      <v-card-text>
        <table style="width: 100%;">
          <thead>
            <th class="text-left">
              {{ $t('order-center.date-time') }}
            </th>
            <th class="text-left">
              {{ $t('order-center.barcode') }}
            </th>
            <th class="text-right">
              {{ $t('order-center.scan-ready-to-ship-by') }}
            </th>
          </thead>
          <tr v-for="log in logs" :key="log.id">
            <td class="text-left">
              {{ $datetime(log.create_date) }}
            </td>
            <td class="text-left">
              {{ log.barcode }}
            </td>
            <td class="text-right">
              {{ log.create_by.full_name }}
            </td>
          </tr>
        </table>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    value: {
      type: Boolean,
      default: false
    },
    logs: {
      type: Array,
      default () {
        return []
      }
    }
  }
})
</script>
