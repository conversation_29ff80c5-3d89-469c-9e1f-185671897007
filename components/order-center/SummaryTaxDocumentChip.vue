<template>
  <div class="d-flex">
    <div style="border: 1px solid #ababab; border-radius: 5px">
      <div
        v-for="(row, rowIndex) in documentButtons"
        :key="rowIndex"
        class="d-flex"
      >
        <v-tooltip v-for="btn in row" :key="btn.key" top>
          <template #activator="{ on, attrs }">
            <v-btn
              tile
              x-small
              elevation="0"
              width="35px"
              :color="getTaxDocShipInfo(doc_summary[btn.key].doc).color"
              :disabled="doc_summary[btn.key].disabled"
              :class="btn.class"
              v-bind="attrs"
              v-on="on"
              @click="openDocDialog(doc_summary[btn.key].doc?.id ?? btn.key)"
            >
              {{ btn.label }}
              <span v-if="doc_summary[btn.key].doc?.edit_count">
                ({{ doc_summary[btn.key].doc?.edit_count }})
              </span>
            </v-btn>
          </template>
          <div
            v-if="!pickOrder.order_number.includes('---')"
            class="text-center"
          >
            <span>{{ btn.title }}</span>
            <br>
            <span>
              ({{ getTaxDocShipInfo(doc_summary[btn.key].doc).text }})
            </span>
          </div>

          <div v-else>
            <span>กรุณาออกเอกสารจากออเดอร์
              {{ pickOrder.order_number.split("---")[0] }}</span>
          </div>
        </v-tooltip>
      </div>
    </div>

    <v-tooltip top>
      <template #activator="{ on, attrs }">
        <v-btn
          icon
          :disabled="otherDocumentsCount === 0"
          @click="openOtherDocDialog()"
        >
          <v-icon
            class="my-auto"
            v-bind="attrs"
            v-on="on"
          >
            mdi-dots-horizontal-circle-outline
          </v-icon>
          <v-badge
            v-if="otherDocumentsCount > 0"
            dot
            color="green"
            offset-x="4"
            offset-y="-5"
          />
        </v-btn>
      </template>

      เอกสารอื่นๆ
    </v-tooltip>

    <!-- Status Icon: Auto Etax Error -->
    <v-tooltip
      v-if="
        pickOrder.order_json.extra?.auto_etax_errors &&
        getTaxDocShipInfo(doc_summary.tax_invoice.doc).status == 'new'
      "
      top
    >
      <template #activator="{ on }">
        <v-icon color="error" small v-on="on">
          mdi-file-document-alert-outline
        </v-icon>
      </template>
      <span>ไม่สามารถออก e-Tax อัตโนมัติได้เนื่องจากข้อมูลที่ไม่ถูกต้อง</span>
      <ul>
        <li
          v-for="(value, field) in pickOrder.order_json.extra.auto_etax_errors"
          :key="field"
        >
          {{ field }} : {{ value.join(", ") }}
        </li>
      </ul>
    </v-tooltip>

    <!-- Status Icon: Buyer Request Etax -->
    <v-tooltip v-else-if="pickOrder.order_json.customeridnumber" top>
      <template #activator="{ on }">
        <v-icon class="no-print ps-2" color="primary" small v-on="on">
          mdi-file-document-alert-outline
        </v-icon>
      </template>
      <span>ลูกค้าขอใบกำกับภาษี</span>
    </v-tooltip>
  </div>
</template>

<script lang="ts">
import _ from 'lodash'
import Vue from 'vue'
import { TaxDocument, TaxDocumentStatus } from '~/models'

interface DocumentButton {
  key: string
  label: string
  title: string
  class: string
}

interface DocSummary {
  doc: TaxDocument | null
  docs: TaxDocument[]
  disabled: boolean
}

interface DocumentSummaryMap {
  [key: string]: DocSummary
}

export default Vue.extend({
  props: {
    tableData: {
      type: Object,
      required: true
    },
    pickOrder: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      selected: ''
    }
  },
  computed: {
    documentButtons (): DocumentButton[][] {
      return [
        [
          // Row 1
          {
            key: 'tax_invoice',
            label: 'RT',
            title: 'ใบกำกับภาษี/ใบเสร็จรับเงิน',
            class: 'rounded-tl'
          },
          {
            key: 'invoice',
            label: 'INV',
            title: 'ใบแจ้งหนี้',
            class: ''
          },
          {
            key: 'receipt',
            label: 'R',
            title: 'ใบเสร็จรับเงิน',
            class: 'rounded-tr'
          }
        ],
        [
          // Row 2
          {
            key: 'credit_note',
            label: 'CN',
            title: 'ใบลดหนี้',
            class: 'rounded-bl'
          },
          {
            key: 'debit_note',
            label: 'DN',
            title: 'ใบเพิ่มหนี้',
            class: ''
          },
          {
            key: 'return_receipt',
            label: 'RC',
            title: 'ใบเสร็จรับเงิน/รับคืนสินค้า',
            class: 'rounded-br'
          }
        ]
      ]
    },

    otherDocumentButtons (): DocumentButton[] {
      return [
        {
          key: 'delivery_invoice',
          label: 'DIV',
          title: 'ใบส่งของ/ใบกำกับภาษี',
          class: ''
        }
      ]
    },

    doc_summary (): DocumentSummaryMap {
      const related_documents: DocumentSummaryMap = {
        tax_invoice: {
          doc: null as TaxDocument | null,
          docs: [] as TaxDocument[],
          disabled: true
        },
        invoice: {
          doc: null as TaxDocument | null,
          docs: [] as TaxDocument[],
          disabled: true
        },
        receipt: {
          doc: null as TaxDocument | null,
          docs: [] as TaxDocument[],
          disabled: true
        },
        debit_note: {
          doc: null as TaxDocument | null,
          docs: [] as TaxDocument[],
          disabled: true
        },
        credit_note: {
          doc: null as TaxDocument | null,
          docs: [] as TaxDocument[],
          disabled: true
        },
        return_receipt: {
          doc: null as TaxDocument | null,
          docs: [] as TaxDocument[],
          disabled: true
        },
        delivery_invoice: {
          doc: null as TaxDocument | null,
          docs: [] as TaxDocument[],
          disabled: true
        }
      }

      if (
        !this.$hasCompanyFeatureFlag('etax') ||
        !this.$hasPerms('view_taxdocument')
      ) {
        return related_documents
      }

      if (
        this.pickOrder.order_json.status === 'Voided' &&
        this.pickOrder.taxdocument_set.length === 0
      ) {
        return related_documents
      }

      const hasType = [] as string[]

      console.log(this.pickOrder.order_number)
      for (const doc_id of this.pickOrder.taxdocument_set) {
        const doc = this.tableData.tax_documents.get(doc_id) as TaxDocument
        related_documents[doc.doc_type].docs.push(doc)
        related_documents[doc.doc_type].disabled = false
        hasType.push(doc.doc_type)
      }

      for (const item of Object.values(related_documents)) {
        item.doc = _.maxBy(item.docs, 'id') || null
      }

      if (!hasType.includes('tax_invoice') && !hasType.includes('receipt')) {
        related_documents.tax_invoice.disabled = false
      }

      if (hasType.includes('delivery_invoice')) {
        related_documents.tax_invoice.disabled = true
      }

      return related_documents
    },

    chips (): any[] {
      return []
    },

    otherDocumentsCount (): number {
      return this.otherDocumentButtons.filter(btn =>
        !this.doc_summary[btn.key].disabled &&
        this.doc_summary[btn.key].doc
      ).length
    }
  },
  methods: {
    getTaxDocShipInfo (tax_doc: TaxDocument | null) {
      if (!tax_doc) {
        return {
          color: '',
          status: 'new',
          text: 'ไม่มีเอกสาร'
        }
      }

      const status = TaxDocumentStatus.find(
        x => x.value === (tax_doc as TaxDocument).status
      )
      if (!status) {
        throw new Error('Invalid TaxDocumentStatus value, ' + tax_doc.status)
      }

      let text = status.text
      if (tax_doc.edit_count > 0) {
        text += ` - แก้ไข ${tax_doc.edit_count} ครั้ง`
      }

      return {
        color: status.color,
        status: status.value,
        text
      }
    },
    openDocDialog (docId: string | number | null) {
      if (this.pickOrder.order_number.includes('---')) {
        this.$alert({
          title: 'ไม่สามารถออกเอกสารจากออเดอร์ที่ถูกแตกกล่องได้',
          theme: 'warning',
          text: ` กรุณาดำเนินการออกเอกสารจากออเดอร์หลักหมายเลข ${
            this.pickOrder.order_number.split('---')[0]
          } แทน หากมีคำถามเพิ่มเติมหรือต้องการความช่วยเหลือ กรุณาติดต่อทีมงาน Dobybot Support ได้`
        })
        return
      }
      this.$emit('open-doc-dialog', docId)
    },
    openOtherDocDialog () {
      // Find the first available document from otherDocumentButtons
      for (const btn of this.otherDocumentButtons) {
        if (!this.doc_summary[btn.key].disabled && this.doc_summary[btn.key].doc) {
          // If document exists, open it using the existing openDocDialog function
          this.openDocDialog(this.doc_summary[btn.key].doc?.id ?? btn.key)
          return
        }
      }

      // If no document exists yet, open with the key of the first button
      if (this.otherDocumentButtons.length > 0) {
        this.openDocDialog(null)
      }
    }
  }
})
</script>

<style scoped></style>
