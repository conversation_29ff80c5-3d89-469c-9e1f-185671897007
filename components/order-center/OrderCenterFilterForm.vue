<template>
  <v-form class="mb-3" @submit.prevent="">
    <v-row dense>
      <!-- Start & End -->
      <v-col cols="12" md="12">
        <v-row dense>
          <v-col cols="6" md="2">
            <date-picker
              data-test="start-date"
              :label="$t('order-center.from')"
              :value="formData.start"
              :min="min_date"
              outlined
              dense
              hide-details
              @input="update({ start: $event })"
            />
          </v-col>
          <v-col cols="6" md="2">
            <date-picker
              data-test="end-date"
              :min="min_date"
              :label="$t('order-center.to')"
              :value="formData.end"
              outlined
              dense
              hide-details
              @input="update({ end: $event })"
            />
          </v-col>

          <!-- <v-col cols="12" md="2">
            <v-select
              label="Warehouse"
              outlined
              dense
              hide-details
              clearable
              :items="warehouse_choices"
              :value="formData.warehouse"
              @change="update({ order_warehousecode: $event })"
            />
          </v-col> -->
        </v-row>
      </v-col>

      <v-col cols="12" md="12">
        <v-row dense>
          <!-- <v-col cols="12" md="4">
            <v-select
              label="Warehouse"
              outlined
              dense
              hide-details
              clearable
              :items="warehouse_choices"
              :value="formData.warehouse"
              @change="update({ order_warehousecode: $event })"
            />
          </v-col> -->
          <v-col cols="12" md="2">
            <v-select
              label="Shop"
              data-test="filter-shop"

              outlined
              dense
              hide-details
              clearable
              :items="saleschannel_choices"
              :value="formData.order_saleschannel"
              @change="update({ order_saleschannel: $event })"
            />
          </v-col>
          <v-col cols="12" md="2">
            <v-select
              data-test="shipping-channel"

              label="Shipping Channel"
              outlined
              dense
              hide-details
              clearable
              :items="shippingchannel_choices"
              :value="formData.order_shippingchannel"
              @change="update({ order_shippingchannel: $event })"
            />
          </v-col>
          <!-- </v-row>
      </v-col>

      <v-col cols="12" md="6">
        <v-row dense> -->
          <v-col cols="12" md="2">
            <v-select
              :label="$t('order-center.video')"
              outlined
              dense
              hide-details
              :items="video_choices"
              :value="formData.has_videos"
              @change="update({ has_videos: $event })"
            />
          </v-col>
          <v-col cols="12" md="2">
            <v-select
              :label="$t('order-center.ready-status')"
              outlined
              dense
              hide-details
              :items="ready_to_ship_choices"
              :value="formData.ready_to_ship"
              @change="update({ ready_to_ship: $event })"
            />
          </v-col>
          <v-col cols="12" md="2">
            <v-select
              label="สถานะใบกำกับภาษีอิเล็กทรอนิกส์"
              outlined
              dense
              hide-details
              :items="etax_status_choices"
              :value="formData.etax_status"
              @change="update({ etax_status: $event })"
            />
          </v-col>
          <v-col cols="12" md="2">
            <v-select
              outlined
              dense
              chips
              hide-details
              small-chips
              label="อื่น ๆ"
              multiple
              :items="[
                { text: 'มี Fix Cases', value: 'has_fixcases' },
                { text: 'มี หมายเหตุ', value: 'has_remark' },
                { text: 'มี ของคืน', value: 'has_return_videos' },
                { text: 'มี ขอใบกำกับภาษี', value: 'is_request_tax_invoice' },
              ]"
              @change="update({
                has_fixcases: $event.includes('has_fixcases'),
                has_remark: $event.includes('has_remark'),
                has_return_videos: $event.includes('has_return_videos'),
                is_request_tax_invoice: $event.includes('is_request_tax_invoice')
              })"
            />
          </v-col>

          <!-- <v-col cols="12" md="2">
            <v-checkbox :label="$t('order-center.fix-case')" dense :input-value="formData.has_fixcases" @change="update({ has_fixcases: $event })" />
          </v-col>
          <v-col cols="12" md="2">
            <v-checkbox :label="$t('order-center.have-remark')" dense :input-value="formData.has_remark" @change="update({ has_remark: $event })" />
          </v-col> -->
        </v-row>
      </v-col>
    </v-row>
    <v-row v-if="show_advance_filter" class="mt-2" dense>
      <v-col cols="12">
        <v-divider class="mb-2" />
      </v-col>
      <v-col cols="12" md="2">
        <v-select
          :label="$t('order-center.filter_order_status')"
          outlined
          dense
          hide-details
          :items="order_status_choices"
          :value="formData.order_status"
          @change="update({ order_status: $event })"
        />
      </v-col>
      <v-col cols="12" md="2">
        <v-select
          :label="$t('order-center.filter_printing_pick_slip_status')"
          outlined
          dense
          hide-details
          :items="printing_pick_slip_status_choices"
          :value="formData.printing_pick_slip_status"
          @change="update({ printing_pick_slip_status: $event })"
        />
      </v-col>
      <v-col cols="12" md="2">
        <v-select
          :label="$t('order-center.filter_printing_airway_bill_status')"
          outlined
          dense
          hide-details
          :items="printing_airway_bill_status_choices"
          :value="formData.printing_airway_bill_status_choices"
          @change="update({ printing_airway_bill_status_choices: $event })"
        />
      </v-col>

      <v-col cols="12" md="2">
        <v-select
          :label="$t('order-center.filter_trackingno')"
          outlined
          dense
          hide-details
          :items="order_trackingno_choices"
          :value="formData.order_trackingno"
          @change="update({ order_trackingno: $event })"
        />
      </v-col>
      <v-col cols="6" md="2">
        <date-picker
          :label="$t('order-center.record-from')"
          :value="formData.record_start"
          outlined
          dense
          clearable
          hide-details
          @input="update({ record_start: $event })"
        />
      </v-col>
      <v-col cols="6" md="2">
        <date-picker
          :label="$t('order-center.record-to')"
          :value="formData.record_end"
          outlined
          dense
          clearable
          hide-details
          @input="update({ record_end: $event })"
        />
      </v-col>
      <v-col cols="6" md="2">
        <date-picker
          :label="$t('order-center.rts-from')"
          :value="formData.rts_start"
          outlined
          dense
          clearable
          hide-details
          @input="update({ rts_start: $event })"
        />
      </v-col>
      <v-col cols="6" md="2">
        <date-picker
          :label="$t('order-center.rts-to')"
          :value="formData.rts_end"
          outlined
          dense
          clearable
          hide-details
          @input="update({ rts_end: $event })"
        />
      </v-col>
    </v-row>
    <v-row justify="center">
      <v-btn x-small text color="primary" @click="toggleAdvanceFilter()">
        Advance Filter &nbsp;
        <v-icon x-small>
          {{ show_advance_filter ? 'mdi-arrow-up' : 'mdi-arrow-down' }}
        </v-icon>
      </v-btn>
    </v-row>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import DatePicker from '../global/DatePicker.vue'
export default Vue.extend({
  components: {
    DatePicker
  },

  props: {
    formData: {
      type: Object,
      required: true
    }
  },

  data () {
    return {
      min_date: '',
      max_date: '',
      video_choices: [
        { text: this.$t('order-center.all'), value: null },
        { text: this.$t('order-center.video-save'), value: true },
        { text: this.$t('order-center.not-record'), value: false }
      ],
      ready_to_ship_choices: [
        { text: this.$t('order-center.all'), value: null },
        { text: this.$t('order-center.ready-to'), value: true },
        { text: this.$t('order-center.not-ready'), value: false }
      ],
      warehouse_choices: [],
      shippingchannel_choices: [],
      saleschannel_choices: [],
      show_advance_filter: false,
      order_status_choices: [
        'Pending',
        'Waiting',
        'Packed',
        'Shipping',
        'Success',
        'Voided',
        'Partial Transfer',
        'Partial Voided',
        'Failed Shipment'
      ],
      printing_pick_slip_status_choices: [
        { text: this.$t('order-center.all'), value: null },
        { text: this.$t('order-center.printed'), value: true },
        { text: this.$t('order-center.not-printed'), value: false }
      ],
      printing_airway_bill_status_choices: [
        { text: this.$t('order-center.all'), value: null },
        { text: this.$t('order-center.printed'), value: true },
        { text: this.$t('order-center.not-printed'), value: false }
      ],
      order_trackingno_choices: [
        { text: this.$t('order-center.all'), value: null },
        { text: this.$t('order-center.no_order_trackingno'), value: false },
        { text: this.$t('order-center.order_trackingno'), value: true }
      ],
      etax_status_choices: [
        {
          value: '',
          text: 'ทั้งหมด'
        },
        {
          value: 'new',
          text: 'ไม่มีเอกสาร'
        }, {
          value: 'success',
          text: 'สำเร็จ'
        },
        {
          value: 'on_hold',
          text: 'ไม่สำเร็จ*'
        },
        {
          value: 'failed',
          text: 'ไม่สำเร็จ'
        },
        {
          value: 'cancel',
          text: 'ยกเลิก'
        }
      ]
    }
  },

  created () {
    // this.getChoices('warehouse_choices')
    this.getChoices('shippingchannel_choices')
    this.getChoices('saleschannel_choices')

    this.min_date = this.$moment().subtract(90, 'days').format('YYYY-MM-DD')
    this.max_date = this.$moment().format('YYYY-MM-DD')
  },

  methods: {
    update (data: any) {
      console.log('FilterData', data)
      this.$emit('update:form-data', { ...this.formData, ...data })

      if ('start' in data || 'end' in data) {
        this.getChoices('shippingchannel_choices')
        this.getChoices('saleschannel_choices')
      }
    },

    async getChoices (choice_name: 'warehouse_choices'|'shippingchannel_choices'|'saleschannel_choices') {
      const params = {} as any
      if (this.formData.start) {
        params.order_date__gte = this.formData.start
      }
      if (this.formData.end) {
        params.order_date__lte = this.formData.end
      }
      this[choice_name] = (await this.$axios.get(`/api/companies/choices/${choice_name}/`, {
        params
      })).data
    },

    toggleAdvanceFilter () {
      this.show_advance_filter = !this.show_advance_filter

      if (!this.show_advance_filter) {
        this.update({ order_status: null })
      }
    }
  }

})
</script>
