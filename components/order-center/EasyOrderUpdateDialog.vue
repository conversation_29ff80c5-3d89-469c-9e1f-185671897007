<template>
  <v-dialog :value="value" persistent max-width="960">
    <v-card>
      <v-card-title>
        <span class="headline">แก้ไข Order</span>
        <v-spacer />
        <v-btn icon @click="close()">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider />

      <v-card-text class="pt-3">
        <div v-if="orderJson">
          <v-alert
            v-if="taxDocument"
            class="mb-5"
            type="warning"
          >
            {{ $t('order-center.tax-document-exist') }}
          </v-alert>

          <easy-order-form
            ref="form"
            :order-json.sync="order_json_form"
            :auto-generate-tracking="is_auto_generate_tracking"
            :disabled="['number', 'orderdateString']"
            :disabled-all="isDisabledForm"
            @toggle-gen-tracking="toggleGenTracking($event)"
            @update-order-form="updateOrderForm($event)"
          />
        </div>
      </v-card-text>

      <v-alert
        v-show="error_message"
        class="mt-5"
        text
        outlined
        type="error"
      >
        <div v-html="sanitize( error_message )" />
      </v-alert>
      <v-divider />

      <v-card-actions>
        <v-btn
          :loading="loading"
          color="error"
          @click="voidOrder()"
        >
          ยกเลิกออเดอร์
        </v-btn>
        <v-spacer />
        <v-btn
          :loading="loading"
          :disabled="isDisabledForm"
          color="success"
          @click="editOrder()"
        >
          แก้ไข
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import DOMPurify from 'dompurify'
import EasyOrderForm from '~/components/order-center/EasyOrderForm.vue'
import ShippingModule from '~/repositories/modules/shipping'
import { OrderJson, PickOrder, PickOrderTrackingNo, TaxDocument } from '~/models'

export default Vue.extend({
  components: {
    EasyOrderForm
  },
  props: {
    value: {
      type: Boolean,
      required: true
    },
    orderJson: {
      type: Object,
      requred: false,
      default: () => null
    } as Vue.PropOptions<OrderJson | null>,

    taxDocument: {
      type: Object,
      required: false,
      default: () => null
    } as Vue.PropOptions<TaxDocument | null>,
    poTracking: {
      type: Object,
      required: false,
      default: () => null
    } as Vue.PropOptions<PickOrderTrackingNo | null>
  },

  data () {
    return {
      loading: false,
      error_message: '',
      order_json_form: null as OrderJson | null,
      form_errors: {} as any,
      is_auto_generate_tracking: false
    }
  },

  computed: {
    isDisabledForm () {
      return this.taxDocument !== null || (this.poTracking !== null && this.poTracking.status !== 'CANCELLED')
    }
  },

  watch: {
    orderJson: {
      handler (order_json: OrderJson) {
        if (!order_json) {
          return
        }

        this.order_json_form = order_json
      },
      deep: true,
      immediate: true
    }

  },

  methods: {
    sanitize (html: string) {
      return DOMPurify.sanitize(html)
    },
    close () {
      this.$emit('input', false)
    },

    toggleGenTracking (bool: boolean) {
      this.is_auto_generate_tracking = bool
    },

    updateOrderForm (data: any) {
      this.order_json_form = { ...this.order_json_form, ...data }
    },

    async editOrder () {
      if (!this.orderJson) {
        return
      }

      if (!this.order_json_form) {
        return
      }

      if (this.loading) {
        return
      }

      if (!(this.$refs.form as any).validate()) {
        return
      }

      if (this.taxDocument !== null) {
        await this.$confirm({
          text: (
            '<div style="text-align: center;">' +
            'ไม่สามารถ "แก้ใขออเดอร์ " ได้เนื่องจาก <br> ' +
            '<b>ออเดอร์นี้มีใบกำกับภาษี</b><br><br>' +
            '</div>'
          )
        })
        return
      }

      this.loading = true
      const order_number = this.order_json_form.number
      const couriers = ['DBB_FLASH', 'DBB_THAIPOST_EMS', 'DBB_THAIPOST_REG', 'DBB_KERRY']

      try {
        const res = await this.$axios.put(`/api/picking/easy-orders/${order_number}/`, this.order_json_form)
        const pick_order = res.data

        if (couriers.includes(pick_order.order_shippingchannel) && this.order_json_form.trackingno === '') {
          this.autoGenerateTrackingNumber(pick_order)
        }
        this.$emit('update:pick_order', pick_order)
        this.$emit('input', false)
      } catch (error: any) {
        console.error(error)
        if (error.response) {
          if (error.response.status === 400) {
            if (error.response.data.non_field_errors) {
              this.error_message = this.$t(error.response.data.non_field_errors[0].code)
            } else {
              this.$snackbar('error', this.$t('error.unknown'))
            }
          } else {
            this.$snackbar('error', this.$t('error.unknown'))
          }
        }
      }

      this.loading = false
    },

    async voidOrder () {
      if (!this.orderJson) {
        return
      }

      if (!this.order_json_form) {
        return
      }

      if (this.loading) {
        return
      }

      if (this.taxDocument !== null) {
        if (this.taxDocument.status === 'success') {
          await this.$confirm({
            text: (
              '<div style="text-align: center;">' +
              'ไม่สามารถ "ยกเลิกออเดอร์ " ได้เนื่องจาก <br> ' +
              '<b>ออเดอร์นี้มีใบกำกับภาษี</b><br><br>' +
              'กรุณาทำการยกเลิกใบกำกับภาษีก่อน' +
              '</div>'
            )
          })
          return
        }
      }

      this.loading = true
      const order_number = this.order_json_form.number
      this.order_json_form.status = 'Voided'

      try {
        const res = await this.$axios.put(`/api/picking/easy-orders/${order_number}/`, this.order_json_form)
        const pick_order = res.data

        this.$emit('update:pick_order', pick_order)
        this.$emit('input', false)
      } catch (error: any) {
        console.error(error)
        if (error.response) {
          if (error.response.status === 400) {
          } else {
            this.$snackbar('error', this.$t('error.unknown'))
          }
        }
      }

      this.loading = false
    },

    async autoGenerateTrackingNumber (pick_order: PickOrder) {
      const form_data = {
        shippingname: pick_order.order_json.shippingname,
        shippingphone: pick_order.order_json.shippingphone,
        shippingaddress: pick_order.order_json.shippingaddress,
        shippingprovince: pick_order.order_json.shippingprovince,
        shippingdistrict: pick_order.order_json.shippingdistrict,
        shippingsubdistrict: pick_order.order_json.shippingsubdistrict,
        shippingpostcode: pick_order.order_json.shippingpostcode
      }
      const shipping_module = new ShippingModule(this.$axios)
      const response = await shipping_module.generateTracking(pick_order)

      // Todo: Handle error
      this.$emit('created:tracking-number', { new_po_tracking: response.data, form_data })
    }
  }
})
</script>
