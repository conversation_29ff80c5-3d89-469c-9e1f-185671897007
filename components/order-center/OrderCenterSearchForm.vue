<template>
  <v-form @submit.prevent="submit()">
    <v-row dense>
      <v-col cols="9" md="10">
        <v-text-field
          data-test="search-box"
          :label="$t('order-center.order-no-tracking-no')"
          prepend-inner-icon="mdi-magnify"
          dense
          outlined
          clearable
          hide-details
          :value="search"
          @input="$emit('update:search', ($event || '').trim())"
        />
      </v-col>
      <v-col cols="3" md="2">
        <v-btn data-test="search-btn" type="submit" block color="success" height="40">
          ค้นหา
        </v-btn>
      </v-col>
    </v-row>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    search: {
      type: String,
      required: true
    }
  },

  methods: {
    submit () {
      this.$emit('submit')
    }
  }
})
</script>
