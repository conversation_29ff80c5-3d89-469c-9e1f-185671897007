<template>
  <div>
    <v-menu offset-y>
      <template #activator="{ on }">
        <v-btn color="primary" icon small class="mx-2" v-on="on">
          <v-icon>mdi-dots-vertical</v-icon>
        </v-btn>
      </template>
      <v-list>
        <v-list-item v-for="item in menus" :key="item.name" @click="$emit(`click-${item.action}`)">
          <v-list-item-title>{{ item.name }}</v-list-item-title>
          <v-list-item-avatar>
            <v-icon>{{ item.icon }}</v-icon>
          </v-list-item-avatar>
        </v-list-item>
      </v-list>
    </v-menu>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  data () {
    return {
      items: [
        {
          name: this.$t('order-center.print-pick-orders'),
          action: 'print-pick-orders',
          icon: 'mdi-printer',
          feature_flag: 'printing',
          perm: 'any'
        },
        {
          name: this.$t('etax.order-center-qr-menu'),
          action: 'print-etax-qrcode',
          icon: 'mdi-qrcode',
          feature_flag: 'any',
          perm: 'any'
        },
        {
          name: this.$t('export-excel'),
          action: 'export-pick-order-xlsx',
          icon: 'mdi-microsoft-excel',
          feature_flag: 'any',
          perm: 'export_pickorder'
        },
        {
          name: this.$t('export-etax-excel'),
          action: 'export-pick-order-detail-xlsx',
          icon: 'mdi-microsoft-excel',
          feature_flag: 'any',
          perm: 'export_pickorder_detail'
        }
      ]
    }
  },

  computed: {
    menus (): any[] {
      return this.items.filter((x) => {
        let has_perm = true
        if (x.perm !== 'any') {
          has_perm = this.$hasPerms(x.perm)
        }

        let has_feature_flag = true
        if (x.feature_flag !== 'any') {
          has_feature_flag = this.$hasCompanyFeatureFlag(x.feature_flag)
        }

        return has_perm && has_feature_flag
      })
    }
  }
})
</script>
