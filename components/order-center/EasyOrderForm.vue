<template>
  <v-form ref="form" @submit.prevent="$emit('submit')">
    <v-row dense>
      <!-- Customer -->
      <v-col cols="12" md="3" class="mb-3">
        <h4>ข้อมูลลูกค้า</h4>
      </v-col>
      <v-col cols="12" md="9" class="mb-6">
        <v-row dense>
          <v-col cols="6">
            <search-order-with-customer
              outlined
              item-title="order_customer"
              hide-details="auto"
              label="ชื่อลูกค้า"
              prepend-inner-icon="mdi-magnify"
              query-with="order_customer"
              update-field="customername"
              list-item-title="ค้นหาออเดอร์ด้วยชื่อลูกค้า"
              :disabled="disabledAll"
              :readonly="readOnly"
              :value="orderJson.customername"
              @update-order-json="$emit('update-order-form', $event)"
            />
          </v-col>
          <v-col cols="6">
            <search-order-with-customer
              :rules="[$rules.phoneNumberFormat]"
              outlined
              hide-details="auto"
              label="เบอร์โทรศัพท์"
              prepend-inner-icon="mdi-magnify"
              query-with="order_customerphone"
              update-field="customerphone"
              list-item-title="ค้นหาออเดอร์ด้วยเบอร์โทรศัพท์"
              :disabled="disabledAll"
              :readonly="readOnly"
              :value="orderJson.customerphone"
              @update-order-json="$emit('update-order-form', $event)"
            />
          </v-col>
        </v-row>
      </v-col>

      <!-- Shippin -->
      <v-col cols="12" md="3" class="mb-3">
        <h4>รายละเอียดการจัดส่ง</h4>
      </v-col>
      <v-col cols="12" md="9" class="mb-6">
        <v-row dense>
          <!-- <v-col cols="6">
            <v-text-field
              v-if="autoGenerateTracking"
              label="ชื่อผู้รับ *"
              outlined
              hide-details
              :rules="[$rules.required]"
              :value="orderJson.shippingname"
              @change="update({ shippingname: $event })"
            />
          </v-col>
          <v-col cols="6">
            <v-text-field
              v-if="autoGenerateTracking"
              class="mb-2"
              label="เบอร์โทรผู้รับ *"
              outlined
              hide-details
              :rules="[$rules.phoneNumberFormat]"
              :value="orderJson.shippingphone"
              @change="update({ shippingphone: $event })"
            />
          </v-col> -->
          <v-col cols="12">
            <v-textarea
              rows="2"
              outlined
              hide-details
              :label="`ที่อยู่จัดส่ง ${autoGenerateTracking ? '*' : ''}`"
              :disabled="disabledAll"
              :readonly="readOnly"
              :rules="autoGenerateTracking ? [$rules.required] : []"
              :value="orderJson.shippingaddress"
              @change="update({ shippingaddress: $event })"
            />
          </v-col>
          <v-col cols="12">
            <v-switch
              v-if="autoGenerateTracking"
              dense
              label="COD (Cash On Delivery)"
              :disabled="disabledAll"
              :readonly="readOnly"
              :input-value="orderJson.isCOD"
              @change="update({ isCOD: $event })"
            />
            <form-address
              v-if="autoGenerateTracking"
              :province="orderJson.shippingprovince"
              :district="orderJson.shippingdistrict"
              :subdistrict="orderJson.shippingsubdistrict"
              :postcode="orderJson.shippingpostcode"
              @update:address_detail="updateShippingData($event)"
            />
          </v-col>

          <v-col cols="6">
            <v-select
              v-if="autoGenerateTracking"
              outlined
              label="ขนส่ง *"
              hide-details
              :items="[
                'DBB_FLASH',
                'DBB_KERRY',
                'DBB_THAIPOST_REG',
                'DBB_THAIPOST_EMS',
              ]"
              :rules="[$rules.required]"
              :disabled="disabledAll"
              :readonly="readOnly"
              :value="orderJson.shippingchannel"
              @change="update({ shippingchannel: $event })"
            />
            <v-text-field
              v-else
              outlined
              label="ขนส่ง"
              hide-details
              :disabled="disabledAll"
              :readonly="readOnly"
              :value="orderJson.shippingchannel"
              @change="update({ shippingchannel: $event })"
            />
            <v-checkbox
              dense
              label="สร้าง Tracking Number อัตโนมัติ"
              hide-details
              :disabled="disabledAll"
              :readonly="readOnly"
              :value="autoGenerateTracking"
              @change="onAutoGenrateTrackingChange()"
            />
          </v-col>
          <v-col cols="6">
            <v-text-field
              outlined
              label="เลขที่ติดตามพัสดุ"
              hide-details
              :disabled="autoGenerateTracking || disabledAll"
              :readonly="readOnly"
              :value="
                autoGenerateTracking
                  ? '--- ระบบจะสร้างอัตโนมัติ ---'
                  : orderJson.trackingno
              "
              @change="update({ trackingno: $event })"
            />
          </v-col>
        </v-row>
      </v-col>

      <!-- Order & Tracking -->
      <v-col cols="12" md="3" class="mb-3">
        <h4>รายละเอียดคำสั่งซื้อ</h4>
      </v-col>
      <v-col cols="12" md="9" class="mb-6">
        <v-row dense>
          <v-col cols="6" md="4">
            <v-text-field
              label="เลขที่คำสั่งซื้อ *"
              placeholder="กรอก ESO-XXX เพื่อออกเลขออเดอร์อัตโนมัติ"
              hide-details
              outlined
              :rules="[$rules.required]"
              :error-messages="errors.number"
              :value="orderJson.number"
              :disabled="disabled.includes('number') || disabledAll"
              :readonly="readOnly"
              @change="update({ number: $event })"
            />
          </v-col>
          <v-col cols="6" md="4">
            <date-picker
              outlined
              hide-details
              label="วันที่สั่งซื้อ *"
              :value="orderJson.orderdateString"
              :disabled="disabled.includes('orderdateString') || disabledAll"
              :readonly="readOnly"
              :rules="[$rules.required]"
              @input="update({ orderdate: $event, orderdateString: $event })"
            />
          </v-col>
          <v-col cols="12" md="4">
            <v-combobox
              outlined
              hide-details
              label="ร้านค้า"
              :prefix="orderJson.saleschannel.startsWith('ESO-') ? '' : 'ESO-'"
              :items="shops"
              :disabled="disabledAll"
              :readonly="readOnly"
              :value="orderJson.saleschannel"
              @change="updateSaleChannel($event || '')"
            />
          </v-col>

          <v-col cols="12">
            <v-textarea
              outlined
              rows="1"
              label="หมายเหตุ"
              auto-grow
              :disabled="disabledAll"
              :readonly="readOnly"
              :value="orderJson.remark"
              @change="update({ remark: $event })"
            />
          </v-col>
        </v-row>
      </v-col>
    </v-row>

    <v-divider class="่my-5" />

    <product-detail-table
      :order-list="orderJson.list"
      :read-only="readOnly"
      :disabled-all="disabledAll"
      @update:order-list="update({ list: $event })"
    />

    <v-divider class="my-4" />
    <v-row justify="space-between">
      <v-col cols="12" sm="6" md="6">
        <div v-if="enable_etax && settings.company.ETAX_SELLER">
          <v-autocomplete
            v-if="!disabledAll"
            label="สาขา (ออกใบเสร็จ/ใบกำกับภาษี)"
            dense
            outlined
            hide-details=""
            :items="branches"
            item-value="number"
            item-text="name"
            :value="orderJson?.extra?.branch_number"
            @change="updateExtra({ branch_number: $event })"
          />
          <div class="mt-3 ml-1">
            <!-- <h4>ข้อมูลออกใบเสร็จ/ใบกำกับภาษี</h4> -->
            {{ settings.company.ETAX_SELLER.name }}<br>

            <div>
              <label style="width: 40px; white-space: nowrap">ที่อยู๋:</label>
              <span v-if="selected_branch">{{ selected_branch.address }}</span><br>
            </div>
          </div>
        </div>
      </v-col>
      <v-col cols="12" sm="6" md="4">
        <order-summary
          :order-json="orderJson"
          :read-only="readOnly"
          :disabled="disabledAll"
          @update:order-json="update($event)"
        />
      </v-col>
    </v-row>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import DatePicker from '../global/DatePicker.vue'
import FormAddress from '~/components/settings/shipping/FormAddress.vue'
import SearchOrderWithCustomer from '~/components/order-center/SearchOrderWithCustomer.vue'
import ProductDetailTable from '~/components/products/ProductDetailTable.vue'
import OrderSummary from '~/components/order-center/OrderSummary.vue'
import { FEATURE_FLAGS, OrderJson } from '~/models'
import { EtaxBranch, SettingState } from '~/store/settings'

interface AddressDetail {
  id: number;
  province: string;
  province_en: string;
  district: string;
  district_en: string;
  subdistrict: string;
  subdistrict_en: string;
  postcode: string;
}

export default Vue.extend({
  components: {
    FormAddress,
    SearchOrderWithCustomer,
    ProductDetailTable,
    OrderSummary,
    DatePicker
  },
  props: {
    orderJson: {
      type: Object,
      required: true
    } as Vue.PropOptions<OrderJson>,

    errors: {
      type: Object,
      default: () => ({})
    },

    disabled: {
      type: Array,
      default: () => [],
      required: false
    },

    autoGenerateTracking: {
      type: Boolean,
      default: false,
      required: false
    },

    readOnly: {
      type: Boolean,
      default: false,
      required: false
    },

    disabledAll: {
      type: Boolean,
      default: false,
      required: false
    }
  },

  data () {
    return {
      shops: [] as string[]
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    },
    branches (): EtaxBranch[] {
      const branches = this.settings.company.ETAX_BRANCHES.map(x => ({
        ...x,
        name: `${x.number} - ${x.name}`
      }))

      if (branches.length === 0) {
        branches.push({
          number: this.settings.company.ETAX_SELLER.branch_id,
          name: this.settings.company.ETAX_SELLER.seller_branch_name,
          address: this.settings.company.ETAX_SELLER.address
        })
      }

      return branches
    },
    selected_branch (): EtaxBranch | undefined {
      const extra = this.orderJson.extra || {}
      return this.branches.find(x => x.number === extra.branch_number)
    },
    enable_etax (): boolean {
      return this.$hasCompanyFeatureFlag(FEATURE_FLAGS.ETAX)
    }
  },

  mounted () {
    this.getShopList()
  },
  methods: {
    validate (): boolean {
      return (this.$refs.form as any).validate()
    },

    update (data: any) {
      this.$emit('update:order-json', { ...this.orderJson, ...data })
    },
    updateExtra (data: any) {
      if (data.branch_number && data.branch_number === 'สำนักงานใหญ่') {
        data.branch_number = '00000'
      }
      this.update({ extra: { ...this.orderJson.extra, ...data } })
    },

    onAutoGenrateTrackingChange () {
      if (this.autoGenerateTracking) {
        // Note: Close
        this.update({
          trackingno: '',
          shippingchannel: '',
          shippingprovince: '',
          shippingdistrict: '',
          shippingsubdistrict: '',
          shippingpostcode: ''
        })
      } else {
        // Note: Open
        this.update({
          shippingname: this.orderJson.customername,
          shippingphone: this.orderJson.customerphone
        })
      }

      this.$emit('toggle-gen-tracking', !this.autoGenerateTracking)
    },

    updateShippingData (address_detail: AddressDetail) {
      if (!address_detail) {
        return
      }

      this.update({
        shippingprovince: address_detail.province,
        shippingdistrict: address_detail.district,
        shippingsubdistrict: address_detail.subdistrict,
        shippingpostcode: address_detail.postcode
      })
    },

    async getShopList () {
      this.shops = (
        await this.$axios.get('/api/companies/choices/saleschannel_choices/')
      ).data
    },

    updateSaleChannel (new_saleschannel: string) {
      let easy_order_salechannel = new_saleschannel

      // IF new_salechannel have don't have prefix ESO
      if (easy_order_salechannel.startsWith('ESO-') === false) {
        easy_order_salechannel = `ESO-${new_saleschannel}`
      }

      this.update({ saleschannel: easy_order_salechannel })
    }
  }
})
</script>

<style scoped>
table {
  border-collapse: collapse;
}

th {
  text-align: left;
}

th.fit {
  width: 1%;
  white-space: nowrap;
}
</style>
