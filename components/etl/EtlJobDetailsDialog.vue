<template>
  <v-dialog
    :value="value"
    max-width="1200"
    scrollable
    @input="$emit('input', $event)"
  >
    <v-card v-if="job">
      <v-card-title class="headline">
        <v-icon left>
          mdi-database-import
        </v-icon>
        ETL Job Details
        <v-spacer />
        <v-btn
          icon
          @click="$emit('close')"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>

      <v-card-text>
        <v-container>
          <!-- Basic Information -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">
                Basic Information
              </h3>
            </v-col>

            <v-col cols="12" md="6">
              <v-list dense>
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-title>Job ID</v-list-item-title>
                    <v-list-item-subtitle>{{ job.uuid }}</v-list-item-subtitle>
                  </v-list-item-content>
                </v-list-item>

                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-title>Company</v-list-item-title>
                    <v-list-item-subtitle>{{ job.company_name }}</v-list-item-subtitle>
                  </v-list-item-content>
                </v-list-item>

                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-title>Status</v-list-item-title>
                    <v-list-item-subtitle>
                      <v-chip
                        :color="getStatusColor(job.status)"
                        small
                        text-color="white"
                      >
                        {{ getStatusText(job.status) }}
                      </v-chip>
                    </v-list-item-subtitle>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
            </v-col>

            <v-col cols="12" md="6">
              <v-list dense>
                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-title>Date Range</v-list-item-title>
                    <v-list-item-subtitle>
                      {{ formatDate(job.start_date) }} to {{ formatDate(job.end_date) }}
                    </v-list-item-subtitle>
                  </v-list-item-content>
                </v-list-item>

                <v-list-item>
                  <v-list-item-content>
                    <v-list-item-title>Created At</v-list-item-title>
                    <v-list-item-subtitle>{{ formatDateTime(job.created_at) }}</v-list-item-subtitle>
                  </v-list-item-content>
                </v-list-item>

                <v-list-item v-if="job.completed_at">
                  <v-list-item-content>
                    <v-list-item-title>Completed At</v-list-item-title>
                    <v-list-item-subtitle>{{ formatDateTime(job.completed_at) }}</v-list-item-subtitle>
                  </v-list-item-content>
                </v-list-item>
              </v-list>
            </v-col>
          </v-row>

          <v-divider class="my-4" />

          <!-- Performance Summary -->
          <v-row v-if="job.performance_summary && Object.keys(job.performance_summary).length > 0">
            <v-col cols="12">
              <h3 class="text-h6 mb-3">
                Performance Summary
              </h3>
            </v-col>

            <v-col v-if="job.performance_summary.total_duration_ms" cols="12" md="3">
              <v-card outlined>
                <v-card-text class="text-center">
                  <div class="text-h4 font-weight-bold text-primary">
                    {{ formatDuration(job.performance_summary.total_duration_ms) }}
                  </div>
                  <div class="text-caption text--secondary">
                    Total Duration
                  </div>
                </v-card-text>
              </v-card>
            </v-col>

            <v-col v-if="job.performance_summary.total_records_processed" cols="12" md="3">
              <v-card outlined>
                <v-card-text class="text-center">
                  <div class="text-h4 font-weight-bold text-success">
                    {{ job.performance_summary.total_records_processed.toLocaleString() }}
                  </div>
                  <div class="text-caption text--secondary">
                    Total Records
                  </div>
                </v-card-text>
              </v-card>
            </v-col>

            <v-col v-if="job.performance_summary.peak_memory_usage_mb" cols="12" md="3">
              <v-card outlined>
                <v-card-text class="text-center">
                  <div class="text-h4 font-weight-bold text-warning">
                    {{ job.performance_summary.peak_memory_usage_mb }}MB
                  </div>
                  <div class="text-caption text--secondary">
                    Peak Memory
                  </div>
                </v-card-text>
              </v-card>
            </v-col>

            <v-col v-if="job.performance_summary.operations_count" cols="12" md="3">
              <v-card outlined>
                <v-card-text class="text-center">
                  <div class="text-h4 font-weight-bold text-info">
                    {{ job.performance_summary.operations_count }}
                  </div>
                  <div class="text-caption text--secondary">
                    Operations
                  </div>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>

          <v-divider class="my-4" />

          <!-- Results -->
          <v-row>
            <v-col cols="12">
              <h3 class="text-h6 mb-3">
                Results
              </h3>
            </v-col>

            <v-col cols="12" md="4">
              <v-card outlined>
                <v-card-text class="text-center">
                  <div class="text-h4 font-weight-bold text-primary">
                    {{ job.orders_processed.toLocaleString() }}
                  </div>
                  <div class="text-caption text--secondary">
                    Orders Processed
                  </div>
                </v-card-text>
              </v-card>
            </v-col>

            <v-col cols="12" md="4">
              <v-card outlined>
                <v-card-text class="text-center">
                  <div class="text-h4 font-weight-bold text-success">
                    {{ job.order_items_processed.toLocaleString() }}
                  </div>
                  <div class="text-caption text--secondary">
                    Order Items Processed
                  </div>
                </v-card-text>
              </v-card>
            </v-col>

            <v-col cols="12" md="4">
              <v-card outlined>
                <v-card-text class="text-center">
                  <div class="text-h4 font-weight-bold text-info">
                    {{ (job.orders_processed + job.order_items_processed).toLocaleString() }}
                  </div>
                  <div class="text-caption text--secondary">
                    Total Records
                  </div>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>

          <v-divider class="my-4" />

          <!-- Error Message -->
          <v-row v-if="job.error_message">
            <v-col cols="12">
              <h3 class="text-h6 mb-3">
                Error Information
              </h3>
              <v-alert
                type="error"
                outlined
              >
                {{ job.error_message }}
              </v-alert>
            </v-col>
          </v-row>

          <!-- Log Summary -->
          <v-row v-if="job.log_summary && Object.keys(job.log_summary).length > 0">
            <v-col cols="12">
              <h3 class="text-h6 mb-3">
                Log Summary
              </h3>
              <div class="d-flex flex-wrap">
                <v-chip
                  v-for="(count, level) in job.log_summary"
                  :key="level"
                  :color="getLogLevelColor(level)"
                  class="mr-2 mb-2"
                  small
                  text-color="white"
                >
                  {{ level }}: {{ count }}
                </v-chip>
              </div>
            </v-col>
          </v-row>

          <!-- Detailed Logs -->
          <v-row v-if="job.logs && job.logs.length > 0">
            <v-col cols="12">
              <h3 class="text-h6 mb-3">
                Execution Logs
              </h3>
              <v-expansion-panels>
                <v-expansion-panel
                  v-for="(log, index) in job.logs"
                  :key="index"
                >
                  <v-expansion-panel-header>
                    <div class="d-flex align-center">
                      <v-chip
                        :color="getLogLevelColor(log.level)"
                        small
                        text-color="white"
                        class="mr-3"
                      >
                        {{ log.level }}
                      </v-chip>
                      <span class="text-caption text--secondary mr-3">
                        {{ formatDateTime(log.timestamp) }}
                      </span>
                      <span>{{ log.message }}</span>
                    </div>
                  </v-expansion-panel-header>

                  <v-expansion-panel-content>
                    <div v-if="log.context">
                      <strong>Context:</strong>
                      <pre class="text-caption">{{ JSON.stringify(log.context, null, 2) }}</pre>
                    </div>

                    <div v-if="log.performance" class="mt-2">
                      <strong>Performance:</strong>
                      <pre class="text-caption">{{ JSON.stringify(log.performance, null, 2) }}</pre>
                    </div>

                    <div v-if="log.metadata" class="mt-2">
                      <strong>Metadata:</strong>
                      <pre class="text-caption">{{ JSON.stringify(log.metadata, null, 2) }}</pre>
                    </div>
                  </v-expansion-panel-content>
                </v-expansion-panel>
              </v-expansion-panels>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>

      <v-card-actions>
        <v-spacer />
        <v-btn
          color="primary"
          text
          @click="$emit('close')"
        >
          Close
        </v-btn>
      </v-card-actions>
    </v-card>

    <!-- Loading State -->
    <v-card v-else>
      <v-card-text class="text-center py-12">
        <v-progress-circular
          indeterminate
          color="primary"
          size="64"
        />
        <div class="mt-4">
          Loading job details...
        </div>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'

interface ETLJobDetail {
  uuid: string
  company_name: string
  status: string
  start_date: string
  end_date: string
  created_at: string
  started_at: string | null
  completed_at: string | null
  orders_processed: number
  order_items_processed: number
  error_message: string | null
  duration_ms: number | null
  log_summary: Record<string, number>
  performance_summary: Record<string, any>
  logs: any[]
  error_logs: any[]
  logs_by_operation: Record<string, any[]>
  created_by: string | null
  source: string
  job_type: string
}

export default Vue.extend({
  props: {
    value: {
      type: Boolean,
      default: false
    },
    jobUuid: {
      type: String,
      default: null
    }
  },

  data () {
    return {
      job: null as ETLJobDetail | null,
      loading: false
    }
  },

  watch: {
    value (newVal) {
      if (newVal && this.jobUuid) {
        this.loadJobDetails()
      }
    },

    jobUuid (newVal) {
      if (newVal && this.value) {
        this.loadJobDetails()
      }
    }
  },

  methods: {
    async loadJobDetails () {
      if (!this.jobUuid) { return }

      this.loading = true
      this.job = null

      try {
        const response = await this.$axios.get(`/api/etl/jobs/${this.jobUuid}/`)
        this.job = response.data
      } catch (error) {
        console.error('Error loading job details:', error)
        this.$snackbar('error', 'Failed to load job details')
      } finally {
        this.loading = false
      }
    },

    getStatusColor (status: string): string {
      switch (status) {
        case 'completed': return 'success'
        case 'running': return 'warning'
        case 'failed': return 'error'
        case 'pending': return 'info'
        default: return 'grey'
      }
    },

    getStatusText (status: string): string {
      switch (status) {
        case 'completed': return 'Completed'
        case 'running': return 'Running'
        case 'failed': return 'Failed'
        case 'pending': return 'Pending'
        default: return status
      }
    },

    getLogLevelColor (level: string): string {
      switch (level) {
        case 'ERROR': return 'error'
        case 'WARNING': return 'warning'
        case 'INFO': return 'info'
        case 'DEBUG': return 'grey'
        default: return 'grey'
      }
    },

    formatDate (dateString: string): string {
      return new Date(dateString).toLocaleDateString()
    },

    formatDateTime (dateString: string): string {
      return new Date(dateString).toLocaleString()
    },

    formatDuration (ms: number): string {
      if (ms < 1000) {
        return `${ms}ms`
      } else if (ms < 60000) {
        return `${(ms / 1000).toFixed(1)}s`
      } else {
        return `${(ms / 60000).toFixed(1)}m`
      }
    }
  }
})
</script>

<style scoped>
pre {
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
