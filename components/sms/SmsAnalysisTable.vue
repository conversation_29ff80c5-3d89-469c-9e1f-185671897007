<template>
  <v-card outlined :style="error ? 'border-color: red' : ''">
    <v-card-text>
      <v-row v-if="error">
        <v-col>
          <v-alert v-if="mode == 'excel'" type="error">
            {{ $t('sms.please-fix-error-in-excel-file') }}
          </v-alert>
          <v-alert v-if="mode == 'quick'" type="error">
            {{ $t('sms.please-fix-error-in-quick-form') }}
          </v-alert>
        </v-col>
      </v-row>
      <v-row v-if="!error" justify="end">
        <v-spacer />
        <v-col cols="12" md="3">
          <div>
            <table style="float: right" class="pr-3">
              <tbody>
                <tr>
                  <td class="font-weight-bold text-right pr-3">
                    {{ $t('sms.recipients') }}
                  </td>
                  <td class="font-weight-bold text-right">
                    {{ totalRecipient }}
                  </td>
                  <td class="pl-2">
                    {{ $t('sms.number') }}
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-bold text-right pr-3">
                    {{ $t('sms.number-messege') }}
                  </td>
                  <td class="font-weight-bold text-right">
                    {{ items.length }}
                  </td>
                  <td class="pl-2">
                    {{ $t('sms.messege') }}
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-bold text-right pr-3">
                    {{ $t('sms.amount') }}
                  </td>
                  <td class="font-weight-bold text-right">
                    {{ totalCredit }}
                  </td>
                  <td class="pl-2">
                    {{ $t('sms.credit') }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </v-col>
      </v-row>

      <v-data-table :headers="headers" :items="items">
        <template #item.index="props">
          <span v-if="mode=='excel'">
            {{ props.index + 2 }}
          </span>
          <span v-else-if="mode == 'quick'">
            {{ props.index + 1 }}
          </span>
        </template>
      </v-data-table>
    </v-card-text>
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import _ from 'lodash'

export default Vue.extend({
  props: {
    items: {
      type: Array,
      default: () => []
    },
    error: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'excel'
    }
  },

  data () {
    return {
      headers: [
        { text: this.$t('sms.row'), value: 'index' },
        { text: this.$t('sms.phone-number'), value: 'to' },
        { text: this.$t('sms.messege-text'), value: 'text' },
        { text: this.$t('sms.note'), value: 'remark' },
        { text: this.$t('sms.amount-credit'), value: 'analysis.messages' }
      ]
    }
  },

  computed: {
    totalCredit () {
      return _.sumBy(this.items, 'analysis.messages')
    },

    totalRecipient () {
      return _.uniqBy(this.items, 'to').length
    }
  }
})
</script>

<style>
.border-error {
  border-color: 'red' !important;
}
</style>
