<template>
  <div>
    <v-btn to="/sms/excel" :color="currentPath == '/sms/excel' ? 'primary': ''">
      ส่ง SMS ด้วย Excel
    </v-btn>
    <v-btn to="/sms/quick" :color="currentPath == '/sms/quick' ? 'primary': ''" class="mx-3">
      ส่ง SMS ด่วน
    </v-btn>
    <v-btn to="/sms/api" :color="currentPath == '/sms/api' ? 'primary': ''">
      ส่ง SMS ผ่าน API
    </v-btn>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  computed: {
    currentPath (): string {
      return this.$route.path
    }
  }
})
</script>

<style lang="scss" scoped>

</style>
