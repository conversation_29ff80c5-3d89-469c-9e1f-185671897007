<template>
  <v-row class="pa-3" justify="space-between" align="end">
    <h2 class="mb-1">
      {{ title }}
    </h2>
    <div class="caption pb-2 text-right" style="line-height: 110%" :title="`Pending: ${sms_credit_onhold}`">
      <div>SMS Credit Balance: <label>{{ sms_balance }}</label></div>
    </div>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    title: {
      type: String,
      default: ''
    }
  },

  data () {
    return {
      sms_balance: 0,
      sms_credit_onhold: 0
    }
  },

  mounted () {
    this.getSmsCredit()
    this.$nuxt.$off('update-sms-credit', this.getSmsCredit)
  },

  beforeDestroy () {
    this.$nuxt.$off('update-sms-credit')
  },

  methods: {
    getSmsCredit () {
      this.$axios.get('/api/sms/credit-balance/').then((response) => {
        this.sms_balance = response.data.sms_balance
        this.sms_credit_onhold = response.data.sms_credit_onhold
      })
    }
  }
})
</script>

<style scoped>
label {
  display: inline-block;
  font-weight: bold;
  min-width: 40px;
}
</style>
