<template>
  <v-dialog
    max-width="800"
    :value="value"
    @input="close()"
  >
    <v-card data-test-id="v-card-setting-dialog">
      <v-card-title class="headline grey lighten-2">
        {{ $t('setting') }}
        <v-spacer />
        <v-btn data-test="v-btn-close-dialog" icon @click="close()">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>

      <v-card-text>
        <!-- Record Option Settings -->
        <v-row wrap dense class="pt-3">
          <v-col cols="12">
            <h3>{{ $t('setting.record') }}</h3>
          </v-col>

          <v-col
            id="v-select-camera"
            cols="12"
            md="6"
          >
            <v-select
              data-test="v-select-camera"
              filled
              :label="$t('setting.camera')"
              item-text="label"
              item-value="deviceId"
              return-object
              hide-details
              :items="devices"
              :value="settings.local.deviceId"
              @input="setLocalSetting({deviceId: $event.deviceId})"
            >
              <template #no-data>
                <v-list-item @click.stop="requestUserMediaPermission()">
                  <v-list-item-content>
                    <v-list-item-title>{{ $t('setting.please-allow-access-to-camera') }}</v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </template>
            </v-select>
          </v-col>

          <v-col
            id="v-select-resolution"
            cols="12"
            md="6"
          >
            <v-select
              data-test-id="v-select-resolution"
              filled
              :label="$t('setting.resolution')"
              hide-details
              :items="resolutions"
              :value="settings.local.resolution"
              @input="setResolution($event)"
            />
          </v-col>

          <v-col cols="12" md="6">
            <v-select
              id="v-select-filetype"
              data-test="v-select-filetype"
              filled
              :label="$t('setting.filetype')"
              hide-details=""
              :items="file_types"
              :value="settings.local.filetype"
              @input="setLocalSetting({filetype: $event, speed: '1x'})"
              @change="onFiletypeChange($event)"
            />
            <progress-line
              v-if="progress.load_ffmpeg.status"
              class="mt-2"
              :status="progress.load_ffmpeg.status"
              :text="progress.load_ffmpeg.text"
            />
            <progress-line
              v-if="progress.test_ffmpeg.status"
              :status="progress.test_ffmpeg.status"
              :text="progress.test_ffmpeg.text"
            >
              <a v-if="progress.test_ffmpeg.status === 'success'" download :href="progress.test_ffmpeg.result">{{ $t('setting.download-for-testing') }}</a>
            </progress-line>
          </v-col>
          <v-col cols="12" md="6">
            <v-select
              data-test-id="v-select-playback-speed"
              filled
              :label="$t('setting.speed')"
              hide-details=""
              :disabled="settings.local.filetype !== 'mp4'"
              :items="['1x', '2x']"
              :value="settings.local.speed"
              @input="setLocalSetting({speed: $event})"
              @change="onFiletypeChange(settings.local.filetype)"
            />
          </v-col>

          <v-col cols="12">
            <v-checkbox
              class="mt-0 pt-0"
              data-test-id="v-checkbox-enable-sound"
              hide-details
              :label="$t('setting.play-sound-when-start-or-stop-recording')"
              :input-value="settings.local.enableSound"
              @change="setLocalSetting({enableSound: $event})"
            />
          </v-col>

          <v-col cols="12">
            <v-checkbox
              class="mt-0 pt-0"
              data-test-id="v-checkbox-enable-qrcode-pickitem"
              hide-details
              :label="$t('settings.pick-item-qr-code-enable')"
              :input-value="settings.local.pickItemShowQRCode"
              @change="setLocalSetting({pickItemShowQRCode: $event})"
            />
          </v-col>

          <!-- Pre-record action setting -->
          <template v-if="!!settings.company.PRINTNODE_API_KEY">
            <!-- Print Airway Bill -->
            <v-col cols="12">
              <v-checkbox
                class="mt-0 pt-0"
                data-test-id="v-checkbox-auto-print-airway-bill"
                :label="$t('setting.print-airway-bill-when-start-recording')"
                hide-details
                :input-value="settings.local.printAirwayBillBeforeRecord"
                @change="setLocalSetting({printAirwayBillBeforeRecord: $event})"
              />
            </v-col>
            <v-col v-if="settings.local.printAirwayBillBeforeRecord" cols="12">
              <printnode-printer-select
                filled
                hide-details
                :value="settings.local.airwayBillPrinterId"
                @input="setLocalSetting({airwayBillPrinterId: $event})"
              />
            </v-col>

            <!-- Print Receipt -->
            <v-col cols="12">
              <v-checkbox
                class="mt-0 pt-0"
                data-test-id="v-checkbox-auto-print-receipt"
                :label="$t('setting.print-receipt-when-start-recording')"
                hide-details
                :input-value="settings.local.printReceiptBeforeRecord"
                @change="setLocalSetting({printReceiptBeforeRecord: $event})"
              />
            </v-col>
            <v-col v-if="settings.local.printReceiptBeforeRecord" cols="12">
              <printnode-printer-select
                filled
                hide-details
                :value="settings.local.receiptPrinterId"
                @input="setLocalSetting({receiptPrinterId: $event})"
              />
            </v-col>

            <!-- Print Weight Slip -->
            <v-col cols="12">
              <v-checkbox
                class="mt-0 pt-0"
                data-test-id="v-checkbox-auto-print-airway-bill"
                :label="$t('setting.print-weight-slip-after-recording')"
                hide-details
                :input-value="settings.local.printWeightSlipAfterRecord"
                @change="setLocalSetting({printWeightSlipAfterRecord: $event})"
              />
            </v-col>
            <v-col v-if="settings.local.printWeightSlipAfterRecord" cols="12">
              <printnode-printer-select
                filled
                hide-details
                :value="settings.local.weightSlipPrinterId"
                @input="setLocalSetting({weightSlipPrinterId: $event})"
              />
            </v-col>
          </template>
        </v-row>

        <!-- Weight Scale -->
        <v-row v-if="!!settings.company.PRINTNODE_API_KEY" wrap dense class="mt-4 pt-4">
          <v-col cols="12">
            <h3>ตั้งค่าเครื่องชั่งน้ำหนัก</h3>
          </v-col>
          <v-col cols="12" md="9">
            <printnode-computer-select
              filled
              hide-details
              :value="settings.local.weightScaleComputerId"
              @input="setLocalSetting({ weightScaleComputerId: $event })"
            />
            <div v-if="scale.device" class="mt-2">
              <label class="w-100">เครื่องชั่ง</label>: {{ scale.device.device_name }} ({{ scale.device.device_num }})<br>
              <label class="w-100">นำ้หนัก</label>: {{ weight_display }}
            </div>
          </v-col>
          <v-col cols="12" md="3">
            <v-btn block x-large class="mt-1" @click="testConnectWeightScale(settings.local.weightScaleComputerId)">
              <v-icon left>
                mdi-scale
              </v-icon>
              เชื่อมต่อ
            </v-btn>
          </v-col>
        </v-row>

        <!-- Video Overlay -->
        <v-row wrap dense class="mt-4 pt-4">
          <v-col cols="12">
            <h3>ตั้งค่าวิดีโอ Overlay</h3>
            <div>เพิ่ม Overlay ด้านล่างของวิดีโอเพื่อแสดงข้อมูลต่าง ๆ เพิ่มเติม รองรับเฉพาะการอัดวิดีโอความละเอียด 1280x720 ลงไปเท่านั้น (แนะนำให้ใช้คอม spec สูง ติดต่อ Dobybot Support ก่อนเปิดใช้งาน)</div>
          </v-col>
          <v-col cols="12" md="12">
            <v-checkbox
              label="แสดง Overlay ด้านล่างของวิดีโอ"
              hide-details
              dense
              :input-value="settings.local.enableOverlay"
              :disabled="settings.local.resolution === '1920x1080'"
              @change="setLocalSetting({ enableOverlay: $event })"
            />
            <div class="pl-8">
              <v-checkbox
                label="แสดงวันที่ / เวลา, ชื่อผู้แพ็คสินค้า, หมายเลขคำสั่งซื้อ"
                hide-details
                dense
                :disabled="!settings.local.enableOverlay"
                :input-value="settings.local.overlayShowDatetime"
                @change="setLocalSetting({ overlayShowDatetime: $event })"
              />
              <v-checkbox
                label="แสดงน้ำหนัก"
                hide-details
                dense
                :disabled="!settings.local.enableOverlay"
                :input-value="settings.local.overlayShowWeight"
                @change="setLocalSetting({ overlayShowWeight: $event })"
              />
              <v-checkbox
                label="แสดง SKU สินค้าที่สแกน"
                hide-details
                dense
                :disabled="!settings.local.enableOverlay"
                :input-value="settings.local.overlayShowScannedSKU"
                @change="setLocalSetting({ overlayShowScannedSKU: $event })"
              />
            </div>
          </v-col>
        </v-row>

        <!-- Cheerup Sound -->
        <v-row wrap dense class="mt-4 pt-4">
          <v-col cols="12">
            <h3>ตั้งค่าวิดีโอ Cheerup Sound</h3>
            <div>เล่นเสียงชมเชยเมื่อทำการสแกนสินค้าครบทุกรายการ</div>
          </v-col>
          <v-col cols="12" md="12">
            <v-checkbox
              label="เล่นเสียง Cheerup"
              hide-details
              dense
              :input-value="settings.local.enableCheerupSound"
              @change="setLocalSetting({ enableCheerupSound: $event })"
            />

            <div class="ml-8">
              <cheerup-sound-select
                :value="settings.local.selectedCheerupSounds"
                :disabled="!settings.local.enableCheerupSound"
                @input="setLocalSetting({ selectedCheerupSounds: $event })"
              />
            </div>
          </v-col>
        </v-row>

        <!-- Storage Settings -->
        <v-row wrap dense class="mt-4 pt-4">
          <v-col cols="12">
            <h3>{{ $t('setting.storage') }}</h3>
          </v-col>

          <v-col cols="12">
            <v-checkbox
              class="mt-0"
              data-test-id="v-checkbox-auto-delete-old-videos"
              :label="$t('setting.automatically-delete-uploaded-video')"
              :hint="$t('setting.delete-only-video-on-this-device-not-on-google-drive')"
              persistent-hint
              :input-value="settings.local.automaticallyRemoveOldVideo"
              @change="setLocalSetting({automaticallyRemoveOldVideo: $event})"
            />
          </v-col>

          <v-col v-if="settings.local.automaticallyRemoveOldVideo" cols="12">
            <v-text-field
              dense
              filled
              data-test-id="v-text-field-storage-duration"
              :label="$t('setting.storage-period')"
              :hint="`วิดีโอที่อัพโหลดแล้ว และ มีอายุมากกว่า ${settings.local.storageDuration} วันจะถูกลบอัตโนมัติ`"
              :value="settings.local.storageDuration"
              @input="setLocalSetting({storageDuration: $event})"
            />
          </v-col>
        </v-row>

        <!-- Google Drive Setting -->
        <v-row dense class="mt-4 pt-4">
          <v-col cols="12">
            <h3>{{ $t('setting.google-drive') }}</h3>
            <span v-if="settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID">
              <div>
                <v-icon small color="success" />

                {{ $t('setting.using-share-drive') }} <span v-if="$auth.user.is_superuser">{{ settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID }}</span><br>
                <ul class="pl-8" style="max-height: 75px; overflow-y: scroll">
                  <li v-for="email in settings.company.RECORD_GMAIL_ALLOWED_LIST" :key="email">
                    {{ email }}
                  </li>
                </ul>
              </div>
            </span>
            <div v-if="!gapiAuth">
              <a data-test-id="a-gapi-signin" href="!#" @click.prevent="gapiSignIn()">เชื่อมต่อ Google Drive</a>
            </div>
            <div v-if="gapiAuth">
              <v-icon small color="success">
                mdi-check
              </v-icon>
              {{ $t('setting.connected-to-google-drive', [gapiUser.email]) }}
              <a data-test-id="a-gapi-signout" href="!#" class="black--text" @click.prevent="gapiSignOut()">ยกเลิกการเชื่อมต่อ</a>
            </div>
            <div v-if="gapiAuth && storage && !settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID">
              <span data-test-id="span-total-storage">พื้นที่เก็บข้อมูลทั้งหมด: {{ storage.limitGB }}</span>,
              <span data-test-id="span-used-storage">ใช้ไปแล้ว: {{ storage.usageGB }}</span>,
              <span data-test-id="span-available-storage">ใช้งานได้: {{ storage.availableGB }}</span>
            </div>
            <div v-if="gapiAuth">
              <br>
              <span data-test-id="span-root-folder">
                <b>Root Folder</b>:
                <span v-if="settings.local.googleDriveRootFolder">
                  {{ settings.local.googleDriveRootFolder.name }}
                  <a
                    v-if="$hasGroup('owner')"
                    target="_blank"
                    :href="`https://drive.google.com/drive/folders/${settings.local.googleDriveRootFolder.id}`"
                  >view</a>
                </span>
                <span v-else>-</span>
              </span>
              <br>
              <span data-test-id="span-today-folder">
                <b>Today Folder</b>:
                <span v-if="settings.local.googleDriveTodayFolder">
                  {{ settings.local.googleDriveRootFolder.name }}/{{ settings.local.googleDriveTodayFolder.name }}
                  <a
                    v-if="$hasGroup('owner')"
                    target="_blank"
                    :href="`https://drive.google.com/drive/folders/${settings.local.googleDriveTodayFolder.id}`"
                  >view</a>
                </span>
                <span v-else>-</span>
              </span>
            </div>
          </v-col>
        </v-row>

        <!-- Retry Setting -->
        <v-row dense class="mt-4 pt-4">
          <v-col cols="12">
            <h3>{{ $t('setting.retry-upload') }}</h3>
          </v-col>
          <v-col cols="12">
            <v-checkbox
              class="mt-0"
              data-test-id="v-checkbox-retry-upload"
              :label="$t('setting.automatically-retry-upload-video')"
              :hint="$t('home.retry-upload-strategy')"
              persistent-hint
              :input-value="settings.local.automaticallyRetryUploadVideo"
              @change="setLocalSetting({automaticallyRetryUploadVideo: $event})"
            />
          </v-col>
        </v-row>

        <v-divider class="my-5" />

        <v-row>
          <v-col cols="8">
            <h2>ตั้งค่าขั้นสูง</h2>
          </v-col>
          <v-col cols="4">
            <v-btn
              block
              small
              color="primary"
              data-test="v-btn-enable-sound-recording"
              outlined
              @click="toggleLock()"
            >
              <v-icon left>
                {{ lock ? 'mdi-lock' : 'mdi-lock-open-variant' }}
              </v-icon>
              {{ lock ? 'คลิกเพื่อปลดล็อก' : 'คลิกเพื่อล็อก' }}
            </v-btn>
          </v-col>
        </v-row>
        <v-row dense wrap>
          <v-col cols="12">
            <v-checkbox
              :disabled="lock"
              class="mt-1"
              hide-details
              :label="$t('setting.record_audio')"
              :input-value="settings.local.recordAudio"
              @change="setLocalSetting({recordAudio: $event})"
            />
          </v-col>
        </v-row>
      </v-card-text>

      <v-divider />
      <v-card-actions>
        <v-btn
          data-test="v-btn-close-setting-dialog"
          color="primary"
          text
          @click="close()"
        >
          ปิด
        </v-btn>
        <v-spacer />
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex'
import moment from 'moment'
import { createFFmpeg } from '@ffmpeg/ffmpeg'
import ProgressLine from './progress/ProgressLine.vue'
import PrintnodeComputerSelect from './printers/PrintnodeComputerSelect.vue'
import CheerupSoundSelect from './settings-local/CheerupSoundSelect.vue'
import { SettingState } from '~/store/settings'
import { IStorageQuota, createFolder, getStorageQuota, listFoldersInMyDrive, findFolderInShareDrive, findFolderInFolder } from '~/api/googledrive'
import printnodePrinterSelect from '~/components/printers/PrintnodePrinterSelect.vue'
import { isMobile } from '~/utils/devices'
import { formatWeight, transcodeMP4 } from '~/utils/video'
import { PrintnodeScale, PrintnodeWebsocket } from '~/models'
import { setupPrintnodeWebsocket, waitUntil } from '~/utils'

export default Vue.extend({
  components: {
    printnodePrinterSelect,
    ProgressLine,
    PrintnodeComputerSelect,
    CheerupSoundSelect
  },

  props: {
    value: { type: Boolean, required: true }
  },

  data () {
    return {
      devices: [] as MediaDeviceInfo[],
      resolutions: [
        '640x360',
        '1280x720',
        '1920x1080'
      ],

      file_types: ['webm', 'mp4', 'webm-raw'],

      storage: null as IStorageQuota | null,

      progress: {
        load_ffmpeg: {
          status: null as string | null,
          text: ''
        },
        test_ffmpeg: {
          status: null as string | null,
          text: '',
          result: null as string | null
        }
      },

      printnode_ws: null as PrintnodeWebsocket | null,
      scale: {
        device: null as PrintnodeScale | null,
        weight: 0.0
      },

      lock: true
    }
  },

  computed: {
    ...mapState('gapi', { gapiAuth: 'isAuth' }),
    // ...mapState('settings', { localSetting: 'local', companySetting: 'company' }),

    ...mapGetters('gapi', {
      gapiToken: 'token',
      gapiUser: 'currentUser'
    }),

    settings (): SettingState {
      return this.$store.state.settings
    },

    weight_display (): string {
      return formatWeight(this.scale.weight)
    }
  },

  watch: {
    gapiAuth: {
      handler (val) {
        if (val) {
          setTimeout(async () => {
            this.storage = await getStorageQuota()
            this.setupFolders()

            this.$nuxt.$on('UPDATE_GOOGLE_DRIVE_TODAY_FOLDER', () => {
              // แก้ปํญหาสร้าง Google Drive Folder ทับซ้อนกัน ให้แต่ละเครื่อง delay
              // เป็นเวลา random 0 - 5 วินาทีก่อนทำการสร้าง google drive folder
              setTimeout(this.setupFolders, Math.round(Math.random() * 5000))
            })
          }, 2000)
        } else {
        }
      },
      immediate: true
    },

    value: {
      handler (val) {
        if (val) {
          this.onOpen()
        }
      }
    }
  },

  mounted () {
    this.getDevicesList()

    if (isMobile()) {
      this.file_types = ['webm']
      this.setLocalSetting({ filetype: 'webm', speed: '1x' })
    }

    if (localStorage.getItem('open-google-signin')) {
      localStorage.removeItem('open-google-signin')
      this.gapiSignIn()
    }
  },

  beforeDestroy () {
    this.$nuxt.$off('UPDATE_GOOGLE_DRIVE_TODAY_FOLDER', this.setupFolders)
  },

  methods: {
    onOpen () {
      if (this.settings.local.weightScaleComputerId) {
        this.testConnectWeightScale(this.settings.local.weightScaleComputerId)
      }
    },

    setResolution (resolution: string) {
      if (resolution === '1920x1080') {
        this.setLocalSetting({ resolution, enableOverlay: false })
      } else {
        this.setLocalSetting({ resolution })
      }
    },

    getDevicesList (): void {
      try {
        navigator.mediaDevices.enumerateDevices().then(
          (deviceInfos: MediaDeviceInfo[]) => {
            for (const device of deviceInfos) {
              if (device.kind === 'videoinput' && device.deviceId !== '') {
                this.devices.push(device)
              }
            }
          }
        )
      } catch (error) {
        console.error('The navigator.mediaDevices is undefined in insecure context.\nOperation failed with error RTC0x0001')
      }
    },

    requestUserMediaPermission () {
      const constraints = {
        video: {
          width: { min: 640, ideal: 1280, max: 2560 },
          height: { min: 480, ideal: 720, max: 1440 }
        }
      }

      if (!navigator.mediaDevices) {
        alert(this.$t('error.unable-to-capture-your-camera-noperation-failed-with-error-rtc0x0002'))
        return
      }

      navigator.mediaDevices
        .getUserMedia(constraints)
        .then((camera: MediaStream) => {
          // callback(camera)
          this.getDevicesList()
          camera.getTracks().forEach(track => track.stop())
        })
        .catch((error) => {
          alert(this.$t('error.unable-to-capture-your-camera-noperation-failed-with-error-rec0x0002'))
          console.error(error)
        })
    },

    async setupFolders () {
      await waitUntil('gapi.client.drive loaded', () =>
        window.gapi &&
        window.gapi.client !== undefined &&
        window.gapi.client.drive !== undefined
      )

      if (this.settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID) {
        this.setupShareDriveFolder()
      } else {
        this.setupMyDriveFolder()
      }
    },

    async setupShareDriveFolder () {
      console.log('[drive] setupShareDriveFolder')
      const share_drive_id = this.settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID

      // Setup Root Folder
      console.log('[drive] setupShareDriveFolder - setup root folder')
      const rootFolderName = `Dobybot Videos (${this.gapiUser.email})`
      try {
        let rootFolder = await findFolderInShareDrive(share_drive_id, rootFolderName)
        if (!rootFolder) {
          rootFolder = await createFolder(rootFolderName, { parentFolderId: share_drive_id })
        }
        this.setLocalSetting({ googleDriveRootFolder: rootFolder })
      } catch (error: any) {
        this.$alert({
          title: this.$t('error.unable-to-connect-to-google-drive'),
          text: error.result,
          theme: 'error'
        })
        throw error
      }

      // Setup Today Folder
      console.log('[drive] setupShareDriveFolder - setup today folder')
      const todayFolderName = moment().format('YYYY-MM-DD')

      if (!this.settings.local.googleDriveRootFolder) {
        return
      }

      try {
        let todayFolder = await findFolderInFolder(todayFolderName, this.settings.local.googleDriveRootFolder.id)
        if (!todayFolder && this.settings.local.googleDriveRootFolder) {
          todayFolder = await createFolder(todayFolderName, {
            parentFolderId: this.settings.local.googleDriveRootFolder.id
          })
        }
        this.setLocalSetting({ googleDriveTodayFolder: todayFolder })
      } catch (error: any) {
        this.$alert({
          title: this.$t('error.unable-to-connect-to-google-drive'),
          text: error.result,
          theme: 'error'
        })
        throw error
      }

      console.log('[drive] setupShareDriveFolder - OK')
    },

    async setupMyDriveFolder () {
      console.log('[drive] setupMyDriveFolder')
      const folders = await listFoldersInMyDrive()
      const rootFolderName = 'Packing Videos'
      const todayFolderName = moment().format('YYYY-MM-DD')

      let rootFolder = folders.find(f => f.name === rootFolderName)
      if (!rootFolder) {
        console.log('[drive] setupMyDriveFolder - create root folder')
        rootFolder = await createFolder(rootFolderName)
      }
      this.setLocalSetting({ googleDriveRootFolder: rootFolder })

      let todayFolder = folders.find(f => f.name === todayFolderName)
      if (!todayFolder && this.settings.local.googleDriveRootFolder) {
        console.log('[drive] setupMyDriveFolder - create today folder')
        todayFolder = await createFolder(todayFolderName, {
          parentFolderId: this.settings.local.googleDriveRootFolder.id
        })
      }
      this.setLocalSetting({ googleDriveTodayFolder: todayFolder })
      console.log('[drive] setupMyDriveFolder - OK')
    },

    async onFiletypeChange (filetype: 'mp4'|'webm') {
      const progress = this.progress
      progress.load_ffmpeg.status = null
      progress.test_ffmpeg.status = null

      if (filetype === 'mp4') {
        progress.load_ffmpeg.status = 'loading'
        progress.load_ffmpeg.text = this.$t('record.downloading-mp4-engine')
        try {
          if (!window.ffmpeg) {
            window.ffmpeg = createFFmpeg({
              log: process.env.NODE_ENV === 'development',
              corePath: '/static/js/@ffmpeg/core@0.11.0/ffmpeg-core.js'
            })
            await window.ffmpeg.load()
          }
          progress.load_ffmpeg.status = 'success'
        } catch (error: any) {
          progress.load_ffmpeg.status = 'error'
          progress.load_ffmpeg.text = error.toString()
          console.error(error)
          return
        }

        progress.test_ffmpeg.status = 'loading'
        progress.test_ffmpeg.text = this.$t('record.testing-transcode-mp4')
        const test_video_url = require('~/assets/video/test-h264.webm')
        const blob = await fetch(test_video_url).then(r => r.blob())
        try {
          const blob_mp4 = await transcodeMP4(blob, { speed: this.settings.local.speed })
          progress.test_ffmpeg.status = 'success'
          progress.test_ffmpeg.result = URL.createObjectURL(blob_mp4)
        } catch (error: any) {
          progress.test_ffmpeg.status = 'error'
          progress.test_ffmpeg.text = error.toString()
          console.error(error)
        }
      }
    },

    async toggleLock () {
      if (this.lock) {
        while (true) {
          const passwd = await this.$prompt({ text: 'กรุณากรอกรหัสผ่าน Supervisor', title: 'ปลดล็อกตั้งค่าขั้นสูง', preset: 'password' })
          if (passwd === null) {
            break
          }
          if (passwd === this.settings.company.SUPERVISOR_PASSWORD) {
            this.lock = false
            break
          }
        }
      } else {
        this.lock = true
      }
    },

    async testConnectWeightScale (computerId: number) {
      try {
        const result = await this.$axios.$get(`/api/picking/computers/${computerId}/scales/`)
        this.scale.device = result[0]
      } catch (error) {
        alert('Can fetch scales')
        return
      }

      const device = this.scale.device
      if (!device) {
        alert('there are no scale connected to this computer')
        return
      }

      if (!this.printnode_ws) {
        this.printnode_ws = await setupPrintnodeWebsocket(this.settings.company.PRINTNODE_API_KEY)
      }
      if (!this.printnode_ws) {
        return
      }

      this.printnode_ws.getScales({ computerId: Number(device.computer_id) }, (data: any) => {
        if (data.measurement.g) {
          this.scale.weight = data.measurement.g / 10e8
        }
        if (data.measurement.kg) {
          this.scale.weight = data.measurement.kg / 10e5
        }
      })
    },

    close () {
      this.lock = true
      this.$emit('input', false)

      if (this.printnode_ws) {
        this.printnode_ws.closeSocket()
        this.printnode_ws = null
      }
      if (this.scale.device) {
        this.scale.device = null
      }
    },

    ...mapActions('gapi', {
      gapiSignIn: 'signIn',
      gapiSignOut: 'signOut'
    }),

    ...mapMutations('settings', {
      setLocalSetting: 'setLocalSetting'
    })
  }
})
</script>
