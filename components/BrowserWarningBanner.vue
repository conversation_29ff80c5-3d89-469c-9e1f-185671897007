<template>
  <v-alert
    v-if="!isBrowserSupported && !browserWarningDismissed "
    dense
    colored-border
    color="warning"
    border="left"
    elevation="2"
    class="browser-warning-banner ma-2"
  >
    <div>
      <strong>{{ $t('browser.warning') }}</strong>: {{ localizedWarningMessage }}
      <v-btn
        small
        color="warning"
        class="ml-2"
        outlined
        @click="showMoreInfo = !showMoreInfo"
      >
        {{ showMoreInfo ? $t('browser.less_info') : $t('browser.more_info') }}
      </v-btn>
      <v-btn
        small
        color="error"
        class="ml-2"
        outlined
        @click="dismissBrowserWarning()"
      >
        {{ $t('browser.dismiss') }}
      </v-btn>
    </div>

    <div v-if="showMoreInfo" class="mt-2">
      <p>{{ $t('browser.compatibility_info') }}</p>
      <p>{{ $t('browser.minimum_versions') }}:</p>
      <ul>
        <li>Chrome: {{ minVersions.Chrome }}+</li>
        <li>Firefox: {{ minVersions.Firefox }}+</li>
        <li>Edge: {{ minVersions.Edge }}+</li>
        <li>Safari: {{ minVersions.Safari }}+</li>
      </ul>
      <div class="d-flex mt-2">
        <v-btn
          small
          outlined
          color="warning"
          class="mr-2"
          href="https://www.google.com/chrome/"
          target="_blank"
          rel="noopener noreferrer"
        >
          <v-icon small left>
            mdi-google-chrome
          </v-icon>
          Chrome
        </v-btn>
        <v-btn
          small
          outlined
          color="warning"
          class="mr-2"
          href="https://www.mozilla.org/firefox/new/"
          target="_blank"
          rel="noopener noreferrer"
        >
          <v-icon small left>
            mdi-firefox
          </v-icon>
          Firefox
        </v-btn>
        <v-btn
          small
          outlined
          color="warning"
          href="https://www.microsoft.com/edge"
          target="_blank"
          rel="noopener noreferrer"
        >
          <v-icon small left>
            mdi-microsoft-edge
          </v-icon>
          Edge
        </v-btn>
      </div>
    </div>
  </v-alert>
</template>

<script lang="ts">
import Vue from 'vue'
import { getBrowserSupportMessage, MIN_VERSIONS, BrowserInfo } from '~/utils/browser-version'

export default Vue.extend({
  data () {
    return {
      isBrowserSupported: true,
      showMoreInfo: false,

      minVersions: MIN_VERSIONS,
      browserInfo: null as BrowserInfo | null,
      browserWarningDismissed: false
    }
  },

  computed: {
    localizedWarningMessage (): string {
      if (!this.browserInfo) { return '' }

      const { name, version, minVersion, isSupported } = this.browserInfo

      if (isSupported) {
        return this.$t('browser.supported', { name, version }).toString()
      } else {
        return this.$t('browser.not_supported', { name, version, minVersion }).toString()
      }
    }
  },

  mounted () {
    // Check browser compatibility
    const browserInfo = getBrowserSupportMessage()
    this.isBrowserSupported = browserInfo.isSupported
    this.browserInfo = browserInfo
    this.browserWarningDismissed = localStorage.getItem('browser-warning-unsupported-dismissed') === 'true'
  },

  methods: {
    dismissBrowserWarning () {
      this.browserWarningDismissed = true
      localStorage.setItem('browser-warning-unsupported-dismissed', 'true')
    }
  }
})
</script>

<style scoped>
.browser-warning-banner {
  position: relative;
  z-index: 1000;
}
</style>
