<template>
  <v-autocomplete
    :model-value="prop.selected"
    @update:model-value="$emit('update:selected', $event)"
    label="Company"
    :items="companies"
    item-text="name"
    item-value="id"
  />
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    selected: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      companies: []
      // selected: null
    }
  },
  mounted () {
    this.getCompanies()
  },
  methods: {
    async getCompanies () {
      this.companies = await this.$axios.$get('/api/users/admin/replica-company/')
    }
  }
})
</script>
