<template>
  <v-dialog v-model="dialog" max-width="500">
    <v-card>
      <v-card-title>Change Company</v-card-title>
      <v-card-text>
        <v-autocomplete
          v-model="selected"
          label="Company"
          :items="companies"
          item-text="name"
          item-value="id"
        />
        <v-btn block color="primary" @click="changeCompany()">
          Submit
        </v-btn>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  data () {
    return {
      dialog: false,
      companies: [],
      selected: null
    }
  },
  mounted () {
    this.getCompanies()
  },
  methods: {
    async getCompanies () {
      this.companies = await this.$axios.$get('/api/users/admin/change-company/')
    },
    async changeCompany () {
      await this.$axios.post('/api/users/admin/change-company/', { company: this.selected })
      window.location.reload()
    },
    show () {
      this.dialog = true
    },
    hide () {
      this.dialog = false
    }
  }
})
</script>
