<template>
  <v-menu
    v-model="menu"
    :close-on-content-click="false"
    :nudge-right="40"
    transition="scale-transition"
    offset-y
    min-width="290px"
  >
    <template #activator="{ on, attrs }">
      <v-text-field
        :value="value"
        :label="label"
        :clearable="$auth.user.is_superuser"
        prepend-inner-icon="mdi-calendar"
        readonly
        v-bind="{ ...attrs, ...$attrs }"
        v-on="on"
        @click:clear="$emit('input', null)"
      />
    </template>
    <v-date-picker
      :min="min"
      :max="max"
      :value="value"
      @input="$emit('input', $event); menu = false"
    />
  </v-menu>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    label: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      required: false,
      default: ''
    },
    min: {
      type: String,
      default: undefined
    },
    max: {
      type: String,
      default: undefined
    }
  },
  data () {
    return {
      menu: false
    }
  }
})
</script>
