<template>
  <v-alert
    :value="visible"
    :color="theme"
    border="left"
    elevation="1"
    colored-border
    class="no-print"
  >
    <v-row align="center" dense>
      <v-col class="grow" v-html="text" />
      <v-col class="shrink">
        <div style="white-space: nowrap;">
          <v-btn
            v-for="btn in buttons"
            :key="btn.text"
            :color="btn.color"
            class="ml-2"
            text
            @click="btn.onclick"
          >
            {{ btn.text }}
          </v-btn>
        </div>
      </v-col>
    </v-row>
  </v-alert>
</template>

<script lang="ts">

import Vue from 'vue'
import { mapState } from 'vuex'
export default Vue.extend({
  computed: {
    ...mapState('banner', ['visible', 'theme', 'text', 'buttons'])
  }
})
</script>

<style scoped>
@media print {
  .no-print {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
  }
}
</style>
