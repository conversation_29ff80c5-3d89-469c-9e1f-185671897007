<template>
  <v-alert
    :value="show"
    color="warning"
    border="left"
    elevation="1"
    colored-border
    prominent
    dismissible
    icon="mdi-alert"
    style="position: sticky"
    class="no-print"
    @click:dismissible="close()"
  >
    <v-row align="center" dense>
      <v-col class="grow">
        <ul>
          <li v-for="item, i in warnings" :key="i">
            {{ item }}
          </li>
        </ul>
      </v-col>
    </v-row>
  </v-alert>
</template>

<script lang="ts">
import Vue from 'vue'
import moment from 'moment'
import { LoggedInUser, Marketplace } from '~/models'
import { SettingState } from '~/store/settings'

function formatMarketplaceName (name: string) {
  let formatted_name = name.replace('_', ' ')
  formatted_name = formatted_name.charAt(0).toUpperCase() + formatted_name.slice(1)
  return formatted_name
}

export default Vue.extend({
  data () {
    return {
      dismiss: false,
      marketplaces: [] as Marketplace[]
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    },

    show (): boolean {
      if (this.dismiss) {
        return false
      }
      return this.warnings.length > 0
    },

    warnings (): string[] {
      const low_rec_credit = this.hasLowRecordCredit()
      const low_sms_credit = this.hasLowSmsCredit()
      const storage_almost_full = this.isGoogleDriveStorageAlmostFull()
      const marketplace_errors = this.getErrorMargetplaces(this.marketplaces)
      const warnings = [] as string[]

      if (low_rec_credit && low_sms_credit) {
        warnings.push(this.$t('global.vdo-sms'))
      } else if (low_rec_credit) {
        warnings.push(this.$t('global.vdo'))
      } else if (low_sms_credit) {
        warnings.push(this.$t('global.sms'))
      }

      if (storage_almost_full) {
        warnings.push(this.$t('global.storage-google-drive'))
      }

      for (const mp of marketplace_errors) {
        // const exp_text = mp.status === 'expired' ? 'หมดอายุ' : 'ใกล้หมดอายุ'
        let message = `${this.$t('connection')} ${mp.marketplace} ${this.$t('marketplace-status-' + mp.status)}`

        if (['expired', 'expired_soon'].includes(mp.status)) {
          message += ` (${mp.expire_date})`
        }

        warnings.push(message)
      }

      const user = this.$auth.user as never as LoggedInUser
      const expire_date = user.company.expire_date
      if (expire_date && moment().add(7, 'days').isAfter(expire_date)) {
        warnings.push(this.$t('global.this-date-expire_date', [this.$date(expire_date)]))
      }

      return warnings
    }
  },

  created () {
    this.getMarketplaces()
  },

  methods: {
    async getMarketplaces () {
      const shops: Marketplace[] = (await this.$axios.get('/api/companies/marketplaces/')).data
      this.marketplaces = shops
    },

    hasLowRecordCredit () {
      const min_record_credit = this.settings.company.LOW_RECORD_CREDIT_WARNING_AMOUNT
      const company = (this.$auth.user as never as LoggedInUser).company
      return min_record_credit > 0 && company.record_balance < min_record_credit
    },

    hasLowSmsCredit () {
      const company = (this.$auth.user as never as LoggedInUser).company
      const min_sms_credit = this.settings.company.LOW_SMS_CREDIT_WARNING_AMOUNT
      return min_sms_credit > 0 && company.sms_balance < min_sms_credit
    },

    isGoogleDriveStorageAlmostFull () {
      const company = (this.$auth.user as never as LoggedInUser).company
      if (!company.google_drive_limit) {
        return false
      }

      const available_space = company.google_drive_limit - company.google_drive_usage
      return available_space / company.google_drive_limit < 0.1
    },

    getErrorMargetplaces (marketplaces: Marketplace[]) {
      const results = [] as any[]
      for (const mp of marketplaces) {
        const expire_in = moment(mp.refresh_token_expire).diff(moment(), 'days')
        if (mp.refresh_token_expire && expire_in < 0) {
          results.push({
            marketplace: formatMarketplaceName(mp.marketplace) + ', ' + mp.seller_shop_name,
            expire_date: moment(mp.refresh_token_expire).format('DD/MMM/YYYY'),
            status: 'expired'
          })
        } else if (mp.refresh_token_expire && expire_in < 7) {
          results.push({
            marketplace: formatMarketplaceName(mp.marketplace) + ', ' + mp.seller_shop_name,
            expire_date: moment(mp.refresh_token_expire).format('DD/MMM/YYYY'),
            status: 'expired_soon'
          })
        } else if (!mp.access_token || !mp.refresh_token) {
          results.push({
            marketplace: formatMarketplaceName(mp.marketplace) + ', ' + mp.seller_shop_name,
            expire_date: null,
            status: 'error'
          })
        }
      }

      return results
    },

    close () {
      this.dismiss = true
    }
  }
})
</script>
