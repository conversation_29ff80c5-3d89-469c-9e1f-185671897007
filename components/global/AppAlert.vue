<template>
  <v-dialog :value="visible" persistent :max-width="width">
    <v-card>
      <v-card-title v-if="title">
        <v-icon :color="theme">
          mdi-information
        </v-icon>
        &nbsp;
        {{ title }}
        <div v-if="$auth.user.is_superuser" class="caption grey--text pl-9">
          {{ $t('global.shift-esc') }}
        </div>
      </v-card-title>
      <v-divider v-if="title" />
      <v-card-text class="pa-4" style="min-height: 100px;">
        <div v-if="typeof text === 'string'" class="pa-3 alert-html" v-html="sanitize( text )" />
        <div v-else>
          <h3>Errors</h3>
          <json-viewer style="border: 1px solid #eeeeee" :value="text" />
        </div>

        <div>
          <v-form autocomplete="off" class="px-3" @submit.prevent="">
            <v-text-field
              v-for="(input, i) in inputs.filter(x => x.tag === 'v-text-field')"
              :key="i"
              v-bind="input.attrs"
              :value="input.value"
              @change="input.onchange"
            />

            <div
              v-for="(input, i) in inputs.filter(x => x.tag === 'v-otp-input')"
              :key="i"
              style="display: flex; justify-content: center;"
            >
              <v-otp-input
                data-test="v-otp-input"
                style="max-width: 350px"
                v-bind="input.attrs"
                :value="input.value"
                autocomplete="off"
                @change="input.onchange"
              />
            </div>
          </v-form>
        </div>
      </v-card-text>
      <v-card-actions>
        <v-btn
          v-for="btn in buttons.filter(x => x.align === 'left')"
          :key="btn.text"
          :color="btn.color"
          data-test="v-btn-left"
          @click="btn.onclick"
        >
          {{ btn.text }}
        </v-btn>
        <v-spacer />
        <v-btn
          v-for="btn in buttons.filter(x => !x.align || x.align === 'center')"
          :key="btn.text"
          :color="btn.color"
          data-test="v-btn-center"
          @click="btn.onclick"
        >
          {{ btn.text }}
        </v-btn>
        <v-spacer />
        <v-btn
          v-for="btn in buttons.filter(x => x.align === 'right')"
          :key="btn.text"
          :color="btn.color"
          data-test="v-btn-right"
          @click="btn.onclick"
        >
          {{ btn.text }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import JsonViewer from 'vue-json-viewer'
import DOMPurify from 'dompurify'
import { LoggedInUser } from '~/models'

export default Vue.extend({
  components: {
    JsonViewer
  },

  computed: {
    ...mapState('alert', ['visible', 'title', 'theme', 'text', 'inputs', 'buttons', 'width'])
  },

  watch: {
    visible: {
      handler (val: boolean) {
        if (val) {
          document.addEventListener('keyup', this.handleKeyup)
        } else {
          document.removeEventListener('keyup', this.handleKeyup)
        }
      }
    }
  },
  methods: {
    sanitize (html: string) {
      return DOMPurify.sanitize(html)
    },
    handleKeyup (e: KeyboardEvent) {
      const user: LoggedInUser = this.$auth.user as never
      if (user.is_superuser && e.shiftKey && e.key === 'Escape') {
        this.$store.dispatch('alert/hide')
      }
    }
  }
})
</script>

<style>
.alert-html h1 {
  margin-top: 15px;
  margin-bottom: 15px;
}
.alert-html p {
  margin-bottom: 5px;
}
.alert-html {
  font-size: 1.25em;
  color: black;
}
</style>
