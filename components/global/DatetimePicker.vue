<template>
  <v-menu
    v-model="menu"
    :close-on-content-click="false"
    :nudge-right="40"
    transition="scale-transition"
    offset-y
    min-width="290px"
  >
    <template #activator="{ on, attrs }">
      <v-text-field
        :value="value ? $moment(value).format('YYYY-MM-DD HH:mm') : ''"
        :label="label"
        class="pt-1"
        prepend-inner-icon="mdi-calendar"
        clearable
        readonly
        v-bind="{ ...attrs, ...$attrs }"
        v-on="on"
        @click:clear="$emit('input', '')"
      />
    </template>
    <v-card style="overflow: hidden;">
      <v-row>
        <v-col class="pr-0" style="width: 290px;">
          <v-date-picker v-model="state.date" @input="updateDate($event);" />
        </v-col>
        <v-col class="px-0" style="width: 100px;">
          <div class="v-picker__title primary">
            <div class="v-date-picker-title">
              <div class="v-picker__title__btn v-date-picker-title__year justify-end">
                ชั่วโมง
              </div>
              <div class="v-picker__title__btn v-date-picker-title__date v-picker__title__btn--active text-right">
                {{ state.hour || '-' }}
              </div>
            </div>
          </div>
          <v-list style="height: 290px; overflow: scroll;" class="py-0 my-0">
            <v-list-item v-for="i in hours" :key="i" class="justify-end" @click="updateHour(i)">
              {{ i }}
            </v-list-item>
          </v-list>
        </v-col>
        <v-col class="px-0" style="width: 100px;">
          <div class="v-picker__title primary">
            <div class="v-date-picker-title">
              <div class="v-picker__title__btn v-date-picker-title__year">
                นาที
              </div>
              <div class="v-picker__title__btn v-date-picker-title__date v-picker__title__btn--active">
                {{ state.minute || '-' }}
              </div>
            </div>
          </div>
          <v-list style="height: 290px; overflow: scroll;" class="py-0 my-0">
            <v-list-item v-for="i in minutes" :key="i" @click="updateMinute(i)">
              {{ i }}
            </v-list-item>
          </v-list>
        </v-col>
      </v-row>
    </v-card>
  </v-menu>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    label: {
      type: String,
      default: ''
    },
    value: {
      type: String,
      required: false,
      default: ''
    }
  },
  data () {
    return {
      menu: false,

      minutes: [
        '00', '01', '02', '03', '04', '05', '06', '07', '08', '09',
        '10', '11', '12', '13', '14', '15', '16', '17', '18', '19',
        '20', '21', '22', '23', '24', '25', '26', '27', '28', '29',
        '30', '31', '32', '33', '34', '35', '36', '37', '38', '39',
        '40', '41', '42', '43', '44', '45', '46', '47', '48', '49',
        '50', '51', '52', '53', '54', '55', '56', '57', '58', '59'
      ],
      hours: [
        '00', '01', '02', '03', '04', '05', '06', '07', '08', '09',
        '10', '11', '12', '13', '14', '15', '16', '17', '18', '19',
        '20', '21', '22', '23'
      ],

      state: {
        date: '',
        hour: '',
        minute: ''
      },

      once: false
    }
  },

  watch: {
    value: {
      handler () {
        if (!this.once) {
          this.once = true
          const [date, hour, minute] = this.splitDateParts(this.value)
          this.state.date = date
          this.state.hour = hour
          this.state.minute = minute
        }
      },
      immediate: true
    }
  },

  methods: {
    splitDateParts (datetime: string) {
      if (datetime) {
        return ['', '', '']
      }

      let date = datetime
      let hour = '00'
      let minute = '00'

      if (datetime.includes('T')) {
        const [date_part, time_part] = datetime.split('T')
        date = date_part

        if (time_part) {
          const time_parts = time_part.split(':')
          hour = time_parts[0] || '00'
          minute = time_parts[1] || '00'
        }
      }
      return [date, hour, minute]
    },

    updateDate (date: string) {
      this.state.date = date
      this.update()
    },

    updateMinute (min: string) {
      this.state.minute = min
      this.update()
    },

    updateHour (hour: string) {
      this.state.hour = hour
      this.update()
    },

    update () {
      const { date, hour, minute } = this.state
      if (!date) {
        return
      }
      this.$emit('input', `${date}T${hour}:${minute}:00`)
    }
  }
})
</script>
