<template>
  <v-snackbar

    :value="show"
    :color="color"
    v-bind="$attrs"
    @input="(val) => !val ? hide() : null"
  >
    <span data-test="v-snackbar-top-right" :data-test-color="color" style="white-space: pre;">{{ text }}</span>

    <template #action="{ attrs }">
      <v-btn
        text
        icon
        v-bind="attrs"
        @click="hide()"
      >
        <v-icon>mdi-close</v-icon>
      </v-btn>
    </template>
  </v-snackbar>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState, mapActions } from 'vuex'
export default Vue.extend({
  computed: {
    ...mapState('snackbar', ['show', 'color', 'text'])
  },

  methods: {
    ...mapActions('snackbar', ['hide'])
  }
})
</script>
