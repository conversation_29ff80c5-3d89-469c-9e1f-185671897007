<template>
  <span>
    <v-chip
      v-if="value"
      x-small
      color="green darken-2"
      text-color="white"
    >
      <v-icon x-small>
        mdi-check
      </v-icon>
      {{ trueMsg }}
    </v-chip>
    <v-chip
      v-else
      x-small
      color="error"
      text-color="white"
    >
      <v-icon x-small>
        mdi-close
      </v-icon>
      {{ falseMsg }}
    </v-chip>
  </span>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    value: {
      type: Boolean,
      required: true
    },
    trueMsg: {
      type: String,
      default: 'True'
    },
    falseMsg: {
      type: String,
      default: 'False'
    }
  }
})
</script>
