<template>
  <v-alert
    :value="alert"
    color="info"
    border="left"
    elevation="1"
    colored-border
    prominent
    icon="mdi-refresh"
    style="position: sticky"
    class="no-print"
  >
    <v-row align="center" dense>
      <v-col class="grow">
        {{ $t('global.update-refresh-f5') }}
      </v-col>
      <v-col class="shrink">
        <v-btn color="primary" @click="refreshPage()">
          Refresh
        </v-btn>
      </v-col>
    </v-row>
  </v-alert>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  data () {
    return {
      timerId: null as any
    }
  },
  computed: {
    A (): string {
      return this.$store.state.frontend.dobybot_ui_version
    },
    B (): string {
      return this.$store.state.backend.dobybot_version
    },
    alert (): boolean {
      if (!this.A) { return false }
      if (!this.B) { return false }
      return this.A !== this.B
    }
  },
  mounted () {
    this.timerId = setInterval(() => {
      this.$store.dispatch('getVersions')
    }, 10 * 60 * 1000)
  },
  beforeDestroy () {
    if (this.timerId) {
      clearInterval(this.timerId)
    }
  },
  methods: {
    refreshPage () {
      window.location.reload()
    }
  }
})
</script>
