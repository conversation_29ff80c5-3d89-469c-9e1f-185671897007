<template>
  <div>
    <div>
      <label>{{ $t('setting.filetype') }}</label>
    </div>
    <v-radio-group
      :value="value"
      class="mt-1"
      style="margin-left: -4px; margin-right: -4px;"
      @change="$emit('input', $event)"
    >
      <div class="d-flex">
        <v-card
          v-for="item in choices"
          :key="item.value"
          style="position: relative;"
          class="box"
        >
          <v-radio :label="item.text" :value="item.value" />

          <v-chip v-if="item.recommend" small dense color="primary" style="position: absolute; top: 8px; right: 8px;">
            {{ $t('recommended') }}
          </v-chip>
          <div class="grey--text text--darken-2 mt-2 px-1 text-caption">
            <div v-if="item.value === 'webm'" class="mt-1">
              {{ $t('webm-short-desc') }}่
              <ul>
                <li>{{ $t('webm-use-less-storage') }}</li>
                <li>{{ $t('webm-load-faster-smaller-filesize') }}</li>
              </ul>
              <br>
              <b>{{ $t('webm-recommendation') }}</b>
            </div>
            <div v-if="item.value === 'mp4'" class="mt-1">
              {{ $t('mp4-short-desc') }}
              <ul>
                <li>{{ $t('mp4-can-be-viewed-from-every-device') }}</li>
                <li>{{ $t('mp4-use-more-system-resources') }}</li>
              </ul>
              <br><br>
              <b>{{ $t('mp4-recommendation') }}</b>
            </div>
          </div>

          <slot :name="item.value" />
        </v-card>
      </div>
    </v-radio-group>
  </div>
</template>

<script>
import Vue from 'vue'
import { isMobile } from '~/utils/devices'
export default Vue.extend({
  props: {
    value: {
      type: String,
      required: true
    },
    recommend: {
      type: String,
      default: 'webm'
    }
  },
  data () {
    return {
      resolution: '1280x720'
    }
  },
  computed: {
    choices () {
      const _choices = [
        {
          text: 'Webm',
          value: 'webm',
          info: '',
          recommend: this.$auth.user.company.package === this.$package.RECORD_ONLY
        },
        {
          text: 'MP4',
          value: 'mp4',
          info: `
          `,
          recommend: this.$auth.user.company.package === this.$package.FULL_INTEGRATION
        }
      ]

      if (isMobile()) {
        return _choices.filter(item => item.value !== 'mp4')
      }

      return _choices
    }
  }
})
</script>

<style scoped>
.box {
  flex-grow: 1;
  flex-basis: 50%;
  min-height: 190px;
  margin: 5px 5px 5px 5px;
  padding: 8px;
}
</style>
