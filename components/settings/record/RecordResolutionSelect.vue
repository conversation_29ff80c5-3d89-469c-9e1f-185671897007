<template>
  <div>
    <div>
      <label>{{ $t('setting.resolution') }}</label>
      <div class="grey--text text--darken-2 text-caption">
        {{ $t('resolution-setting-recommendation') }}
        <v-icon small>
          mdi-cog
        </v-icon>
      </div>
    </div>
    <v-radio-group
      :value="value"
      class="mt-1"
      style="margin-left: -4px; margin-right: -4px;"
      @change="$emit('input', $event)"
    >
      <div class="d-flex">
        <v-card
          v-for="item in choices"
          :key="item.value"
          style="position: relative;"
          class="box"
        >
          <v-radio :label="item.text" :value="item.value" />

          <v-chip v-if="item.recommend" small dense color="primary" style="position: absolute; top: 8px; right: 8px;">
            {{ $t('recommended') }}
          </v-chip>

          <div class="grey--text text--darken-2 mt-2 px-1 text-caption">
            <div>
              <v-icon small>
                mdi-video
              </v-icon>
              <span>{{ $t('setting.resolution') }} {{ item.value }}</span>
              <br>
              <span class="ml-5" style="position: relative; top: -2px;">{{ item.info }}</span>
            </div>

            <div class="mt-1">
              <v-icon small>
                mdi-folder
              </v-icon>
              {{ $t('home.file-size') }} ~{{ item.file_size_per_minute }} MB / {{ $t('minute') }}
            </div>

            <div v-if="item.wifi" class="mt-2">
              <v-icon small>
                mdi-wifi
              </v-icon>
              {{ item.wifi }}
            </div>

            <div v-if="!item.wifi" class="mt-2">
              <v-icon small>
                mdi-wifi-off
              </v-icon>
              {{ $t('not-recommend-wifi') }}
            </div>

            <div v-if="item.required_lan" class="mt-2">
              <v-icon small>
                mdi-ethernet-cable
              </v-icon>
              <span>{{ $t('recommend-lan-connection') }}</span>
            </div>
          </div>
        </v-card>
      </div>
    </v-radio-group>
  </div>
</template>

<script>
import Vue from 'vue'
export default Vue.extend({
  props: {
    value: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      resolution: '1280x720'
    }
  },
  computed: {
    choices () {
      return [
        {
          value: '640x360',
          text: 'SD (360p)',
          file_size_per_minute: '15',
          recommend: false,
          required_lan: false,
          info: this.$t('save-storage-space'),
          wifi: this.$t('wifi-n-station', [8])
        },
        {
          value: '1280x720',
          text: 'HD (720p)',
          file_size_per_minute: '30',
          recommend: true,
          required_lan: true,
          info: this.$t('hd-for-normal-usage'),
          wifi: this.$t('wifi-n-station', [4])
        },
        {
          value: '1920x1080',
          text: 'FHD (1080p)',
          file_size_per_minute: '60',
          recommend: false,
          required_lan: true,
          info: 'Full HD',
          wifi: false
        }
      ]
    }
  }
})
</script>

<style scoped>
.box {
  flex-grow: 1;
  flex-basis: 33%;
  height: 170px;
  margin: 5px 5px 5px 5px;
  padding: 8px;
}
</style>
