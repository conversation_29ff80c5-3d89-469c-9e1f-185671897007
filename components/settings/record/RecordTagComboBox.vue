<template>
  <div>
    <v-row v-for="item, i in items" :key="i" dense class="my-1">
      <v-col cols="4">
        <v-combobox
          label="Tag"
          filled
          hide-details
          :items="['box_size', 'difficulty']"
          :value="item.label"
          @change="updateItem(i, {label: $event})"
        />
      </v-col>
      <v-col cols="7">
        <v-combobox
          label="Barcodes"
          filled
          hide-details
          clearable
          multiple
          small-chips
          :value="item.barcodes"
          @change="updateItem(i, {barcodes: $event})"
        />
      </v-col>
      <v-col>
        <v-btn icon class="mt-3" @click="deleteItem(i)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-col>
    </v-row>

    <v-btn class="mt-2" small outlined @click="addItem()">
      <v-icon left>
        mdi-plus
      </v-icon>
      {{ $t('add') }}
    </v-btn>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { RecordTag } from '~/models'
export default Vue.extend({
  props: {
    value: {
      type: Array,
      default: () => ([] as RecordTag[])
    }
  },

  computed: {
    items (): RecordTag[] {
      if (this.value === null) {
        return []
      }

      return this.value as RecordTag[]
    }
  },

  methods: {
    deleteItem (index: number) {
      const items = [...this.items]
      items.splice(index, 1)
      this.$emit('change', items)
    },

    updateItem (index: number, data: any) {
      const items = [...this.items]
      items[index] = { ...items[index], ...data }
      this.$emit('change', items)
    },

    addItem () {
      const items = [
        ...this.items,
        { label: '', barcodes: [] }
      ]
      this.$emit('change', items)
    }
  }
})
</script>
