<template>
  <v-data-table
    :footer-props="{'items-per-page-options': [5, 10, 25]}"
    :headers="headers"
    :items="items"
    dense
  >
    <template #item.enable="{item}">
      <v-checkbox
        :input-value="value[item.seller_id]"
        hide-details
        class="ma-0 pa-0"
        @change="update({ [item.seller_id]: $event })"
      />
    </template>

    <template #item.seller_shop_name="{item}">
      {{ item.seller_id }} {{ item.seller_shop_name }}
    </template>

    <template #item.refresh_token_expire="{item}">
      {{ item.refresh_token_expire ? $date(item.refresh_token_expire) : '-' }}
    </template>

    <template #item.status="{item}">
      <span v-if="!checkMarketplaceStatus(item)">
        <v-icon color="red">mdi-alert-circle</v-icon>
        <span class="red--text">{{ $t('marketplace-connection-error') }} {{ $t('please') }} <a @click.prevent="$emit('click-reconnect')">{{ $t('reconnect') }}</a></span>
      </span>
      <span v-else>
        <v-icon color="green">mdi-check-circle</v-icon>
        <span class="green--text">{{ $t('connected') }}</span>
      </span>
    </template>

    <!-- <template #item.datetime="{item}">
      {{ $datetime(item.datetime) }}
    </template> -->
    <template #footer.prepend>
      <slot name="footer.prepend" />
    </template>
  </v-data-table>
</template>

<script lang="ts">
import moment from 'moment'
import Vue, { PropOptions } from 'vue'
import { Marketplace, VDataTableHeader } from '~/models'
export default Vue.extend({
  props: {
    value: {
      type: Object,
      required: true
    },
    items: {
      type: Array,
      required: true
    } as PropOptions<Marketplace[]>
    // add some props
  },
  computed: {
    headers (): VDataTableHeader[] {
      return [
        { text: this.$t('enabled'), value: 'enable', sortable: false, width: '120px' },
        { text: this.$t('marketplace.shop_name'), value: 'seller_shop_name', sortable: false, width: '400px' },
        { text: this.$t('marketplace.token_expire'), value: 'refresh_token_expire', sortable: false, width: '150px' },
        { text: this.$t('home.status'), value: 'status', sortable: false }
      ]
    }
  },
  methods: {
    update (data: any) {
      this.$emit('input', { ...(this.value || {}), ...data })
    },
    getStatus (marketplace: Marketplace) {
      if (['shopee', 'lazada'].includes(marketplace.marketplace)) {
        return moment().isBefore(marketplace.refresh_token_expire)
      }
      if (marketplace.marketplace === 'zort') {
        return true
      }
      return true
    },
    checkMarketplaceStatus (mp: Marketplace) {
      const expire_in = moment(mp.refresh_token_expire).diff(moment(), 'days')
      if (!mp.is_active) {
        return false
      }

      if (expire_in < 0) {
        return false
      }

      if (!mp.access_token) {
        return false
      }

      if (!mp.refresh_token) {
        return false
      }

      return true
    }
  }
})
</script>
