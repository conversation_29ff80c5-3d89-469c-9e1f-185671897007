
<template>
  <v-form @submit.prevent="">
    <v-row>
      <v-col class="pb-0" cols="8">
        <v-text-field
        data-test="search-user"
          :value="formData.search"
          outlined
          dense
          prepend-inner-icon="mdi-magnify"
          :placeholder="$t('settings.search')"
          @input="update({ search: $event })"
        />
      </v-col>
      <v-col class="pb-0" cols="4">
        <v-select
          :value="formData.permissions"
          :label="$t('settings.permission')"
          :items="permission_list"
          item-text="name"
          item-value="codename"
          deletable-chips
          chips
          multiple
          outlined
          small-chips
          dense
          @change="update({ permissions: $event })"
        />
      </v-col>
    </v-row>
    <v-checkbox
      :value="formData.show_delete_user"
      class="mt-0"
      :label="$t('settings.delete-users')"
      @change="update({ show_delete_user: $event })"
    />
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import { PagePermission, Permission } from '~/models'
export default Vue.extend({
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      permission_list: [] as Permission[]
    }
  },
  async created () {
    // get permission list
    const res = await this.$axios.get('/api/users/page-permission/')
    res.data.forEach((permission: PagePermission) => {
      permission.permissions.forEach((p :Permission) => this.permission_list.push(({
        ...p,
        name: this.$t('permission.' + p.codename)
      })))
    })
  },
  methods: {
    update (data: any) {
      this.$emit('update:form-data', { ...this.formData, ...data })
    }
  }
})
</script>
