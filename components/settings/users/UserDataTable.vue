<template>
  <v-data-table
    :headers="table.headers"
    :items="table.items"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <!-- Permission Tooltips -->
    <template #item.permissions="{ item }">
      <v-tooltip bottom max-width="600">
        <template #activator="{ on, attrs }">
          <v-btn
            v-bind="attrs"
            plain
            v-on="on"
          >
            {{ item.user_permissions.length }}/{{ permissions.length }}
            <v-icon
              right
            >
              mdi-shield-account
            </v-icon>
          </v-btn>
        </template>

        <v-chip v-for="perm in item.user_permissions" :key="perm" class="ma-1" small>
          {{ permissions_map[perm] }}
        </v-chip>
      </v-tooltip>
    </template>

    <!-- Actions -->
    <template v-if="$hasPerms('change_user')" #item.action="{ item }">
      <v-btn v-if="item.is_active" icon :to="{path: `/settings/users/${item.id}`}">
        <v-icon>mdi-pencil</v-icon>
      </v-btn>
      <v-btn v-else icon @click="$emit('bring-user-back',item.id)">
        <v-icon>mdi-arrow-down-bold-box-outline</v-icon>
      </v-btn>
    </template>
  </v-data-table>
</template>

<script lang="ts">
import Vue from 'vue'
import { Permission, User } from '~/models'
import { VDataTable, VDataTableHeader } from '~/models/vuetify'
export default Vue.extend({
  props: {
    items: {
      type: Array,
      default () {
        return []
      }
    },
    permissions: {
      type: Array,
      default: () => {
        return [] as Permission[]
      }
    }
  },
  computed: {
    table (): VDataTable {
      const headers: VDataTableHeader[] = [
        { text: this.$t('settings.username'), value: 'username' },
        { text: this.$t('settings.first-name'), value: 'first_name' },
        { text: this.$t('settings.last-name'), value: 'last_name' },
        { text: this.$t('settings.permissions'), value: 'permissions' },
        { text: '', value: 'action' }
      ]

      const items : any = this.items as any as User

      return {
        headers,
        items
      }
    },
    permissions_map (): {[key: string]: string} {
      const result = {} as {[key: string]: string}
      const permissions = this.permissions as Permission[]
      for (const perm of permissions) {
        result[perm.codename] = this.$t('permission.' + perm.codename)
      }
      return result
    }
  }

})
</script>

<style scoped>

</style>
