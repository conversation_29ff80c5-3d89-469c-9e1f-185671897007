<template>
  <div>
    <h4 class="mb-2">
      {{ $t('settings.personal') }}
    </h4>
    <v-text-field
      data-test="first-name"
      :label="$t('settings.first-name')"
      :value="formData.first_name"
      :disabled="disabled.includes('first_name')"
      @change="update({first_name: $event})"
    />
    <v-text-field
      data-test="last-name"
      :label="$t('settings.last-name')"
      :value="formData.last_name"
      :disabled="disabled.includes('last_name')"
      @change="update({last_name: $event})"
    />

    <h4 class="mt-5 mb-2">
      {{ $t('settings.user-password') }}
    </h4>
    <v-text-field
      data-test="username"
      :label="$t('settings.username')"
      filled
      :suffix="usernameSuffix"
      :rules="disabled.includes('username') ? [] : [$rules.required, $rules.username(usernameSuffix)]"
      :value="formData.username"
      :disabled="disabled.includes('username')"
      @change="update({username: $event})"
    />
    <password-input

      v-if="!hidePassword"
      filled
      :label="$t('password')"
      :value="formData.password"
      @input="update({password:$event})"
    />
    <confirm-input
      v-if="!hidePassword"


      filled
      :password="formData.password"
      :value="formData.confirm"
      @input="update({confirm:$event})"
    />
    <v-btn
      v-if="hidePassword"
      small
      outlined
      data-test="edit-password"
      block
      class="mb-5"
      @click="$emit('update:hide-password', false)"
    >
      {{ $t('settings.edit-password') }}
    </v-btn>
    
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import ConfirmInput from './ConfirmInput.vue'
import PasswordInput from './PasswordInput.vue'

export default Vue.extend({
  components: { ConfirmInput, PasswordInput },
  props: {
    formData: { type: Object, required: true },
    hidePassword: { type: Boolean, default: false },
    usernameSuffix: { type: String, default: '' },
    disabled: { type: Array, default: () => [] }
  },

  methods: {
    update (data: any) {
      this.$emit('update:form-data', { ...this.formData, ...data })
    }
  }
})
</script>

<style scoped>

</style>
