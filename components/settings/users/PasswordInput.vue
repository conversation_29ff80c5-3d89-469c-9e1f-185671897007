<template>
  <v-text-field
  data-test="password"
    v-bind="$attrs"
    :label="label"
    :rules="rules"
    :value="value"
    :append-icon="seePassword ? 'mdi-eye' : 'mdi-eye-off'"
    :type="seePassword ? 'text' : 'password'"
    @input="$emit('input',$event)"
    @click:append="seePassword = !seePassword"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import { rules } from '@/plugins/rules'

export default Vue.extend({
  props: {
    value: { type: String, required: true },
    label: { type: String, default: 'Password' },
    confirm: { type: Boolean, default: false },
    checkWith: { type: String, default: '' },
    rules: {
      type: Array,
      default () {
        return [rules.required, rules.password]
      }
    }
  },
  data () {
    return {
      seePassword: false
    }
  }
})
</script>

<style scoped>

</style>
