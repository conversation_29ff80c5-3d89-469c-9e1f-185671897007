<template>
  <v-text-field
    v-bind="$attrs"
  data-test="confirm-password"

    :label="label"
    :rules="[
      $rules.required,
      $rules.sameAs(password)
    ]"
    :value="value"
    :append-icon="seePassword ? 'mdi-eye' : 'mdi-eye-off'"
    :type="seePassword ? 'text' : 'password'"
    @input="$emit('input',$event)"
    @click:append="seePassword = !seePassword"
  />
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: {
    password: { type: String, required: true },
    label: {
      type: String,
      default () {
        return this.$t('settings.confirm')
      }
    },
    value: { type: String, required: true }
  },

  data () {
    return {
      seePassword: false
    }
  }
})
</script>

<style scoped>

</style>
