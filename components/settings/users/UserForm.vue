<template>
  <v-form ref="form">
    <v-row>
      <v-col cols="8">
        <user-data-form
          :form-data="formData"
          :hide-password="hidePassword"
          :disabled="disabled"
          :username-suffix="usernameSuffix"
          :error="error"
          @update:form-data="update($event)"
          @update:hide-password="$emit('update:hide-password',false)"
        />
        <v-alert v-if="error.detail || error.username || error.password" outlined dark color="error">
          <span v-if="error.detail">{{ error.detail }}</span>
          <span v-if="error.username">{{ error.username[0] }}</span>
          <span v-if="error.password">{{ error.pessword[0] }}</span>
        </v-alert>
      </v-col>
      <v-col cols="4">
        <permission-form
          :user-permissions="formData.user_permissions"
          :validate.sync="permission_validate"
          @update:user-permissions="update({user_permissions:$event})"
        />
      </v-col>
    </v-row>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import PermissionForm from './PermissionForm.vue'
import UserDataForm from './UserDataForm.vue'
import { VForm } from '~/models'
export default Vue.extend({
  name: 'UserForm',
  components: { PermissionForm, UserDataForm },

  props: {
    formData: { type: Object, required: true },
    hidePassword: { type: Boolean, default: false },
    disabled: { type: Array, default: () => [] },
    usernameSuffix: { type: String, default: '' },
    error: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      permission_validate: false
    }
  },

  methods: {
    update (data: any) {
      this.$emit('update:form-data', { ...this.formData, ...data })
    },

    validate (): boolean {
      if (this.formData.user_permissions.length === 0) {
        this.permission_validate = true
      }

      const form: VForm = this.$refs.form as never
      return form.validate() && !(this.formData.user_permissions.length === 0)
    }
  }
})
</script>
