<template>
  <div>
    <h4 class="mb-2">
      {{ $t('settings.permissions') }}
    </h4>
    <v-treeview
      v-if="items.length > 0"
      type="checkbox"
      selectable
      item-disabled="locked"
      item-key="code"
      item-text="name"

      dense
      selected-color="primary"
      :items="items"
      :value="userPermissions"
      @input="update($event)"
    />

    <v-alert v-if="validate" outlined class="mt-3" dark color="error">
      {{ $t('settings.select-one') }}
    </v-alert>
  </div>
</template>

<script lang="ts">
import _ from 'lodash'
import Vue from 'vue'
import { PagePermission } from '~/models'
const delay = (ms:number) => new Promise(resolve => setTimeout(resolve, ms))
export interface PermissionTreeviewItem {
    name: string;
    code: string;
    children: PermissionTreeviewChild[];
}

export interface PermissionTreeviewChild {
    name: string;
    code: string;
    depends_on: any[];
    locked: boolean;
}

export default Vue.extend({
  props: {
    validate: {
      type: Boolean,
      default: false
    },
    userPermissions: {
      type: Array,
      required: true
    }
  },
  data () {
    return {
      items: [] as PermissionTreeviewItem[],
      loading: false
    }
  },
  watch: {
    userPermissions: {
      handler () {
        if (this.userPermissions.length === 0) {
          this.$emit('update:validate', true)
        } else {
          this.$emit('update:validate', false)
        }
      }
    }
  },
  // eslint-disable-next-line require-await
  async created () {
    this.loading = true
    const res = await this.$axios.get('/api/users/page-permission/')
    const page_permissions: PagePermission[] = res.data
    this.items = page_permissions.map((p: PagePermission) => {
      return {
        name: p.page,
        code: p.page_url,
        children: p.permissions.map(x => ({
          name: this.$t('permission.' + x.codename),
          code: x.codename,
          depends_on: x.depends_on,
          locked: x.depends_on.length > 0
        }))
      }
    })

    await delay(500)
    for (const item of this.userPermissions as string[]) {
      this.unlockDependingTreeviewChildren(item)
    }
    this.loading = false
  },
  methods: {
    update (data: string[]) {
      if (this.loading) {
        return
      }

      const added_items = _.difference(data, this.userPermissions) as string[]
      const removed_items = _.difference(this.userPermissions, data) as string[]

      if (added_items.length > 0) {
        this.unlockDependingTreeviewChildren(added_items[0])
      }

      if (removed_items.length > 0) {
        this.lockDependingTreeviewChildren(removed_items[0])
        data = this.removeDependingPermissions(data, removed_items[0])
      }

      this.$emit('update:user-permissions', data)
    },

    unlockDependingTreeviewChildren (permission_code: string) {
      const items = this.getDependingTreeviewChildren(permission_code)
      for (const item of items) {
        item.locked = false
      }
    },

    lockDependingTreeviewChildren (permission_code: string) {
      const items = this.getDependingTreeviewChildren(permission_code)
      for (const item of items) {
        item.locked = true
      }
    },

    removeDependingPermissions (permission_codes: string[], permission_code: string): string[] {
      const items = this.getDependingTreeviewChildren(permission_code)
      return _.difference(permission_codes, items.map(i => i.code))
    },

    getDependingTreeviewChildren (permission_code: string): PermissionTreeviewChild[] {
      const children = [] as PermissionTreeviewChild[]
      for (const item of this.items) {
        for (const child of item.children) {
          if (child.depends_on.includes(permission_code)) {
            children.push(child)
          }
        }
      }
      return children
    }
  }
})
</script>
