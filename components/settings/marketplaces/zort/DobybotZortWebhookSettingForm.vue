<template>
  <v-form ref="form" @submit.prevent="">
    <h3>ตั้งค่า Webhook จาก Dobybot → Zort</h3>
    <div class="mt-3">
      <div class="font-weight-bold">
        ส่งคำสั่ง READY TO SHIP (Dobybot → Zort → Lazada/Shopee/Tiktok)
      </div>
      <div>
        <ul>
          <li>เปลี่ยนสถานะคำสั่งซื้อใน Zort จาก "รอโอนสินค้า" เป็น "รอส่งสินค้า"</li>
          <li>ถ้าเป็นคำสั่งซื้อที่มาจาก Marketplace (Lazada/TikTok) Zort จะทำการอัพเดตสถานะใน Marketplace นั้น ๆ ด้วย</li>
          <li>ถ้าเป็นคำสั่งซื้อจาก Shopee สถานะคำสั่งซื้อจะเป็น "รอส่งสินค้า" อยู่แล้วเมื่อทำการพิมพ์ใบปะหน้า ไม่จำเป็นต้องทำการส่งคำสั่ง READY TO SHIP</li>
        </ul>
      </div>

      <div class="pl-6 mt-1">
        <v-row dense>
          <v-col cols="12" md="6">
            <v-select
              label="ส่งคำสั่งเมื่อ"
              filled
              hide-details="auto"
              :items="webhook_trigger_choices"
              :value="settings.ZORT_API_V2_READY_TO_SHIP_HOOK"
              @change="update({ ZORT_API_V2_READY_TO_SHIP_HOOK: $event })"
            />
          </v-col>
        </v-row>
      </div>
      <div v-if="settings.ZORT_API_V2_READY_TO_SHIP_HOOK !== ''" class="pl-6 mt-3">
        <div class="font-weight-bold">
          ตั้งค่าคำสั่ง READY TO SHIP
        </div>
        <div>กรุณาระบุช่องทางขนส่ง สำหรับคำสั่งซื้อที่มาจาก Marketplace (Lazada/Shopee/TikTok/)</div>
        <v-row dense wrap>
          <v-col cols="6">
            <v-text-field
              label="shipment (ช่องทางขนส่ง) Lazada"
              filled
              hide-details="auto"
              :rules="[$rules.required]"
              :value="settings.ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG.shipment.lazada"
              @change="update({ 'ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG.shipment.lazada': $event })"
            />
          </v-col>
          <v-col cols="6">
            <v-select
              label="shipment (ช่องทางขนส่ง) Shopee"
              filled
              hide-details="auto"
              :rules="[$rules.required]"
              :items="[
                {text: 'ไม่ส่ง', value: 'none'}
              ]"
              :value="settings.ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG.shipment.shopee"
              @change="update({ 'ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG.shipment.shopee': $event })"
            />
          </v-col>
          <v-col cols="6">
            <v-select
              label="shipment (ช่องทางขนส่ง) TikTok"
              filled
              hide-details="auto"
              :rules="[$rules.required]"
              :items="['pickup', 'dropoff']"
              :value="settings.ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG.shipment.tiktok"
              @change="update({ 'ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG.shipment.tiktok': $event })"
            />
          </v-col>
          <v-col cols="6">
            <v-select
              label="shipment (ช่องทางขนส่ง) อื่น ๆ"
              filled
              hide-details="auto"
              :rules="[$rules.required]"
              :items="[
                {text: 'ไม่ส่ง', value: 'none'}
              ]"
              :value="settings.ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG.shipment.other"
              @change="update({ 'ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG.shipment.other': $event })"
            />
          </v-col>
          <v-col cols="6">
            <v-text-field
              label="warehousecode (รหัสคลังสินค้า/สาขา)"
              hint=""
              persistent-hint
              :value="settings.ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG.warehousecode"
              @change="update({ 'ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG.warehousecode': $event })"
            />
          </v-col>
        </v-row>
        <v-row dense>
          <!-- <v-text-field label="address (รหัสคลังสินค้า/สาขา)" hint="Pickup location keyword for pickup.  Required if the shipment is Shopee Pickup.  Ex. '10420' - Postcode of pickup location." persistent-hint /> -->
          <!-- <v-text-field label="pickuptime" hint="Shopee pickup datetime keyword for pickup.  Not Required for Shopee Pickup.  If this is blank, the system will choose earliest time.  Ex. 18-06-2020" persistent-hint /> -->
          <!-- <v-text-field label="booking" hide-details="auto" />
            <div>
              Booking Request Status<br>
              0 - None (Default)<br>
              1 - Call shipment provider. The available shipment is “flashexpress” (Flash Express), “jtexpress” (J&T) and “kerry” (Kerry). Support only non-marketplace order.
            </div> -->
        </v-row>
      </div>
    </div>

    <div class="mt-5">
      <div class="font-weight-bold">
        ส่งคำสั่ง UPDATE ORDER STATUS (Dobybot → Zort)
      </div>
      <div>
        <ul>
          <li>เปลี่ยนสถานะคำสั่งซื้อใน Zort เป็น "รอส่ง", "กำลังส่ง", "สำเร็จ"</li>
          <li>การเปลี่ยนสถานะเป็น "กำลังส่ง" หรือ "สำเร็จ" ระบบจะทำการตัด Stock ใน Zort</li>
        </ul>
      </div>
      <div class="pl-6 mt-1">
        <v-row dense>
          <v-col cols="12" md="6">
            <v-select
              filled
              label="ส่งคำสั่งเมื่อ"
              hide-details="auto"
              :items="webhook_trigger_choices"
              :value="settings.ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK"
              @change="update({ ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK: $event })"
            />
          </v-col>
        </v-row>
      </div>
      <div v-if="settings.ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK !== ''" class="pl-6 mt-4">
        <div class="font-weight-bold">
          ตั้งค่าคำสั่ง UPDATE ORDER STATUS
        </div>
        <!-- <v-text-field
          label="label"
          :value="formData.text"
          @change="update({ text: $event })"
        /> -->
        <v-row dense>
          <v-col cols="6">
            <v-select
              filled
              label="status (เปลี่ยนสถานะคำสั่งซื้อเป็น)"
              :rules="[ $rules.required ]"
              :items="['Waiting', 'Packed', 'Shipping', 'Success']"
              :value="settings.ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG.status"
              @change="update({ 'ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG.status': $event })"
            />
          </v-col>
          <v-col cols="6">
            <v-text-field
              label="warehousecode (รหัสคลังสินค้า/สาขา)"
              hint=""
              persistent-hint
              :value="settings.ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG.warehousecode"
              @change="update({ 'ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG.warehousecode': $event })"
            />
          </v-col>
        </v-row>
      </div>
    </div>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import _ from 'lodash'
import { VForm } from '~/models'
export default Vue.extend({
  props: {
    settings: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      webhook_trigger_choices: [
        { text: 'ไม่ส่ง', value: '' },
        { text: 'เริ่มอัดวิดีโอ', value: 'pre_record' },
        { text: 'หลังอัดวิดีโอเสร็จ', value: 'post_record' },
        { text: 'สถานะออเดอร์ถูกเปลี่ยนเป็น Ready to ship', value: 'ready_to_ship' }
      ]
    }
  },
  methods: {
    update (data: object) {
      const settings = JSON.parse(JSON.stringify({
        ZORT_API_V2_READY_TO_SHIP_HOOK: this.settings.ZORT_API_V2_READY_TO_SHIP_HOOK,
        ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG: this.settings.ZORT_API_V2_READY_TO_SHIP_HOOK_CONFIG,
        ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK: this.settings.ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK,
        ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG: this.settings.ZORT_API_V2_UPDATE_ORDER_STATUS_HOOK_CONFIG
      }))
      for (const [path, value] of Object.entries(data)) {
        _.set(settings, path, value)
      }
      this.$emit('update:settings', settings)
    },

    validate () {
      const form: VForm = this.$refs.form as never as VForm
      return form.validate()
    }
  }
})
</script>
