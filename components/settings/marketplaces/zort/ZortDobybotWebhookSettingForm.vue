<template>
  <v-form ref="form" @submit.prevent="">
    <h3>ตั้งค่า Webhook จาก Zort → Dobybot</h3>
    <v-row dense class="mt-3">
      <v-col style="max-width: 100px;">
        <v-switch
          class="ml-5 my-0"
          hide-details="auto"
          :input-value="webhooks.add_order_url != ''"
          :loading="loading"
          @change="updateWebhookSetting({ add_order_url: $event ? get_add_order_url() : '' })"
        />
      </v-col>
      <v-col class="my-auto">
        <div class="font-weight-bold">
          ADDORDER
          <span v-if="$auth.user.is_staff">
            ({{ webhooks.add_order_url || '-' }})
          </span>
        </div>
        <div>
          เมื่อมีการ <b>เพิ่ม</b> คำสั่งซื้อใหม่ ส่งข้อมูลคำสั่งซื้อมาที่ Dobybot แบบ realtime
        </div>
      </v-col>
    </v-row>
    <v-row dense class="mt-3">
      <v-col style="max-width: 100px;">
        <v-switch
          class="ml-5 my-0"
          hide-details="auto"
          :input-value="webhooks.update_order_url != ''"
          :loading="loading"
          @change="updateWebhookSetting({ update_order_url: $event ? get_update_order_url() : '' })"
        />
      </v-col>
      <v-col class="my-auto">
        <div class="font-weight-bold">
          UPDATEORDER
          <span v-if="$auth.user.is_staff">
            ({{ webhooks.update_order_url || '-' }})
          </span>
        </div>
        <div>เมื่อมีการ <b>แก้ไขหรืออัพเดตสถานะ</b> คำสั่งซื้อ ส่งข้อมูลคำสั่งซื้อมาที่ Dobybot แบบ realtime</div>
      </v-col>
    </v-row>
    <v-row dense class="mt-3">
      <v-col style="max-width: 100px;">
        <v-switch
          class="ml-5 my-0"
          hide-details="auto"
          :input-value="webhooks.update_order_tracking_url != ''"
          :loading="loading"
          @change="updateWebhookSetting({ update_order_tracking_url: $event ? get_update_order_tracking_url() : '' })"
        />
      </v-col>
      <v-col class="my-auto">
        <div class="font-weight-bold">
          UPDATEORDERTRACKING
          <span v-if="$auth.user.is_staff">
            ({{ webhooks.update_order_tracking_url || '-' }})
          </span>
        </div>
        <div>เมื่อมีการ <b>อัพเดตเลขที่ tracking</b> ของคำสั่งซื้อ ส่งข้อมูลคำสั่งซื้อมาที่ Dobybot แบบ realtime</div>
      </v-col>
    </v-row>
  </v-form>
</template>

<script lang="ts">
import Vue, { PropOptions } from 'vue'
import { mapMutations } from 'vuex'
import { Marketplace, VForm } from '~/models'
import { SettingState } from '~/store/settings'

export default Vue.extend({
  props: {
    webhooks: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      loading: false
    }
  },
  methods: {
    validate () {
      const form: VForm = this.$refs.form as never as VForm
      return form.validate()
    },

    async updateWebhookSetting (data: object) {
      const webhooks = { ...this.webhooks, ...data }
      this.loading = true

      try {
        await this.$axios.post('/api/companies/dobybot-connect/zort/webhook/', webhooks)
      } catch (error) {
        console.error(error)
        this.$snackbar('error', this.$t('error.unknown'))
      }
      this.loading = false
      this.$emit('update:webhooks', webhooks)
    },
    get_webhook_host () {
      const hostname = window.location.hostname
      if (hostname === 'localhost') {
        return 'https://job8000.dobybot.com'
      } else if (hostname === 'uat.dobybot.com') {
        return 'https://api-uat.dobybot.com'
      } else if (hostname === 'cloud.dobybot.com') {
        return 'https://dobybot-task-service-yuo4mnnlaa-as.a.run.app'
      }
    },
    get_add_order_url () {
      return `${this.get_webhook_host()}/api/picking/zort/webhook/order/`
    },
    get_update_order_url () {
      return `${this.get_webhook_host()}/api/picking/zort/webhook/order/`
    },
    get_update_order_tracking_url () {
      return `${this.get_webhook_host()}/api/picking/zort/webhook/order/update-order-tracking/`
    }
  }
})
</script>
