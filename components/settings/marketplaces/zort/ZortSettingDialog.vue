<template>
  <v-dialog max-width="800" :value="value" persistent>
    <v-card>
      <v-card-title class="headline grey lighten-2">
        ตั้งค่าการเชื่อมต่อ Zort
        <v-spacer />
        <v-btn data-test-id="v-btn-close" icon @click="close()">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text class="pt-3">
        <h3 class="mt-5">
          การเชื่อมต่อข้อมูลคำสั่งซื้อ
        </h3>
        <v-row dense class="mt-3">
          <v-col style="max-width: 100px;">
            <v-switch
              class="ml-5 my-0"
              hide-details
              :input-value="settings.company.ZORT_API_V2.sync_order"
              :loading="loading"
              @change="updateZortSetting({ sync_order: $event })"
            />
          </v-col>
          <v-col class="my-auto">
            <div class="font-weight-bold">
              เชื่อมต่อข้อมูลคำสั่งซื้อ
            </div>
            <div>ระบบจะนำเข้ารายการขายล่าสุด ทุก ๆ 15 นาที</div>
          </v-col>
        </v-row>

        <v-divider class="my-5" />
        <zort-dobybot-webhook-setting-form
          ref="form1"
          :webhooks="settings.company.ZORT_API_V2_WEBHOOKS"
          @update:webhooks="$emit('update:webhooks', {ZORT_API_V2_WEBHOOKS: $event})"
        />

        <v-divider class="my-5" />
        <dobybot-zort-webhook-setting-form
          ref="form2"
          :settings="settings.company"
          @update:settings="$emit('update:settings', $event)"
        />
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import DobybotZortWebhookSettingForm from './DobybotZortWebhookSettingForm.vue'
import ZortDobybotWebhookSettingForm from './ZortDobybotWebhookSettingForm.vue'
import { SettingState } from '~/store/settings'
import { VForm } from '~/models'
export default Vue.extend({
  components: { ZortDobybotWebhookSettingForm, DobybotZortWebhookSettingForm },

  props: {
    value: {
      type: Boolean,
      required: true
    },

    zort: {
      type: Object,
      required: true
    }
  },

  data () {
    return {
      loading: false
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },

  methods: {
    close () {
      const form1 = this.$refs.form1 as never as VForm
      const form2 = this.$refs.form2 as never as VForm
      console.log('close', form1, form2, form1.validate(), form2.validate())

      if (!form1.validate() || !form2.validate()) {
        this.$snackbar('error', this.$t('error.please_fix_form_error'))
        return
      }

      this.$emit('input', false)
    },

    async updateZortSetting (data: any) {
      const settings = { ...this.settings.company.ZORT_API_V2, ...data }

      this.loading = true
      try {
        const payload = {
          marketplace: this.zort.marketplace,
          shop_id: this.zort.seller_id,
          settings
        }
        await this.$axios.put(
          '/api/companies/dobybot-connect/shop-settings/',
          payload
        )
        this.$emit('update:zort', { ZORT_API_V2: settings })
      } catch (error) {
        this.$snackbar('error', this.$t('error.unknown'))
      }
      this.loading = false
    }
  }
})
</script>
