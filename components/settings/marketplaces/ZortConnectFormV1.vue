
<template>
  <v-card flat>
    <v-card-text class="py-5">
      <v-row>
        <v-col xs="12">
          <v-img
            class="mx-auto"
            :max-width="400"
            src="https://storage.googleapis.com/dobybot-public-bucket/images/dobybot-connect/zort-logo.png"
          />

          <h3>วิธีการเชื่อมต่อ</h3>
          <ol>
            <li>
              ไปที่ <a target="_blank" href="https://secure.zortout.com/Integration/ApiReference?">https://secure.zortout.com/Integration/ApiReference?</a>
            </li>
            <li>
              และคัดลอก storename, apikey, apisecret ลงในช่องด่านล่าง
            </li>
          </ol>
        </v-col>
        <v-col sm="12">
          <v-text-field
            label="STORENAME"
            filled
            hide-details
            :value="formData.ZORT_STORENAME"
            @change="update({ ZORT_STORENAME: $event })"
          />
        </v-col>
        <v-col sm="6">
          <v-text-field
            label="API KEY"
            filled
            hide-details
            :value="formData.ZORT_API_KEY"
            @change="update({ ZORT_API_KEY: $event })"
          />
        </v-col>
        <v-col sm="6">
          <v-text-field
            label="API SECRET"
            filled
            hide-details
            :value="formData.ZORT_API_SECRET"
            @change="update({ ZORT_API_SECRET: $event })"
          />
        </v-col>
      </v-row>
    </v-card-text>
    <v-card-actions>
      <v-btn class="mx-auto" color="primary" width="120" @click="submit()">
        SUBMIT
      </v-btn>
    </v-card-actions>
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: {
    formData: {
      type: Object,
      required: true
    }
  },

  methods: {
    update (data: any) {
      this.$emit('update:form-data', { ...this.formData, ...data })
    },
    submit () {
      this.$emit('submit')
    }
  }
})
</script>
