<template>
  <v-dialog
    :value="value"
    max-width="600"
    @click:outside="$emit('input', false)"
  >
    <v-card>
      <v-card-title>
        <span class="headline">เชื่อมต่อ NexGenCommerce</span>
        <v-spacer />
        <v-btn icon @click="$emit('input', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider />

      <v-card-text class="pt-3">
        <v-row class="my-3" justify="center">
          <v-img
            max-width="200"
            src="https://storage.googleapis.com/dobysync-bucket/image/nex-gen-commerce.png"
          />
        </v-row>
        <!-- <div>
          วิธีการเชื่อมต่อ LineMyShop
          <ol>
            <li>
              เข้าไปที่หน้า LineMyShop เลือกเมนู <code>Settings</code> และ
              <code>API Keys</code>
            </li>
            <li>
              กดปุ่ม Generate ใส่ชื่อ API Key และคัดลอก
              <code>API Key</code> กรอกลงในฟอร์มด้านล่าง
            </li>
          </ol>
        </div> -->
        <v-form ref="form" class="mt-5">
          <v-text-field
            v-model="form.storename"
            :rules="[$rules.required]"
            filled
            label="Store Name"
          />
          <v-text-field
            v-model="form.shop_id"
            :rules="[$rules.required]"
            filled
            label="Shop Id"
          />
        </v-form>

        <v-alert v-if="error" color="error" outlined>
          {{ error }}
        </v-alert>
      </v-card-text>
      <v-divider />

      <v-card-actions class="justify-center">
        <v-btn color="primary" @click="submit()">
          Submit
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { VForm } from '~/models'
export default Vue.extend({
  props: {
    value: {
      type: Boolean,
      required: true
    }
  },

  data () {
    return {
      form: {
        storename: '',
        shop_id: ''
      },
      error: ''
    }
  },

  methods: {
    async submit () {
      const form = this.$refs.form as any as never as VForm
      if (!form.validate()) {
        return
      }

      try {
        await this.$axios.post(
          '/api/companies/dobybot-connect/seller/auth/nex_gen_commerce/',
          this.form
        )
        this.$emit('connected')
        this.reset()
      } catch (error: any) {
        const res = error.response
        if (res.status === 400) {
          this.error = res.data.message
        } else {
          this.$snackbar('error', this.$t('error.unknown'))
          console.error(error)
        }
      }
    },

    reset () {
      this.form = {
        storename: '',
        shop_id: ''
      }
      this.error = ''
    }
  }
})
</script>
