<template>
  <v-data-table
    :footer-props="{'items-per-page-options': [5, 10, 25]}"
    :headers="headers"
    :items="items"
  >
    <template #header.status>
      <v-btn icon small @click="$emit('click:refresh')">
        <v-icon small>
          mdi-refresh
        </v-icon>
      </v-btn>
    </template>

    <template #item.status="{item}">
      <v-icon v-if="getStatus(item)" color="success">
        mdi-check
      </v-icon>
      <v-icon v-else color="error">
        mdi-close
      </v-icon>
    </template>
    <template #item.refresh_token_expire="{item}">
      {{ item.refresh_token_expire ? $date(item.refresh_token_expire) : '-' }}
    </template>

    <!-- <template #item.datetime="{item}">
      {{ $datetime(item.datetime) }}
    </template> -->
    <template #footer.prepend>
      <slot name="footer.prepend" />
    </template>
  </v-data-table>
</template>

<script lang="ts">
import moment from 'moment'
import Vue, { PropOptions } from 'vue'
import { Marketplace, VDataTableHeader } from '~/models'
export default Vue.extend({
  props: {
    items: {
      type: Array,
      required: true
    } as PropOptions<Marketplace[]>
    // add some props
  },
  computed: {
    headers (): VDataTableHeader[] {
      return [
        { text: '', value: 'status', sortable: false, width: 50 },
        { text: this.$t('marketplace.marketplace'), value: 'marketplace', sortable: false },
        { text: this.$t('marketplace.shop_name'), value: 'seller_shop_name', sortable: false },
        { text: this.$t('marketplace.token_expire'), value: 'refresh_token_expire', sortable: false }
      ]
    }
  },

  methods: {
    getStatus (marketplace: Marketplace) {
      if (['shopee', 'lazada'].includes(marketplace.marketplace)) {
        return moment().isBefore(marketplace.refresh_token_expire)
      }
      if (marketplace.marketplace === 'zort') {
        return true
      }
      return true
    }
  }
})
</script>
