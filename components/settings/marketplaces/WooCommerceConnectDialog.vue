<template>
  <v-dialog :value="value" max-width="600" @click:outside="$emit('input', false)">
    <v-card>
      <v-card-title>
        <span class="headline">เชื่อมต่อ WooCommerce</span>
        <v-spacer />
        <v-btn icon @click="$emit('input', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider />

      <v-card-text class="pt-3">
        <v-row class="my-1" justify="center">
          <v-img max-width="200" height="100px" src="https://storage.googleapis.com/dobysync-bucket/image/woo_commerce_logo.png" />
        </v-row>
        <div>
          เชื่อมต่อ WooCommerce
          <ol>
            <li>เข้าไปหน้า WooCommerce Admin และไป <code>WooCommerce</code> > <code>Settings</code> > <code>Advanced</code> > <code>REST API</code></li>
            <li>กดปุ่ม <code>Add key</code> เพื่อสร้าง API Key ใหม่</li>
            <li>ตั้งชื่อ Key และเป็น <code>Read/Write</code></li>
            <li>ลอก <code>Consumer Key</code> และ <code>Consumer Secret</code> มากรอกในฟอร์มด้านล่าง</li>
          </ol>
        </div>
        <v-form ref="form" class="mt-5">
          <v-text-field v-model="form.storename" :rules="[$rules.required]" filled label="Store name" />
          <v-text-field
            v-model="form.store_url"
            :rules="[$rules.required]"
            :error-messages="error_data.url"
            filled
            label="Store URL"
            hint="https://example.com"
          />
          <v-text-field v-model="form.client_key" :rules="[$rules.required]" filled label="Consumer Key" />
          <v-text-field v-model="form.client_secret" :rules="[$rules.required]" filled label="Consumer Secret" />
        </v-form>

        <v-alert v-if="error" color="error" outlined>
          {{ error }}
        </v-alert>
      </v-card-text>
      <v-divider />

      <v-card-actions class="justify-center">
        <v-btn color="primary" :loading="loading" @click="submit()">
          Submit
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { VForm } from '~/models'
export default Vue.extend({
  props: {
    value: {
      type: Boolean,
      required: true
    }
  },

  data () {
    return {
      form: {
        storename: '',
        store_url: '',
        client_key: '',
        client_secret: ''
      },
      loading: false,
      error: '',
      error_data: {} as any
    }
  },

  methods: {
    async submit () {
      const form = this.$refs.form as any as never as VForm
      if (!form.validate()) { return }

      this.loading = true
      this.error = ''

      try {
        await this.$axios.post('/api/companies/dobybot-connect/seller/auth/woo-commerce/', this.form)
        this.$emit('connected')
        this.reset()
        this.$snackbar('success', 'เชื่อมต่อร้านค้า WooCommerce สำเร็จ')
      } catch (error: any) {
        const res = error.response
        if (res.status === 400) {
          this.error = res.data.message
          this.error_data = res.data
        } else {
          this.$snackbar('error', this.$t('error.unknown').toString())
          console.error(error)
        }
      } finally {
        this.loading = false
      }
    },

    reset () {
      this.form = {
        storename: '',
        store_url: '',
        client_key: '',
        client_secret: ''
      }
      this.error = ''
      this.error_data = {}
    }
  }
})
</script>
