<template>
  <v-dialog max-width="600" :value="value" @input="$emit('input', $event)">
    <v-card>
      <v-card-title>
        ใช้งาน Marketplace V2
      </v-card-title>
      <v-divider />
      <v-card-text class="pt-3">
        <div>
          ฟีเจอร์ใหม่<br>
          <ul>
            <li>เพิ่มการเชื่อมต่อ NocNoc</li>
            <li>ปรับปรุงประสิทธิภาพ</li>
            <li>และอื่น ๆ มากมาย</li>
          </ul>
        </div>
        <div class="mt-3">
          หมายเหตุ:
          เมื่อเปลี่ยนไปใช้งาน Marketplace V2 แล้วท่านจะต้องทำการเชื่อมต่อ marketplace ต่าง ๆ อีกรอบ<br>
        </div>
      </v-card-text>
      <v-divider />
      <v-card-actions class="justify-center">
        <v-btn color="primary" :loading="loading" @click="submit()">
          ใช้งาน Marketplace V2
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    value: {
      type: Boolean,
      required: true
    }
  },

  data () {
    return {
      loading: false
    }
  },

  methods: {
    async submit () {
      try {
        this.loading = true
        await this.$axios.post('/api/companies/dobybot-connect/switch-to-v2/')
        this.$snackbar('success', 'Switched to v2 successfully')
        await this.$store.dispatch('settings/loadFromServer')
        this.loading = false

        this.$router.replace('/settings/marketplace/v2')
      } catch (error: any) {
        console.error(error)
        // TODO: show error message
        const response = error.response
        if (response) {
          if (response.status === 400) {
            this.$alert({
              theme: 'error',
              title: 'Error',
              text: response.data.message
            })
          }
          if (response.status >= 500) {
            this.$alert({
              theme: 'error',
              title: 'Error',
              text: 'Something went wrong. Please try again later.'
            })
          }
        }
      }
    }
  }
})
</script>
