<template>
  <v-card>
    <v-card-title>
      <span
        style="max-width: 380px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
        :title="shop.seller_shop_name"
      >
        <v-chip class="text-capitalize mr-2">
          {{ splitText(shop.marketplace, '_') }}
        </v-chip>
        {{ shop.seller_shop_name }}
      </span>
      <v-spacer />
      <v-btn v-if="features.includes('setting')" icon @click="$emit('click:setting')">
        <v-icon>mdi-cog</v-icon>
      </v-btn>
    </v-card-title>
    <v-divider />
    <v-card-text style="height: 300px; overflow-y: scroll;">
      <div>
        <label class="w-100">Shop</label>
        {{ shop.seller_shop_name }}
      </div>
      <div>
        <label class="w-100">Shop ID</label>
        {{ shop.seller_id }}
      </div>
      <div>
        <label class="w-100">API หมดอายุ</label>
        {{ shop.refresh_token_expire ? $date(shop.refresh_token_expire) : '-' }}
        <a v-if="shop.refresh_token_expire" href="#!" @click.prevent="$emit('reconnect')">ต่ออายุ</a>
        <div v-if="shop.refresh_token === false || !get(shop,'is_active', true)" class="red--text" style="padding-left: 100px;">
          เกิดข้อผิดพลาดในการเชื่อมต่อ Marketplace กรุณาทำการ <a href="#!" @click.prevent="$emit('reconnect')">เชื่อมต่อใหม่</a>

          <v-tooltip v-if="shop.remark" bottom>
            <template #activator="{ on, attrs }">
              <v-icon
                size="16"
                v-bind="attrs"
                v-on="on"
              >
                mdi-information
              </v-icon>
            </template>
            <span>{{ shop.remark }}</span>
          </v-tooltip>
        </div>
      </div>

      <div v-if="shop.marketplace == 'tiktok_shop'">
        <v-alert
          class="mt-3"
          text
          prominent
          type="error"
          icon="mdi-cloud-alert"
        >
          API Tiktok Shop Version 1 ระงับการใช้งานแล้ว กรุณากดเชื่อมต่อใหม่ Tiktok Shop ใหม่
          <v-btn color="success" class="mt-3" block size="lg" @click="$emit('reconnect')">
            เชื่อมต่อใหม่
          </v-btn>
        </v-alert>
      </div>

      <div v-if="shop.marketplace != 'tiktok_shop' || $auth.user.is_superuser">
        <v-row dense rounded class="mt-3">
          <v-col style="max-width: 100px;">
            <v-switch
              class="ml-5 my-0"
              hide-details
              :input-value="shopSetting.sync_order"
              :loading="loading"
              @change="updateShopSetting({ sync_order: $event })"
            />
          </v-col>
          <v-col class="my-auto">
            <div class="font-weight-bold">
              เชื่อมต่อข้อมูลคำสั่งซื้อ
            </div>
            <div>ระบบจะนำเข้ารายการขายล่าสุด ทุก ๆ 15 นาที</div>
          </v-col>
        </v-row>

        <v-row v-if="features.includes('ready_to_ship')" dense rounded class="mt-3">
          <v-col style="max-width: 100px;">
            <v-switch
              class="ml-5 my-0"
              hide-details
              :input-value="shopSetting.ready_to_ship"
              :loading="loading"
              @change="updateShopSetting({ ready_to_ship: $event })"
            />
          </v-col>
          <v-col class="my-auto">
            <div class="font-weight-bold">
              ส่งคำสั่ง ready to ship
            </div>
            <div>ระบบจะส่งคำสั่ง ready to ship ไปที่ Marketplace เมื่อสถานะออเดอร์ใน dobybot ถูกเปลี่ยนเป็น ready to ship</div>
          </v-col>
        </v-row>

        <v-row v-if="features.includes('airway_bill')" dense>
          <v-col style="max-width: 100px;">
            <v-switch
              class="ml-5 my-0"
              hide-details
              :input-value="shopSetting.airway_bill"
              disabled
            />
          </v-col>
          <v-col class="my-auto">
            <div class="font-weight-bold">
              ดาวโหลดน์ Airway Bill ผ่าน API
            </div>
            <div>
              แก้ไขใน
              <router-link :to="{path: '/settings/printing'}">
                ตั้งค่า Printing
              </router-link>
            </div>
          </v-col>
        </v-row>

        <v-row v-if="features.includes('chat')" dense>
          <v-col style="max-width: 100px;">
            <v-switch
              class="ml-5 my-0"
              hide-details
              disabled
              :input-value="shopSetting.chat"
            />
          </v-col>
          <v-col class="my-auto">
            <div class="font-weight-bold">
              ส่งลิงค์วิดีโอผ่าน Chat API
            </div>
            <div>
              แก้ไขใน
              <router-link :to="{path: '/settings/sms'}">
                ตั้งค่า Chat / SMS
              </router-link>
            </div>
          </v-col>
        </v-row>

        <v-row v-if="features.includes('sync_product')">
          <v-col style="max-width: 100px;">
            <v-btn fab color="primary" small class="ml-5" @click="$emit('click:sync-product')">
              <v-icon>mdi-database-sync</v-icon>
            </v-btn>
          </v-col>
          <v-col class="my-auto">
            <div class="font-weight-bold">
              เชื่อมต่อข้อมูลสินค้า
            </div>
            <div>
              คลิกที่นี่เพื่อทำการ sync ข้อมูลสินค้าจาก {{ shop.marketplace }}
            </div>
          </v-col>
        </v-row>
      </div>

      <slot name="dialog" />
    </v-card-text>
    <v-divider v-if="features.includes('disconnect')" />
    <v-card-actions>
      <template v-if="features.includes('disconnect')">
        <v-btn small outlined color="error" @click="$emit('click:disconnect')">
          ยกเลิกการเชื่อมต่อ
        </v-btn>
      </template>
    </v-card-actions>
  </v-card>
</template>

<script lang="ts">
import Vue, { PropOptions } from 'vue'
import _ from 'lodash'
import { Marketplace } from '~/models'
export default Vue.extend({
  props: {
    shop: {
      type: Object,
      required: true
    } as PropOptions<Marketplace>,
    shopSetting: {
      type: Object,
      default: () => ({
        sync_order: false,
        airway_bill: false,
        chat: false
      })
    },
    autoPersist: {
      type: Boolean,
      default: true
    },
    features: {
      type: Array,
      default: () => ['sync_order', 'airway_bill', 'chat']
    }
  },

  data () {
    return {
      loading: false,
      dialog: false
    }
  },

  methods: {
    splitText (text: string, delimiter: string) {
      return text.split(delimiter).join(' ')
    },

    async updateShopSetting (data: any) {
      const settings = { ...this.shopSetting, ...data }

      this.loading = true
      try {
        if (this.autoPersist) {
          const payload = {
            marketplace: this.shop.marketplace,
            shop_id: this.shop.seller_id,
            settings
          }
          await this.$axios.put(
            '/api/companies/dobybot-connect/shop-settings/',
            payload
          )
        }
        this.$emit('update:shop-setting', settings)
      } catch (error) {
        this.$snackbar('error', this.$t('error.unknown'))
      }
      this.loading = false
    },

    get (obj: any, path: string, defaultValue: any) {
      return _.get(obj, path, defaultValue)
    }
  }
})
</script>
