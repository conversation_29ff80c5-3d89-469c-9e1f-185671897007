<template>
  <v-dialog :value="value" max-width="600" @click:outside="$emit('input', false)">
    <v-card>
      <v-card-title>
        <span class="headline">เชื่อมต่อ NocNoc</span>
        <v-spacer />
        <v-btn icon @click="$emit('input', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider />

      <v-card-text class="pt-3">
        <v-row class="my-3" justify="center">
          <v-img max-width="200" src="https://storage.googleapis.com/dobybot-public-bucket/images/dobybot-connect/nocnoc-logo.png" />
        </v-row>
        <div>
          วิธีการเชื่อมต่อ NocNoc
          <ol>
            <li>แจ้งความประสงค์ขอเชื่อมต่อระบบ Dobybot กับผู้ดูแลร้านจากทาง NocNoc เพื่อขอ <code>client id</code> และ <code>secret key</code></li>
            <li>นำข้อมูล <code>client id</code> และ <code>secret key</code> กรอกลงในฟอร์มด้านล่าง</li>
          </ol>
        </div>
        <v-form ref="form" class="mt-5">
          <v-text-field v-model="form.storename" :rules="[$rules.required]" filled label="Store name" />
          <v-text-field v-model="form.client_id" :rules="[$rules.required]" filled label="Client ID" />
          <v-text-field v-model="form.secret_key" :rules="[$rules.required]" filled label="Client Secret" />
        </v-form>

        <v-alert v-if="error" color="error" outlined>
          {{ error }}
        </v-alert>
      </v-card-text>
      <v-divider />

      <v-card-actions class="justify-center">
        <v-btn color="primary" @click="submit()">
          Submit
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { VForm } from '~/models'
export default Vue.extend({
  props: {
    value: {
      type: Boolean,
      required: true
    }
  },

  data () {
    return {
      form: {
        storename: '',
        client_id: '',
        secret_key: ''
      },
      error: ''
    }
  },

  methods: {
    async submit () {
      const form = this.$refs.form as any as never as VForm
      if (!form.validate()) { return }

      try {
        await this.$axios.post('/api/companies/dobybot-connect/seller/auth/nocnoc/', this.form)
        this.$emit('connected')
        this.reset()
      } catch (error: any) {
        const res = error.response
        if (res.status === 400) {
          this.error = res.data.message
        } else {
          this.$snackbar('error', this.$t('error.unknown'))
          console.error(error)
        }
      }
    },

    reset () {
      this.form = {
        storename: '',
        client_id: '',
        secret_key: ''
      }
      this.error = ''
    }
  }
})
</script>
