
<template>
  <v-card flat>
    <v-card-title>
      เชื่อมต่อ Zort (V2)
    </v-card-title>
    <v-divider />

    <v-card-text class="py-5">
      <v-form ref="form">
        <v-row>
          <v-col xs="12">
            <v-img
              class="mx-auto"
              :max-width="400"
              src="https://storage.googleapis.com/dobybot-public-bucket/images/dobybot-connect/zort-logo.png"
            />

            <h3>วิธีการเชื่อมต่อ</h3>
            <ol>
              <li>
                ไปที่ <a target="_blank" href="https://secure.zortout.com/Integration/ApiReference?">https://secure.zortout.com/Integration/ApiReference?</a>
              </li>
              <li>
                และคัดลอก storename, apikey, apisecret ลงในช่องด่านล่าง
              </li>
            </ol>
          </v-col>
          <v-col sm="12">
            <v-alert v-if="error" color="error" outlined>
              {{ error }}
            </v-alert>
          </v-col>

          <v-col sm="12">
            <v-text-field
              v-model="storename"
              label="STORENAME"
              filled
              hide-details
              :rules="[$rules.required]"
            />
          </v-col>
          <v-col sm="6">
            <v-text-field
              v-model="apikey"
              label="API KEY"
              filled
              hide-details
              :rules="[$rules.required]"
            />
          </v-col>
          <v-col sm="6">
            <v-text-field
              v-model="apisecret"
              label="API SECRET"
              filled
              hide-details
              :rules="[$rules.required]"
            />
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
    <v-card-actions>
      <v-btn class="mx-auto" color="primary" width="120" @click="submit()">
        SUBMIT
      </v-btn>
    </v-card-actions>
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import { VForm } from '~/models'

export default Vue.extend({
  props: {

  },

  data () {
    return {
      storename: '',
      apikey: '',
      apisecret: '',

      error: ''
    }
  },

  methods: {
    async submit () {
      this.error = ''
      const form = this.$refs.form as never as VForm
      if (!form.validate()) {
        return
      }

      const payload = {
        storename: this.storename,
        apikey: this.apikey,
        apisecret: this.apisecret
      }

      try {
        await this.$axios.post('/api/companies/dobybot-connect/seller/auth/zort/', payload)
        this.$snackbar('success', this.$t('success'))
        this.$emit('success')
      } catch (error: any) {
        console.error(error)

        if (error.response) {
          if (error.response.status === 400) {
            this.error = error.response.data
          } else {
            this.error = 'เกิดข้อผิดพลาด กรุณาติดต่อทีมงาน Dobybot Support'
          }
        }
      }
    }
  }
})
</script>
