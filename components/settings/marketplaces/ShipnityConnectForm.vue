<template>
  <v-card flat>
    <v-card-title>
      เชื่อมต่อ Shipinity
    </v-card-title>
    <v-divider />

    <v-card-text class="py-5">
      <v-form ref="form">
        <v-row dense>
          <v-col xs="12">
            <v-img
              class="mx-auto"
              :max-width="400"
              src="https://storage.googleapis.com/dobybot-public-bucket/images/dobybot-connect/shipnity-logo.png"
            />

            <h3>วิธีการเชื่อมต่อ</h3>
            <ol>
              <li>ขอ API token จากทาง Shipnity</li>
              <li>กรอกข้อมูลลงในฟอร์มด้านล่าง</li>
            </ol>
          </v-col>
          <v-col sm="12">
            <v-alert v-if="error" color="error" outlined>
              {{ error }}
            </v-alert>
          </v-col>
          <v-col sm="12">
            <v-text-field
              v-model="email"
              label="Email"
              filled
              :rules="[ $rules.required, $rules.email ]"
            />
          </v-col>
          <v-col sm="12">
            <v-text-field
              v-model="token"
              label="Token"
              filled
              hide-details
              :rules="[ $rules.required ]"
            />
          </v-col>
        </v-row>
      </v-form>
    </v-card-text>
    <v-card-actions>
      <v-btn :loading="loading" class="mx-auto" color="primary" width="120" @click="submit()">
        SUBMIT
      </v-btn>
    </v-card-actions>
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import { VForm } from '~/models'

export default Vue.extend({
  data () {
    return {
      email: '',
      token: '',
      error: '',
      loading: false
    }
  },
  methods: {
    async submit () {
      this.error = ''
      const form = this.$refs.form as never as VForm
      if (!form.validate()) {
        return
      }

      const payload = {
        email: this.email,
        token: this.token
      }

      this.loading = true
      try {
        await this.$axios.post('/api/companies/dobybot-connect/seller/auth/shipnity/', payload)
        this.$snackbar('success', this.$t('success'))
        this.$emit('success')
      } catch (error: any) {
        console.error(error)

        if (error.response) {
          if (error.response.status === 400) {
            this.error = error.response.data
          } else {
            this.error = 'เกิดข้อผิดพลาด กรุณาติดต่อทีมงาน Dobybot Support'
          }
        }
      }
      this.loading = false
    }
  }
})
</script>
