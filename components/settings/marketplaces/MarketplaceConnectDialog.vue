<template>
  <v-dialog
    max-width="800"
    min-height="400"
    :value="value"
    @input="$emit('input', event)"
  >
    <v-card>
      <v-card-title class="headline grey lighten-2">
        เชื่อมต่อ Marketplace และ บริการอื่น ๆ
        <v-spacer />
        <v-btn data-test-id="v-btn-close" icon @click="$emit('input', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text>
        <div>
          <h1 class="text-h6 mt-4">
            Marketplaces
          </h1>
          <v-divider />
          <v-row class="mt-4 px-3">
            <v-col
              v-for="mp in marketplaces"
              :key="mp.title"
              v-package="$package.FULL_INTEGRATION"
              style="position: relative"
              cols="3"
              class="marketplace"
              @click="mp.onClick()"
            >
              <v-img :src="mp.logo" contain height="50px" />
              <div class="text-center mt-2 text-caption">
                {{ mp.title }}
              </div>
            </v-col>
          </v-row>
        </div>

        <div class="mt-5 pt-5">
          <h1 class="text-h6">
            Order Management Systems
          </h1>
          <v-divider />
          <v-row class="mt-4 px-3">
            <v-col
              v-for="mp in order_management_systems"
              :key="mp.title"
              v-package="$package.FULL_INTEGRATION"
              style="position: relative"
              cols="3"
              class="marketplace"
              @click="mp.onClick()"
            >
              <v-img :src="mp.logo" contain height="50px" />
              <div class="text-center mt-2 text-caption">
                {{ mp.title }}
              </div>
            </v-col>
          </v-row>
        </div>

        <div style="height: 50px" />
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    value: {
      type: Boolean,
      required: true
    },
    version: {
      type: Number,
      default: 1
    }
  },

  computed: {
    marketplaces (): any[] {
      const items = [
        {
          title: 'เชื่อมต่อร้านค้า Shopee',
          logo: 'https://storage.googleapis.com/dobybot-public-bucket/images/dobybot-connect/shopee-logo.png',
          onClick: () => {
            this.$emit('connect', 'shopee')
          }
        },
        {
          title: 'เชื่อมต่อร้านค้า Lazada',
          logo: 'https://storage.googleapis.com/dobybot-public-bucket/images/dobybot-connect/lazada-logo.jpg',
          onClick: () => {
            this.$emit('connect', 'lazada')
          }
        },
        // {
        //   title: 'เชื่อมต่อร้านค้า TikTok Shop 202212',
        //   logo: 'https://storage.googleapis.com/dobybot-public-bucket/images/dobybot-connect/tiktok_shop-logo.png',
        //   onClick: () => {
        //     this.$emit('connect', 'tiktok_shop')
        //   }
        // },
        {
          title: 'เชื่อมต่อร้านค้า TikTok Shop 202306',
          logo: 'https://storage.googleapis.com/dobybot-public-bucket/images/dobybot-connect/tiktok_shop-logo.png',
          onClick: () => {
            this.$emit('connect', 'tiktok_shop_v2')
          }
        }
      ]

      if (this.version === 2) {
        items.push({
          title: 'เชื่อมต่อร้านค้า NocNoc',
          logo: 'https://storage.googleapis.com/dobybot-public-bucket/images/dobybot-connect/nocnoc-logo.png',
          onClick: () => {
            this.$emit('connect:nocnoc')
          }
        })
        // Add WooCommerce
        items.push({
          title: 'เชื่อมต่อร้านค้า WooCommerce',
          logo: 'https://storage.googleapis.com/dobysync-bucket/image/woo_commerce_logo.png',
          onClick: () => {
            this.$emit('connect:woo-commerce')
          }
        })
        items.push({
          title: 'เชื่อมต่อร้านค้า LineMyShop',
          logo: 'https://storage.googleapis.com/dobybot-public-bucket/images/dobybot-connect/linemyshop-logo.png',
          onClick: () => {
            this.$emit('connect:linemyshop')
          }
        })
        items.push({
          title: 'เชื่อมต่อร้านค้า NexGenCommerce',
          logo: 'https://storage.googleapis.com/dobysync-bucket/image/nex-gen-commerce.png',
          onClick: () => {
            this.$emit('connect:nexgencommerce')
          }
        })
      }

      return items
    },

    order_management_systems (): any[] {
      const items = [
        {
          title: 'เชื่อมต่อ Zort (V1)',
          logo: 'https://storage.googleapis.com/dobybot-public-bucket/images/dobybot-connect/zort-logo.png',
          onClick: () => {
            this.$emit('connect:zort-v1')
          }
        },
        {
          title: 'เชื่อมต่อ Zort (V2)',
          logo: 'https://storage.googleapis.com/dobybot-public-bucket/images/dobybot-connect/zort-logo.png',
          onClick: () => {
            this.$emit('connect:zort-v2')
          }
        }
      ]

      if (this.version === 2) {
        items.splice(0, 1)
      }

      return items
    }
  }
})
</script>

<style scoped>
.marketplace {
  border: 1px solid #eeeeee;
  border-radius: 5px;
  cursor: pointer;
}
.marketplace-img {
  height: 50px;
  width: 100%;
}
</style>
