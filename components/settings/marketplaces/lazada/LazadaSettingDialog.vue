<template>
  <v-dialog max-width="800" :value="value" @input="$emit('input', $event)">
    <v-card>
      <v-card-title class="headline grey lighten-2">
        Lazada Setting Dialog
        <v-spacer />
        <v-btn data-test-id="v-btn-close" icon @click="$emit('input', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text>
        {{ shop }}

        <br>
        <hr>
        {{ shopSetting }}

        <!--  -->
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    value: {
      type: Boolean,
      required: true
    },

    shop: {
      type: Object,
      required: true
    },

    shopSetting: {
      type: Object,
      required: true
    }
  }
})
</script>
