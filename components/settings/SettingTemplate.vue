<template>
  <div>
    <h1>{{ title }}</h1>
    <v-divider class="mb-3" />

    <div>
      <slot name="overview" />
    </div>

    <v-form ref="form" class="mt-3">
      <slot name="form" :update="update" :settings="settings" />
    </v-form>

    <v-divider class="my-5" />
    <v-row class="my-5">
      <v-spacer />
      <v-col cols="12" md="4">
        <v-btn
          color="success"
          block
          :loading="loading"
          @click="onSaveButtonClick()"
        >
          {{ $t('settings.save') }}
        </v-btn>
      </v-col>
      <v-spacer />
    </v-row>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapActions, mapMutations } from 'vuex'
import { VForm } from '~/models'
import { SettingState } from '~/store/settings'

export default Vue.extend({
  props: {
    title: {
      type: String,
      required: true
    }
  },

  data () {
    return {
      changes: {} as any,
      loading: false
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },

  created () {
    if (!this.$hasPerms('change_settingvalue')) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }
  },

  methods: {
    ...mapActions('settings', {
      saveSettings: 'saveSettings'
    }),

    ...mapMutations('settings', {
      setCompanySetting: 'setCompanySetting'
    }),

    async update (data: any, submit = false) {
      this.changes = { ...this.changes, ...data }
      this.setCompanySetting(data)
      if (submit) {
        await this.saveSettings(Object.keys(this.changes))
        this.changes = []
      }
    },

    hasUnsavedChanges (): boolean {
      return Object.keys(this.changes).length > 0
    },

    async onSaveButtonClick () {
      const form = this.$refs.form as never as VForm
      if (!form.validate()) {
        this.$snackbar('error', this.$t('settings.correct-data'))
        return
      }

      this.loading = true
      try {
        await this.saveSettings(Object.keys(this.changes))
        this.$snackbar('success', this.$t('settings.save-successful'))
        this.changes = []
      } catch (error) {
        this.$snackbar('error', `${error}`)
        console.error(error)
      }
      this.loading = false
    }
  }
})
</script>
