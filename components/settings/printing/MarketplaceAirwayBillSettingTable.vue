<template>
  <v-data-table
    :footer-props="{'items-per-page-options': [5, 10, 25]}"
    :headers="headers"
    :items="items"
    hide-default-footer
    dense
  >
    <template #item.enable="{item}">
      <v-checkbox
        hide-details
        class="ma-0 pa-0"
        :input-value="isChecked(item)"
        @change="updateShopSetting(item.seller_id, $event)"
      />
    </template>
    <template #item.refresh_token_expire="{item}">
      {{ item.refresh_token_expire ? $date(item.refresh_token_expire) : '-' }}
    </template>

    <!-- <template #item.datetime="{item}">
      {{ $datetime(item.datetime) }}
    </template> -->
    <template #footer.prepend>
      <slot name="footer.prepend" />
    </template>
  </v-data-table>
</template>

<script lang="ts">
import moment from 'moment'
import Vue, { PropOptions } from 'vue'
import { Marketplace, VDataTableHeader } from '~/models'
export default Vue.extend({
  props: {
    value: {
      type: Object,
      required: true
    },
    platform: {
      type: String,
      required: true
    },
    items: {
      type: Array,
      required: true
    } as PropOptions<Marketplace[]>
  },
  computed: {
    headers (): VDataTableHeader[] {
      return [
        { text: 'เปิดการใช้งาน', value: 'enable', sortable: false, width: '120px' },
        { text: this.$t('marketplace.shop_name') + ' ' + this.platform, value: 'seller_shop_name', sortable: false },
        { text: this.$t('marketplace.token_expire'), value: 'refresh_token_expire', sortable: false, width: '100px' }
      ]
    }
  },
  methods: {
    update (data: any) {
      this.$emit('input', { ...(this.value || {}), ...data })
    },
    updateShopSetting (shop_id: string, enable_awb: boolean) {
      this.update({
        [shop_id]: {
          ...this.value[shop_id],
          airway_bill: enable_awb
        }
      })
    },
    isChecked (marketplace: Marketplace) {
      if (this.value[marketplace.seller_id]) {
        return this.value[marketplace.seller_id].airway_bill
      }
      return false
    },
    getStatus (marketplace: Marketplace) {
      if (['shopee', 'lazada'].includes(marketplace.marketplace)) {
        return moment().isBefore(marketplace.refresh_token_expire)
      }
      if (marketplace.marketplace === 'zort') {
        return true
      }
      return true
    }
  }
})
</script>
