<template>
  <div style="position: relative;" class="textarea-border">
    <label class="v-label" style="position: absolute; top: -8px; left: 10px; font-size: 12px; padding: 0 2px; background: white;">
      {{ $t('shipping-form') }}
    </label>
    <!-- <v-divider /> -->

    <div>
      <table style="width: 100%;">
        <tr>
          <td style="width: 84px">
            {{ $t('shipping-form-province') }}
          </td>
          <td class="pr-2" :style="textColor(province)">
            <strong>{{ province ?? '-' }}</strong>
          </td>
          <td style="width: 84px">
            {{ $t('shipping-form-district') }}
          </td>
          <td :style="textColor(district)">
            <strong>{{ district ?? '-' }}</strong>
          </td>
        </tr>
        <tr>
          <td style="width: 84px">
            {{ $t('shipping-form-subdistrict') }}
          </td>
          <td class="pr-2" :style="textColor(subdistrict)">
            <strong>{{ subdistrict ?? '-' }}</strong>
          </td>
          <td style="width: 84px">
            {{ $t('shipping-form-postcode') }}
          </td>
          <td :style="textColor(postcode)">
            <strong>{{ postcode ?? '-' }}</strong>
          </td>
        </tr>
      </table>
    </div>

    <div class="mt-3">
      <a v-show="!is_editing && !readonly" href="" @click.prevent="is_editing = true"> เเก้ไข</a>
    </div>

    <address-autocomplete
      v-if="is_editing"
      @update:address_detail="updateAddressDetail($event)"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import AddressAutocomplete from '~/components/settings/shipping/AddressAutocomplete.vue'

interface AddressDetail {
  id: number;
  province: string;
  province_en: string;
  district: string;
  district_en: string;
  subdistrict: string;
  subdistrict_en: string;
  postcode: string;
}

export default Vue.extend({
  components: {
    AddressAutocomplete
  },
  props: {
    province: {
      type: String,
      default: ''
    },
    district: {
      type: String,
      default: ''
    },
    subdistrict: {
      type: String,
      default: ''
    },
    postcode: {
      type: String,
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      is_editing: false as boolean,
      is_error: false as boolean,
      text: '*string' as string
    }
  },

  methods: {
    updateAddressDetail (address_detail : AddressDetail) {
      this.is_editing = false
      this.is_error = false

      this.$emit('update:address_detail', address_detail)
    },

    textColor (text : String) {
      if (text == null) {
        return
      }
      if (text.includes('*')) {
        this.is_error = true
        return {
          color: 'red'
        }
      }
    },

    invisibleMarker (text: String) {
      if (text.includes('*')) {
        this.is_error = true
        return text.replace('*', '')
      }
      return text
    }
  }
})
</script>

<style scoped>
.no-border .v-input__control {
  border: none !important;
  box-shadow: none !important;
}

.border-color {
  border: 1px solid
}

.textarea-border {
  border: 1px solid rgb(158,158,158);
  padding: 20px;
  width: 100%;
  border-radius: 5px;
}

.autocomplete-address-table{
 border-bottom: 1px solid;

}

.autocomplete-address-table td{
  font-size: 13px;
}

</style>
