<template>
  <v-autocomplete
    class="mt-4"
    label="ค้นหา จังหวัด, เเขวง/ตำบล, เขต/อำเภอ, เลขไปรษณีย์"
    dense
    filled
    return-object
    no-filter
    hide-details
    :items="items"
    :search-input="search"
    item-value="id"
    clearable
    @update:search-input="search=$event; debouncedGetAddress()"
    @change="updateAddressDetail($event)"
  >
    <template #selection="{ item }">
      <span>{{ item.province }} {{ item.district }} {{ item.subdistrict }} {{ item.postcode }}</span>
    </template>

    <template #item="data">
      <table class="autocomplete-address-table" style="width: 100%;">
        <tr style="margin-top: 7px">
          <td style="width: 84px; color: rgba(0, 0, 0, 0.6); ">
            จังหวัด
          </td>
          <td style="width: 104px">
            <strong v-html="highlightAll(data.item.province, search)" />
          </td>
          <td style="width: 84px; color: rgba(0, 0, 0, 0.6);">
            เขต/อำเภอ
          </td>
          <td style="width: 104px">
            <strong v-html="highlightAll(data.item.district, search)" />
          </td>
        </tr>
        <tr style="margin-bottom: 7px">
          <td style="width: 84px; color: rgba(0, 0, 0, 0.6);">
            เเขวง/ตำบล
          </td>
          <td style="width: 104px" class="">
            <strong v-html="highlightAll(data.item.subdistrict, search)" />
          </td>
          <td style="width: 84px; color: rgba(0, 0, 0, 0.6);">
            เลขไปรษณีย์
          </td>
          <td style="width: 104px">
            <strong v-html="highlightAll(data.item.postcode, search)" />
          </td>
        </tr>
      </table>
    </template>
  </v-autocomplete>
</template>

<script lang="ts">
import Vue from 'vue'
import _ from 'lodash'

interface AddressDetail {
  id: number;
  province: string;
  province_en: string;
  district: string;
  district_en: string;
  subdistrict: string;
  subdistrict_en: string;
  postcode: string;
}

export default Vue.extend({
  data () {
    return {
      items: [] as AddressDetail[],
      search: '' as string,
      debouncedGetAddress: null as Function |null
    }
  },

  created () {
    this.debouncedGetAddress = _.debounce(() => {
      this.getAddress()
    }, 500)
  },

  methods: {
    async getAddress () {
      const params = {} as any
      params.search = this.search
      this.items = (await this.$axios.get('/api/thaiaddress/search/', { params })).data
    },

    highlightAll (text: string, search: string) {
      if (!search) {
        return text
      }
      const keywords = search.split(' ')
      for (const keyword of keywords) {
        text = this.highlight(text, keyword)
      }
      return text
    },

    highlight (text: string, search: string) {
      const index = text.indexOf(search)
      if (index >= 0) {
        text = (text.substring(0, index) +
          "<span style='background-color: yellow;'>" +
          text.substring(index, index + search.length) +
          '</span>' +
          text.substring(index + search.length)
        )
        return text
      }

      return text
    },

    updateAddressDetail (address_detail : AddressDetail) {
      if (!address_detail) {
        return
      }

      this.$emit('update:address_detail', address_detail)
    }

  }

})
</script>

<style scoped>

.border-color {
  border: 1px solid
}

.textarea-border {
  border: 1px solid rgb(158,158,158);
  padding: 20px;
  width: 100%;
  border-radius: 5px;
}

.autocomplete-address-table{
 border-bottom: 1px solid;

}

.autocomplete-address-table td{
  font-size: 13px;
}
</style>
