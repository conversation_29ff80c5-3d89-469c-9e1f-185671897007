<template>
  <v-dialog max-width="960" :value="dialog" persistent>
    <v-card>
      <v-card-title>
        <div class="text-h5">
          เเบบฟอร์มขนส่ง ({{ pickOrder.order_number }})
        </div>
        <v-spacer />
        <v-icon @click="close()">
          mdi-close
        </v-icon>
      </v-card-title>

      <v-divider />

      <v-card-text>
        <div class="my-2 px-3">
          <v-row>
            <v-col cols="4" class="ps-0">
              <div class="text-h6">
                เลือกขนส่ง
              </div>
            </v-col>
            <v-col cols="8">
              <v-radio-group
                v-model="selected_courier"
                class="mt-0"
                hide-details
                :readonly="readonly"
                inline
                row
              >
                <v-col cols="6">
                  <v-radio
                    label="Flash Express"
                    value="DBB_FLASH"
                  />
                  <v-radio
                    class="mt-3"
                    label="Kerry Express"
                    value="DBB_KERRY"
                  />
                </v-col>

                <v-col cols="6">
                  <v-radio
                    label="ไปรษณีย์ไทย (EMS)"
                    value="DBB_THAIPOST_EMS"
                  />
                  <v-radio
                    class="mt-3"
                    label="ไปรษณีย์ไทย (ลงทะเบียน)"
                    value="DBB_THAIPOST_REG"
                  />
                </v-col>
              </v-radio-group>
            </v-col>
          </v-row>
        </div>

        <v-divider />

        <div class="my-3 px-3">
          <v-row>
            <v-col cols="4" class="ps-0">
              <div class="text-h6">
                ที่อยู่ผู้ส่ง
              </div>
            </v-col>
            <v-col cols="8">
              <span class="text-subtitle-1 text--secondary">{{ settings.company.SHIPPING_SENDER.name }}</span>
              <span class="text-subtitle-1 text--secondary">{{ settings.company.SHIPPING_SENDER.phone }}</span>
              <span class="text-subtitle-1 text--secondary">
                {{ settings.company.SHIPPING_SENDER.address }}
              </span>
              <table style="width: 100%;">
                <tr>
                  <td style="width: 84px">
                    {{ $t('shipping-form-province') }}
                  </td>
                  <td class="pr-2">
                    <strong>{{ settings.company.SHIPPING_SENDER.province ?? '-' }}</strong>
                  </td>
                  <td style="width: 84px">
                    {{ $t('shipping-form-district') }}
                  </td>
                  <td>
                    <strong>{{ settings.company.SHIPPING_SENDER.district ?? '-' }}</strong>
                  </td>
                </tr>
                <tr>
                  <td style="width: 84px">
                    {{ $t('shipping-form-subdistrict') }}
                  </td>
                  <td class="pr-2">
                    <strong>{{ settings.company.SHIPPING_SENDER.subdistrict ?? '-' }}</strong>
                  </td>
                  <td style="width: 84px">
                    {{ $t('shipping-form-postcode') }}
                  </td>
                  <td>
                    <strong>{{ settings.company.SHIPPING_SENDER.postcode ?? '-' }}</strong>
                  </td>
                </tr>
              </table>
              <a v-show="!readonly" class="text-end" href="/settings/shipping">{{ $t('edit') }}</a>
            </v-col>
          </v-row>
        </div>

        <v-divider />

        <div class="my-5 px-3">
          <v-row>
            <v-col cols="4" class="ps-0">
              <div class="text-h6">
                {{ $t('recipient-information') }}
              </div>
            </v-col>

            <v-col cols="8">
              <v-form ref="form">
                <v-row>
                  <v-col cols="7">
                    <v-text-field
                      v-model="form_data.shippingname"
                      :readonly="readonly"
                      :rules="[$rules.required]"
                      outlined
                      dense
                      :label="$t('recipient-full-name')"
                    />
                  </v-col>
                  <v-col>
                    <v-text-field
                      v-model="form_data.shippingphone"
                      :readonly="readonly"
                      :rules="[$rules.phoneNumberFormat]"
                      outlined
                      dense
                      :label="$t('recipient-phone')"
                    />
                  </v-col>
                </v-row>

                <div>
                  <v-textarea
                    v-model="form_data.shippingaddress"
                    :readonly="readonly"
                    :rules="[$rules.required]"
                    style="width:100%"
                    rows="2"
                    :label="$t('address')"
                    outlined
                  />
                </div>
              </v-form>

              <form-address
                :province="form_data.shippingprovince"
                :district="form_data.shippingdistrict"
                :subdistrict="form_data.shippingsubdistrict"
                :postcode="form_data.shippingpostcode"
                :readonly="readonly"
                @update:address_detail="updateCustomerAddress($event)"
              />

              <div>
                <v-checkbox
                  v-model="form_data.isCOD"
                  :readonly="readonly"
                  label="COD (Cash On Delivery)"
                />
              </div>

              <div class="d-flex">
                <v-fab-transition class="justify-end">
                  <v-btn
                    v-show="!is_save && !readonly"
                    color="success"
                    outlined
                    class="mt-1"
                    small
                    @click="saveCustomerDetail()"
                  >
                    <v-icon dense>
                      mdi-content-save
                    </v-icon>
                    {{ $t('save') }}
                  </v-btn>
                </v-fab-transition>
              </div>
            </v-col>
          </v-row>
        </div>

        <v-divider />

        <v-alert
          v-show="error_message"
          class="mt-5"
          text
          outlined
          type="error"
        >
          <div v-html="error_message" />
        </v-alert>

        <div class="d-flex mt-5">
          <template v-if="!readonly">
            <v-spacer />
            <v-btn
              :loading="loading"
              :disabled="!is_save"
              class="primary"
              @click="createTrackingNumber()"
            >
              ออกเลข Tracking
            </v-btn>
          </template>

          <template v-else>
            <v-btn
              v-if="poTracking.status == 'PENDING'"
              :loading="loading"
              color="red"
              outlined
              @click="cancelTrackingNumber(poTracking.tracking_no)"
            >
              ยกเลิกเลข Tracking
            </v-btn>

            <v-spacer />

            <v-btn
              v-if="poTracking.status != 'SUCCESS'"
              class="mx-1"
              color="primary"
              outlined
              :loading="loading"
              @click="updateShippingStatus(poTracking.tracking_no)"
            >
              อัพเดทสถานะ
            </v-btn>
          </template>
        </div>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { SettingState } from '~/store/settings'
import { PickOrder, PickOrderTrackingNo, VForm } from '~/models'
import FormAddress from '~/components/settings/shipping/FormAddress.vue'

interface AddressDetail {
  id: number;
  province: string;
  province_en: string;
  district: string;
  district_en: string;
  subdistrict: string;
  subdistrict_en: string;
  postcode: string;
}

interface Courier {
  image: string
  value: string
}

export default Vue.extend({
  components: {
    FormAddress
  },
  props: {
    dialog: { type: Boolean, required: true },
    pickOrder: { type: Object, required: true },
    poTracking: { type: Object, required: false, default: () => null }
  },

  data () {
    return {
      form_data: {
        shippingname: '',
        shippingphone: '',
        shippingaddress: '',
        shippingprovince: '',
        shippingdistrict: '',
        shippingsubdistrict: '',
        shippingpostcode: '',
        isCOD: false
      },

      selected_courier: '' as 'DBB_FLASH' | 'DBB_THAIPOST_EMS' | 'DBB_THAIPOST_REG' | 'DBB_KERRY' | '',
      items: [] as AddressDetail[],
      is_save: true,
      skip_first_watcher: true,
      error_message: '',
      loading: false
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    },
    readonly (): boolean {
      return !!this.poTracking
    }
  },

  watch: {
    form_data: {
      handler () {
        if (this.skip_first_watcher) {
          this.skip_first_watcher = false
          return
        }

        this.is_save = false
      },
      deep: true
    },
    pickOrder: {
      handler (value:PickOrder) {
        const val = value.order_json
        this.form_data = {
          shippingname: val.shippingname,
          shippingphone: val.shippingphone,
          shippingaddress: val.shippingaddress,
          shippingprovince: val.shippingprovince,
          shippingdistrict: val.shippingdistrict,
          shippingsubdistrict: val.shippingsubdistrict,
          shippingpostcode: val.shippingpostcode,
          isCOD: val.isCOD
        }
        if (this.settings.company.AUTO_GENERATE_TRACKINGNO_PROVIDER) {
          this.selected_courier = this.settings.company.AUTO_GENERATE_TRACKINGNO_PROVIDER
        }

        if (value.order_shippingchannel) {
          if (value.order_shippingchannel !== 'ไม่ระบุ' && (
            value.order_shippingchannel === 'DBB_FLASH' ||
            value.order_shippingchannel === 'DBB_THAIPOST_EMS' ||
            value.order_shippingchannel === 'DBB_THAIPOST_REG' ||
            value.order_shippingchannel === 'DBB_KERRY'
          )) {
            this.selected_courier = value.order_shippingchannel
          }
        }
      },
      immediate: true,
      deep: true
    }

  },

  methods: {
    close () {
      this.is_save = true
      this.skip_first_watcher = true
      this.error_message = ''
      this.$emit('click-close', false)
    },

    updateCustomerAddress (event : AddressDetail) {
      if (!event) {
        return
      }

      const obj = {
        shippingprovince: event.province,
        shippingdistrict: event.district,
        shippingsubdistrict: event.subdistrict,
        shippingpostcode: event.postcode
      }

      this.form_data = { ...this.form_data, ...obj }
    },

    saveCustomerDetail () {
      const new_order_json = { ...this.pickOrder.order_json, ...this.form_data }
      const new_pick_order = { ...this.pickOrder, order_json: new_order_json }

      this.is_save = true
      this.$snackbar('success', 'บันทึกเเล้ว')
      this.$emit('update:pick_order', new_pick_order)
    },

    async createTrackingNumber () {
      const form = this.$refs.form as never as VForm
      const is_valid = form.validate()
      let action = ''
      let params = {}
      if (!is_valid) {
        return
      }

      this.loading = true
      switch (this.selected_courier) {
        case 'DBB_FLASH':
          action = '/api/shipping/flash/create-tracking-number/'
          break

        case 'DBB_THAIPOST_EMS':
          action = '/api/shipping/thaipost/create-tracking-number/'
          params = {
            shipping_channel: 'DBB_THAIPOST_EMS'
          }
          break

        case 'DBB_THAIPOST_REG':
          action = '/api/shipping/thaipost/create-tracking-number/'
          params = {
            shipping_channel: 'DBB_THAIPOST_REG'
          }
          break

        case 'DBB_KERRY':
          action = '/api/shipping/kerry/create-tracking-number/'
          break
      }
      try {
        const new_po_tracking: PickOrderTrackingNo = await this.$axios.$post(action, { pick_order_id: this.pickOrder.id }, { params })
        this.$emit('created:tracking-number',
          {
            new_po_tracking,
            form_data: this.form_data
          })
        this.close()
        this.$snackbar('success', this.$t('success'))
      } catch (error : any) {
        console.log(error)
        const res_data = error.response.data

        if (res_data.code === 'INVALID_ADDRESS') {
          this.error_message = res_data.message

          this.form_data.shippingprovince = res_data.error.province
          this.form_data.shippingdistrict = res_data.error.district
          this.form_data.shippingsubdistrict = res_data.error.subdistrict
          this.form_data.shippingpostcode = res_data.error.postcode
        } else if (res_data.code === 'SHIPPING_ERROR_500') {
          this.error_message = `
            <div>เกิดข้อผิดพลาดกับ API ของ Flash</div>
            <ul>
              <li>ยังไม่ได้เชื่อมต่อ Flash API</li>
              <li>ที่อยู่ผู้ส่งไม่ถูกต้อง</li>
            </ul>
          `
          this.$snackbar('error', 'ข้อมูลที่ส่งไม่ถูกต้องโปรดตรวจสอบข้อมูลใหม่อีกครั้ง')
        } else if (res_data.code === 'UN_CONFIGURED_FLASH_API') {
          this.error_message = this.$t('not-login-flash')
        } else if (res_data.code === 'INVALID_SENDER_ADDRESS') {
          this.error_message = this.$t('no-sender-address')
        } else {
          this.error_message = res_data.message
        }
      }
      this.loading = false
    },

    async cancelTrackingNumber (tracking_no:string) {
      let action = ''
      switch (this.selected_courier) {
        case 'DBB_FLASH':
          action = '/api/shipping/flash/cancel-tracking-number/'
          break

        case 'DBB_THAIPOST_EMS':
          action = '/api/shipping/thaipost/cancel-tracking-number/'
          break

        case 'DBB_THAIPOST_REG':
          action = '/api/shipping/thaipost/cancel-tracking-number/'
          break

        case 'DBB_KERRY':
          action = '/api/shipping/kerry/cancel-tracking-number/'
          break
      }
      this.loading = true
      try {
        const response = await this.$axios.post(action, { tracking_no })

        this.$emit('updated:po_tracking', response.data)
        this.close()
        this.$snackbar('success', this.$t('success'))
      } catch (error) {
        this.$snackbar('error', this.$t('error.unknown'))
      }
      this.loading = false
    },

    async updateShippingStatus (tracking_no:string) {
      let action = ''
      let params = {}

      switch (this.selected_courier) {
        case 'DBB_FLASH':
          action = '/api/shipping/flash/update-shipping-status/'
          break

        case 'DBB_THAIPOST_EMS':
          action = '/api/shipping/thaipost/update-shipping-status/'
          params = {
            shipping_channel: 'DBB_THAIPOST_EMS'
          }
          break

        case 'DBB_THAIPOST_REG':
          action = '/api/shipping/thaipost/update-shipping-status/'
          params = {
            shipping_channel: 'DBB_THAIPOST_REG'
          }
          break

        case 'DBB_KERRY':
          action = '/api/shipping/kerry/update-shipping-status/'
          break
      }
      this.loading = true
      try {
        const response = await this.$axios.post(action, { tracking_no }, { params })
        this.$emit('updated:po_tracking', response.data)
        this.close()
        this.$snackbar('success', this.$t('success'))
      } catch (error) {
        this.$snackbar('error', this.$t('error.unknown'))
        console.log(error)
      }
      this.loading = false
    }

  }
})
</script>

<style scoped>
</style>
