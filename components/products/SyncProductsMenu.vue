<template>
  <span>
    <v-menu offset-y>
      <template #activator="{ on }">
        <v-btn small outlined color="primary" class="mx-1 px-1" v-on="on">
          <v-icon small> mdi-menu-down </v-icon>
        </v-btn>
      </template>

      <v-list>
        <v-list-item
          v-for="(item, index) in marketplaces"
          :key="index"
          :two-line="!getDefault(item, 'is_active', true)"
          @click="onItemClick(item)"
        >
          <v-list-item-content>
            <v-list-item-title>
              <v-chip small>
                {{ item.marketplace }}
              </v-chip>
              <span>
                {{ item.seller_shop_name }}
              </span>
            </v-list-item-title>
            <v-list-item-subtitle
              v-if="!getDefault(item, 'is_active', true)"
              class="red--text"
            >
              {{ $t("product-sync-menu") }}
              <span class="primary--text">{{ $t("reconnect") }}</span>
            </v-list-item-subtitle>
          </v-list-item-content>
        </v-list-item>
        <v-divider />
        <v-list-item
          v-if="$auth.user.is_superuser"
          @click="deleteAllProducts()"
        >
          <v-list-item-action>
            <v-icon>mdi-alert</v-icon>
          </v-list-item-action>
          <v-list-item-content> ล้างรายการสินค้าทั้งหมด </v-list-item-content>
        </v-list-item>
      </v-list>
    </v-menu>

    <sync-task-dialog
      v-model="sync_task_dialog.show"
      :task-id="sync_task_dialog.task_id"
    />
  </span>
</template>

<script lang="ts">
import { get } from 'lodash'
import Vue from 'vue'
import saveAs from 'file-saver'
import { Company, LoggedInUser, Marketplace } from '~/models'
import { SettingState } from '~/store/settings'
import SyncTaskDialog from '~/components/order-center/SyncTaskDialog.vue'
import { wait } from '~/utils'

export default Vue.extend({
  components: {
    SyncTaskDialog
  },

  data () {
    return {
      marketplaces: [] as Marketplace[],
      dialog: {
        show: false,
        marketplace: null as Marketplace | null
      },
      sync_task_dialog: {
        show: false,
        task_id: ''
      }
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    },
    dbbc_version (): number {
      return this.settings.company.DOBYBOT_CONNECT_VERSION
    }
  },

  created () {
    this.getMarketplaces()
  },

  methods: {
    async getMarketplaces () {
      const shops: Marketplace[] = (
        await this.$axios.get('/api/companies/marketplaces/')
      ).data
      this.marketplaces = shops.filter(x =>
        [
          'zort_v2',
          'shopee',
          'lazada',
          'tiktok_shop',
          'tiktok_shop_v2',
          'line_my_shop',
          'nex_gen_commerce'
        ].includes(x.marketplace)
      )
    },

    async onItemClick (item: Marketplace) {
      if (!item.is_active) {
        this.$router.push('/settings/marketplace')
        return
      }

      this.$loader(true)
      const payload = {
        marketplace: item.marketplace,
        shop_id: item.seller_id
      }
      try {
        const result = await this.$axios.$post(
          '/api/companies/dobybot-connect/sync-shop-products/',
          payload
        )

        this.$loader(false)
        this.sync_task_dialog.show = true
        this.sync_task_dialog.task_id = result.task_id
      } catch (error: any) {
        console.log(error)
        this.$loader(false)
        this.$alert(error.response.data)
      }
    },

    getDefault (data: any, key: any, defaultValue: any) {
      return get(data, key, defaultValue)
    },

    async exportProductXlsx () {
      this.$loader(true, 'Exporting products as xlsx ...')
      try {
        const response = await this.$axios.get(
          '/api/picking/products/export/',
          { responseType: 'blob' }
        )
        saveAs(response.data, 'products.xlsx')
      } catch (error) {
        console.error(error)
      }
      this.$loader(false)
    },

    async deleteAllProducts () {
      const user = this.$auth.user as never as LoggedInUser
      const company = user.company as Company
      while (true) {
        const msg = await this.$prompt({
          title: `ยืนยันการลบข้อมูลโดย ${user.username}`,
          text: `
            ระบบจะทำการ Export Excel เพื่อ Backup ข้อมูลก่อนลบ
            <br><br>

            คุณกำลังจะทำการลบช้อมูล "สินค้าทั้งหมด" ร้านค้า 
            <b style="color: #E74C3C;">"${company.name}"</b> 
            โดยใช้บัญชีผู้ใช้ <b style="color: #E74C3C;">"${user.username}"</b>
            <br><br>

            หากแน่ใจกรุณาพิมพ์คำว่า "CONFIRM"</b> `,
          preset: 'text'
        })
        if (msg === null) {
          return
        }
        if (msg === 'CONFIRM') {
          break
        }
      }

      await this.exportProductXlsx()
      await wait(2000)

      const password = prompt(
        'Please enter secret password to delete all products'
      )
      if (password !== '142668') {
        alert('Password is incorrect')
        return
      }

      this.$loader(true, 'Deleting all products ...')
      try {
        this.$axios.$delete('/api/picking/products/reset/')
        this.$snackbar('info', 'All products has been deleted')
        this.$emit('reset')
      } catch (error) {
        console.error(error)
        this.$snackbar('error', this.$t('error.unknown'))
      }
      this.$loader(false)
    }
  }
})
</script>
