<template>
  <v-form ref="form" @submit.prevent="submit()">
    <v-row dense class="mt-1">
      <v-col cols="12">
        <div>
          นำเข้าข้อมูลสินค้าเพื่อใช้ในการจับคู่รหัส SKU กับรหัสบาร์โค๊ด<br>
        </div>
        <div class="mt-5">
          <b>วิธีการนำเข้าข้อมูลสินค้า</b>
          <ol>
            <li>ดาวน์โหลด <a href="https://storage.googleapis.com/dobybot-public-bucket/example/importdata/003-example_sku_barcode_map_v3.xlsx" download>ไฟล์ตัวอย่าง</a></li>
            <li>กรอกข้อมูลในรูปแบบตามไฟล์ตัวอย่าง และ ทำการอัพโหลดไฟล์ที่ช่องด้านล่าง</li>
          </ol>
        </div>
      </v-col>

      <v-col v-if="formData.errors.length > 0" cols="12" class="mb-3">
        <file-upload-error-list :errors="formData.errors" />
      </v-col>

      <v-col cols="12" md="9">
        <v-file-input
          outlined
          dense
          :label="$t('order-import.select-file')"
          show-size
          truncate-length="50"
          accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
          :hint="$t('xlsx-only-max-32-MB')"
          clearable
          persistent-hint
          :rules="[$rules.required]"
          :value="formData.file"
          @change="update({ file: $event })"
        />
      </v-col>

      <v-col cols="12" md="3">
        <v-btn :loading="loading" color="success" block type="submit">
          {{ $t('order-import.import-data') }}
        </v-btn>
      </v-col>
    </v-row>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import FileUploadErrorList from '../errors/FileUploadErrorList.vue'
import { VForm } from '~/models'

export interface ImportType {
    help: string;
    example: string;
    company: string;
}

export default Vue.extend({
  components: { FileUploadErrorList },
  props: {
    formData: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },

  methods: {
    update (data: any) {
      this.$emit('update:form-data', { ...this.formData, ...data })
    },

    submit () {
      this.$emit('submit')
    },

    validate () {
      return (this.$refs.form as unknown as VForm).validate()
    }
  }
})
</script>
