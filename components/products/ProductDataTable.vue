<template>
  <div>
    <v-text-field
      data-test="v-text-field-search-product"
      :value="search"
      prepend-inner-icon="mdi-magnify"
      :placeholder="$t('search')"
      @input="$emit('update:search', $event)"
    />
    <v-data-table
      :footer-props="{ 'items-per-page-options': [10, 25, 50, 100] }"
      :headers="headers"
      :items="items"
      :loading="table.loading"
      :server-items-length="table.meta.total_results"
      :options="table.options"
      @update:options="updateTable({ options: $event })"
    >
      <template #item.main_image="{ item }">
        <v-img
          v-if="item.main_image"
          :src="item.main_image"
          class="img-border"
        />
        <div v-else class="d-flex justify-center align-center img-border">
          <v-icon> mdi-image </v-icon>
        </div>
      </template>
      <template #item.name="{ item }">
        <table>
          <tr>
            <td colspan="2">
              {{ item.name }}
            </td>
          </tr>
          <tr>
            <td width="55" class="text--secondary">
              SKU
            </td>
            <td>{{ item.sku || "-" }}</td>
          </tr>
          <tr>
            <td width="55" class="text--secondary">
              Barcode
            </td>
            <td>
              <span style="white-space: pre">{{ item.barcode || "-" }}</span>
            </td>
          </tr>
          <tr>
            <td width="55" class="text--secondary">
              Location
            </td>
            <td>
              <span style="white-space: pre">{{ item.location || "-" }}</span>
            </td>
          </tr>
        </table>
      </template>
      <template #item.dimension="{ item }">
        {{ getItemDimension(item) }}
      </template>
      <template #item.vat_percent="{ item }">
        {{ Math.round(item.vat_percent) }}%
      </template>
      <template #item.show_qrcode_on_record_page="{ item }">
        <v-icon
          class="text-h6"
          :color="item.show_qrcode_on_record_page ? 'success' : 'error'"
        >
          {{
            item.show_qrcode_on_record_page
              ? "mdi-check-circle"
              : "mdi-close-circle"
          }}
        </v-icon>
      </template>
      <template #item.action="{ item }">
        <div style="width: 80px">
          <v-tooltip top>
            <template #activator="{ on, attrs }">
              <v-btn
                v-bind="attrs"
                icon
                v-on="on"
                @click="$emit('click-print-qrcode', item)"
              >
                <v-icon> mdi-qrcode </v-icon>
              </v-btn>
            </template>
            <span>Print QRCode</span>
          </v-tooltip>

          <v-tooltip top>
            <template #activator="{ on, attrs }">
              <v-btn
                v-bind="attrs"
                icon
                v-on="on"
                @click="$emit('click-print-barcode', item)"
              >
                <v-icon> mdi-barcode </v-icon>
              </v-btn>
            </template>
            <span>Print BarCode</span>
          </v-tooltip>

          <v-btn icon color="primary" @click="$emit('click-edit', item)">
            <v-icon>mdi-pencil</v-icon>
          </v-btn>
          <v-btn icon color="error" @click="$emit('click-delete', item)">
            <v-icon>mdi-delete</v-icon>
          </v-btn>
        </div>
      </template>
    </v-data-table>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { VDataTableHeader } from '~/models'
export default Vue.extend({
  props: {
    // add some props
    products: {
      type: Array,
      default: () => []
    },

    table: {
      type: Object,
      required: true
    },

    search: {
      type: String,
      default: ''
    }
  },

  computed: {
    headers (): VDataTableHeader[] {
      return [
        { text: 'Image', value: 'main_image' },
        { text: 'Name & SKU & Barcode & Location', value: 'name' },
        { text: 'Price', value: 'price', align: 'end' },
        { text: 'Color', value: 'color' },
        { text: 'Weight (g)', value: 'weight' },
        { text: 'Dimension (mm x mm x mm)', value: 'dimension' },
        { text: 'P-Score', value: 'packing_score' },
        { text: 'VAT (%)', value: 'vat_percent' },
        {
          text: 'QR Code',
          value: 'show_qrcode_on_record_page',
          width: '100px',
          align: 'center'
        },
        { text: '', value: 'action', align: 'end' }
      ]
    },
    items () {
      return this.products
    }
  },

  methods: {
    getItemDimension (item: any) {
      const dims = [item.width, item.length, item.height]
      return dims.filter(dim => dim).join(' x ')
    },
    updateTable (data: any) {
      this.$emit('update:table', { ...this.table, ...data })
    }
  }
})
</script>

<style lang="css" scoped>
.img-border {
  border: 0.5px grey solid;
  width: 50px;
  height: 50px;
  border-radius: 5px;
}
</style>
