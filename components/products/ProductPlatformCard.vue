<template>
  <v-card outlined elevation="0" rounded="lg" height="100%">
    <v-card-title>
      {{ syncProduct.marketplace }}
      <v-spacer />
      <v-btn v-if="!isSelected" text @click="$emit('click')">
        {{ $t('product-barcode.select') }}
      </v-btn>
      <v-btn v-else color="primary">
        {{ $t('product-barcode.selected') }}
      </v-btn>
    </v-card-title>

    <v-card-text class="text-medium-emphasis">
      <div class="d-flex w-100 overflow-auto">
        <v-img
          v-for="img in syncProduct.images"
          :key="img"
          :src="img"
          max-width="50px"
          max-height="50px"
          class="rounded mb-1 me-1"
          style="border: 1px solid #eeeeee;"
        />
      </div>
      <div class="mb-1">
        {{ syncProduct.name }}
        <br>
        {{ $t('product-barcode.price') }}: {{ syncProduct.skus[0].price }}
      </div>
    </v-card-text>
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import { PropType } from 'vue/types/options'
import { DobysyncProduct } from '~/models'

export default Vue.extend({
  name: 'ProductPlatformCard',
  props: {
    syncProduct: {
      type: Object as PropType<DobysyncProduct>,
      required: true
    },
    isSelected: {
      type: Boolean,
      default: false
    }
  }
})
</script>

<style lang="scss" scoped>

</style>
