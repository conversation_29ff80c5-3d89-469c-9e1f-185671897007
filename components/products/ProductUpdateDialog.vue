<template>
  <v-dialog max-width="955" :value="value" @input="$emit('input', $event)">
    <v-card>
      <v-card-title class="headline grey lighten-2">
        {{ $t('product-barcode.edit-product') }}
        <v-spacer />
        <v-btn data-test-id="v-btn-close" icon @click="$emit('input', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text class="pa-5">
        <div class="pb-5">
          {{ $t('product-barcode.sku-barcode-record') }}
        </div>
        <product-form ref="form" :form-data.sync="form" :form-errors="errors" @submit="updateProduct()" />
      </v-card-text>
      <v-card-actions>
        <v-spacer />
        <v-btn
          data-test="v-btn-update-save"
          color="success"
          :loading="loading"
          :disabled="loading"
          @click="updateProduct()"
        >
          {{ $t('save') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { PropType } from 'vue/types/options'
import _ from 'lodash'
import ProductForm from './ProductForm.vue'
import { Product, VForm } from '~/models'
import FileUploadModule from '~/repositories/modules/FileUpload'

export default Vue.extend({
  components: { ProductForm },
  props: {
    value: {
      type: Boolean,
      required: true
    },
    product: {
      type: Object as PropType<Product>,
      default: () => null
    }
  },

  data () {
    return {
      form: {
        sku: '',
        barcode: '',
        name: '',
        product_json: {},
        product_oms: 'web',
        images: [],
        main_image: '',
        uploaded_images: [],
        price: 0,
        vat_percent: 7,
        color: '',
        marketplace: null,
        dobysync_products: [],
        main_shop_id: null,
        weight: null,
        location: '',
        width: null,
        length: null,
        height: null,
        packing_score: null,
        show_qrcode_on_record_page: true
      } as Product,
      db_uploaded_images: [],
      errors: {},
      loading: false
    }
  },

  watch: {
    product: {
      immediate: true,
      handler (product) {
        if (!product) {
          return
        }

        this.resetForm()
        this.form = {
          sku: product.sku,
          barcode: product.barcode,
          name: product.name,
          product_json: product.product_json,
          product_oms: product.product_oms,
          images: product.images,
          main_image: product.main_image,
          uploaded_images: product.uploaded_images,
          price: product.price,
          vat_percent: product.vat_percent ? parseInt(product.vat_percent) : 7,
          color: product.color,
          marketplace: product.marketplace,
          dobysync_products: product.dobysync_products,
          main_shop_id: product.main_shop_id,
          weight: product.weight,
          location: product.location,
          width: product.width,
          length: product.length,
          height: product.height,
          packing_score: product.packing_score,
          show_qrcode_on_record_page: product.show_qrcode_on_record_page
        }
        this.db_uploaded_images = _.cloneDeep(product.uploaded_images)
      }
    }
  },

  methods: {
    resetForm () {
      this.form = {
        sku: '',
        barcode: '',
        name: '',
        product_json: {},
        product_oms: 'web',
        images: [],
        price: 0,
        vat_percent: 7,
        color: '',
        marketplace: null,
        dobysync_products: [],
        uploaded_images: [],
        main_image: '',
        main_shop_id: null,
        weight: null,
        width: null,
        length: null,
        height: null,
        packing_score: null,
        location: null,
        show_qrcode_on_record_page: true
      }
      this.errors = {}
    },

    async updateProduct () {
      this.errors = {}
      const isValid = (this.$refs.form as never as VForm).validate()
      if (!isValid) { return }

      this.loading = true

      // Check if main_image is an uploaded image and get the index for replace with image url
      let main_image_index = null as any
      if (typeof this.form.main_image !== 'string') {
        main_image_index = this.form.uploaded_images.indexOf(this.form.main_image)
      }

      const module = new FileUploadModule(this.$axios)
      this.form.uploaded_images = await module.uploadFiles(this.form.uploaded_images, `products/${(this.$auth.user as any).company.id}`)
      if (main_image_index !== null) {
        this.form.main_image = this.form.uploaded_images[main_image_index]
      }

      // delete removed images
      const removed_images = this.db_uploaded_images.filter((img: string) => !this.form.uploaded_images.includes(img))
      if (removed_images.length > 0) {
        await module.deleteFiles(removed_images)
      }

      try {
        const res = await this.$axios.put(`/api/picking/resource/products/${(this.product as any).id}/`, this.form)
        const product = res.data.product
        this.$emit('input', false)
        this.$emit('updated', product)
        this.$snackbar('success', this.$t('snackbar.save_success'))
        this.resetForm()
      } catch (error: any) {
        console.error(error)
        const res = error.response
        if (!res) {
          return this.$snackbar('error', this.$t('error.unknown'))
        }

        if (res.status === 400) {
          this.errors = res.data
        }
      }
      this.loading = false
    }
  }
})
</script>
