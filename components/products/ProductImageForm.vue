<template>
  <div>
    <h4>
      {{ $t('product-barcode.product-images') }}
    </h4>
    <!-- Upload File Preview -->
    <v-row v-if="uploaded_images.length || formData.images.length" wrap no-gutters>
      <v-col v-if="mainImage" cols="12">
        <div class="ma-1" style="border: 1px solid #eeeeee">
          <v-img
            :src="mainImage"
            object-fit="cover"
          />
        </div>
        {{ $t('product-barcode.click-to-select-main-image') }}
      </v-col>
      <v-col v-if="formData.images.length" cols="12">
        <h4>{{ $t('product-barcode.platform-images') }}</h4>
      </v-col>
      <v-col
        v-for="(img_url, index) in formData.images"
        :key="`platform-img-${index}`"
        cols="3"
        md="2"
      >
        <div class="ma-1">
          <v-img
            height="60"
            :src="img_url"
            cover
            style="border: 1px solid #eeeeee;"
            @click="update({ main_image: img_url })"
          />
        </div>
      </v-col>
      <v-col v-if="uploaded_images.length" cols="12">
        <h4>{{ $t('product-barcode.uploaded-images') }}</h4>
      </v-col>
      <v-col
        v-for="(file, index) in uploaded_images"
        :key="`uploaded-${index}`"
        cols="3"
        md="2"
      >
        <div class="ma-1">
          <v-img
            height="60"
            :src="file"
            cover
            style="border: 1px solid #eeeeee;"
            @click="update({ main_image: formData.uploaded_images[index] })"
          />
          <div class="text-caption d-flex align-center" @click="removeImage(index)">
            <v-icon left small class="me-0">
              mdi-delete
            </v-icon>
            {{ $t('product-barcode.delete-image') }}
          </div>
        </div>
      </v-col>
    </v-row>
    <v-row v-else>
      <v-col>
        <div style="width: 100%; height: 300px; border: 1px dashed #888888; flex-direction: column; cursor: pointer;" class="d-flex justify-center align-center" @click="showImagePicker()">
          <v-icon size="80">
            mdi-image
          </v-icon>
          <div class="font-weight-bold">
            {{ $t('product-barcode.add-image') }}
          </div>
        </div>
      </v-col>
    </v-row>
    <v-row no-gutters>
      <v-col cols="12">
        <v-file-input
          :key="v_file_input_key"
          id="file_input"
          class="mt-4"
          label="Choose File"
          prepend-icon="mdi-paperclip"
          multiple
          accept="image/*"
          @change="addUploadFile($event)"
        />
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { PropType } from 'vue/types/options'
import { Product } from '~/models'

export default Vue.extend({
  props: {
    formData: {
      type: Object as PropType<Product>,
      required: true
    }
  },
  data () {
    return {
      v_file_input_key: 0
    }
  },
  watch: {
    'formData.uploaded_images': {
      handler(newVal: File[], oldVal: File[]) {
        if (newVal.length == 0 && oldVal.length != 0) {
          this.v_file_input_key++
        }
      }
    }
  },
  computed: {
    uploaded_images (): string[] {
      const result: string[] = []
      for (const item of this.formData.uploaded_images) {
        if (typeof item === 'string') {
          result.push(item)
        } else {
          result.push(URL.createObjectURL(item))
        }
      }
      return result
    },

    mainImage (): string {
      const item = this.formData.main_image
      if (typeof item === 'string') {
        return item
      } else {
        return URL.createObjectURL(item)
      }
    }
  },
  methods: {
    reset () {
      this.v_file_input_key++
    },

    showImagePicker () {
      const fileInput = document.getElementById('file_input') as HTMLInputElement
      if (!fileInput) {
        return
      }
      fileInput.click()
    },

    addUploadFile (files: File[]) {
      const uploaded_images:any = { uploaded_images: [...this.formData.uploaded_images, ...files] }
      if (this.formData.main_image === '') {
        uploaded_images.main_image = uploaded_images.uploaded_images[0]
      }
      this.update(uploaded_images)
    },

    update (data: any) {
      this.$emit('update:form-data', { ...this.formData, ...data })
    },

    removeImage (index:number) {
      let main_image = this.formData.main_image
      const main_image_index = this.formData.uploaded_images.indexOf(this.formData.main_image)
      if (main_image_index === index) {
        if (this.formData.uploaded_images.length > 1) {
          main_image = this.formData.uploaded_images[main_image_index === 0 ? 1 : 0]
        } else if (this.formData.images.length > 0) {
          main_image = this.formData.images[0]
        } else {
          main_image = ''
        }
      }

      const uploaded_images = this.formData.uploaded_images
      uploaded_images.splice(index, 1)

      this.update({ uploaded_images, main_image })
    }
  }
})
</script>

<style lang="scss" scoped>

</style>
