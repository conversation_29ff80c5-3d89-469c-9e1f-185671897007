<template>
  <div style="max-width: 100%">
    <v-row dense class="mx-0">
      <v-col class="pa-0" cols="12">
        <v-combobox
          placeholder="SKU"
          :readonly="readOnly"
          :value="sku"
          :items="items.filter(x => !!x.sku)"
          :search-input.sync="sku_keyword"
          item-text="sku"
          item-value="id"
          filled
          v-bind="$attrs"
          :clearable="!readOnly"
          @change="onSkuChange($event)"
        />
      </v-col>
      <v-col class="pt-2 pa-0" cols="12">
        <v-combobox
          ref="combobox_name"
          placeholder="Name"
          :readonly="readOnly"
          :value="name"
          :items="items.filter(x => !!x.name)"
          :search-input.sync="name_keyword"
          item-text="name"
          item-value="id"
          filled
          v-bind="$attrs"
          :clearable="!readOnly"
          @change="onNameChange($event)"
        />
      </v-col>
    </v-row>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import _ from 'lodash'

interface ComboboxItem {
  id: number
  sku: string
  name: string
}

export default Vue.extend({
  props: {
    sku: {
      type: String,
      default: ''
    },

    name: {
      type: String,
      default: ''
    },

    readOnly: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data () {
    return {
      items: [] as ComboboxItem[],
      sku_keyword: '',
      name_keyword: '',
      search_keywords: {} as any
    }
  },

  watch: {
    sku_keyword: {
      async handler (val) {
        if (!val) { return }
        const keyword = val.toLowerCase().trim()

        if (keyword.length < 2) { return }
        const items = await this.findItemsByKeyword(keyword)
        this.search_keywords[keyword] = true
        this.items = _.uniqBy([...this.items, ...items], 'id')
      }
    },

    name_keyword: {
      async handler (val) {
        if (!val) { return }
        const keyword = val.toLowerCase().trim()

        if (keyword.length < 2) { return }
        const items = await this.findItemsByKeyword(keyword)
        this.search_keywords[keyword] = true
        this.items = _.uniqBy([...this.items, ...items], 'id')
      }
    }
  },

  mounted () {
    // @ts-ignore
    const target = this.$refs.combobox_name.$el.children[0].children[0]
    target.style.borderRadius = '0'
    target.style.position = 'relative'
    target.style.top = '-7px'
  },

  methods: {
    async findItemsByKeyword (keyword: string): Promise<ComboboxItem[]> {
      return await this.$axios.$get('/api/picking/products/search/', { params: { search: keyword } })
    },

    onSkuChange (event: string | ComboboxItem) {
      if (typeof event === 'string') {
        this.$emit('update:sku', event)
      } else if (event === null) {
        this.$emit('update:sku', '')
      } else {
        this.selectItem(event)
      }
    },

    onNameChange (event: string | ComboboxItem) {
      if (typeof event === 'string') {
        this.$emit('update:name', event)
      } else if (event === null) {
        this.$emit('update:name', '')
      } else {
        this.selectItem(event)
      }
    },

    selectItem (item: any) {
      this.$emit('select', {
        sku: item.sku,
        name: item.name,
        pricepernumber: item.price || 0,
        eso_vatpercent: parseInt(item.vat_percent),
        location: item.location || ''
      })
    }
  }
})
</script>
