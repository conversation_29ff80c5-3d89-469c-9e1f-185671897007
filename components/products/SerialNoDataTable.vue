<template>
  <div>
    <v-text-field
      data-test="v-text-field-search-product"
      :value="search"
      prepend-inner-icon="mdi-magnify"
      :placeholder="$t('search')"
      @input="$emit('update:search', $event)"
    />
    <v-data-table
      :footer-props="{'items-per-page-options': [10, 25, 50, 100]}"
      :headers="headers"
      :items="items"
      :loading="table.loading"
      :server-items-length="table.meta.total_results"
      :options="table.options"
      @update:options="updateTable({ options: $event })"
    >
      <template #item.in_stock="{item}">
        <v-icon :color="item.in_stock ? 'success' : 'error'">
          {{ item.in_stock ? 'mdi-check-circle' : 'mdi-close-circle' }}
        </v-icon>
      </template>
    </v-data-table>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { VDataTableHeader } from '~/models'
export default Vue.extend({
  props: {
    // add some props
    serialNos: {
      type: Array,
      default: () => []
    },

    table: {
      type: Object,
      required: true
    },

    search: {
      type: String,
      default: ''
    }
  },

  computed: {
    headers (): VDataTableHeader[] {
      return [
        { text: 'Serial No.', value: 'serial_no' },
        { text: 'SKU', value: 'sku' },
        { text: 'Name', value: 'product_name' },
        { text: 'In Stock', value: 'in_stock', align: 'center' }
      ]
    },
    items () {
      return this.serialNos
    }
  },

  methods: {
    updateTable (data: any) {
      this.$emit('update:table', { ...this.table, ...data })
    }
  }
})
</script>

<style lang="css" scoped>

.img-border{
  border:0.5px grey solid;
  width: 50px;
  height: 50px;
  border-radius: 5px;
}
</style>
