<template>
  <div style="max-width: 100%; overflow-x: auto; overflow-y: hidden">
    <table class="mt-4" style="width: 100%; min-width: 800px">
      <thead>
        <tr>
          <th class="px-1" style="width: 35%; text-align: left">
            รายการสินค้า (รหัส/ชื่อ)
          </th>
          <th class="px-1" style="width: 160px">
            ราคารวม VAT/หน่วย
          </th>
          <th class="px-1" style="width: 140px">
            จำนวน
          </th>
          <th class="px-1" style="width: 140px">
            ส่วนลด
          </th>
          <th class="px-1" style="width: 140px">
            ภาษี (%)
          </th>
          <th class="px-1" style="width: 140px">
            ราคารวม
          </th>
          <th class="fit" />
        </tr>
      </thead>

      <tbody v-if="orderList">
        <template v-for="(order_item, index) in orderList">
          <tr :key="index">
            <td style="width: 30%" class="py-1 pr-1">
              <!-- Autocomplete -->
              <product-combobox
                class="mt-1"
                hide-details="auto"
                dense
                :disabled="disabledAll"
                :read-only="readOnly"
                :rules="[$rules.required, $rules.is_not_blank]"
                :sku="order_item.sku"
                :name="order_item.name"
                @select="selectProduct(index, $event)"
                @update:sku="updateList(index, { sku: $event })"
                @update:name="updateList(index, { name: $event })"
              />
            </td>

            <td style="width: 15%" class="pr-1">
              <v-text-field
                class="mt-2"
                hide-details="auto"
                filled
                dense
                reverse
                type="number"
                min="0"
                :disabled="disabledAll"
                :readonly="readOnly"
                :rules="[$rules.required]"
                :value="order_item.pricepernumber"
                @click="selectText($event)"
                @change="updatePricePerNumber(index, $event)"
              />
            </td>

            <td style="width: 10%" class="pr-1">
              <v-text-field
                class="mt-2"
                hide-details="auto"
                filled
                dense
                reverse
                type="number"
                min="1"
                :rules="[$rules.required, $rules.number_min(1),$rules.numberIntegerOnly]"
                :disabled="disabledAll"
                :readonly="readOnly"
                :value="order_item.number"
                @click="selectText($event)"
                @change="updateNumber(index, $event)"
              />
            </td>

            <td style="width: 10%" class="pr-1">
              <v-text-field
                class="mt-2"
                hide-details="auto"
                filled
                dense
                reverse
                type="number"
                min="0"
                :disabled="disabledAll"
                :readonly="readOnly"
                :rules="[$rules.required, $rules.number_min(0)]"
                :value="order_item.seller_discount"
                @click="selectText($event)"
                @change="updateSellerDiscount(index, Number($event))"
              />
            </td>
            <td style="width: 10%;" class="pr-1">
              <v-select
                class="mt-2 select-vat"
                hide-details="auto"
                reverse
                filled
                dense
                :disabled="disabledAll"
                :readonly="readOnly"
                :items="vat_percent_choice"
                :value="order_item.eso_vatpercent?? 7"
                @change="updateList(index ,{ eso_vatpercent: $event })"
              >
                <!-- <template #item="{ item }">
                  {{ item }}%
                </template> -->
              </v-select>
            </td>

            <td style="width: 10%" class="pr-1">
              <v-text-field
                class="mt-2"
                hide-details="auto"
                filled
                dense
                reverse
                disabled
                type="number"
                :readonly="readOnly"
                :value="order_item.totalprice"
              />
            </td>
            <td class="px-1">
              <v-btn v-show="!readOnly && !disabledAll" icon tabindex="-1" @click="removeList(index)">
                <v-icon> mdi-close </v-icon>
              </v-btn>
            </td>
          </tr>
        </template>
        <tr>
          <td colspan="4">
            <v-btn
              v-show="!readOnly && !disabledAll"
              class="mt-2"
              outlined
              x-small
              color="primary"
              @click="appendList()"
            >
              <v-icon left>
                mdi-plus
              </v-icon>
              เพิ่มสินค้า
            </v-btn>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import ProductCombobox from '../products/ProductCombobox.vue'
import { PickItem } from '~/models'

export default Vue.extend({
  components: {
    ProductCombobox
  },
  props: {
    orderList: {
      type: Array,
      required: true,
      default: () => []
    } as Vue.PropOptions<PickItem[]>,

    readOnly: {
      type: Boolean,
      default: false,
      required: false
    },

    disabledAll: {
      type: Boolean,
      default: false,
      required: false
    }
  },

  data () {
    return {
      vat_percent_choice: [7, 0]
    }
  },

  methods: {
    updateList (index: number, data: any) {
      const orderItems = [...this.orderList]
      orderItems[index] = { ...orderItems[index], ...data }
      this.$emit('update:order-list', orderItems)
    },
    updatePricePerNumber (index: number, price: number) {
      const orderItems = [...this.orderList]
      orderItems[index].pricepernumber = price
      orderItems[index].totalprice = this.$dec(price * orderItems[index].number - orderItems[index].seller_discount)
      this.$emit('update:order-list', orderItems)
    },
    updateNumber (index: number, number: number) {
      const orderItems = [...this.orderList]
      orderItems[index].number = number
      orderItems[index].totalprice = this.$dec(number * orderItems[index].pricepernumber - orderItems[index].seller_discount)
      this.$emit('update:order-list', orderItems)
    },
    updateSellerDiscount (index: number, discount: number) {
      const orderItems = [...this.orderList]
      orderItems[index].seller_discount = discount
      orderItems[index].totalprice = this.$dec(orderItems[index].pricepernumber * orderItems[index].number - discount)
      this.$emit('update:order-list', orderItems)
    },
    removeList (index: number) {
      const orderItems = [...this.orderList]
      orderItems.splice(index, 1)
      this.$emit('update:order-list', orderItems)
    },
    appendList () {
      const emptyItem = {
        id: null,
        sku: '',
        name: '',
        number: 1,
        status: null,
        skutype: null,
        discount: '',
        mfg_date: null,
        unittext: '',
        productid: null,
        totalprice: 0,
        producttype: 0,
        sku_barcode: null,
        serialnolist: [],
        discountamount: 0,
        original_price: 0,
        pricepernumber: 0,
        eso_vatpercent: 7,
        seller_discount: 0,
        shipping_amount: 0,
        platform_discount: 0,
        original_shipping_amount: 0,
        seller_shipping_discount: 0,
        platform_shipping_discount: 0
      }
      this.$emit('update:order-list', [...this.orderList, emptyItem])
    },
    selectProduct (index: number, event: any) {
      const data = {
        ...event,
        totalprice: this.$dec(event.pricepernumber * this.orderList[index].number - this.orderList[index].seller_discount)
      }

      this.updateList(index, data)
    },
    selectText (event: PointerEvent) {
      const target = event.target as HTMLInputElement
      target.select()
      target.focus()
    }
  }

})
</script>

<style scoped>
table {
  border-collapse: collapse;
}
th {
  text-align: right;
}
td {
  vertical-align: baseline;
}
.select-vat {
  position: relative;
  top: 4px;
}
</style>
