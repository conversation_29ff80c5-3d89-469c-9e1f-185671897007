<template>
  <v-dialog max-width="1000" :value="value" @input="$emit('input', $event)">
    <v-card>
      <v-card-title class="headline grey lighten-2">
        {{ $t('product-barcode.create-product-barcode') }}
        <v-spacer />
        <v-btn data-test-id="v-btn-close" icon @click="$emit('input', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text class="pa-5">
        <div class="pb-5">
          {{ $t('product-barcode.sku-barcode-record') }}
        </div>
        <product-form ref="form" :form-data.sync="form" :form-errors="errors" @submit="createProduct()" />
      </v-card-text>
      <v-card-actions>
        <v-spacer />
        <v-btn
          data-test="v-btn-save-product"
          color="success"
          :loading="loading"
          :disabled="loading"
          @click="createProduct()"
        >
          {{ $t('save') }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import ProductForm from './ProductForm.vue'
import { VForm } from '~/models/vuetify'
import FileUploadModule from '~/repositories/modules/FileUpload'
import { Product } from '~/models'
export default Vue.extend({
  components: { ProductForm },
  props: {
    value: {
      type: Boolean,
      required: true
    }
  },

  data () {
    return {
      form: {
        sku: '',
        barcode: '',
        name: '',
        product_json: {},
        product_oms: 'web',
        images: [] as string[],
        price: 0,
        color: '',
        marketplace: null,
        dobysync_products: [],
        uploaded_images: [],
        main_image: '',
        main_shop_id: null,
        weight: null,
        location: '',
        width: null,
        length: null,
        height: null,
        packing_score: null,
        vat_percent: 7,
        show_qrcode_on_record_page: true
      } as Product,
      errors: {},
      loading: false
    }
  },

  methods: {
    resetForm () {
      this.form = {
        sku: '',
        barcode: '',
        name: '',
        product_json: {},
        product_oms: 'web',
        images: [],
        uploaded_images: [],
        main_image: '',
        price: 0,
        vat_percent: 7,
        color: '',
        marketplace: null,
        dobysync_products: [],
        main_shop_id: null,
        weight: null,
        location: '',
        width: null,
        length: null,
        height: null,
        packing_score: null,
        show_qrcode_on_record_page: true
      }
      this.errors = {}
    },

    async createProduct () {
      this.errors = {}
      const vform = this.$refs.form as never as VForm
      if (!vform.validate()) {
        return
      }
      this.loading = true
      try {
        let main_image_index = null as any
        if (typeof this.form.main_image !== 'string') {
          main_image_index = this.form.uploaded_images.indexOf(this.form.main_image)
        }

        const module = new FileUploadModule(this.$axios)
        this.form.uploaded_images = await module.uploadFiles(this.form.uploaded_images, `products/${(this.$auth.user as any).company.id}`)
        if (main_image_index !== null) {
          this.form.main_image = this.form.uploaded_images[main_image_index]
        }

        const res = await this.$axios.post('/api/picking/resource/products/', this.form)
        const product = res.data.product
        this.$emit('created', product)
        this.$emit('input', false)
        this.$snackbar('success', this.$t('snackbar.save_success'))
        this.resetForm()
      } catch (error: any) {
        console.error(error)
        const res = error.response
        if (!res) {
          return this.$snackbar('error', this.$t('error.unknown'))
        }

        if (res.status === 400) {
          this.errors = res.data
        }
      }
      this.loading = false
    }
  }
})
</script>
