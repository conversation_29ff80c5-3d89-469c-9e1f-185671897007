<template>
  <v-form ref="form" @submit.prevent="$emit('submit')">
    <v-row>
      <v-col cols="12" md="5">
        <product-image-form
          ref="product_image_form"
          :form-data="formData"
          @update:form-data="update($event)"
        />
      </v-col>
      <v-col cols="12" md="7">
        <v-row no-gutters>
          <v-col cols="12">
            <h4>SKU & Barcode</h4>
          </v-col>
          <v-col cols="6" class="pr-1">
            <v-text-field
              data-test="v-text-field-sku"
              filled
              label="SKU*"
              type="text"
              :rules="[$rules.required]"
              :value="formData.sku"
              autocomplete="off"
              :error-messages="formErrors.sku"
              @change="update({ sku: $event })"
            />
          </v-col>
          <v-col cols="6" class="pl-1">
            <v-text-field
              data-test="v-text-field-barcode"
              filled
              label="Barcode"
              type="text"
              :value="formData.barcode"
              autocomplete="off"
              @change="update({ barcode: $event })"
            />
          </v-col>
        </v-row>

        <!-- Dimensions -->
        <v-row no-gutters class="mt-4">
          <v-col cols="12">
            <h4>
              {{ $t("product-barcode.general-product-infomation") }}
            </h4>
          </v-col>
          <v-col cols="12">
            <v-text-field
              data-test="v-text-field-product-name"
              label="Name"
              :value="formData.name"
              autocomplete="off"
              @change="update({ name: $event })"
            />
          </v-col>
          <v-col cols="3">
            <v-text-field
              data-test="v-text-field-product-name"
              label="Price"
              type="number"
              :value="formData.price"
              @change="update({ price: $event })"
            >
              <template #append>
                <v-tooltip bottom>
                  <template #activator="{ on, attrs }">
                    <v-icon color="grey" dark v-bind="attrs" v-on="on">
                      mdi-information
                    </v-icon>
                  </template>
                  <span>{{ $t("product.barcode-vat_percent") }}</span>
                </v-tooltip>
              </template>
            </v-text-field>
          </v-col>
          <v-col class="mx-3" cols="2">
            <v-select
              reverse
              label="VAT (%)"
              :items="vat_percent_choice"
              :value="formData.vat_percent"
              @change="update({ vat_percent: $event })"
            >
              <template #item="{ item }">
                {{ item }}%
              </template>
            </v-select>
          </v-col>
          <v-col cols="6">
            <v-text-field
              data-test="v-text-field-product-color"
              label="Color"
              class="ms-2"
              :value="formData.color"
              type="text"
              @change="update({ color: $event })"
            />
          </v-col>
          <v-col cols="4" class="pr-1">
            <v-text-field
              type="number"
              label="Width (mm)"
              :value="formData.width"
              :error-messages="formErrors.width"
              @change="update({ width: asNumber($event) })"
            />
          </v-col>
          <v-col cols="4" class="px-1">
            <v-text-field
              type="number"
              label="Length (mm)"
              :value="formData.length"
              :error-messages="formErrors.length"
              @change="update({ length: asNumber($event) })"
            />
          </v-col>
          <v-col cols="4" class="pl-1">
            <v-text-field
              type="number"
              label="Height (mm)"
              :value="formData.height"
              :error-messages="formErrors.height"
              @change="update({ height: asNumber($event) })"
            />
          </v-col>
          <v-col cols="12">
            <v-text-field
              type="number"
              label="Weight (grams)"
              :value="formData.weight"
              :error-messages="formErrors.weight"
              @change="update({ weight: asNumber($event) })"
            />
          </v-col>
          <v-col cols="12">
            <v-text-field
              type="text"
              label="Location"
              :value="formData.location"
              :error-messages="formErrors.location"
              @change="update({ location: $event })"
            />
          </v-col>
        </v-row>

        <v-row no-gutters class="mt-4">
          <v-col cols="12">
            <h4>
              P-Score (Packing Score) คะแนนความยากในการแพ็คสินค้า 1-10 (1 =
              ง่าย, 10 = ยาก)
            </h4>
          </v-col>
          <v-col cols="12">
            <v-text-field
              type="number"
              label="PScore"
              hint="1 - 10 (1 = easy, 10 = hard)"
              :value="formData.packing_score"
              :error-messages="formErrors.packing_score"
              @change="update({ packing_score: asNumber($event) })"
            />
          </v-col>
        </v-row>

        <v-row no-gutters class="mt-4">
          <v-col cols="12">
            <h4>ตั้งค่าอื่นๆ</h4>
          </v-col>
          <v-col cols="12">
            <v-checkbox
              label="แสดง QRCode หน้าบันทึกวิดีโอ"
              :input-value="formData.show_qrcode_on_record_page"
              :error-messages="formErrors.show_qrcode_on_record_page"
              @change="update({ show_qrcode_on_record_page: $event })"
            />
          </v-col>
        </v-row>
      </v-col>

      <v-col v-if="formData.dobysync_products.length > 0" cols="12">
        <v-row no-gutters>
          <v-col cols="12" class="mb-2">
            <h4>
              {{ $t("product-barcode.product-from-marketplace") }}
            </h4>
          </v-col>
          <v-col
            v-for="sync_product in formData.dobysync_products"
            :key="sync_product.shop_id"
            cols="12"
            md="4"
            class="pe-1 pb-1"
          >
            <product-platform-card
              :sync-product="sync_product"
              :is-selected="formData.main_shop_id === sync_product.shop_id"
              @click="selectMainShop(sync_product)"
            />
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import { PropType } from 'vue/types/options'
import ProductPlatformCard from './ProductPlatformCard.vue'
import ProductImageForm from './ProductImageForm.vue'
import { Product, VForm } from '~/models'

export default Vue.extend({
  components: {
    ProductPlatformCard,
    ProductImageForm
  },
  props: {
    formData: {
      type: Object as PropType<Product>,
      required: true
    },
    formErrors: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      vat_percent_choice: [7, 0]
    }
  },
  methods: {
    update (data: any) {
      this.$emit('update:form-data', { ...this.formData, ...data })
    },

    validate () {
      const vform = this.$refs.form as never as VForm
      return vform.validate()
    },

    asNumber (n: any) {
      if (n === '') {
        return null
      }
      return Number(n)
    },

    selectMainShop (syncProduct: any) {
      let main_image = this.formData.main_image
      if (!this.formData.uploaded_images.includes(main_image)) {
        if (syncProduct.images.length > 0) {
          main_image = syncProduct.images[0]
        } else {
          main_image = ''
        }
      }

      this.update({
        main_shop_id: syncProduct.shop_id,
        marketplace: syncProduct.marketplace,
        name: syncProduct.name,
        images: syncProduct.images,
        main_image,
        price: syncProduct.skus[0].price,
        width: syncProduct.skus[0].width,
        length: syncProduct.skus[0].length,
        height: syncProduct.skus[0].height,
        weight: syncProduct.skus[0].weight
      })
    }
  }
})
</script>
