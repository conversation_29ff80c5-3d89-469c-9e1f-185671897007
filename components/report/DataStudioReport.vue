<template>
  <div>
    <iframe
      v-if="iframe_src"
      :src="iframe_src"
      frameborder="0"
      style="border: 0; width: 100%; height: calc(100vh - 100px);"
      allowfullscreen
    />
    <v-row class="px-3" justify="end">
      <v-checkbox v-model="auto_refresh" :label="`Refresh every 30 minutes  ${durationText}`" @change="toggleAutoRefresh($event)" />
    </v-row>
    <!-- <div class="mx-3">
      <h1>{{ report_name }} Report</h1>
      <p>
        ขณะนี้เรากำลังปรับปรุงระบบรายงาน ขอให้ท่านเปลี่ยนไปใช้การดาวน์โหลดรายงานเป็นไฟล์ Excel ชั่วคราวเพื่อการเข้าถึงรายงาน
        หากท่านต้องการความช่วยเหลือหรือมีคำถามใดๆ กรุณาติดต่อทีมสนับสนุนของเรา
      </p>
    </div>
    <v-form style="max-width: 600px;" @submit.prevent="downloadExcelReport">
      <v-row class="mx-0">
        <v-col>
          <DatePicker v-model="form.start_date" class="mr-5" :label="$t('from')" />
        </v-col>
        <v-col>
          <DatePicker v-model="form.end_date" :label="$t('to')" />
        </v-col>
      </v-row>
      <v-row class="mx-3">
        <v-btn type="Submit" block color="primary">
          ดาวน์โหลดรายงาน (Excel)
        </v-btn>
      </v-row>
    </v-form> -->
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { formatDuration } from '~/api/utils'
import { getReportServiceHost } from '~/utils'

export default Vue.extend({
  middleware: ['auth'],

  props: {
    requiredPermission: { type: String, required: true },
    reportUrl: { type: String, required: true },
    datasourcePath: { type: String, required: true },
    params: { type: Object, default () { return {} } }
  },

  data () {
    return {
      iframe_src: '',
      iframe_src2: '',
      auto_refresh: false,
      timer: null as any | null,
      counter: 0,

      form: {
        start_date: '',
        end_date: ''
      },
      report_name: ''
    }
  },

  computed: {
    durationText (): string {
      if (!this.timer) {
        return ''
      }
      return '(' + formatDuration(this.counter / 1000) + ')'
    }
  },

  created () {
    if (!this.$hasPerms(this.requiredPermission)) {
      this.$nuxt.error({ statusCode: 403, message: 'Unauthorized' })
    }
  },

  beforeDestroy () {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },

  mounted () {
    this.init()

    this.form.start_date = this.$moment().format('YYYY-MM-DD')
    this.form.end_date = this.$moment().format('YYYY-MM-DD')

    // get report name from url path
    const current_path = this.$route.path
    const path_array = current_path.split('/')
    const report_name = path_array[path_array.length - 1]
    const report_name_array = report_name.split('-')
    for (let i = 0; i < report_name_array.length; i++) {
      report_name_array[i] = report_name_array[i].charAt(0).toUpperCase() + report_name_array[i].slice(1)
    }
    this.report_name = report_name_array.join(' ')
  },

  methods: {
    init () {
      if (!this.$auth.user) { return }
      const access_token = (this.$auth.strategy as any).token.get()

      const params: any = {
        'ds0.access_token': access_token,
        'ds0.report_url': `${this.getDatasourceHost()}/${this.datasourcePath}`,
        ...this.params
      }
      const paramsAsString = JSON.stringify(params)
      const encodedParams = encodeURIComponent(paramsAsString)
      this.iframe_src = `${this.reportUrl}?params=${encodedParams}`
      this.iframe_src2 = `${this.reportUrl}?params=${encodedParams}`
    },

    getDatasourceHost () {
      const host = getReportServiceHost()
      if (window.location.hostname === 'localhost') {
        return 'https://api-uat.dobybot.com'
      }
      return host
    },

    toggleAutoRefresh () {
      const SECOND = 1000
      const MINUTE = 60 * SECOND

      if (this.auto_refresh) {
        this.timer = setInterval(() => {
          if (this.counter > 30 * MINUTE) {
            this.iframe_src = ''
            setTimeout(() => { this.iframe_src = this.iframe_src2 }, 0)
            this.counter = 0
          } else {
            this.counter += 1 * SECOND
          }
        }, 1 * SECOND)
      } else {
        clearInterval(this.timer)
        this.timer = null
      }
    },

    async downloadExcelReport () {
      const access_token = (this.$auth.strategy as any).token.get()
      const params = {
        start_date: this.form.start_date,
        end_date: this.form.end_date,
        fmt: 'xlsx'
      }
      const url = `${this.getDatasourceHost()}/${this.datasourcePath}?${new URLSearchParams(params).toString()}`

      this.$loader(true)
      const response = await fetch(
        url,
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `${access_token}`
          }
        }
      )

      const blob = await response.blob()
      const download_url = window.URL.createObjectURL(new Blob([blob]))
      const link = document.createElement('a')
      link.href = download_url
      link.setAttribute('download', 'report.xlsx')
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      this.$loader(false)
    }
  }
})
</script>
