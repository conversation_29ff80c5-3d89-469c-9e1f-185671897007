<template>
  <div>
    <v-text-field
      outlined
      dense
      append-icon="mdi-content-copy"
      label="host"
      :value="apikey.host"
      readonly
      @click:append="copyText(apikey.host)"
    />
    <v-text-field
      outlined
      dense
      append-icon="mdi-content-copy"
      label="token"
      :value="apikey.token"
      readonly
      @click:append="copyText(apikey.token)"
    />
    <div v-if="$auth.user?.is_superuser" class="mb-5" style="border: 1px solid #ccc; padding: 10px; position: relative;">
      <h5>Token User</h5>
      {{ apikey.username }}

      <h5 class="mt-5">
        Token Permissions
        <a target="_blank" :href="`${apikey.host}/admin/users/user/${apikey.user_id}/change/`">Change</a>
      </h5>
      <ul>
        <li v-for="perm in apikey.permissions" :key="perm">
          {{ perm }}
        </li>
      </ul>

      <div style="position: absolute; right: 0; bottom: 0; font-size: 0.5rem; background: #ccc; padding: 2px 5px; color: black;">
        STAFF ONLY
      </div>
    </div>
    <v-btn color="primary" outlined :disabled="apikey.token !== '-'" @click="generateApiToken()">
      Generate Token
    </v-btn>
    <v-btn color="error" outlined :disabled="apikey.token === '-'" @click="revokeToken()">
      Revoke Token
    </v-btn>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { getBackendHost } from '~/utils'
export default Vue.extend({

  data () {
    return {
      apikey: {
        host: '',
        token: '',
        user_id: null,
        username: '',
        permissions: []
      }
    }
  },
  created () {
    this.getApiToken()
  },
  mounted () {
    this.apikey.host = getBackendHost()
  },
  methods: {
    copyText (text: string) {
      navigator.clipboard.writeText(text)
      this.$snackbar('info', 'คัดลอกข้อความแล้ว')
    },

    async getApiToken () {
      const { data } = await this.$axios.get('/api/users/openapi-token/')
      this.apikey.token = data.token || '-'
      this.apikey.permissions = data.permissions || []
      this.apikey.username = data.username || ''
      this.apikey.user_id = data.user_id || null
    },

    async generateApiToken () {
      const password = await this.$prompt({
        text: 'กรุณากรอกรหัสผ่านเพื่อยืนยันการสร้าง Token',
        preset: 'password-text'
      })
      if (!password) {
        return
      }

      try {
        const { data } = await this.$axios.post('/api/users/openapi-token/', { password })
        this.apikey.token = data.token
        this.$snackbar('success', 'Generate token success')
        this.getApiToken()
      } catch (error: any) {
        console.error(error)
        if (error.response) {
          if (error.response.status === 400) {
            this.$snackbar('error', this.$t('error.invalid_password'))
            return
          }
        }
        this.$snackbar('error', this.$t('error.unknown'))
      }
    },

    async revokeToken () {
      const confirm = await this.$confirm({
        text: this.$t('confirm-revoke-token')
      })
      if (!confirm) {
        return
      }

      try {
        await this.$axios.delete('/api/users/openapi-token/')
        this.apikey.token = '-'
        this.$snackbar('success', this.$t('revoke-token-success'))
      } catch (error: any) {
        console.error(error)
        this.$snackbar('error', this.$t('error.unknown'))
      }
    }

  }
})
</script>
