<template>
  <v-dialog max-width="800" :value="value" @input="$emit('input', $event)">
    <v-card>
      <v-card-title class="headline grey lighten-2">
        Webhook Test Result
        <v-spacer />
        <v-btn data-test-id="v-btn-close" icon @click="$emit('input', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-card-text>
        <h3 class="mt-3">
          Request
        </h3>
        <div style="border: 1px solid black; max-height: 250px; overflow: scroll;">
          <pre>{{ JSON.stringify(request, null, 4) }}</pre>
        </div>

        <h3 class="mt-3">
          Response
        </h3>
        <div style="border: 1px solid black; max-height: 250px; overflow: scroll;">
          <pre>{{ JSON.stringify(response, null, 4) }}</pre>
        </div>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    value: {
      type: Boolean,
      required: true
    },
    request: {
      type: Object,
      required: true
    },
    response: {
      type: Object,
      required: true
    }
  }
})
</script>
