<template>
  <v-form @submit.prevent="">
    <!-- Docs -->
    <v-row v-if="docs.length > 0" no-gutters>
      <v-col cols="12">
        <h4 class="mb-1">
          Documentations
        </h4>
      </v-col>

      <v-col cols="12">
        <v-btn
          v-for="doc in docs"
          :key="doc.url"
          small
          color="primary"
          :href="doc.url"
          target="_blank"
          outlined
          class="mr-2"
        >
          <v-icon left>
            mdi-file-document
          </v-icon>
          {{ doc.title }}
        </v-btn>
        <!-- <ul>
          <li v-for="doc in docs" :key="doc.url">
            <a :href="doc.url">{{ doc.title }}</a>
          </li>
        </ul> -->
      </v-col>
    </v-row>

    <v-row no-gutters class="mt-4">
      <v-col cols="12">
        <h4 class="mb-1">
          URL
        </h4>
      </v-col>
      <v-col cols="2">
        <v-select
          outlined
          dense
          :items="allowMethods"
          :value="form_data.method"
          hide-details="auto"
          @change="update({ method: $event })"
        />
      </v-col>
      <v-col cols="8">
        <v-text-field
          outlined
          dense
          placeholder="https://example.com/video-update/?params=1234"
          :value="form_data.url"
          hide-details="auto"
          @change="update({ url: $event })"
        />
      </v-col>
    </v-row>

    <v-row no-gutters class="mt-4">
      <v-col cols="12">
        <h4 class="mb-1">
          Headers
        </h4>
      </v-col>
      <template v-for="header, i in form_data.headers">
        <v-col :key="'key-' + i" cols="2">
          <v-text-field
            :value="header.key"
            outlined
            dense
            placeholder="Key"
            hide-details="auto"
            @change="updateHeader(i, { key: $event })"
          />
        </v-col>
        <v-col :key="'value-' + i" cols="8">
          <v-text-field
            :value="header.value"
            outlined
            dense
            placeholder="Value"
            hide-details="auto"
            @change="updateHeader(i, { value: $event })"
          />
        </v-col>
        <v-col :key="'action-' + i" class="text-center" cols="1">
          <v-btn icon @click="removeHeader(i)">
            <v-icon>mdi-delete</v-icon>
          </v-btn>
        </v-col>
      </template>
      <v-col cols="12" class="mt-2">
        <v-btn outlined small @click="addHeader()">
          <v-icon left>
            mdi-plus
          </v-icon> ADD HEADER
        </v-btn>
      </v-col>
    </v-row>

    <v-row no-gutters class="mt-4">
      <v-col cols="12">
        <h4 class="mb-1">
          Example Payload
        </h4>
        <div
          style="white-space: pre; font-family: monospace; max-height: 400px; overflow-y: scroll; border: 1px solid #dddddd; padding: 4px;"
          v-text="JSON.stringify(examplePayload, null, 4)"
        />
      </v-col>
    </v-row>

    <v-row no-gutters class="mt-4">
      <v-col cols="12">
        <h4 class="mb-1">
          Testing
        </h4>
        <v-btn color="primary" small @click="sendTestWebhook()">
          Send Test Request
        </v-btn>
      </v-col>
    </v-row>

    <webhook-response-dialog v-model="dialog.show" :request="dialog.request" :response="dialog.response" />
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import WebhookResponseDialog from './WebhookResponseDialog.vue'
import { webhookConfigToForm, webhookFormToConfig } from '~/utils'
export default Vue.extend({
  components: {
    WebhookResponseDialog
  },

  props: {
    formData: {
      type: Object,
      default: () => null
    },
    examplePayload: {
      type: Object,
      required: true
    },
    allowMethods: {
      type: Array,
      default: () => ['GET', 'POST']
    },
    docs: {
      type: Array,
      default: () => []
    }
  },

  data () {
    return {
      form_data: null as any | null,
      dialog: {
        show: false,
        request: {},
        response: {}
      }
    }
  },

  watch: {
    formData: {
      handler (val: any) {
        if (val) {
          this.form_data = webhookConfigToForm(val)
        } else {
          this.form_data = {
            method: this.allowMethods[0],
            url: '',
            headers: []
          }
        }
      },
      immediate: true,
      deep: true
    }
  },

  methods: {
    update (data: any) {
      const formData = { ...this.form_data, ...data }
      this.$emit('update:form-data', { ...this.formData, ...webhookFormToConfig(formData) })
    },

    updateHeader (index: number, data: any) {
      const headers = this.form_data.headers.map((header, i) => {
        if (i === index) {
          return { ...header, ...data }
        }
        return header
      })
      this.update({ headers })
    },

    addHeader () {
      this.form_data.headers.push({
        key: '',
        value: ''
      })
    },

    removeHeader (index: number) {
      this.update({
        headers: this.form_data.headers.filter((_, i) => i !== index)
      })
    },

    async sendTestWebhook () {
      const config = {
        ...this.form_data,
        headers: this.form_data.headers.reduce((acc: any, header: any) => {
          if (header.key) {
            acc[header.key] = header.value
          }
          return acc
        }, {}),
        body: this.examplePayload
      }

      try {
        const response = await this.$axios.$post('/api/debugtools/webhook/', config)
        this.dialog.request = config
        this.dialog.response = response
        this.dialog.show = true
      } catch (error) {
        console.error(error)
      }
    }
  }
})
</script>
