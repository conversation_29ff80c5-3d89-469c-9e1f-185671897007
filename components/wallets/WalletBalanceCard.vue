<template>
  <v-card class="pa-3">
    <h4 class="d-flex">
      <v-icon>mdi-wallet</v-icon>
      <span class="my-auto ml-3">
        <table>
          <!-- <tr>
            <td>เครติด Record คงเหลือ</td>
            <td class="pl-3">{{ $auth.user.company.record_balance.toFixed(2) }}</td>
          </tr> -->
          <tr>
            <td>{{ $t('sms.credit-remain') }}</td>
            <td class="pl-3">{{ $auth.user.company.sms_balance.toFixed(2) }}</td>
          </tr>
        </table>
      </span>
      <v-spacer />
      <v-btn text color="primary" class="my-auto" @click="deposit_dialog = true">
       {{ $t('sms.top-up-credit') }}
      </v-btn>
    </h4>
    <deposit-cash-dialog v-model="deposit_dialog" />
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import DepositCashDialog from '../dialogs/DepositCashDialog.vue'

export default Vue.extend({
  components: {
    DepositCashDialog
  },
  data () {
    return {
      deposit_dialog: false
    }
  }
})
</script>
