<template>
  <v-row class="mt-2">
    <v-col v-for="sound in sounds" :key="sound" cols="4">
      <v-row>
        <v-btn class="mt-1 pt-1" icon small @click="playTestSound(sound)">
          <v-icon small>
            mdi-music
          </v-icon>
        </v-btn>
        <v-checkbox
          dense
          :value="sound"
          :input-value="value"
          :label="$t(sound)"
          :disabled="disabled"
          hide-details=""
          style="width: 180px;"
          @change="$emit('input', $event)"
        />
      </v-row>
    </v-col>
  </v-row>
</template>

<script lang="ts">
import Vue from 'vue'
import { playSound } from '~/utils'
export default Vue.extend({
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    value: {
      type: Array,
      default: () => [],
      required: true
    }
  },

  data () {
    return {
      sounds: [
        'esan1',
        'esan2',
        'esan3',
        'esan4',
        'female1',
        'female2',
        'female3',
        'female4',
        'kid1',
        'kid2',
        'kid3',
        'kid4',
        'male1',
        'male2',
        'male3',
        'male4',
        'pingpong',
        'ding'
      ]
    }
  },

  methods: {
    playTestSound (sound: string) {
      playSound('cheerup/' + sound)
    }
  }
})
</script>
