<template>
  <div class="d-flex">
    <!-- Company Buyer Card -->
    <v-card

      width="300"
      elevation="3"
      hover
      class="recent-buyer-card"
    >
      <v-card-title class="text-subtitle-1 pa-4">
        {{ buyerData.is_personal?"ข้อมูลบุคคลธรรมดา":"ข้อมูลนิติบุคคล" }}
      </v-card-title>

      <v-card-text class="pa-4 pt-0">
        <div class="text-body-2 mb-1">
          <v-icon small class="mr-1">
            mdi-cards-diamond
          </v-icon>
          {{ buyerData.tax_id }}
        </div>
        <div class="text-body-2 mb-1">
          <v-icon small class="mr-1">
            {{ buyerData.is_personal?"mdi-account":"mdi-domain" }}
          </v-icon>
          {{ buyerData.buyer_name }}
        </div>
        <div class="d-flex text-body-2 ">
          <div class="mb-2 ">
            <v-icon small class="mr-1">
              mdi-map-marker
            </v-icon>
          </div>
          <span>
            {{ buyerData.address }} {{ buyerData.post_code }}
          </span>
        </div>
        <div class="text-body-2 mb-2">
          <v-icon small class="mr-1">
            mdi-email
          </v-icon>
          {{ buyerData.email }}
        </div>
        <div class="text-body-2 mb-1">
          <v-icon small class="mr-1">
            mdi-phone
          </v-icon>
          {{ buyerData.phone_number }}
        </div>
      </v-card-text>

      <v-card-actions class="pa-4 pt-0">
        <v-btn
          small
          text
          color="primary"
          @click="$emit('select-buyer',buyerData)"
        >
          ใช้ข้อมูลนี้
        </v-btn>
      </v-card-actions>
    </v-card>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: {
    buyerData: {
      type: Object,
      required: true
    }
  }
})
</script>
