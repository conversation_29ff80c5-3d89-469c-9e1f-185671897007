<template>
  <v-card
    class="mx-auto"
    max-width="400"
    elevation="0"
  >
    <v-card-text>
      <!-- Header -->
      <div contenteditable style="color: black;" class="d-flex justify-center text-center">
        {{ header || settings.ETAX_HEADER_MESSAGE_BILL }}
      </div>
      <div class="d-flex justify-center text-center">
        <v-btn class="do-not-print" color="success" outlined @click="navigateButton ">
          ลิงค์ออกใบกำกับภาษี
        </v-btn>
      </div>

      <!-- QR Code Container -->
      <div class="my-3 text-center">
        <img
          class="pa-0"
          style="width:30mm; height:30mm;"
          :src="qrCodeUrl"
          alt="QR Code"
        >
      </div>

      <!-- Footer -->
      <div contenteditable style="color: black;" class="d-flex justify-center text-center">
        {{ footer || settings.ETAX_FOOTER_MESSAGE_BILL }}
      </div>
    </v-card-text>
  </v-card>
</template>

<script lang="ts">
import Vue from 'vue'
import { SettingState } from '~/store/settings'

export default Vue.extend({
  props: {
    header: {
      type: String,
      default: ''
    },
    footer: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      setting: (this.$auth.user)
    }
  },
  computed: {
    settings (): SettingState {
      return this.$store.state.settings.company
    },
    qrCodeUrl () {
      const company_id = (this.$auth.user as any).company.uuid
      const qrcode = `${window.location.origin}/public/etax-invoice?cid=${company_id}`
      return `https://go-barcode-yuo4mnnlaa-as.a.run.app/qrcode/?data=${qrcode}`
    }
  },
  methods: {
    navigateButton () {
      const company_id = (this.$auth.user as any).company.uuid
      const link = `/public/etax-invoice?cid=${company_id}`
      this.$router.push(link)
    }
  }
})
</script>
<style>
@media print {
  .do-not-print {
    display: none; /* Hide elements with this class when printing */
  }
}
</style>
