<template>
  <v-card
    class="mx-1 my-4 d-flex flex-column justify-center "
    variant="outlined"
    max-width="100%"
    height="150"
    rounded="rounded-xl"
    elevation="2"
  >
    <!-- Card Title -->
    <v-card-title class="text-h6 py-1 font-weight-bold text-center primary--text d-flex  align-center">
      รายละเอียดออเดอร์
      <v-img
        v-if="logo"
        :src="require(`~/static/platform-logo/${logo}.png`)"
        class="ml-auto"
        max-width="40"
        max-height="40"
      />
    </v-card-title>

    <v-divider class="my-1 mx-3" />

    <v-card-text class="py-0 mt-3">
      <div class="d-flex font-weight-medium text-subtitle-2">
        <span class="mr-5">เลขที่ออเดอร์:</span>
        <v-spacer />
        <span># {{ orderOverview.order_number }}</span>
      </div>

      <div class="d-flex font-weight-medium text-subtitle-2">
        <span>ราคารวม:</span>
        <v-spacer />
        <span>{{ orderOverview.payment_amount }} ฿</span>
      </div>
    </v-card-text>
  </v-card>
</template>

<script lang='ts' >
import Vue from 'vue'

export default Vue.extend({
  props: {
    logo: {
      type: String,
      default: ''
    },
    orderOverview: {
      type: Object,
      default: () => {}
    }
  }
})
</script>

<style>

</style>
