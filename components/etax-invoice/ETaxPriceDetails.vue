<template>
  <table style="width: 100%; font-size: 1rem;">
    <tbody>
      <tr>
        <td>จํานวนภาษีมูลค่าเพิ่ม </td>
        <td class="text-right">
          {{ $fmtCurrency(vatDetails.vat_amount) }} บาท
        </td>
      </tr>
      <tr class="pt-2">
        <td>มูลค่าราคารวมทั้งสิ้น </td>
        <td class="text-right">
          {{ $fmtCurrency(vatDetails.amount_before_vat) }} บาท
        </td>
      </tr>
      <tr>
        <td colspan="2">
          <v-divider class="my-2" />
        </td>
      </tr>
      <tr>
        <td class="text-h6">
          <b>จํานวนเงินรวมทั้งสิ้น</b>
        </td>
        <td class="text-right text-h6">
          <b>{{ $fmtCurrency(vatDetails.grand_amount) }} บาท</b>
        </td>
      </tr>
    </tbody>
  </table>
</template>

<script lang="ts">
import Vue from 'vue'
interface VatDetail{
  amount_before_vat:number;
  vat_amount:number,
  grand_amount:number
}
export default Vue.extend({
  props: {
    orderJson: {
      type: Object,
      default: () => {}
    },
    selectVatType: {
      type: String,
      default: localStorage.getItem('vat_mode')
    }
  },
  data () {
    return {

    }
  },
  computed: {
    vatDetails ():VatDetail {
      const totalPrice = this.orderJson.list.reduce(
        (acc: number, item: any) => acc + item.totalprice,
        0
      )

      if (this.selectVatType === 'inclusive_vat') {
        const amount_before_vat = (totalPrice / 1.07)
        const vat_amount = totalPrice - amount_before_vat
        return { amount_before_vat, vat_amount, grand_amount: totalPrice }
      }
      if (this.selectVatType === 'exclusive_vat') {
        const amount_before_vat = totalPrice
        const vat_amount = (totalPrice * 1.07) - totalPrice
        return { amount_before_vat, vat_amount, grand_amount: totalPrice * 1.07 }
      }
      return { amount_before_vat: 0, vat_amount: 0, grand_amount: 0 }
    }
  }

})
</script>
