<template>
  <v-form ref="form" :value="isValid" @input="$emit('update:is-valid',$event)" @submit.prevent>
    <div class="pa-0 ">
      <div class="text-h6 ">
        ข้อมูลสำหรับการจัดส่งเอกสาร
      </div>
      <p>
        <!-- ระบบจะจัดส่งใบกำกับภาษีอิเล็กทรอนิกส์ไปยังอีเมลที่ท่านระบุ -->
        กรุณาตรวจสอบความถูกต้องของข้อมูลการติดต่อ
      </p>
    </div>

    <v-text-field
      label="อีเมล *"
      outlined
      filled
      clearable
      dense
      hide-details="auto"
      :rules="[$rules.required, $rules.email]"
      :value="formData.email"
      @change="update({email: $event ? $event.trim() : ''})"
    />
    <v-text-field
      label="อีเมลสำรอง (ถ้ามี)"
      outlined
      filled
      clearable
      dense
      class="mt-1"
      :rules="[$rules.email]"
      :value="formData.email2"
      @change="update({email2: $event ? $event.trim() : ''})"
    />

    <v-text-field
      label="เบอร์โทรศัพท์ *"
      outlined
      filled
      clearable

      dense
      :value="formData.phone_number"
      :rules="[$rules.required, $rules.phoneNumberFormat,$rules.NoSpacesAllowed]"
      @change="update({phone_number:$event})"
    />
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import { VForm } from '~/models'
export default Vue.extend({
  props: {
    formData: {
      type: Object,
      required: true
    },
    isValid: {
      type: Boolean,
      default: false

    }
  },
  data () {
    return {

    }
  },
  methods: {
    update (data:any) {
      this.$emit('update:form-data', { ...this.formData, ...data })
    },
    validate ():boolean {
      const form = this.$refs.form as never as VForm
      return form.validate()
    }
  }
})
</script>
