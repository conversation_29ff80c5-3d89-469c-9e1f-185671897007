<template>
  <v-form ref="form" @submit.prevent>
    <v-text-field
      :value="formData.buyer_name"
      label="ชื่อ-นามสกุล *"
      outlined
      filled
      dense
      :rules="[$rules.required, $rules.is_not_blank, $rules.maxlength(150)]"
      @change="update({buyer_name: $str($event)})"
    />

    <v-text-field
      label="เลขที่ผู้เสียภาษี (13 หลัก) *"
      outlined
      filled
      dense
      type="number"
      :rules="[$rules.required,$rules.exactLength(13)]"
      :value="formData.tax_id"
      @change="update({tax_id: $str($event)})"
    />

    <v-textarea
      label="ที่อยู่สำหรับออกใบกำกับภาษี *"
      outlined
      filled
      dense
      :rules="[$rules.required, $rules.is_not_blank, $rules.maxlength(198)]"
      rows="4"
      :value="formData.address"
      @change="update({address: $str($event)})"
    />
    <v-text-field
      label="รหัสไปรษณีย์ *"
      outlined
      clearable
      filled
      dense
      :rules="[$rules.required,$rules.numberOnlyWithLength(5)]"
      :value="formData.post_code"
      type="number"
      height="40"
      @input="update({post_code: $str($event)})"
    />
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import { VForm } from '~/models'
export default Vue.extend({
  props: {
    formData: {
      type: Object,
      required: true
    }
  },
  data () {
    return {

    }
  },

  methods: {
    update (data:any) {
      this.$emit('update:form-data', { ...this.formData, ...data })
    },

    validate ():boolean {
      const form = this.$refs.form as never as VForm
      return form.validate()
    }
  }
})
</script>
