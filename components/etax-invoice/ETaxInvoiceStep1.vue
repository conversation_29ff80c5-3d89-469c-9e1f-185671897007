<template>
  <div>
    <!-- Section: Info -->
    <div>
      <div class="text-h6">
        ขอใบกำกับภาษีอิเล็กทรอนิกส์ (E-Tax Invoice)
      </div>

      <div v-if="settings.ETAX_ALLOW_ON_ORDER_RECEIVED" class="text-body-1 black--text mt-2">
        <v-icon color="primary" class="mr-4">
          mdi-alert-circle
        </v-icon>
        ต้อง <b>กดรับของ</b> จากแพลตฟอร์มก่อน
      </div>
      <!-- <div class="text-body-1 black--text mt-2">
        <v-icon color="primary" class="mr-4">
          mdi-calendar-alert
        </v-icon>
        ต้องขอภายใน {{ settings.ETAX_ORDER_OPEN_DAYS }} วัน นับจากวันสั่งสินค้า
      </div> -->
      <div class="text-body-1 black--text mt-2">
        <v-icon color="primary" class="mr-4">
          mdi-account-alert
        </v-icon>
        {{ settings.ETAX_CUSTOMER_EDIT_MESSAGE? settings.ETAX_CUSTOMER_EDIT_MESSAGE : 'ต้องการแก้ไข กรุณาติดต่อแอดมินร้านค้า' }}
      </div>
    </div>

    <div v-if="settings.ETAX_WELCOME_MESSAGE" class="mt-5" style="white-space: pre;" v-text="settings.ETAX_WELCOME_MESSAGE" />

    <v-divider class="my-8" />

    <div v-if="isShow">
      กรอก <b>เลขออเดอร์</b> เพื่อเริ่มขอใบกำกับภาษีอิเล็กทรอนิกส์ (E-Tax
      Invoice)
    </div>

    <!-- Section: Form -->
    <v-form
      ref="orderForm"
      class="mt-5"
      @submit.prevent="checkETaxInvoiceExist()"
    >
      <v-text-field
        v-if="isShow"
        autofocus
        clearable
        outlined
        hide-details="auto"
        placeholder="กรอกเลขที่ออเดอร์ของท่าน"
        :rules="[$rules.required]"
        :value="orderNumber"
        :error-messages="form_error.order_number"
        @change="$emit('update:order-number', $event)"
      />

      <v-alert
        v-if="non_field_error.show"
        color="error"
        class="mt-2 mb-0"
        outlined
        type="error"
      >
        {{ settings.ETAX_ALERT_OVERDUE_MESSAGE? settings.ETAX_ALERT_OVERDUE_MESSAGE : $t(non_field_error.text) }}
      </v-alert>

      <div v-if="isShow" class="d-flex mt-5">
        <v-btn block color="primary" type="submit" :loading="loading">
          ดำเนินการต่อ
        </v-btn>
      </div>
    </v-form>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { VForm } from '~/models'

export default Vue.extend({
  props: {
    orderNumber: {
      type: String,
      required: true
    },
    companyUuid: {
      type: String,
      required: true
    },
    taxDocumentUuid: {
      type: null as any as never,
      required: true
    },
    // isStaffUpdate: {
    //   type: Boolean,
    //   default: () => false
    // },
    isStaff: {
      type: Boolean,
      default: () => false
    },
    isUpdate: {
      type: Boolean,
      default: () => false
    },
    orderUuid: {
      type: String,
      required: true
    },
    settings: {
      type: Object,
      required: true
    }
  },

  data () {
    return {
      non_field_error: {
        show: false,
        text: ''
      },
      form_error: {},
      loading: false
    }
  },
  computed: {

    isShow ():boolean {
      if (this.non_field_error.text === 'ORDER_IS_NO_LONGER_ELIGIBLE_FOR_ETAX_REQUEST') {
        return false
      }
      return true
    }

  },
  // watch: {
  //   orderUuid: {
  //     async handler () {
  //       if (this.orderUuid) {
  //         await this.checkTaxInvByOrder()
  //       }
  //     }
  //   }
  // },

  mounted () {
    if (this.orderUuid) {
      this.checkTaxInvByOrder()
    }
  },

  methods: {
    async checkETaxInvoiceExist () {
      this.non_field_error.show = false
      const form = this.$refs.orderForm as never as VForm
      if (!form.validate()) {
        return
      }
      await this.checkTaxInvByOrder()
    },
    async checkTaxInvByOrder () {
      this.loading = true

      try {
        const response: any = await this.$axios.post('/api/etax/get-etax/', {
          company_uuid: this.companyUuid,
          order_number: this.orderNumber,
          order_uuid: this.orderUuid,
          is_staff: this.isStaff,
          is_update: this.isUpdate
          // is_staff_update: this.isStaff && this.isUpdate
        })

        if (response.data.tax_document_uuid && !this.$route.query.update) {
          // redirect to success page
          this.$router.push({
            path: '/public/success-etax',
            query: {
              tid: response.data.tax_document_uuid,
              cid: this.companyUuid,
              oid: this.orderNumber,
              type: 'exist_order'
            }
          })
        } else {
          // change to step 2
          this.$emit('customer-data', response.data.tax_customer_info)
          this.$emit('next-step')
        }
      } catch (error: any) {
        const err = error.response.data
        this.$emit('update:error')

        if (err.non_field_errors) {
          this.non_field_error.show = true
          this.non_field_error.text = err.non_field_errors[0].code
        }
        this.form_error = err
      }
      this.loading = false
    }
  }
})
</script>
