<template>
  <v-dialog :value="dialog" max-width="500" persistent>
    <v-card>
      <v-card-title class="text-h5">
        ตั้งค่า
        <v-spacer />
        <v-btn icon @click="$emit('update:dialog', false)">
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <v-divider class="mb-3" />

      <v-card-text>
        <div>
          <h3>เลือกสาขา</h3>
          <v-select
            :items="branches"
            :value="value.seller_branch"
            item-value="number"
            item-text="name"
            @change="update({ seller_branch: $event })"
          />
        </div>

        <div class="mt-5">
          <h3>
            เลือกประเภทราคาสินค้า
          </h3>
          <v-radio-group
            :value="vatType"
            @change="$emit('update:vat-type', $event)"
          >
            <v-radio
              class="mb-0"
              :label="options[0].title"
              :value="options[0].value"
            />
            <div class="text-help">
              ราคาขาย 100 บาท, ภาษีมูลค่าเพิ่ม 6.54 บาท, ลูกค้าชำระเงิน 100 บาท
            </div>
            <v-radio
              class="mt-2 mb-0"
              :label="options[1].title"
              :value="options[1].value"
            />
            <div class="text-help">
              ราคาขาย 100 บาท, ภาษีมูลค่าเพิ่ม 7 บาท, ลูกค้าชำระเงิน 107 บาท
            </div>
          </v-radio-group>
        </div>

        <div class="mt-5">
          <h3>อื่น ๆ</h3>
          <v-checkbox
            label="แสดงหมายเหตุ ยกเลิกใบกำกับภาษีอย่างย่อ"
            :input-value="value.show_remark_cancel_abb"
            hide-details
            @change="update({ show_remark_cancel_abb: $event })"
          />
          <div class="text-help">
            ระบบจะเพิ่มหมายเหตุท้ายใบเสร็จรับเงิน/ใบกำกับภาษี "เป็นการยกเลิกใบกำกับภาษีอย่างย่อเลขที่ [เลขที่เอกสาร] และออกใบกำกับภาษีเต็มรูปแบบแทน"
          </div>
        </div>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import { EtaxBranch, SettingState } from '~/store/settings'
export default Vue.extend({
  props: {
    dialog: {
      type: Boolean
    },
    vatType: {
      type: String,
      default: 'inclusive_vat'
    },
    value: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      options: [
        { title: 'รวมภาษี (Inc VAT)', value: 'inclusive_vat' },
        { title: 'แยกภาษี (Exc VAT)', value: 'exclusive_vat' }
      ]
    }
  },
  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    },
    branches (): EtaxBranch[] {
      const branches = this.settings.company.ETAX_BRANCHES.map(x => ({
        ...x,
        name: `${x.number} - ${x.name}`
      }))

      if (branches.length === 0) {
        branches.push({
          number: '00000',
          name: 'สำนักงานใหญ่',
          address: ''
        })
      }

      return branches
    }
  },
  methods: {
    update (data: any) {
      this.$emit('input', { ...this.value, ...data })
    }
  }

})
</script>

<style>

</style>
