<template>
  <v-form ref="form" @submit.prevent>
    <!-- Search Tax ID -->
    <span class="d-flex align-center">
      <v-text-field
        label="เลขที่ผู้เสียภาษี (13 หลัก) *"
        outlined
        clearable
        filled
        dense
        :rules="[$rules.required,$rules.numberOnlyWithLength(13)]"
        :loading="is_loading"
        :value="formData.tax_id"
        height="40"
        @input="update({tax_id: $str($event)})"
        @keyup.enter="handleTaxIdSearch()"
      />
      <v-btn
        color="primary"
        :loading="is_loading"
        :disabled="!formData.tax_id || formData.tax_id.length !== 13"
        class="ml-2 mb-7"
        height="40"
        @click="handleTaxIdSearch()"
      >
        <v-icon>mdi-magnify</v-icon>
      </v-btn>
    </span>

    <!-- Company Name & Branch -->
    <v-row>
      <v-col cols="7">
        <v-text-field
          filled
          label="ชื่อบริษัท *"
          :rules="[$rules.required, $rules.is_not_blank, $rules.maxlength(150)]"
          dense
          outlined
          :value="formData.buyer_name"
          :loading="is_loading"
          @input="update({buyer_name: $str($event)})"
        />
      </v-col>
      <v-col cols="5">
        <v-combobox
          item-text="branchCode"
          item-value="branchCode"
          label="เลือกสาขา *"
          outlined
          filled
          dense
          clearable
          :rules="[$rules.required, $rules.numberOnlyWithLength(5)]"
          :items="branch_list"
          :loading="is_loading"
          :value="formData.branch_code"
          :success="tax_id_found && branch_list.length > 0"
          :success-messages="tax_id_found && branch_list.length > 0 ? `พบข้อมูล ${branch_list.length} สาขา` : ''"
          @change="handleBranchSelection($event)"
        >
          <template #item="{ item }">
            <v-list-item-content>
              <v-list-item-title>
                {{ item.branch_name || '' }} ({{ item.branchCode }})
              </v-list-item-title>
              <v-list-item-subtitle>
                รหัสไปรษณีย์: {{ item.zipCode }}
              </v-list-item-subtitle>
            </v-list-item-content>
          </template>
        </v-combobox>
      </v-col>
    </v-row>

    <!-- Address & PostCode -->
    <v-textarea
      label="ที่อยู่สำหรับออกใบกำกับภาษี *"
      outlined
      filled
      dense
      rows="4"
      :value="formData.address"
      :rules="[$rules.required, $rules.is_not_blank, $rules.maxlength(198)]"
      @change="update({address: $str($event)})"
    />
    <v-text-field
      label="รหัสไปรษณีย์ *"
      outlined
      clearable
      filled
      dense
      :rules="[$rules.required, $rules.numberOnlyWithLength(5)]"
      :loading="is_loading"
      :value="formData.post_code"
      type="number"
      height="40"
      @input="update({post_code: $str($event)})"
    />
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'

import { CompanyBranch, VForm } from '~/models'

export default Vue.extend({

  props: {
    formData: {
      type: Object,
      required: true
    }
  },

  data () {
    return {
      branch_list: [] as CompanyBranch[],
      is_loading: false,
      tax_id_not_found: false,
      tax_id_found: false

    }
  },

  methods: {
    update (data:any) {
      this.$emit('update:form-data', { ...this.formData, ...data })
    },

    validate ():boolean {
      const form = this.$refs.form as never as VForm
      return form.validate()
    },

    resetValidation () {
      const form = this.$refs.form as never as VForm
      form.resetValidation()
    },

    async handleTaxIdSearch () {
      if (this.$rules.numberOnlyWithLength(13)(this.formData.tax_id) !== true) {
        return
      }

      // Reset Form
      this.branch_list = []
      this.update({
        buyer_name: '',
        branch_name: '',
        branch_code: '',
        address: ''
      })
      this.resetValidation()
      this.tax_id_not_found = false
      this.tax_id_found = false

      // Send API, Search company data from tax_id
      this.is_loading = true
      try {
        const response = await this.$axios.post('/api/etax/get-company-data/', {
          tax_id: this.formData.tax_id
        })

        this.tax_id_found = true
        this.branch_list = response.data.results.map((branch: CompanyBranch) => ({
          ...branch,
          branchDisplay: branch.branch || branch.branchCode
        }))

        this.$snackbar('success', `พบข้อมูลจำนวน ${this.branch_list.length} สาขา`)

        // Set company buyer_name from first result
        if (this.branch_list.length > 0) {
          const branch = this.branch_list[0]
          this.update({
            buyer_name: branch.name,
            branch_name: branch.branchCode,
            branch_code: branch.branchCode,
            address: branch.addressLocal,
            post_code: branch.zipCode
          })
        }
      } catch (error) {
        console.error('Error fetching company data:', error)
        this.$snackbar('info', 'พบ 0 รายการ')
      }

      this.is_loading = false
    },

    handleBranchSelection (branch_name: string | any) {
      if (!branch_name) {
        return
      }
      let branchCode = ''

      if (this.formData.branch_code === branch_name.branchCode) {
        return
      }
      if (!branch_name) {
        this.update({
          branch_name: '',
          branch_code: ''
        })
        return
      }

      if (typeof branch_name === 'string') {
        branchCode = branch_name
      } else {
        branchCode = branch_name.branchCode
      }

      const selected_branch = this.branch_list.find(b => b.branchCode === branchCode)
      if (selected_branch) {
        this.update({
          branch_name: selected_branch.branchCode,
          branch_code: selected_branch.branchCode,
          address: selected_branch.addressLocal
        })
      } else {
        this.update({
          branch_name: branchCode,
          branch_code: branchCode
        })
      }
    }
  }
})
</script>
