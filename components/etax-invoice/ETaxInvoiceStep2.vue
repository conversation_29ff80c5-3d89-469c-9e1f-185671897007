<template>
  <v-form
    @submit.prevent="isUpdate ? updateETaxInvoice() : createETaxInvoice()"
  >
    <div class="mb-6">
      <div class="text-h6">
        แบบฟอร์มขอใบกำกับภาษีอิเล็กทรอนิกส์
      </div>
      <p>กรุณาเลือกประเภทผู้ขอใบกำกับภาษีและกรอกข้อมูลให้ครบถ้วน</p>
    </div>

    <v-radio-group v-model="form_type" hide-details row class="mb-4 ml-2">
      <v-radio label="บุคคลธรรมดา" value="personal_form" />
      <v-radio label="นิติบุคคล" value="company_form" />
    </v-radio-group>

    <!-- company Form -->
    <template v-if="form_type === 'company_form'">
      <e-tax-company-form
        ref="company_form_validate"
        :form-data="company_form"
        @update:form-data="setupCompanyForm($event)"
      />
    </template>

    <!-- Personal Form -->
    <template v-if="form_type === 'personal_form'">
      <e-tax-personal-form
        ref="personal_form_validate"
        :form-data="personal_form"
        @update:form-data="setupPersonalForm($event)"
      />
    </template>

    <!-- Common Fields -->
    <e-tax-contact-form
      ref="contact_form_validate"
      :form-data.sync="contact_form"
    />

    <v-alert
      v-if="non_field_error.show"
      color="error"
      class="mt-2 mb-0"
      outlined
      type="error"
    >
      {{ non_field_error.text }}
    </v-alert>

    <v-row v-if="!isUpdate" class="mt-2">
      <v-col cols="6">
        <v-btn
          outlined
          color="error"
          :disabled="loading"
          block
          @click="clearForm()"
        >
          ล้างฟอร์ม
        </v-btn>
      </v-col>
      <v-col cols="6">
        <v-btn color="primary" block type="submit" :loading="loading">
          ยืนยันข้อมูล
        </v-btn>
      </v-col>
      <v-col v-if="settings.ETAX_SHOW_CONSENT_CHECKBOX" cols="12">
        <span class="d-flex mt-2">
          <v-checkbox
            v-model="is_consent_marketing"
            color="grey"
            class="mt-0 pt-0 ml-2"
          />
          <p class="consent-text">
            ข้าพเจ้ายินยอมให้บริษัทเก็บรวบรวมและใช้ข้อมูลส่วนบุคคลเพื่อการวิเคราะห์ประมวลผล
            การพัฒนาผลิตภัณฑ์หรือบริการ การดำเนินกิจกรรมทางการตลาด
            ข้อเสนอพิเศษที่ท่านอาจสนใจ เกี่ยวกับผลิตภัณฑ์และบริการของบริษัท
            หรือบริษัทในกลุ่ม
            เจ้าของข้อมูลส่วนบุคคลสามารถถอนความยินยอมได้ทุกเมื่อ
            แต่จะต้องไม่กระทบซึ่งสิทธิของบริษัทตามฐานทางกฎหมายอื่นภายใต้พระราชบัญญัติคุ้มครองข้อมูลส่วนบุคคล
            พ.ศ. 2562
          </p>
        </span>
      </v-col>
    </v-row>

    <v-row v-if="isUpdate" class="mt-6">
      <v-col cols="12">
        <v-btn color="primary" block type="submit" :loading="loading">
          อัปเดตข้อมูล
        </v-btn>
      </v-col>
    </v-row>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import ETaxContactForm from './ETaxContactForm.vue'
import ETaxPersonalForm from './ETaxPersonalForm.vue'

import { CustomerTaxData, VForm } from '~/models'

export default Vue.extend({
  components: {
    ETaxContactForm,
    ETaxPersonalForm
  },
  props: {
    companyUuid: {
      type: String,
      required: true
    },
    taxDocumentUuid: {
      type: String,
      required: false,
      default: ''
    },
    orderNumber: {
      type: String,
      required: false,
      default: ''
    },
    orderUuid: {
      type: String,
      required: false,
      default: ''
    },
    isStaff: {
      type: Boolean,
      required: true,
      default: false
    },
    isUpdate: {
      type: Boolean,
      required: true,
      default: false
    },
    userUuid: {
      type: String,
      required: false,
      default: ''
    },
    settings: {
      type: Object,
      required: true
    }
  },

  data () {
    return {
      ready: false,
      contact_form: {
        email: '',
        email2: '',
        phone_number: ''
      },
      form_type: 'personal_form' as 'company_form' | 'personal_form',
      personal_form: {
        buyer_name: '',
        tax_id: '',
        address: '',
        post_code: ''
      },
      company_form: {
        buyer_name: '',
        tax_id: '',
        branch_name: '',
        branch_code: '',
        address: '',
        post_code: ''
      },
      is_consent_marketing: false,
      company_form_valid: false,
      personal_form_valid: false,
      contact_form_valid: false,
      loading: false,
      company_uuid: '',
      tax_doc_operate_error: false,
      non_field_error: {
        show: false,
        text: ''
      }
    }
  },
  watch: {
    settings: {
      handler () {
        this.is_consent_marketing = this.settings.ETAX_SHOW_CONSENT_CHECKBOX
      },
      deep: true
    }
  },
  async mounted () {
    await this.checkETax()

    if (this.isUpdate) {
      // buyer or staff update mode
      this.tryShowTaxDocumentData()
    } else if (this.isStaff) {
      // staff create mode
      await this.tryShowPlatformData()
    } else {
      // buyer create mode
      const is_filled = await this.tryShowPlatformData()
      if (is_filled) { return }
      this.loadBuyerInfoFromLocalStorage(this.form_type)
    }
  },

  methods: {

    async checkETax () {
      try {
        await this.$axios.post('/api/etax/get-etax/', {
          company_uuid: this.companyUuid,
          order_number: this.orderNumber,
          order_uuid: this.orderUuid,
          is_staff: this.isStaff,
          is_update: this.isUpdate
        })
        this.ready = true
      } catch (error: any) {
        this.$emit('error', error.response.data)
        this.ready = false
      }
    },

    // LocalStorage
    loadBuyerInfoFromLocalStorage (form_type: string) {
      if (this.isStaff) {
        return
      }
      const is_personal = form_type === 'personal_form'

      const storage_key = 'recently_buyer_info'
      const buyer_infos = JSON.parse(localStorage.getItem(storage_key) || '[]')
      const buyer = buyer_infos.find(
        (buyer: any) => buyer.is_personal === is_personal
      )

      if (buyer) {
        this.contact_form.email = buyer.email
        this.contact_form.email2 = buyer.email2
        this.contact_form.phone_number = buyer.phone_number
        if (is_personal) {
          this.personal_form = buyer
        } else {
          this.company_form = buyer
        }
      }
    },

    saveBuyerInfoToLocalStorage (buyer_info: any) {
      const storage_key = 'recently_buyer_info'
      const buyer_infos = JSON.parse(localStorage.getItem(storage_key) || '[]')
      const index = buyer_infos.findIndex(
        (buyer: any) => buyer.is_personal === buyer_info.is_personal
      )
      if (index === -1) {
        // add new
        buyer_infos.push(buyer_info)
      } else {
        // update exists
        buyer_infos[index] = buyer_info
      }
      localStorage.setItem(storage_key, JSON.stringify(buyer_infos))
    },

    // Validation
    validate (): boolean {
      let form1: VForm
      let form2: VForm

      if (this.form_type === 'company_form') {
        form1 = this.$refs.company_form_validate as never as VForm
        form2 = this.$refs.contact_form_validate as never as VForm
      } else if (this.form_type === 'personal_form') {
        form1 = this.$refs.personal_form_validate as never as VForm
        form2 = this.$refs.contact_form_validate as never as VForm
      } else {
        throw new Error('Invalid form type')
      }

      const form1_valid = form1.validate()
      const form2_valid = form2.validate()
      return form1_valid && form2_valid
    },

    prepareBuyerInfo () {
      let payload: any = {}

      if (this.form_type === 'company_form') {
        payload = {
          ...this.company_form,
          ...this.contact_form,
          is_personal: false
        }
      } else {
        payload = {
          ...this.personal_form,
          ...this.contact_form,
          is_personal: true
        }
      }
      payload = { ...payload, is_consent_marketing: this.is_consent_marketing }
      this.saveBuyerInfoToLocalStorage(payload)

      payload = {
        ...payload,
        company_uuid: this.companyUuid,
        tax_document_uuid: this.taxDocumentUuid,
        order_number: this.orderNumber,
        order_uuid: this.orderUuid,
        user_uuid: this.userUuid,
        is_staff_update: this.isStaff && this.isUpdate
      }

      if (this.contact_form.email2) {
        payload.email = payload.email + ';' + payload.email2
      }

      return payload
    },

    async createETaxInvoice () {
      if (!this.validate()) {
        return
      }

      const payload = this.prepareBuyerInfo()

      this.loading = true
      try {
        const res = await this.$axios.post('/api/etax/create-document/', payload)
        this.goToSuccessPage(res.data.tax_document_uuid, 'create')
      } catch (error: any) {
        const err = error.response.data

        if (err.non_field_errors) {
          this.non_field_error.show = true
          this.non_field_error.text = err.non_field_errors[0].code
        }
      }
      this.loading = false
    },

    async tryShowTaxDocumentData () {
      try {
        const response = await this.$axios.post('/api/etax/get-etax-detail/', {
          tax_document_uuid: this.taxDocumentUuid
        })
        const buyer = response.data.buyer

        if (buyer.email && buyer.email.includes(';')) {
          const [email, email2] = buyer.email.split(';')
          this.contact_form.email = email
          this.contact_form.email2 = email2
        } else {
          this.contact_form.email = buyer.email
        }

        this.contact_form.phone_number = buyer.phone_number
        this.is_consent_marketing = buyer.is_consent_marketing
        if (buyer.branch_code) {
          this.company_form.address = buyer.address
          this.company_form.branch_code = buyer.branch_code
          this.company_form.branch_name = buyer.branch_name
          this.company_form.tax_id = buyer.tax_id
          this.company_form.buyer_name = buyer.buyer_name
          this.company_form.post_code = buyer.post_code
          this.form_type = 'company_form'
        } else {
          this.personal_form.address = buyer.address
          this.personal_form.buyer_name = buyer.buyer_name
          this.personal_form.tax_id = buyer.tax_id
          this.personal_form.post_code = buyer.post_code

          this.form_type = 'personal_form'
        }
      } catch (error) {
        console.error(error)
        this.$snackbar(
          'error',
          'ไม่สามารถค้นหาเอกสารได้ กรุณาติดต่อเจ้าหน้าที่'
        )
      }
    },

    async updateETaxInvoice () {
      const payload = this.prepareBuyerInfo()

      this.loading = true
      try {
        const res = await this.$axios.post('/api/etax/update/', payload)
        this.goToSuccessPage(res.data.tax_document_uuid, 'update')
      } catch (error: any) {
        const err = error.response.data
        if (err.non_field_errors) {
          this.non_field_error.show = true
          this.non_field_error.text = err.non_field_errors[0].code
        }
      }
      this.loading = false
    },

    async tryShowPlatformData () {
      try {
        const response = await this.$axios.post('/api/etax/get-etax/', {
          company_uuid: this.companyUuid,
          order_uuid: this.orderUuid,
          is_staff: this.isStaff,
          is_update: this.isUpdate
        })
        const tax_data = response.data.tax_customer_info as CustomerTaxData
        if (!tax_data.customer_id_number) {
          return false
        }
        if (tax_data.customer_branch_no) {
          this.form_type = 'company_form'
        } else {
          this.form_type = 'personal_form'
        }

        // setup company form
        this.company_form.buyer_name = tax_data.customer_name
        this.company_form.tax_id = tax_data.customer_id_number ?? ''
        this.company_form.address = tax_data.customer_address
        this.company_form.branch_code = tax_data.customer_branch_no ?? ''
        this.company_form.branch_name = tax_data.customer_branch_name ?? ''
        this.company_form.post_code = tax_data.post_code ?? ''

        // setup personal form
        this.personal_form.buyer_name = tax_data.customer_name
        this.personal_form.tax_id = tax_data.customer_id_number ?? ''
        this.personal_form.address = tax_data.customer_address
        this.personal_form.post_code = tax_data.post_code ?? ''

        // setup contact form
        this.contact_form.email = tax_data.customer_email ?? ''
        if (this.contact_form.email.includes(';')) {
          const [email, email2] = this.contact_form.email.split(';')
          this.contact_form.email = email
          this.contact_form.email2 = email2
        }
        this.contact_form.phone_number = tax_data.customer_phone
        return true
      } catch (error: any) {
        console.error(error)
      }
    },

    setupCompanyForm (event: any) {
      this.company_form = event
    },

    setupPersonalForm (event: any) {
      this.personal_form = event
    },

    clearForm () {
      const is_clear = confirm('คุณต้องการจะล้างฟอร์มใช่หรือไม่')
      if (!is_clear) {
        return
      }
      if (this.form_type === 'personal_form') {
        this.personal_form.address = ''
        this.personal_form.buyer_name = ''
        this.personal_form.tax_id = ''
        this.personal_form.post_code = ''
      } else {
        this.company_form.address = ''
        this.company_form.branch_code = ''
        this.company_form.branch_name = ''
        this.company_form.tax_id = ''
        this.company_form.buyer_name = ''
        this.company_form.post_code = ''
      }
      this.contact_form.email = ''
      this.contact_form.email2 = ''
      this.contact_form.phone_number = ''
    },

    goToSuccessPage (tax_uuid: string, action: any) {
      this.boardcastSuccessEtax()
      this.$router.push({
        path: '/public/success-etax',
        query: {
          tid: tax_uuid,
          oid: this.orderUuid,
          cid: this.companyUuid,
          type: action,
          ct: this.settings.ETAX_SHOW_DOBYBOT_CONTACT
        }
      })
    },

    boardcastSuccessEtax () {
      const channel = new BroadcastChannel('etax-success')
      channel.postMessage({ command: 'success', pick_order_uuid: this.orderUuid })
    }
  }
})
</script>

<style scoped>
.consent-text {
  color: rgb(184, 184, 184);
  font-size: 10px;
}
</style>
