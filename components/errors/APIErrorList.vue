<template>
  <div>
    <v-alert v-if="Object.keys(errors).length > 0" type="error" outlined dismissible>
      กรุณาแก้ไขข้อผิดพลาดด้านล่าง

      <ul>
        <li v-for="(error, key) in errors" :key="key">
          {{ error }}
        </li>
      </ul>
    </v-alert>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    errors: {
      type: Object,
      default: () => ({})
    }
  }
})
</script>
