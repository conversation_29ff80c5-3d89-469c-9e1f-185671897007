<template>
  <div class="error-list">
    มีข้อผิดพลาดในไฟล์อัพโหลด
    <ul>
      <template v-for="err, i in errors">
        <li v-if="Object.keys(err).length > 0" :key="i">
          <div>
            แถวที่ {{ i + 2 }}
            <div v-for="key in Object.keys(err)" :key="key">
              {{ key }}: {{ err[key].join(',') }}
            </div>
          </div>
        </li>
      </template>
    </ul>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    errors: {
      type: Array,
      default: () => []
    }
  }
})
</script>

<style scoped>
div.error-list {
  width: 100%;
  border: 1px red solid;
  padding: 5px;
  color: red;
  border-radius: 5px;

  max-height: 400px;
  overflow-y: scroll;
}
li {
  color: red
}
</style>
