<template>
  <v-list-item
    :to="item.to"
    :href="item.href"
    router
    exact
    :two-line="!!item.sub_title"
  >
    <v-list-item-action>
      <v-icon>{{ item.icon }}</v-icon>
    </v-list-item-action>
    <v-list-item-content>
      <v-list-item-title :class="{'primary--text': item.new}" :data-permission="item.permission">
        {{ item.title }}
        <v-icon v-if="item.new" color="primary">
          mdi-new-box
        </v-icon>
      </v-list-item-title>
      <v-list-item-subtitle v-if="item.sub_title">
        {{ item.sub_title }}
      </v-list-item-subtitle>
    </v-list-item-content>
    <v-list-item-action v-if="item.iconRight">
      <v-icon>{{ item.iconRight }}</v-icon>
    </v-list-item-action>
  </v-list-item>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    item: {
      type: Object,
      required: true
    }
  }
})
</script>
