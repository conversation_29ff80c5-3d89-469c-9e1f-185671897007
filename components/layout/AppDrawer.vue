<template>
  <v-navigation-drawer
    clipped
    fixed
    app
    :value="value"
    @input="$emit('input', $event)"
  >
    <v-list>
      <template v-for="item in items">
        <!-- List Item -->
        <app-drawer-list-item
          v-if="isShowItem(item)"
          :key="item.title"
          :item="item"
        />

        <!-- List Group -->
        <app-drawer-list-group
          v-if="isShowGroup(item)"
          :key="item.title"
          :title="item.title"
          :prepend-icon="item.icon"
          :items="item.children"
        />
      </template>
    </v-list>
    <div style="position: absolute; bottom: 0; right: 6px;" class="blue-grey--text text--lighten-3 text-right">
      <span style="cursor: pointer; user-select: none;" @click="onVersionClick">{{ $version() }}</span>
    </div>

    <!-- Secret Admin Password Dialog -->
    <v-dialog v-model="showPasswordDialog" persistent max-width="300">
      <v-card>
        <v-card-text class="text-center py-8">
          <v-text-field
            v-model="adminPassword"
            type="password"
            outlined
            dense
            autofocus
            hide-details
            class="mx-auto"
            style="max-width: 200px;"
            @keyup.enter="checkAdminPassword"
            @keyup.esc="cancelPasswordDialog"
          />
        </v-card-text>
        <v-card-actions class="justify-center pb-4">
          <v-btn text small @click="cancelPasswordDialog">
            Cancel
          </v-btn>
          <v-btn color="primary" small @click="checkAdminPassword">
            OK
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-navigation-drawer>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapGetters } from 'vuex'
import AppDrawerListGroup from './AppDrawerListGroup.vue'
import AppDrawerListItem from './AppDrawerListItem.vue'
import { FEATURE_FLAGS } from '~/models'
import { SettingState } from '~/store/settings'

export interface ListItem {
  icon?: string
  iconRight?: string
  title: string
  sub_title?: string
  to?: string
  href?: string
  permission: string
  feature_flag: string
}

export interface ListGroup {
  icon: string
  title: string
  children: ListItem[]
  feature_flag?: string
}

export const items: (ListItem | ListGroup)[] = [
  {
    icon: 'mdi-video-outline',
    title: 'Record',
    children: [
      {
        iconRight: 'mdi-video-outline',
        title: 'Record',
        href: '/$/record',
        permission: 'add_videorecordlog',
        feature_flag: FEATURE_FLAGS.RECORD
      },
      {
        iconRight: 'mdi-video-outline',
        title: 'Record (Return)',
        href: '/$/record?mode=return',
        permission: 'add_videorecordlog_return',
        feature_flag: FEATURE_FLAGS.RECORD
      },
      {
        iconRight: 'mdi-video-outline',
        title: 'Record (Transfer)',
        href: '/$/record?mode=transfer',
        permission: 'add_videorecordlog_transfer',
        feature_flag: FEATURE_FLAGS.RECORD
      },
      {
        iconRight: 'mdi-camera',
        title: 'Image Capture',
        to: '/image-capture',
        permission: 'add_imagecapturelog',
        feature_flag: FEATURE_FLAGS.RECORD
      }
    ]
  },
  // {
  //   icon: 'mdi-file-document',
  //   title: 'Packing Status',
  //   to: '/packing-status',
  //   permission: 'view_pickorder'
  // },
  {
    icon: 'mdi-file-document',
    title: 'Order Center',
    children: [
      {
        iconRight: 'mdi-file-document',
        title: 'Order Center',
        href: '/order-center',
        permission: 'view_pickorder',
        feature_flag: FEATURE_FLAGS.ORDER_CENTER
      },
      {
        iconRight: 'mdi-database-import',
        title: 'Order Import',
        to: '/order-import',
        permission: 'view_orderimportrequest',
        feature_flag: FEATURE_FLAGS.ORDER_CENTER
      },
      {
        iconRight: 'mdi-store',
        title: 'Easy E-Tax',
        to: '/easy-etax/create',
        permission: 'add_taxdocument',
        feature_flag: FEATURE_FLAGS.ETAX
      }
    ]
  },
  {
    icon: 'mdi-package-variant-closed',
    title: 'Bulk Pick',
    to: '/bulk-pick',
    permission: 'view_pickorder',
    feature_flag: FEATURE_FLAGS.ORDER_CENTER
  },
  {
    icon: 'mdi-file-document-outline',
    title: 'Airway Bill',
    to: '/airway-bill',
    permission: 'view_airwaybill',
    feature_flag: FEATURE_FLAGS.PRINTING
  },
  {
    icon: 'mdi-message-alert',
    title: 'Fix Case',
    to: '/fixcase',
    permission: 'view_fixcase',
    feature_flag: FEATURE_FLAGS.FIX_CASE
  },
  {
    icon: 'mdi-note',
    title: 'Fast Note',
    to: '/fastnote',
    permission: 'view_fastnote',
    feature_flag: FEATURE_FLAGS.ALL
  },
  {
    icon: 'mdi-send',
    title: 'SMS',
    permission: 'add_smscampaign',
    feature_flag: FEATURE_FLAGS.SMS_CHAT,
    children: [
      {
        title: 'Send with Excel',
        to: '/sms/excel',
        permission: 'add_smscampaign',
        feature_flag: FEATURE_FLAGS.SMS_CHAT
      },
      {
        title: 'Send Quick SMS',
        to: '/sms/quick',
        permission: 'add_smscampaign',
        feature_flag: FEATURE_FLAGS.SMS_CHAT
      },
      {
        title: 'Send via API',
        to: '/sms/api',
        permission: 'add_smscampaign',
        feature_flag: FEATURE_FLAGS.SMS_CHAT
      }
    ]
  },
  {
    icon: 'mdi-finance',
    title: 'Dashboards',
    feature_flag: FEATURE_FLAGS.DASHBOARD,
    children: [
      {
        title: 'Order',
        to: '/report/dashboards/order',
        permission: 'view_order_dashboard',
        feature_flag: FEATURE_FLAGS.DASHBOARD
      },
      {
        title: 'Product',
        to: '/report/dashboards/product',
        permission: 'view_best_selling_products_dashboard',
        feature_flag: FEATURE_FLAGS.DASHBOARD
      },
      {
        title: 'Packing Performance',
        to: '/report/dashboards/performance',
        permission: 'view_performance_dashboard',
        feature_flag: FEATURE_FLAGS.DASHBOARD
      },
      {
        title: 'Order Fixcase',
        to: '/report/dashboards/order-fixcase',
        permission: 'view_fix_case_dashboard',
        feature_flag: FEATURE_FLAGS.DASHBOARD
      }
    ]
  },
  {
    icon: 'mdi-chart-bar',
    title: 'Reports',
    children: [
      {
        title: 'Video Record Report',
        href: '/report/v2/video-record',
        permission: 'view_video_record_report',
        feature_flag: FEATURE_FLAGS.ALL
      },
      {
        title: 'Video Record Report ',
        sub_title: '(No Video Link)',
        href: '/report/v2/no-link-video-record',
        permission: 'view_video_record_report_no_video_link',
        feature_flag: FEATURE_FLAGS.ALL
      },
      {
        title: 'Image Capture Report',
        href: '/report/v2/image-capture-log',
        permission: 'view_image_capture_log_report',
        feature_flag: FEATURE_FLAGS.ALL
      },
      {
        title: 'Diff Report',
        href: '/report/v2/video-record-diff',
        permission: 'view_video_record_diff_report',
        feature_flag: FEATURE_FLAGS.ALL
      },
      {
        title: 'Pick Item By Order Report',
        href: '/report/v2/pick-item',
        permission: 'view_pick_item_report',
        feature_flag: FEATURE_FLAGS.ALL
      },
      {
        title: 'Pick Item Summary Report',
        href: '/report/v2/pick-item-summary',
        permission: 'view_pick_item_daily_summary_report',
        feature_flag: FEATURE_FLAGS.ALL
      },
      {
        title: 'Pick Item Return Summary Report',
        href: '/report/v2/pick-item-return-summary',
        permission: 'view_pick_item_daily_summary_report',
        feature_flag: FEATURE_FLAGS.ALL
      },
      {
        title: 'SMS Report',
        href: '/report/v2/sms',
        permission: 'view_sms_report',
        feature_flag: FEATURE_FLAGS.ALL
      },
      {
        title: 'Fix Case Report',
        href: '/report/v2/fixcase',
        permission: 'view_fixcase_report',
        feature_flag: FEATURE_FLAGS.ALL
      },
      {
        title: 'Performance Report',
        href: '/report/v2/performance',
        permission: 'view_performance_report',
        feature_flag: FEATURE_FLAGS.ALL
      },
      {
        title: 'Performance Analysis Report',
        href: '/report/v2/performance-analysis',
        permission: 'view_performance_report',
        feature_flag: FEATURE_FLAGS.ALL
      },
      {
        title: 'Billing',
        href: '/report/v2/record-transaction',
        permission: 'view_billing_report',
        feature_flag: FEATURE_FLAGS.ALL
      }

    ]
  },
  {
    icon: 'mdi-account-group',
    title: 'User Management',
    to: '/settings/users',
    permission: 'view_user',
    feature_flag: FEATURE_FLAGS.ALL
  },
  {
    icon: 'mdi-package',
    title: 'Product Management',
    feature_flag: FEATURE_FLAGS.PRODUCT_MANAGEMENT,
    children: [
      {
        title: 'Product SKU / Barcode',
        to: '/settings/product/barcode',
        permission: 'view_product',
        feature_flag: FEATURE_FLAGS.PRODUCT_MANAGEMENT
      },
      {
        title: 'Product Set',
        to: '/settings/product/product-set',
        permission: 'view_productset',
        feature_flag: FEATURE_FLAGS.PRODUCT_MANAGEMENT
      },
      {
        title: 'Product Serial No.',
        to: '/settings/product/serial-no',
        permission: 'view_productserialno',
        feature_flag: FEATURE_FLAGS.PRODUCT_MANAGEMENT
      },
      {
        title: 'Print Barcode',
        to: '/settings/product/print-barcode',
        permission: 'view_productset',
        feature_flag: FEATURE_FLAGS.PRODUCT_MANAGEMENT
      }
    ]
  },
  {
    icon: 'mdi-cog',
    title: 'Settings',
    children: [
      {
        title: 'General',
        to: '/settings',
        permission: 'change_settingvalue',
        feature_flag: FEATURE_FLAGS.ALL
      },
      {
        title: 'Marketplace',
        to: '/settings/marketplace',
        permission: 'change_settingvalue',
        feature_flag: FEATURE_FLAGS.ORDER_CENTER
      },
      {
        title: 'Chat / SMS',
        to: '/settings/sms',
        permission: 'change_settingvalue',
        feature_flag: FEATURE_FLAGS.SMS_CHAT
      },
      {
        title: 'Receipt',
        to: '/settings/receipt',
        permission: 'change_settingvalue',
        feature_flag: FEATURE_FLAGS.ALL
      },
      {
        title: 'Printing',
        to: '/settings/printing',
        permission: 'change_settingvalue',
        feature_flag: FEATURE_FLAGS.PRINTING
      },
      {
        title: 'Shipping',
        to: '/settings/shipping',
        permission: 'change_settingvalue',
        feature_flag: FEATURE_FLAGS.SHIPPING
      },
      {
        title: 'ETax',
        to: '/settings/etax',
        permission: 'change_settingvalue',
        feature_flag: FEATURE_FLAGS.ETAX
      }
    ]
  },
  {
    icon: 'mdi-database-import',
    title: 'ETL Operations',
    to: '/etl',
    permission: 'view_etljob',
    feature_flag: FEATURE_FLAGS.DASHBOARD
  },
  {
    icon: 'mdi-api',
    title: 'Dobybot API',
    to: '/settings/apidoc',
    permission: 'change_settingvalue',
    feature_flag: FEATURE_FLAGS.ALL
  }
]

export default Vue.extend({
  components: { AppDrawerListItem, AppDrawerListGroup },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      items,
      // Secret admin feature
      versionClickCount: 0,
      versionClickTimer: null as NodeJS.Timeout | null,
      showPasswordDialog: false,
      adminPassword: ''
    }
  },
  computed: {
    ...mapGetters(['isAuthenticated', 'loggedInUser']),

    settings (): SettingState {
      return this.$store.state.settings
    }
  },

  methods: {
    isShowItem (item: ListItem) {
      if (this.settings.company.CUSTOM_BRAND) {
        if (item.title === 'Dobybot API') {
          return false
        }
      }
      return (item.to || item.href) && (this.$hasPerms(item.permission) || !item.permission) && (this.$hasCompanyFeatureFlag(item.feature_flag))
    },

    isShowGroup (group: ListGroup) {
      // @ts-ignore
      return group.children && group.children.some(item => this.$hasPerms(item.permission)) && this.$hasCompanyFeatureFlag(group.feature_flag)
    },

    onVersionClick () {
      this.versionClickCount++

      if (this.versionClickTimer) {
        clearTimeout(this.versionClickTimer)
      }
      this.versionClickTimer = setTimeout(() => {
        this.versionClickCount = 0
      }, 2000)

      if (this.versionClickCount >= 5) {
        this.showPasswordDialog = true
        this.versionClickCount = 0
        if (this.versionClickTimer) {
          clearTimeout(this.versionClickTimer)
        }
      }
    },

    async checkAdminPassword () {
      const result = await this.$store.dispatch('devmode/activateDevMode', {
        password: this.adminPassword
      })

      if (result.success) {
        this.$snackbar('success', `Developer mode activated! Welcome, ${result.adminName}`)
        this.showPasswordDialog = false
        this.adminPassword = ''
      } else {
        this.$snackbar('error', 'Invalid password')
        this.adminPassword = ''
      }
    },

    cancelPasswordDialog () {
      this.showPasswordDialog = false
      this.adminPassword = ''
      this.versionClickCount = 0
    }
  }
})
</script>
