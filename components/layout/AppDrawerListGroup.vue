<template>
  <v-list-group
    :prepend-icon="prependIcon"
  >
    <template #activator>
      <v-list-item-title>{{ title }}</v-list-item-title>
    </template>

    <template v-for="item in items">
      <!-- List Item -->
      <app-drawer-list-item
        v-if="isShowItem(item)"
        :key="item.title"
        :item="item"
      />
    </template>
    <!-- <app-drawer-list-item v-for="item in items" :key="item.to" :item="item" /> -->
  </v-list-group>
</template>

<script lang="ts">
import Vue from 'vue'
import { ListItem } from './AppDrawer.vue'

import AppDrawerListItem from './AppDrawerListItem.vue'
export default Vue.extend({
  components: {
    AppDrawerListItem
  },
  props: {
    title: {
      type: String,
      required: true
    },
    prependIcon: {
      type: String,
      required: true
    },
    items: {
      type: Array,
      required: true
    }
  },
  methods: {
    isShowItem (item: ListItem) {
      return (item.to || item.href) && (this.$hasPerms(item.permission) || !item.permission) && this.$hasCompanyFeatureFlag(item.feature_flag)
    }
  }
})
</script>
