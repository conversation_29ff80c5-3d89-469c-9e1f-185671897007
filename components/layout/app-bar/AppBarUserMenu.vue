<template>
  <v-menu
    bottom
    left
    offset-y
    close-on-click
  >
    <template #activator="{ on, attrs }">
      <v-btn text v-bind="attrs" data-test="company-menu-button" v-on="on">
        <v-icon left>
          mdi-account
        </v-icon>
        {{ getFullName($auth.user) }}
      </v-btn>
    </template>

    <v-list>
      <!-- Super User Only -->
      <v-list-item v-if="$auth.user.is_superuser" data-test-id="v-list-item-logout" @click="$emit('click:change-company')">
        <v-list-item-title>
          <v-icon left>
            mdi-lock
          </v-icon>
          CHANGE COMPANY
        </v-list-item-title>
      </v-list-item>
      <v-list-item v-if="$auth.user.is_superuser" data-test-id="v-list-item-logout" @click="$router.push('/admin-tools/')">
        <v-list-item-title>
          <v-icon left>
            mdi-lock
          </v-icon>
          Admin Tools
        </v-list-item-title>
      </v-list-item>

      <!-- Menu -->
      <v-list-item data-test-id="v-list-item-my-device" :to="{path:'/me/device'}">
        <v-icon left>
          mdi-devices
        </v-icon>
        <v-list-item-title>{{ $t('menu.my-devices') }}</v-list-item-title>
      </v-list-item>
      <v-list-item data-test-id="v-list-item-change-password" :to="{path:`/me/#me`}">
        <v-icon left>
          mdi-form-textbox-password
        </v-icon>
        <v-list-item-title>{{ $t('menu.change-password') }}</v-list-item-title>
      </v-list-item>
      <v-list-item data-test-id="v-list-item-open-get-start" @click="$store.dispatch('openGetStart'); $router.push('/$/record')">
        <v-icon left>
          mdi-order-bool-ascending-variant
        </v-icon>
        <v-list-item-title>{{ $t('menu.open-get-start') }}</v-list-item-title>
      </v-list-item>
      <v-list-item
        v-for="locale in availableLocales"
        :key="locale.code"
        @click.stop="$i18n.setLocale(locale.code)"
      >
        <v-icon left>
          mdi-translate
        </v-icon>
        <v-list-item-title>
          {{ locale.name }}
        </v-list-item-title>
      </v-list-item>

      <v-list-item @click="downloadDesktopApp()">
        <v-icon left>
          mdi-monitor
        </v-icon>
        <v-list-item-title>
          {{ $t('desktop-app') }} (Windows)
        </v-list-item-title>
      </v-list-item>
      <v-list-item role="" data-test-id="v-list-item-logout" data-test="item-logout" @click="logout()">
        <v-icon left>
          mdi-logout
        </v-icon>
        <v-list-item-title>{{ $t('menu.logout') }}</v-list-item-title>
      </v-list-item>

      <v-list-item
        v-show="currentlyHost === 'localhost:3000'"
        data-test="v-list-item-sign-in-google"
        role=""
        @click="gapiCypressSignIn()"
      >
        <v-icon left>
          mdi-google
        </v-icon>
        <v-list-item-title>Login Google</v-list-item-title>
      </v-list-item>
    </v-list>
  </v-menu>
</template>

<script lang="ts">
import { LocaleObject } from '@nuxtjs/i18n'
import Vue from 'vue'
import { mapActions } from 'vuex'
import { LoggedInUser } from '~/models'
import { performLogout } from '~/utils/logout'

export default Vue.extend({
  data () {
    return {
      currentlyHost: window.location.host
    }
  },
  computed: {
    availableLocales (): LocaleObject[] {
      const locales = this.$i18n.locales as LocaleObject[]
      return locales.filter(x => x.code !== this.$i18n.locale)
    }
  },
  methods: {

    ...mapActions('gapi', {
      gapiCypressSignIn: 'cypressSignIn'
    }),
    getFullName (user: LoggedInUser) {
      if (!user) {
        return ''
      }

      let fullname = ''
      if (user.first_name && user.last_name) {
        fullname = `${user.first_name} ${user.last_name}`
      } else {
        fullname = user.username
      }

      if (user.is_superuser) {
        fullname += ' - ' + user.company.name
      }

      return fullname
    },
    downloadDesktopApp () {
      window.open('https://apps.microsoft.com/detail/9pkml2s2g2pd?ocid=pdpshare&hl=th-TH&gl=TH', '_blank')
    },
    logout () {
      // this.$auth.logout()
      performLogout(this)
    }
  }

})
</script>
