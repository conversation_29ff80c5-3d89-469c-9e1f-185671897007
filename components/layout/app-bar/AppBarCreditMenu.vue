<template>
  <v-menu
    bottom
    left
    offset-y
    close-on-click
  >
    <template #activator="{ on, attrs }">
      <v-btn text style="display: inline-block;" class="caption px-3" v-bind="attrs" v-on="on">
        <div style="display: block; padding-top: 5px;" class="px-2">
          <v-row>
            <div class="px-1">
              <v-icon small style="position: relative; top: -5px;">
                mdi-video
              </v-icon>
              <span data-test="record_balance" style="display: inline-block; width: 80px; text-align: right;" class="text-truncate">
                {{ record_balance }}
              </span>
            </div>
          </v-row>
          <v-row>
            <div class="px-1">
              <v-icon small style="position: relative; top: -5px;">
                mdi-folder-google-drive
              </v-icon>
              <span style="display: inline-block; width: 80px; text-align: right;" class="text-truncate">
                {{ google_drive_text }}
              </span>
            </div>
            <!-- <div class="px-1 text-left" style="width: 90px;">
              <v-icon small style="position: relative; top: -1px;">
                mdi-cellphone-message
              </v-icon>
              {{ user.company.sms_balance }}
            </div> -->
          </v-row>
        </div>
      </v-btn>
    </template>

    <v-card width="330">
      <v-card-text class="px-0 py-2">
        <div class="text-right px-2">
          <small>
            {{ $t('app-bar.last-update') }} {{ $datetime($auth.user.company.wallet_last_update_datetime) }} (V{{ wallet_version }})
          </small>
        </div>

        <v-list>
          <v-list-item>
            <v-list-item-avatar>
              <v-icon left>
                mdi-video
              </v-icon>
            </v-list-item-avatar>

            <v-list-item-content>
              <v-list-item-title>{{ $t('app-bar.credit-video') }}</v-list-item-title>
            </v-list-item-content>

            <v-list-item-action>
              {{ record_balance }}
            </v-list-item-action>
          </v-list-item>

          <v-list-item>
            <v-list-item-avatar>
              <v-icon left>
                mdi-folder-google-drive
              </v-icon>
            </v-list-item-avatar>

            <v-list-item-content>
              <v-list-item-title>{{ $t('app-bar.google-drive-space') }}</v-list-item-title>
              <v-list-item-subtitle>{{ $t('app-bar.used-all') }}</v-list-item-subtitle>
            </v-list-item-content>

            <v-list-item-action>
              {{ google_drive_text }}
            </v-list-item-action>
          </v-list-item>
        </v-list>
        <!-- <v-row dense>
          <v-col cols="8">
            <div class="subtitle-1">
              เครดิตวิดีโอคงเหลือ
            </div>
          </v-col>
          <v-col>
            <div class="my-auto text-right text-h6" style="padding-top: 8px;">
              {{ $auth.user.company.record_balance }}
            </div>
          </v-col>
        </v-row>
        <v-row dense>
          <v-col cols="8">
            <div class="subtitle-1">
              พื้นที่เก็บข้อมูล (ใช้/ทั้งหมด)
            </div>
          </v-col>
          <v-col>
            <div class="my-auto text-right text-h6" style="padding-top: 8px;">
              {{ google_drive_text }}
            </div>
          </v-col>
        </v-row> -->
      </v-card-text>
    </v-card>
  </v-menu>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import { getStorageQuota, IStorageQuota } from '~/api/googledrive'
import { LoggedInUser } from '~/models'
import { SettingState } from '~/store/settings'

export default Vue.extend({
  data () {
    return {
      storage: null as IStorageQuota | null
    }
  },

  computed: {
    ...mapState('gapi', { gapiAuth: 'isAuth' }),
    ...mapState('wallet', {
      record_balance: 'record_balance',
      google_drive_usage: 'google_drive_usage',
      google_drive_limit: 'google_drive_limit',
      wallet_version: 'version'
    }),

    settings (): SettingState {
      return this.$store.state.settings
    },

    google_drive_text (): string {
      let usage = ''
      let limit = ''

      if (this.settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID) {
        // const user = this.$auth.user as never as LoggedInUser
        usage = (this.google_drive_usage / 1000).toFixed(1)
        limit = (this.google_drive_limit / 1000).toFixed(0)
      } else {
        usage = (Number(this.storage?.usage) / 1e9).toFixed(1)
        limit = (Number(this.storage?.limit) / 1e9).toFixed(0)
      }

      return `${usage}/${limit} GB`
    }
  },

  watch: {
    gapiAuth: {
      handler (val) {
        if (val) {
          setTimeout(async () => { this.storage = await getStorageQuota() }, 1000)
        }
      },
      immediate: true
    }
  },

  created () {
    this.getWalletBalance()
  },

  methods: {
    async getWalletBalance () {
      const res = await this.$axios.get('/api/companies/wallet-balance/')
      this.$store.commit('wallet/set', res.data)
    }
  }
})
</script>
