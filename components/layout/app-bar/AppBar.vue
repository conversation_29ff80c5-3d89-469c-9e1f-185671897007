<template>
  <v-app-bar clipped-left dense fixed app>
    <v-app-bar-nav-icon v-if="$auth.loggedIn" @click.stop="$emit('toggle-drawer')" />
    <v-toolbar-title>
      <v-img :src="logo_src" height="44" max-width="250" contain position="left center" />
    </v-toolbar-title>
    <v-spacer />

    <template v-if="!$auth.loggedIn">
      <v-btn data-test-id="v-btn-goto-login" text to="/login">
        <v-icon left>
          mdi-login
        </v-icon>
        {{ $t('login') }}
      </v-btn>
    </template>

    <template v-if="$auth.loggedIn">
      <app-bar-credit-menu />
      <app-bar-user-menu @click:change-company="openChangeCompanyDialog()" />

      <change-company-dialog v-if="user.is_superuser" ref="change_company_dialog" />
    </template>
  </v-app-bar>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapGetters } from 'vuex'
import ChangeCompanyDialog from '../../admin/ChangeCompanyDialog.vue'
import AppBarCreditMenu from './AppBarCreditMenu.vue'
import AppBarUserMenu from './AppBarUserMenu.vue'
import { LoggedInUser } from '~/models'
export default Vue.extend({
  components: { ChangeCompanyDialog, AppBarUserMenu, AppBarCreditMenu },
  props: {
    user: {
      type: [Object, Boolean],
      required: false,
      default: () => null
    }
  },
  data () {
    return {
      // logo_src: '/logo-with-text.png'
    }
  },

  computed: {
    ...mapGetters(['isAuthenticated']),

    logo_src (): string {
      const user: LoggedInUser = this.$auth.user as never
      if (user && user.company && user.company.web_logo) {
        const url = new URL(user.company.web_logo)
        return url.pathname
      }
      return '/logo-with-text.png'
    },

    company_name (): string {
      const user: LoggedInUser = this.$auth.user as never
      if (user && user.company) {
        return user.company.name
      }
      return ''
    }
  },

  methods: {
    openChangeCompanyDialog () {
      const dialog = this.$refs.change_company_dialog as any
      dialog.show()
    }
  }
})
</script>
