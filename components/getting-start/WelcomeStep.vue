<template>
  <div style="height: 300px;">
    <div class="text-center d-flex flex-column align-center" style="height: 300px; padding: 0px">
      <h2 class="mb-3">
        {{ $t('start-setting-dobybot') }}
      </h2>
      <div>
        <div>Choose Language / เลือกภาษา</div>
        <v-radio-group :value="$i18n.locale" class="my-0" hide-details="" @change="$i18n.setLocale($event)">
          <div class="d-flex flex-row">
            <v-card data-test="welcome-step-card-thai-language" width="200" outlined class="pa-3 mr-1" @click="$i18n.setLocale('th')">
              <v-radio  label="Thai / ไทย" value="th" @click.stop />
              <v-img src="/flags/thailand.png" width="60" class="mx-auto mt-2" />
              <div />
            </v-card>
            <v-card data-test="welcome-step-card-english-language" width="200" outlined class="pa-3" @click="$i18n.setLocale('en')">
              <v-radio  label="English / อังกฤษ" value="en" @click.stop />
              <v-img src="/flags/united-kingdom.png" width="60" class="mx-auto mt-2" />
            </v-card>
          </div>
        </v-radio-group>
      </div>

      <div class="mt-4" 
        data-test="cancel-setup"
      >
        <a
          type="button"
          class="black--text text-decoration-underline"
          @click.prevent="$emit('skip')"
        >
          {{ $t('i-am-not-recording-video') }}
        </a>
      </div>
      <v-btn
        color="primary"
        class="mt-5"
        @click="$emit('next')"
        data-test="welcome-step-mext-btn"
      >
        Next
      </v-btn>
    </div>
  </div>
</template>

<script>
export default {
  computed: {
    availableLocales () {
      const locales = this.$i18n.locales
      return locales
    }
  }
}
</script>
