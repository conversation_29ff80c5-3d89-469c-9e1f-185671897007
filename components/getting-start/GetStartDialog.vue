<template>
  <v-dialog
    max-width="760"
    min-height="300"
    :value="value"
    persistent
    @input="close()"
  >
    <v-stepper
      v-model="steps_index"
    >
      <v-stepper-header>
        <v-stepper-step
          v-for="(step, index) in steps"
          :key="index"
          :step="index+1"
          :complete="steps_index > index"
        />
      </v-stepper-header>
      <v-stepper-items>
        <v-stepper-content v-for="(step, index) in steps" :key="index" :step="index+1">
          <!-- if last step when click next call funtion close -->
          <component
            :is="step.component"
            :step-index="steps_index"
            @next="index+1 == steps.length ? close() : steps_index++"
            @back="steps_index--"
            @skip="$emit('skip')"
          />
        </v-stepper-content>
      </v-stepper-items>
    </v-stepper>
  </v-dialog>
</template>

<script lang="ts">
import Vue from 'vue'
import WelcomeStep from './WelcomeStep.vue'
import ConnectWebCamStep from './ConnectWebCamStep.vue'
import ConnectScannerStep from './ConnectScannerStep.vue'
import RecordSettingStep from './RecordSettingStep.vue'
import ConnectGoogleDriveStep from './ConnectGoogleDriveStep.vue'
import GetStartStep7 from './GetStartStep7.vue'
import HardwareFinishedStep from './HardwareFinishedStep.vue'

export default Vue.extend({
  components: {
    WelcomeStep,
    ConnectWebCamStep,
    ConnectScannerStep,
    RecordSettingStep,
    ConnectGoogleDriveStep,
    GetStartStep7,
    HardwareFinishedStep
  },
  props: {
    value: { type: Boolean, required: true }
  },

  data () {
    return {
      steps: [
        { title: 'Step 1', component: 'WelcomeStep' },
        { title: 'Step 2', component: 'ConnectWebCamStep' },
        { title: 'Step 3', component: 'ConnectScannerStep' },
        { title: 'Step 4', component: 'HardwareFinishedStep' },
        { title: 'Step 5', component: 'RecordSettingStep' },
        { title: 'Step 6', component: 'ConnectGoogleDriveStep' },
        { title: 'Step 7', component: 'GetStartStep7' }
      ],
      steps_index: 1
    }
  },

  mounted () {
    if (localStorage.getItem('open-setting') && !localStorage.getItem('get_start_dialog')) {
      this.steps_index = 6
    }
  },

  methods: {
    close () {
      this.$emit('close')
    }
  }
})
</script>
