<template>
  <div class="d-flex align-content-space-between flex-wrap " style="min-height: 300px;">
    <!-- Google Drive Setting -->
    <v-row dense>
      <v-col cols="12">
        <h2>{{ $t('setting.google-drive') }}</h2>
        <p>
          {{ $t('system-will-create-google-drive-folder') }}
        </p>
        <span v-if="settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID">
          <div>
            <v-icon small color="success">
              mdi-check
            </v-icon>
            {{ $t('setting.using-share-drive') }} <span v-if="$auth.user.is_superuser">{{ settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID }}</span><br>
            <ul class="pl-8" style="max-height: 75px; overflow-y: scroll">
              <li v-for="email in settings.company.RECORD_GMAIL_ALLOWED_LIST" :key="email">
                {{ email }}
              </li>
            </ul>
          </div>
        </span>
        <div v-if="!gapiAuth">
          <!-- create card connect with g drive -->
          <v-card class="ma-1 mt-2" elevation="2">
            <div class="d-flex flex-no-wrap">
              <v-avatar
                class="ma-5"
                size="100"
                tile
              >
                <v-img src="/google-drive.png" />
              </v-avatar>

              <div>
                <v-card-title
                  class="text-h5"
                >
                  {{ $t('connect-google-drive') }}
                </v-card-title>

                <v-card-subtitle>
                  {{ $t('connect-google-drive-to-store-video-files') }}
                </v-card-subtitle>

                <v-card-actions>
                  <v-btn
                    class="ml-2"
                    outlined
                    rounded
                    small
                    data-test="connect-google-drive-step-connect-g-drive-btn"
                    @click.prevent="gapiSignIn()"
                  >
                    Connect Google Drive
                  </v-btn>
                </v-card-actions>
              </div>
            </div>
          </v-card>
        </div>
        <div v-if="gapiAuth">
          <v-icon small color="success">
            mdi-check
          </v-icon>
          {{ $t('setting.connected-to-google-drive', [gapiUser.email]) }}
          <a data-test-id="a-gapi-signout" href="!#" class="black--text" @click.prevent="gapiSignOut()">{{ $t('disconnect-google-drive') }}</a>
        </div>
        <div v-if="gapiAuth && storage && !settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID">
          <span data-test-id="span-total-storage">{{ $t('total-google-drive-storage') }}: {{ storage.limitGB }}</span>,
          <span data-test-id="span-used-storage">{{ $t('total-google-drive-usage') }}: {{ storage.usageGB }}</span>,
          <span data-test-id="span-available-storage">{{ $t('google-drive-space-available') }}: {{ storage.availableGB }}</span>
        </div>
        <div v-if="gapiAuth">
          <br>
          <span data-test-id="span-root-folder">
            <b>Root Folder</b>:
            <span v-if="settings.local.googleDriveRootFolder">
              {{ settings.local.googleDriveRootFolder.name }}
              <a
                v-if="$hasGroup('owner')"
                target="_blank"
                :href="`https://drive.google.com/drive/folders/${settings.local.googleDriveRootFolder.id}`"
              >view</a>
            </span>
            <span v-else>-</span>
          </span>
          <br>
          <span data-test-id="span-today-folder">
            <b>Today Folder</b>:
            <span v-if="settings.local.googleDriveTodayFolder">
              {{ settings.local.googleDriveRootFolder.name }}/{{ settings.local.googleDriveTodayFolder.name }}
              <a
                v-if="$hasGroup('owner')"
                target="_blank"
                :href="`https://drive.google.com/drive/folders/${settings.local.googleDriveTodayFolder.id}`"
              >view</a>
            </span>
            <span v-else>-</span>
          </span>
        </div>
      </v-col>
    </v-row>

    <div class="d-flex justify-space-between mt-3" style="width: -webkit-fill-available;">
      <v-btn
        text
        @click="$emit('back')"
        data-test="connect-google-drive-step-back-btn"
      >
        Back
      </v-btn>
      <v-btn
        color="primary"
        :disabled="!canNext"
        :loading="loading"
        @click="$emit('next')"
        data-test="connect-google-drive-step-next-btn"

      >
        Next
      </v-btn>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex'
import moment from 'moment'
import { SettingState } from '~/store/settings'
import { IStorageQuota, createFolder, getStorageQuota, listFoldersInMyDrive, findFolderInShareDrive } from '~/api/googledrive'

export default Vue.extend({

  data () {
    return {
      devices: [] as MediaDeviceInfo[],

      file_types: ['webm', 'mp4', 'webm-raw'],

      storage: null as IStorageQuota | null,

      loading: false
    }
  },

  computed: {
    ...mapState('gapi', { gapiAuth: 'isAuth' }),
    // ...mapState('settings', { localSetting: 'local', companySetting: 'company' }),

    ...mapGetters('gapi', {
      gapiToken: 'token',
      gapiUser: 'currentUser'
    }),

    settings (): SettingState {
      return this.$store.state.settings
    },

    canNext (): boolean {
      if (!this.gapiAuth) { return false }
      if (!this.settings.local.googleDriveRootFolder) { return false }
      if (!this.settings.local.googleDriveTodayFolder) { return false }
      return true
    }
  },

  watch: {
    gapiAuth: {
      handler (val) {
        this.loading = true
        if (val) {
          setTimeout(async () => {
            this.storage = await getStorageQuota()
            await this.setupFolders()
            this.loading = false
          }, 1000)
        } else {
        }
      },
      immediate: true
    }

  },

  mounted () {
    if (localStorage.getItem('open-google-signin')) {
      localStorage.removeItem('open-google-signin')
      this.gapiSignIn()
    }
  },

  methods: {
    async setupFolders () {
      if (this.settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID) {
        await this.setupShareDriveFolder()
      } else {
        await this.setupMyDriveFolder()
      }
    },

    async setupShareDriveFolder () {
      const share_drive_id = this.settings.company.GOOGLE_DRIVE_SHARE_DRIVE_ID

      // Setup Root Folder
      const rootFolderName = `Dobybot Videos (${this.gapiUser.email})`
      try {
        let rootFolder = await findFolderInShareDrive(share_drive_id, rootFolderName)
        if (!rootFolder) {
          rootFolder = await createFolder(rootFolderName, { parentFolderId: share_drive_id })
        }
        this.setLocalSetting({ googleDriveRootFolder: rootFolder })
      } catch (error: any) {
        this.$alert({
          title: this.$t('error.unable-to-connect-to-google-drive'),
          text: error.result,
          theme: 'error'
        })
        throw error
      }

      // Setup Today Folder
      const todayFolderName = moment().format('YYYY-MM-DD')
      try {
        let todayFolder = await findFolderInShareDrive(share_drive_id, todayFolderName)
        if (!todayFolder && this.settings.local.googleDriveRootFolder) {
          todayFolder = await createFolder(todayFolderName, {
            parentFolderId: this.settings.local.googleDriveRootFolder.id
          })
        }
        this.setLocalSetting({ googleDriveTodayFolder: todayFolder })
      } catch (error: any) {
        this.$alert({
          title: this.$t('error.unable-to-connect-to-google-drive'),
          text: error.result,
          theme: 'error'
        })
        throw error
      }
    },

    async setupMyDriveFolder () {
      console.log('setup local folder')
      const folders = await listFoldersInMyDrive()
      const rootFolderName = 'Packing Videos'
      const todayFolderName = moment().format('YYYY-MM-DD')

      let rootFolder = folders.find(f => f.name === rootFolderName)
      if (!rootFolder) {
        console.log('create root folder')
        rootFolder = await createFolder(rootFolderName)
      }
      this.setLocalSetting({ googleDriveRootFolder: rootFolder })

      let todayFolder = folders.find(f => f.name === todayFolderName)
      if (!todayFolder && this.settings.local.googleDriveRootFolder) {
        console.log('create today folder')
        todayFolder = await createFolder(todayFolderName, {
          parentFolderId: this.settings.local.googleDriveRootFolder.id
        })
      }
      this.setLocalSetting({ googleDriveTodayFolder: todayFolder })
    },

    ...mapActions('gapi', {
      gapiSignIn: 'signIn',
      gapiSignOut: 'signOut'
    }),

    ...mapMutations('settings', {
      setLocalSetting: 'setLocalSetting'
    })
  }
})
</script>

<style lang="scss" scoped>

</style>
