<template>
  <v-form ref="videoSettingForm">
    <div>
      <h2>{{ $t('setting-up-the-camera') }}</h2>

      <ol>
        <!-- Step 1 -->
        <li class="mt-3">
          {{ $t('connect-usb-webcam') }}
          <v-img src="/get-start/connect-cam.png" max-width="380" height="200" cover />
        </li>

        <!-- Step 2 -->
        <li class="mt-5">
          {{ $t('please-select-camera') }} {{ $t('and') }} <b class="red--text">{{ $t('Choose') }} <u>Allow</u> {{ $t('to-allow-camera-access') }}</b>
          <v-img src="/get-start/camera-access-prompt.png" width="380" height="160" contain />

          <div class="mt-3">
            <v-select
              data-test="connect-webcam-step-v-select-camera"
              filled
              :label="$t('setting.camera')"
              item-text="label"
              item-value="deviceId"
              return-object
              hide-details
              :items="devices"
              :value="settings.local.deviceId"
              @change="
                setLocalSetting({deviceId: $event.deviceId});
                startCamera()
              "
            >
              <template #no-data>
                <v-list-item @click.stop="requestUserMediaPermission()" data-test="connect-webcam-step-select-camera">
                  <v-list-item-content data-test="connect-webcam-step-allow-camera">
                    <v-list-item-title>{{ $t('setting.please-allow-access-to-camera') }}</v-list-item-title>
                  </v-list-item-content>
                </v-list-item>
              </template>
            </v-select>
          </div>
        </li>

        <!-- Step 3 -->
        <li class="mt-5">
          {{ $t('check-if-camera-is-working') }}
          <br>
          <div>
            <video id="video-player" width="380px" style="border: 1px solid black;" />
          </div>
        </li>
      </ol>

      <v-divider class="my-5" />

      <div class="d-flex justify-space-between " style="width:-webkit-fill-available;">
        <v-btn
          text
          @click="$emit('back')"
           data-test="connect-webcam-step-back-btn"
        >
          Back
        </v-btn>

        <v-btn
          color="primary"
          :disabled="!settings.local.deviceId"
          @click="$emit('next')"
          data-test="connect-webcam-step-next-btn"
        >
          Next
        </v-btn>
      </div>
    </div>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapMutations } from 'vuex'
import { SettingState } from '~/store/settings'

export default Vue.extend({
  props: {
    stepIndex: {
      type: Number,
      required: true
    }
  },

  data () {
    return {
      devices: [] as MediaDeviceInfo[],
      videoElement: null as any,
      stream: null as MediaStream | null
    }
  },

  computed: {
    settings (): SettingState {
      return this.$store.state.settings
    }
  },

  watch: {
    stepIndex (val) {
      if (val === 2 && this.settings.local.deviceId) {
        this.getDevicesList()
      }
      if (val !== 2 && this.stream) {
        this.stopCamera()
      }
    }
  },

  beforeDestroy () {
    this.stopCamera()
  },

  methods: {
    getDevicesList (): void {
      try {
        navigator.mediaDevices.enumerateDevices().then(
          (deviceInfos: MediaDeviceInfo[]) => {
            for (const device of deviceInfos) {
              if (device.kind === 'videoinput' && device.deviceId !== '') {
                this.devices.push(device)
              }
            }
          }
        )
      } catch (error) {
        console.error('The navigator.mediaDevices is undefined in insecure context.\nOperation failed with error RTC0x0001')
      }
    },

    requestUserMediaPermission () {
      const constraints = {
        video: {
          width: { min: 640, ideal: 1280, max: 2560 },
          height: { min: 480, ideal: 720, max: 1440 }
        }
      }

      if (!navigator.mediaDevices) {
        alert(this.$t('error.unable-to-capture-your-camera-noperation-failed-with-error-rtc0x0002'))
        return
      }

      navigator.mediaDevices
        .getUserMedia(constraints)
        .then((camera: MediaStream) => {
          // callback(camera)
          this.getDevicesList()
          camera.getTracks().forEach(track => track.stop())
        })
        .catch((error) => {
          alert(this.$t('error.unable-to-capture-your-camera-noperation-failed-with-error-rec0x0002'))
          console.error(error)
        })
    },

    startCamera () {
      this.stopCamera()

      if (!this.settings.local.deviceId) {
        alert(this.$t('error.please-select-camera'))
      }

      const constraints = {
        audio: false,
        video: { deviceId: { exact: this.settings.local.deviceId as string } }
      }

      navigator.mediaDevices.getUserMedia(constraints)
        .then((stream) => {
          const videoElement = document.getElementById('video-player') as HTMLVideoElement
          if (!videoElement) {
            return
          }
          videoElement.srcObject = stream
          videoElement.play()

          this.stream = stream
        })
        .catch((error) => {
          console.error(error)
        })
    },

    stopCamera () {
      if (this.stream) {
        this.stream.getTracks().forEach((track) => {
          track.stop()
        })

        this.stream = null
      }
    },

    ...mapMutations('settings', {
      setLocalSetting: 'setLocalSetting'
    })
  }
})
</script>

<style lang="scss" scoped>

</style>
