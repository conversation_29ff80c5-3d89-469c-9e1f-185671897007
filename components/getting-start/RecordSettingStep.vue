<template>
  <v-form ref="videoSettingForm" style="min-height: 300px;" class="d-flex align-content-space-between flex-wrap">
    <v-row wrap dense class="mb-3">
      <v-col cols="12" class="mb-3">
        <h2>{{ $t('setting.record') }}</h2>
      </v-col>

      <v-col
        id="v-select-resolution"
        cols="12"
        class="mb-3"
      >
        <record-resolution-select
          :key="key"
          :value="settings.local.resolution"
          @input="setResolution($event)"
        />
      </v-col>

      <v-col cols="12" class="mb-3">
        <record-file-type-select
          :value="settings.local.filetype"
          @input="onFiletypeChange($event)"
        >
          <template #mp4>
            <v-divider v-if="progress.load_ffmpeg.status" class="my-3" />
            <progress-line
              v-if="progress.load_ffmpeg.status"
              class="text-caption mt-2"
              :status="progress.load_ffmpeg.status"
              :text="progress.load_ffmpeg.text"
            />
            <progress-line
              v-if="progress.test_ffmpeg.status"
              class="text-caption"
              :status="progress.test_ffmpeg.status"
              :text="progress.test_ffmpeg.text"
            >
              <a
                v-if="progress.test_ffmpeg.status === 'success'"
                download
                :href="progress.test_ffmpeg.result"
              >{{ $t('setting.download-for-testing') }}</a>
            </progress-line>
          </template>
        </record-file-type-select>
      </v-col>
    </v-row>

    <div class="d-flex justify-space-between " style="width:-webkit-fill-available;">
      <v-btn
        text
        @click="$emit('back')"
        data-test="record-setting-step-back-btn"
      >
        Back
      </v-btn>

      <v-btn
        color="primary"
        :disabled="!canNext"
        @click="$emit('next')"
        data-test="record-setting-step-next-btn"

      >
        Next
      </v-btn>
    </div>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState, mapGetters, mapMutations } from 'vuex'
import { createFFmpeg } from '@ffmpeg/ffmpeg'
import ProgressLine from '../progress/ProgressLine.vue'
import RecordResolutionSelect from '../settings/record/RecordResolutionSelect.vue'
import RecordFileTypeSelect from '../settings/record/RecordFileTypeSelect.vue'
import { SettingState } from '~/store/settings'
import { transcodeMP4 } from '~/utils/video'

export default Vue.extend({
  components: {
    ProgressLine,
    RecordResolutionSelect,
    RecordFileTypeSelect
  },

  data () {
    return {
      key: 0,
      devices: [] as MediaDeviceInfo[],
      resolutions: [
        '640x360',
        '1280x720',
        '1920x1080'
      ],

      file_types: ['webm', 'mp4', 'webm-raw'],

      progress: {
        load_ffmpeg: {
          status: null as string | null,
          text: ''
        },
        test_ffmpeg: {
          status: null as string | null,
          text: '',
          result: null as string | null
        }
      },
      lock: true,
      steps: 6,
      e1: 1
    }
  },

  computed: {
    ...mapState('gapi', { gapiAuth: 'isAuth' }),
    // ...mapState('settings', { localSetting: 'local', companySetting: 'company' }),

    ...mapGetters('gapi', {
      gapiToken: 'token',
      gapiUser: 'currentUser'
    }),

    settings (): SettingState {
      return this.$store.state.settings
    },

    canNext (): boolean {
      if (!this.settings.local.filetype) { return false }
      if (!this.settings.local.resolution) { return false }
      if (this.settings.local.filetype === 'mp4') {
        if (this.progress.load_ffmpeg.status !== 'success') { return false }
        if (this.progress.test_ffmpeg.status !== 'success') { return false }
      }
      return true
    }
  },
  watch: {
    'settings.local.deviceID' () {
      this.getDevicesList()
    }
  },

  mounted () {
    this.getDevicesList()

    // @ts-ignore
    if (this.$auth.user.company.package === this.$package.FULL_INTEGRATION) {
      this.onFiletypeChange('mp4')
    }
  },

  methods: {
    async setResolution (resolution: string) {
      if (resolution === '1920x1080') {
        const ok = await this.$confirm({
          title: this.$t('confirm-using-1080p'),
          text: `<ul>
            <li>${this.$t('using-1080p-will-use-more-storage')}</li>
            <li>${this.$t('using-1080p-will-take-longer-to-upload')}</li>
            <li>${this.$t('using-1080p-required-lan-cable-connection')}</li>
          </ul>`
        })

        if (ok) {
          this.setLocalSetting({ resolution, enableOverlay: false })
        } else {
          this.setLocalSetting({ resolution: '1280x720' })
          this.key++
        }
      } else {
        this.setLocalSetting({ resolution })
      }
    },

    getDevicesList (): void {
      try {
        navigator.mediaDevices.enumerateDevices().then(
          (deviceInfos: MediaDeviceInfo[]) => {
            for (const device of deviceInfos) {
              if (device.kind === 'videoinput' && device.deviceId !== '') {
                this.devices.push(device)
              }
            }
          }
        )
      } catch (error) {
        console.error('The navigator.mediaDevices is undefined in insecure context.\nOperation failed with error RTC0x0001')
      }
    },

    requestUserMediaPermission () {
      const constraints = {
        video: {
          width: { min: 640, ideal: 1280, max: 2560 },
          height: { min: 480, ideal: 720, max: 1440 }
        }
      }

      if (!navigator.mediaDevices) {
        alert(this.$t('error.unable-to-capture-your-camera-noperation-failed-with-error-rtc0x0002'))
        return
      }

      navigator.mediaDevices
        .getUserMedia(constraints)
        .then((camera: MediaStream) => {
          // callback(camera)
          this.getDevicesList()
          camera.getTracks().forEach(track => track.stop())
        })
        .catch((error) => {
          alert(this.$t('error.unable-to-capture-your-camera-noperation-failed-with-error-rec0x0002'))
          console.error(error)
        })
    },

    async onFiletypeChange (filetype: 'mp4'|'webm') {
      const progress = this.progress
      progress.load_ffmpeg.status = null
      progress.test_ffmpeg.status = null

      this.setLocalSetting({ filetype, speed: '1x' })

      if (filetype === 'mp4') {
        progress.load_ffmpeg.status = 'loading'
        progress.load_ffmpeg.text = this.$t('record.downloading-mp4-engine')
        try {
          if (!window.ffmpeg) {
            window.ffmpeg = createFFmpeg({
              log: process.env.NODE_ENV === 'development',
              corePath: '/static/js/@ffmpeg/core@0.11.0/ffmpeg-core.js'
            })
            await window.ffmpeg.load()
          }
          progress.load_ffmpeg.status = 'success'
        } catch (error: any) {
          progress.load_ffmpeg.status = 'error'
          progress.load_ffmpeg.text = error.toString()
          console.error(error)
          this.setLocalSetting({ filetype: 'webm', speed: '1x' })
          return
        }

        progress.test_ffmpeg.status = 'loading'
        progress.test_ffmpeg.text = this.$t('record.testing-transcode-mp4')
        try {
          const test_video_url = require('~/assets/video/test-h264.webm')
          const blob = await fetch(test_video_url).then(r => r.blob())
          const blob_mp4 = await transcodeMP4(blob, { speed: this.settings.local.speed })
          progress.test_ffmpeg.status = 'success'
          progress.test_ffmpeg.result = URL.createObjectURL(blob_mp4)
        } catch (error: any) {
          progress.test_ffmpeg.status = 'error'
          progress.test_ffmpeg.text = error.toString()
          console.error(error)
          this.setLocalSetting({ filetype: 'webm', speed: '1x' })
        }
      }
    },

    ...mapMutations('settings', {
      setLocalSetting: 'setLocalSetting'
    })
  }
})
</script>

<style scoped>
.text-help {
  /* padding-left: 33px; */
  font-size: 0.85rem;
  color: #555555;
}
</style>
