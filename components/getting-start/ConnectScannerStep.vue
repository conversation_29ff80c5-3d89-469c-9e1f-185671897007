<template>
  <div>
    <h2>
      {{ $t('connect-barcode-scanner') }}
    </h2>

    <ol>
      <!-- Step 1 -->
      <li class="mt-3">
        {{ $t('connect-usb-scanner') }}
        <v-img src="/get-start/connect-scanner.png" max-width="380" height="200" />
      </li>

      <li class="mt-5">
        {{ $t('check-if-barcode-scanner-working') }}
        <v-text-field ref="check_barcode" data-test="connect-scanner-step-text-input" v-model="check_barcode" filled :label="$t('click-here-to-start-scanning-barcode')" hide-details />
        <div class="mt-3">
          <div class="d-flex">
            <div style="flex-grow: 1;">
              <div style="width: 100px;" class="text-center">
                <img height="100" src="/get-start/barcode-ready.png">
                <div>READY</div>
              </div>
            </div>
            <div style="flex-grow: 1;">
              <div style="width: 100px;" class="text-center">
                <img height="100" src="/get-start/qrcode-ready.png">
                <div>READY</div>
              </div>
            </div>
          </div>
        </div>
      </li>
    </ol>

    <v-divider class="my-5" />

    <div class="d-flex justify-space-between mt-3">
      <v-btn
        text
        @click="$emit('back')"
          data-test="connect-scanner-step-back-btn"

      >
        Back
      </v-btn>
      <div class="d-flex">
        <v-btn
          text
          @click="$emit('next')"
          data-test="connect-scanner-step-skip-btn"

        >
          Skip
        </v-btn>
        <v-btn
          color="primary"
          :disabled="!check_barcode.startsWith('READY')"
          @click="$emit('next')"
          data-test="connect-scanner-step-next-btn"
        >
          Next
        </v-btn>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    stepIndex: {
      type: Number,
      required: true
    }
  },
  data () {
    return {
      check_barcode: ''
    }
  },
  watch: {
    stepIndex (val) {
      if (val === 3) {
        setTimeout(() =>
          this.$nextTick(() => {
            this.$refs.check_barcode.focus()
          }), 1000
        )
      }
    }
  },
  methods: {
    submitForm (e) {
      if (this.check_barcode === 'connected') {
        this.$emit('next')
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
