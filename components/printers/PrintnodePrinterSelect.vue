<template>
  <v-autocomplete
    data-test-id="v-autocomplete-printer-select"
    :label="$t('printers.printer')"
    item-value="id"
    item-text="text"
    clearable
    v-bind="$attrs"
    :items="items"
    :value="value"
    @change="$emit('input', $event)"
  >
    <template #item="{ item, on }">
      <v-list-item v-on="on">
        <v-icon :color="item.state == 'online' ? 'success' : 'error'">
          mdi-circle-medium
        </v-icon>
        {{ item.text }}
      </v-list-item>
    </template>

    <template #no-data>
      <v-list-item @click="onRefreshPrinterClick()">
        <template v-if="!loading">
          <v-icon left>
            mdi-refresh
          </v-icon>
          {{ $t('printers.load') }}
        </template>
        <template v-else>
          <v-spacer />
          <v-btn text loading />
          <v-spacer />
        </template>
      </v-list-item>
    </template>

    <template #selection="{ item }">
      {{ item.computer_name }} -- {{ item.name }} (id={{ item.id }})
    </template>

    <template #append>
      <v-tooltip top>
        <template #activator="{ on, attrs }">
          <v-icon v-bind="attrs" style="cursor: pointer;" v-on="on" @click="openHelp()">
            mdi-information
          </v-icon>
        </template>
        {{ $t('printer.find-printer-name') }}
      </v-tooltip>
    </template>
  </v-autocomplete>
</template>

<script lang="ts">
import Vue from 'vue'
import { Printer } from '~/models'
export default Vue.extend({
  props: {
    value: {
      type: String,
      default: null
    }
  },

  data () {
    return {
      printers: [] as Printer[],
      loading: false
    }
  },

  computed: {
    items (): (Printer | { text: string })[] {
      return this.printers.map((p: any) => {
        return {
          ...p,
          text: `[${p.state}] ${p.computer_name} -- ${p.name} (id=${p.id})`
        }
      })
    }
  },

  mounted () {
    try {
      this.printers = JSON.parse(localStorage.getItem('PrintnodePrinterSelect.printers') || '[]')
    } catch (error) {}

    (async () => {
      this.loading = true
      this.printers = await this.$store.dispatch('printnode/getPrinters')
      localStorage.setItem('PrintnodePrinterSelect.printers', JSON.stringify(this.printers))
      this.loading = false
    })()
  },

  methods: {
    async onRefreshPrinterClick () {
      this.loading = true
      try {
        this.printers = await this.$store.dispatch('printnode/getPrinters')
        this.$snackbar('success', this.$t('printers.success'))
      } catch (error) {
        this.$snackbar('error', this.$t('printers.fail-to-update-printer-list'))
      }
      this.loading = false
    },

    openHelp () {
      window.open('https://docs.google.com/document/d/1QBjVAKwsL_ejRzqzB8R3V46B1qOR4spzV1Hz4uAZH5I/preview', '_blank')
    }
  }

})
</script>
