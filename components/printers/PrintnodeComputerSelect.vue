<template>
  <v-autocomplete
    data-test-id="v-autocomplete-printer-select"
    :label="$t('printnode.choose-computer-for-scale')"
    item-value="id"
    item-text="text"
    clearable
    v-bind="$attrs"
    :items="items"
    :value="value"
    @change="$emit('input', $event)"
  >
    <template #item="{ item, on }">
      <v-list-item v-on="on">
        <v-icon :color="item.state == 'connected' ? 'success' : 'error'">
          mdi-circle-medium
        </v-icon>
        {{ item.id }} - {{ item.name }}
      </v-list-item>
    </template>

    <template #no-data>
      <v-list-item @click="onRefreshComputerClick()">
        <template v-if="!loading">
          <v-icon left>
            mdi-refresh
          </v-icon>
          {{ $t('printers.load') }}
        </template>
        <template v-else>
          <v-spacer />
          <v-btn text loading />
          <v-spacer />
        </template>
      </v-list-item>
    </template>

    <template #selection="{ item }">
      {{ item.id }} - {{ item.name }}
    </template>

    <template #append>
      <v-tooltip top>
        <template #activator="{ on, attrs }">
          <v-icon v-bind="attrs" style="cursor: pointer;" v-on="on" @click="openHelp()">
            mdi-information
          </v-icon>
        </template>
        {{ $t('printer.find-printer-name') }}
      </v-tooltip>
    </template>
  </v-autocomplete>
</template>

<script lang="ts">
import Vue from 'vue'
import { PrintnodeComputer } from '~/models'
export default Vue.extend({
  props: {
    value: {
      type: [String, Number],
      default: null
    }
  },

  data () {
    return {
      computers: [] as PrintnodeComputer[],
      loading: false
    }
  },

  computed: {
    items (): any[] {
      return this.computers
    }
  },

  mounted () {
    ;(async () => {
      this.computers = await this.getComputers()
    })()
  },

  methods: {
    async onRefreshComputerClick () {
      this.loading = true
      try {
        this.computers = await this.getComputers()
        this.$snackbar('success', this.$t('printnode.computer-list-updated'))
      } catch (error) {
        this.$snackbar('error', this.$t('printnode.cant-update-computer-list'))
      }
      this.loading = false
    },

    getComputers () {
      return this.$axios.$get('/api/picking/computers/')
    },

    openHelp () {
      window.open('https://docs.google.com/document/d/1QBjVAKwsL_ejRzqzB8R3V46B1qOR4spzV1Hz4uAZH5I/preview', '_blank')
    }
  }

})
</script>
