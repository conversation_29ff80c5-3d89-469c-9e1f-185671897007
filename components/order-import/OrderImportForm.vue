<template>
  <v-form ref="form" @submit.prevent="submit()">
    <v-row dense class="mt-1">
      <v-col cols="12" md="2">
        <v-select
          outlined
          dense
          :label="$t('order-import.type')"
          prepend-icon="mdi-file-table"
          clearable
          :items="Object.keys(import_types)"
          :rules="[$rules.required]"
          :value="formData.import_type"
          hide-details="auto"
          @change="updateImportType($event)"
        />
        <a
          v-if="formData.import_type"
          class="ml-9 caption"
          style="position: relative; top: -4px;"
          :href="import_types[formData.import_type].example"
        >ดาวโหลดไฟล์ตัวอย่าง</a>
      </v-col>
      <v-col cols="12" md="8">
        <v-file-input
          outlined
          dense
          :label="$t('order-import.select-file')"
          show-size
          truncate-length="50"
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel, text/csv"
          :hint="$t('xlsx-only-max-32-MB')"
          clearable
          persistent-hint
          :rules="[$rules.required]"
          :value="formData.file"
          @change="update({ file: $event })"
        />

        <div class="d-flex mt-4">
          <div style="width: 24px; margin-right: 9px; margin-top: 8px">
            <v-icon>mdi-cog</v-icon>
          </div>
          <div style="position: relative; border: 1px solid rgba(0, 0, 0, 0.38); border-radius: 4px; padding-top: 4px; padding-bottom: 12px; padding-left: 12px; flex-grow: 1">
            <div style="position: absolute; top: -10px; left: 8px; font-size: 12px; color: rgba(0, 0, 0, 0.6); background: white;">
              ตั้งค่าการนำเข้าข้อมูล
            </div>
            <v-checkbox
              label="ไม่แก้ไขเลข Tracking No."
              dense
              hide-details="auto"
              :value="formData.options.do_not_update_tracking"
              @change="updateOptions({ do_not_update_tracking: !!$event })"
            />
            <div class="text-help">
              สำหรับคำสั่งซื้อที่เคยนำเข้าก่อนหน้านี้ - ระบบจะไม่ทำการอัพเดท / สร้างเลข Tracking No. ใหม่
            </div>
          </div>
        </div>
      </v-col>
      <v-col>
        <v-btn :loading="loading" color="success" block type="submit">
          {{ $t('order-import.import-data') }}
        </v-btn>
      </v-col>
    </v-row>
    <v-row v-if="formData.import_type" wrap>
      <v-col cols="12">
        <div class="text-center" />
      </v-col>
    </v-row>
  </v-form>
</template>

<script lang="ts">
import Vue from 'vue'
import { VForm } from '~/models'

export interface ImportType {
    help: string;
    example: string;
    company: string;
}

export default Vue.extend({
  props: {
    formData: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },

  data () {
    return {
      import_types: {} as {[key: string]: ImportType}
    }
  },

  mounted () {
    this.getImportTypes()
    setTimeout(() => {
      this.updateOptions(JSON.parse(localStorage.getItem('OrderImportForm.options') || '{}'))
    }, 500)
  },

  methods: {
    async getImportTypes () {
      // Load select list
      this.import_types = await this.$axios.$get('/api/importdata/import-types/')

      // Load last selected value from localstorage
      const import_type = localStorage.getItem('OrderImportForm.import_type')
      if (import_type && Object.keys(this.import_types).includes(import_type)) {
        this.update({ import_type })
      }
    },

    update (data: any) {
      this.$emit('update:form-data', { ...this.formData, ...data })
    },

    updateOptions (data: any) {
      console.log('updateOptions', data)
      const options = { ...this.formData.options, ...data }
      this.update({ options })

      if (options) {
        localStorage.setItem('OrderImportForm.options', JSON.stringify(options))
      }
    },

    updateImportType (import_type: string) {
      this.update({ import_type })

      if (import_type) {
        localStorage.setItem('OrderImportForm.import_type', import_type)
      }
    },

    submit () {
      this.$emit('submit')
    },

    validate () {
      return (this.$refs.form as unknown as VForm).validate()
    }
  }
})
</script>
