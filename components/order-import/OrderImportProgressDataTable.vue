<template>
  <v-data-table
    :footer-props="{'items-per-page-options': [5, 10, 25, 50, 100]}"
    :headers="headers"
    :items="items"
    :expanded.sync="expanded"
    :options="tableData.options"
    :server-items-length="tableData.meta.total_results"
    :single-expand="true"
    show-expand
    @update:options="update({ options: $event })"
  >
    <template #item.create_date="{item}">
      {{ $datetime(item.create_date) }}
    </template>
    <template #item.file="{item}">
      <div style="max-width: 900px; overflow-x: auto;">
        <a :href="item.file">{{ getFileName(item.file) }}</a>
      </div>
    </template>
    <template #item.status="{item}">
      <div>
        {{ item.status }}
        <v-btn v-if="$auth.user.is_superuser && item.status == 'in-queue'" x-small color="primary" outlined @click="startImport(item)">
          Import
        </v-btn>
      </div>
    </template>

    <!-- Expanded -->
    <template #expanded-item="{ headers, item }">
      <td :colspan="headers.length" style="white-space: pre;" v-text="item.log" />
    </template>
  </v-data-table>
</template>

<script lang="ts">
import Vue from 'vue'
import { VDataTableHeader } from '~/models'
export default Vue.extend({
  props: {
    tableData: {
      type: Object,
      required: true
    },
    importType: {
      type: String,
      required: false,
      default: '001-full'
    }
  },
  data () {
    return {

      expanded: []
    }
  },
  computed: {
    headers (): VDataTableHeader[] {
      return [
        { text: this.$t('order-import.date-time'), value: 'create_date', sortable: true },
        { text: this.$t('order-import.file'), value: 'file', sortable: false },
        { text: this.$t('order-import.status'), value: 'status', sortable: false },
        { text: this.$t('order-import.by'), value: 'create_by.full_name', sortable: false },
        { text: '', value: 'data-table-expand' }
      ]
    },
    items (): any[] {
      return this.tableData.import_requests
    }
  },

  methods: {
    update (data: any) {
      this.$emit('update:table-data', { ...this.tableData, ...data })
    },

    getFileName (file_path: string) {
      let file_name = file_path.split('/').pop()

      if (file_name?.includes('?')) {
        file_name = file_name.split('?')[0]
      }

      return file_name
    },

    async startImport (item: any) {
      this.$loader(true)
      try {
        await this.$axios.post(`/api/importdata/orders/import/${this.importType}/`, { import_request_id: item.id })
        this.$loader(false)
        alert('ok')
        window.location.reload()
      } catch (e) {
        this.$loader(false)
        alert('error, check console')
        console.log(e)
      }
    }
  }
})
</script>
