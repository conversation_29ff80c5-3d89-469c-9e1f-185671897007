td.fit,th.fit {
  width: 1%;
  white-space: nowrap;
  padding-left: 0;
  padding-right: 0;
}

.main-control-input input:focus {
  background-color: #FCF7CC;
}

div.text-help {
  padding-left: 33px;
  font-size: 0.85rem;
  color: #555555;
}

label.w-100 {
  width: 100px;
  font-weight: bold;
  display: inline-block;
}

label.w-120 {
  width: 120px;
  font-weight: bold;
  display: inline-block;
}

@media print {
    /* Hide the print button */
    .no-print {
      display: none !important;
      visibility: hidden !important;
      height: 0 !important;
    }

    body {
      visibility: hidden;
      -webkit-print-color-adjust: exact;
    }

    .print-container {
        visibility: visible;
    }

    .A4 {
        visibility: visible;
        position: absolute;
        left: 0;
        top: 0;
        margin: 0 !important;
        border: none !important;
        box-shadow: none !important;
        counter-increment: page;
        content: 'Page ' counter(page);
    }

    .header { 
        display: block; 
        text-align: center; 
    } 

    .v-main {
      padding: 0px !important; 
    }

}