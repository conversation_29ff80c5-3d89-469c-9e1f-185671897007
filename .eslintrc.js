module.exports = {
  root: true,
  env: {
    browser: true,
    node: true
  },
  extends: [
    '@nuxtjs/eslint-config-typescript',
    'plugin:nuxt/recommended',
    'plugin:vue/base'
  ],
  plugins: [
  ],
  // add your custom rules here
  rules: {
    camelcase: 'off',
    'vue/valid-v-slot': 'off',
    'vue/valid-v-model': 'off',
    'eslintvue/no-v-model-argument': 'off',
    'import/no-mutable-exports': 'off'
  }
}
