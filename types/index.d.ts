// OAuth2 Code Model
// https://developers.google.com/identity/oauth2/web/reference/js-reference#google.accounts.oauth2.initCodeClient

import { FFmpeg } from '@ffmpeg/ffmpeg'

declare namespace google {

  namespace accounts {

    namespace oauth2 {
      export interface CodeResponse {
        code: string
        scope: string
        state: string
        error: string
        error_description: string
        error_uri: string
      }

      export interface CodeClientConfig {
        client_id: string
        scope: string
        redirect_uri?: string
        callback?: Function
        state?: string
        enable_serial_consent?: string
        hint?: string
        hosted_domain?: string
        ux_mode?: string
        select_account?: string
      }

      export interface CodeClient {
        requestCode(): void;
      }

      export function revoke(access_token: string, callback: Function): void
      export function initCodeClient(config: CodeClientConfig): CodeClient
    }

    namespace id {
      interface IdConfiguration {
        client_id: string
        callback: Function
        nonce?: string
        auto_select?: boolean
      }
      export function initialize(config: IdConfiguration): void
      export function prompt(args: any): void
      export function revoke(): void
      export function cancel(): void
      export function renderButton(...args: any[]): any
    }
  }
}

declare global {
  export interface Window {
    google: any
    ffmpeg: FFmpeg | null
    printnode: any
  }
}
