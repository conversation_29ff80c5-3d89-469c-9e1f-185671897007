# Copyright 2020 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# [START cloudrun_django_cloudmigrate]
steps:
  - id: "build & push image"
    name: 'gcr.io/kaniko-project/executor:latest'
    args:
    - --destination=gcr.io/${PROJECT_ID}/${_SERVICE_NAME}
    - --cache=true
    - --cache-ttl=48h
  
  # Deploy container image to Cloud Run (Main)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args: [
      'run', 'deploy', '${_SERVICE_NAME}', 
      '--platform', 'managed',
      '--region', '${_REGION}',
      '--image', 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}', 
      '--allow-unauthenticated'
    ]

  # Deploy container image to Cloud Run (Primrypie)
  # - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
  #   entrypoint: gcloud
  #   args: [
  #     'run', 'deploy', '${_PRIMRYPIE_SERVICE_NAME}', 
  #     '--platform', 'managed',
  #     '--region', '${_REGION}',
  #     '--image', 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}', 
  #     '--allow-unauthenticated'
  #   ]

substitutions:
  _REGION: asia-southeast1
  _SERVICE_NAME: dobybot-ui-service
  _PRIMRYPIE_SERVICE_NAME: primrypie-dobybot-ui-service

# images:
#   - "gcr.io/${PROJECT_ID}/${_SERVICE_NAME}"
# [END cloudrun_django_cloudmigrate]
