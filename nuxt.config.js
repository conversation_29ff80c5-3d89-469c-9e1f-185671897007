import colors from 'vuetify/es5/util/colors'

export default {
  // Disable server-side rendering (https://go.nuxtjs.dev/ssr-mode)
  ssr: false,

  // middleware to set headers on pages
  serverMiddleware:
    process.env.NODE_ENV === 'development'
      ? ['~/middleware/same-origin-header.ts']
      : [],

  // Target (https://go.nuxtjs.dev/config-target)
  target: 'static',
  server: {
    host: '0.0.0.0', // Listen on all network interfaces
    port: 3000
  },

  // Global page headers (https://go.nuxtjs.dev/config-head)
  head: {
    title: 'Dobybot.com',
    meta: [
      { charset: 'utf-8' },
      {
        name: 'viewport',
        content:
          'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, minimal-ui'
      },
      { hid: 'description', name: 'description', content: '' },

      // Enable SharedArrayBuffer for development
      //
      // Add the meta tag <meta http-equiv="origin-trial" content="token">
      // Get https://developer.chrome.com/origintrials/#/view_trial/303992974847508481
      process.env.NODE_ENV !== 'production'
        ? {
          'http-equiv': 'origin-trial',
          content:
              'AuKHz2ifylDpZfg7iawfCDGtilbLT+tg9CCbVG4PNSxRA1v2NtYGRKAKctmE4Us42+t1lZ3IkPO9s5/VlHy/rwIAAABgeyJvcmlnaW4iOiJodHRwOi8vbG9jYWxob3N0OjMwMDAiLCJmZWF0dXJlIjoiVW5yZXN0cmljdGVkU2hhcmVkQXJyYXlCdWZmZXIiLCJleHBpcnkiOjE2NTg4Nzk5OTl9'
        }
        : {}
    ],
    link: [
      { rel: 'icon', type: 'image/png', href: '/favicon.png' },
      {
        rel: 'stylesheet',
        href: 'https://cdn.jsdelivr.net/npm/intro.js@7.2.0/minified/introjs.min.css'
      },
      {
        rel: 'stylesheet',
        href: 'https://unpkg.com/@mdi/font@7.4.47/css/materialdesignicons.min.css',
        crossorigin: 'anonymous'
      }
    ],
    script: [
      {
        src: 'https://apis.google.com/js/api.js'
      },
      {
        src: 'https://accounts.google.com/gsi/client'
      },
      {
        src: '/static/js/webrtc/EBML.js'
      },
      {
        src: '/static/js/printnode/printnode.js'
      },
      {
        src: 'https://cdn.jsdelivr.net/npm/intro.js@7.2.0/intro.min.js'
      }
    ]
  },

  // Global CSS (https://go.nuxtjs.dev/config-css)
  css: [
    // SCSS file in the project
    '@/assets/css/main.scss'
  ],

  // Plugins to run before rendering page (https://go.nuxtjs.dev/config-plugins)
  plugins: [
    { src: './plugins/posthog', mode: 'client' },
    '~/plugins/hello.ts',
    '~/plugins/db.ts',
    '~/plugins/gapi.client.ts',
    '~/plugins/snackbar.ts',
    '~/plugins/alert.ts',
    '~/plugins/loading.ts',
    '~/plugins/axios-accessor.ts',
    '~/plugins/permission.ts',
    '~/plugins/rules.ts',
    '~/plugins/moment.ts',
    '~/plugins/axios.ts',
    '~/plugins/versions.ts',
    '~/plugins/notification.ts',
    '~/plugins/package.ts',
    '~/plugins/intro.ts',
    '~/plugins/currency.ts',
    '~/plugins/string-util.ts'
  ],

  // Auto import components (https://go.nuxtjs.dev/config-components)
  components: true,

  // Modules for dev and build (recommended) (https://go.nuxtjs.dev/config-modules)
  buildModules: ['@nuxtjs/dotenv', '@nuxt/typescript-build', '@nuxtjs/vuetify'],

  // Modules (https://go.nuxtjs.dev/config-modules)
  modules: [
    '@nuxtjs/axios',
    '@nuxtjs/auth-next',
    '@nuxtjs/gtm',
    '@nuxtjs/pwa',
    '@nuxtjs/sentry',
    '@nuxtjs/i18n'
  ],

  sentry: {
    dsn: process.env.SENTRY_DSN,
    disabled: !process.env.SENTRY_DSN,
    // Additional Module Options go here
    // https://sentry.nuxtjs.org/sentry/options
    config: {
      // Add native Sentry config here
      // https://docs.sentry.io/platforms/javascript/guides/vue/configuration/options/
    }
  },

  // PWA https://pwa.nuxtjs.org/setup
  pwa: {
    manifest: {
      name: 'Dobybot',
      short_name: 'Dobybot',
      lang: 'th'
    }
  },

  // i18n https://i18n.nuxtjs.org/lazy-load-translations
  i18n: {
    strategy: 'no_prefix',
    locales: [
      {
        name: 'ไทย',
        code: 'th',
        file: 'th-TH.json'
      },
      {
        name: 'English',
        code: 'en',
        file: 'en-US.json'
      }
    ],
    lazy: true,
    langDir: 'lang/',
    defaultLocale: 'th'
    // detectBrowserLanguage: {
    //   alwaysRedirect: false,
    //   fallbackLocale: 'th',
    //   redirectOn: 'root',
    //   useCookie: true,
    //   cookieAge: 365,
    //   cookieCrossOrigin: false,
    //   cookieDomain: null,
    //   cookieKey: 'i18n_redirected',
    //   cookieSecure: false
    // }
  },

  // Google Tag Manager (https://github.com/nuxt-community/gtm-module)
  gtm: {
    id: 'G-JSJ5MTWLVC',
    enabled: process.env.NODE_ENV === 'production',
    debug: false,
    scriptURL: 'https://www.googletagmanager.com/gtag/js',
    pageTracking: true
  },

  // Axios module configuration (https://go.nuxtjs.dev/config-axios)
  axios: {
    // Move to ~/plugins/axios.ts
    // baseURL: process.env.NODE_ENV === 'production' ? 'https://api.dobybot.com' : 'http://localhost:8000'
  },

  // Nuxt Auth
  auth: {
    redirect: {
      login: '/login',
      home: false
    },
    cookie: false, // ใน doc บอกว่า cookie ใช้ สำหรับ ssr
    // ถ้าจำเป็นต้องใช้ cookie แต่ยังหาวิธีทำให้เป็น httponly ไม่ได้
    // cookie: {
    //   prefix: 'auth.',
    //   options: {
    //     path: '/',
    //     secure: true
    //   }
    // },
    strategies: {
      local: {
        scheme: 'refresh',
        endpoints: {
          login: { url: '/api/users/login/', method: 'post' },
          refresh: { url: '/api/users/refresh/', method: 'post' },
          logout: { url: '/api/users/logout/', method: 'post' },
          user: { url: '/api/users/me/', method: 'get' }
        },
        token: {
          property: 'access',
          maxAge: 60 * 5 // 5 minutes
        },
        refreshToken: {
          property: 'refresh',
          data: 'refresh',
          maxAge: 60 * 60 * 24 * 3 // 3 days
        },
        user: {
          property: false,
          autoFetch: true
        }
      }
    }
  },

  // Vuetify module configuration (https://go.nuxtjs.dev/config-vuetify)
  vuetify: {
    customVariables: ['~/assets/variables.scss'],
    theme: {
      // dark: true,
      themes: {
        dark: {
          primary: colors.blue.darken2,
          accent: colors.grey.darken3,
          secondary: colors.amber.darken3,
          info: colors.teal.lighten1,
          warning: colors.amber.base,
          error: colors.deepOrange.accent4,
          success: colors.green.accent3
        }
      }
    }
  },

  router: {
    extendRoutes (routes, resolve) {
      routes.push({
        path: '/image-viewer',
        components: {
          default: resolve(__dirname, 'pages/public/image-viewer')
        }
      })
      routes.push({
        path: '/video-viewer',
        components: {
          default: resolve(__dirname, 'pages/public/video-viewer')
        }
      })
      routes.push({
        path: '/receipt',
        components: {
          default: resolve(__dirname, 'pages/public/receipt')
        }
      })
      routes.push({
        path: '/$/record/',
        components: {
          default: resolve(__dirname, 'pages/record/index')
        }
      })
      routes.push({
        path: '/request-etax',
        components: {
          default: resolve(__dirname, 'pages/public/request-etax')
        }
      })
      routes.push({
        path: '/v2/request-etax',
        components: {
          default: resolve(__dirname, 'pages/public/request-etax2')
        }
      })
      routes.push({
        path: '/v3/request-etax',
        components: {
          default: resolve(__dirname, 'pages/public/request-etax3')
        }
      })
    }
  },

  // Build Configuration (https://go.nuxtjs.dev/config-build)
  build: {
    transpile: ['json-editor-vue'],
    extend (config, ctx) {
      config.module.rules.push({
        test: /\.(ogg|mp3|wav|mpe?g)$/i,
        loader: 'file-loader',
        options: {
          name: '[path][name].[ext]'
        }
      })

      config.module.rules.push({
        test: /\.mjs$/,
        include: /node_modules/,
        type: 'javascript/auto'
      })

      if (ctx.isDev) {
        config.devtool = ctx.isClient ? 'source-map' : 'inline-source-map'
      }
    }
  }
}
